name: DEV deployment
on:
  push:
    branches:
      - staging

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: SSH Remote Commands
        uses: appleboy/ssh-action@v1.2.2
        with:
          host: ${{ secrets.DEV_HOST }}
          username: ${{ secrets.DEV_USER }}
          key: ${{ secrets.DEV_KEY }}
          script: |
            set -e
            cd ~/v2.storeseo.dev
            git pull origin staging
            # pm2 stop web/ecosystem-dev.config.js
            pnpm install
            pnpm sequelize db:migrate
            pnpm run translate
            pnpm run build -c shopify.app.storeseo-dev.toml
            pnpm app:dev
