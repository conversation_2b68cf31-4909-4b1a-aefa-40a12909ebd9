name: Admin DEV deployment
on:
  push:
    branches:
      - admin-staging

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: SSH Remote Commands
        uses: appleboy/ssh-action@v1.2.2
        with:
          host: ${{ secrets.DEV_HOST }}
          username: ${{ secrets.DEV_USER }}
          key: ${{ secrets.DEV_KEY }}
          script: |
            set -e
            cd ~/admin.api.storeseo.dev/
            git pull origin admin-staging
            pnpm install
            pnpm sequelize db:migrate
            pnpm run admin:restart
