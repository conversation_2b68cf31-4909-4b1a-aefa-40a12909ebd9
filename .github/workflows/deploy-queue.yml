name: "[PROD] - Queue deployment"
on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: SSH Remote Commands
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.QUEUE_HOST }}
          username: ${{ secrets.QUEUE_USER }}
          key: ${{ secrets.QUEUE_KEY }}
          script_stop: true
          script: |
            export NVM_DIR=~/.nvm
            source ~/.nvm/nvm.shg
            cd ~/v2.storeseo.com/
            git pull origin main
            pnpm install
            pnpm run app:queue
