name: Admin API Deployment
on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: SSH Remote Commands
        uses: appleboy/ssh-action@v1.2.2
        with:
          host: ${{ secrets.ADMIN_HOST }}
          username: ${{ secrets.ADMIN_USER }}
          key: ${{ secrets.ADMIN_KEY }}
          script: |
            set -e
            cd /var/www/html/admin.api.storeseo.com/
            git checkout main
            git pull origin main
            pnpm install
            # pnpm run sequelize db:migrate
            pnpm run admin:restart
