const { FREE, PRO } = require("storeseo-enums/planType");
const { LIFETIME, MONTHLY, ANNUALLY, USAGE } = require("storeseo-enums/planInterval");
const { ACTIVE } = require("storeseo-enums/planStatus");

module.exports = [
  {
    name: "Free",
    subtitle: "Start FREE with the essential features and drive traffic to your store",
    type: FREE,
    interval: LIFETIME,
    price: 0,
    discount: 0,
    status: ACTIVE,
    featured: false,
    order: 0,
    rules: {
      products: 50,

      // common features
      seo_analysis: true,
      control_sitemap: true,
      fix_instruction: true,
      unlimited_tags: true,
      alt_text: true,
      json_ld: true,
      keyword_analytics: true,
      seo_reports: true,
      bulk_seo: true,
      redirect_out_of_stock: true,

      // pro features
      instant_indexing: false,
      html_sitemap: false,
      google_console: false,
      sitemap_submission: false,
      google_analytics: false,
      local_seo: false,
      preview_snippets: false,
      multi_language_seo: false,

      // add-ons
      image_optimizer: null,
      ai_optimizer: null,
    },
  },
  {
    name: "Basic",
    subtitle: "Access core features to build a strong foundation for your needs",
    type: PRO,
    interval: MONTHLY,
    price: 12.35,
    discount: 0,
    status: ACTIVE,
    featured: true,
    order: 2,
    coupon_code: "TREAT30",
    rules: {
      products: 1000,

      // common features
      seo_analysis: true,
      control_sitemap: true,
      fix_instruction: true,
      unlimited_tags: true,
      alt_text: true,
      json_ld: true,
      keyword_analytics: true,
      seo_reports: true,
      bulk_seo: true,
      redirect_out_of_stock: true,

      // pro features
      instant_indexing: true,
      html_sitemap: true,
      google_console: true,
      sitemap_submission: true,
      google_analytics: true,
      local_seo: true,
      preview_snippets: true,
      multi_language_seo: false,

      // add-ons
      image_optimizer: null,
      ai_optimizer: null,
    },
  },
  {
    name: "Premium",
    subtitle: "Unlock advanced tools and features designed for growing businesses",
    type: PRO,
    interval: MONTHLY,
    price: 49.99,
    discount: 0,
    status: ACTIVE,
    featured: true,
    order: 4,
    coupon_code: "TREAT40",
    rules: {
      products: 5000,

      // common features
      seo_analysis: true,
      control_sitemap: true,
      fix_instruction: true,
      unlimited_tags: true,
      alt_text: true,
      json_ld: true,
      keyword_analytics: true,
      seo_reports: true,
      bulk_seo: true,
      redirect_out_of_stock: true,

      // pro features
      instant_indexing: true,
      html_sitemap: true,
      google_console: true,
      sitemap_submission: true,
      google_analytics: true,
      local_seo: true,
      preview_snippets: true,
      multi_language_seo: true,

      // add-ons
      image_optimizer: null,
      ai_optimizer: null,
    },
  },
  {
    name: "Enterprise",
    subtitle: "Designed for large-scale businesses with unlimited product optimization",
    type: PRO,
    interval: MONTHLY,
    price: 499.99,
    discount: 0,
    status: ACTIVE,
    featured: false,
    order: 3,
    coupon_code: "TREAT30",
    rules: {
      products: null,

      // common features
      seo_analysis: true,
      control_sitemap: true,
      fix_instruction: true,
      unlimited_tags: true,
      alt_text: true,
      json_ld: true,
      keyword_analytics: true,
      seo_reports: true,
      bulk_seo: true,
      redirect_out_of_stock: true,

      // pro features
      instant_indexing: true,
      html_sitemap: true,
      google_console: true,
      sitemap_submission: true,
      google_analytics: true,
      local_seo: true,
      preview_snippets: true,
      multi_language_seo: true,

      // add-ons
      image_optimizer: null,
      ai_optimizer: null,
    },
  },
  {
    name: "Basic",
    subtitle: "Access core features to build a strong foundation for your needs",
    type: PRO,
    interval: ANNUALLY,
    price: 99.97,
    discount: 0,
    status: ACTIVE,
    featured: true,
    order: 2,
    coupon_code: "TREAT32",
    rules: {
      products: 1000,

      // common features
      seo_analysis: true,
      control_sitemap: true,
      fix_instruction: true,
      unlimited_tags: true,
      alt_text: true,
      json_ld: true,
      keyword_analytics: true,
      seo_reports: true,
      bulk_seo: true,
      redirect_out_of_stock: true,

      // pro features
      instant_indexing: true,
      html_sitemap: true,
      google_console: true,
      sitemap_submission: true,
      google_analytics: true,
      local_seo: true,
      preview_snippets: true,
      multi_language_seo: false,

      // add-ons
      image_optimizer: null,
      ai_optimizer: null,
    },
  },
  {
    name: "Premium",
    subtitle: "Unlock advanced tools and features designed for growing businesses",
    type: PRO,
    interval: ANNUALLY,
    price: 499.99,
    discount: 0,
    status: ACTIVE,
    featured: true,
    order: 2,
    coupon_code: "TREAT19",
    rules: {
      products: 5000,

      // common features
      seo_analysis: true,
      control_sitemap: true,
      fix_instruction: true,
      unlimited_tags: true,
      alt_text: true,
      json_ld: true,
      keyword_analytics: true,
      seo_reports: true,
      bulk_seo: true,
      redirect_out_of_stock: true,

      // pro features
      instant_indexing: true,
      html_sitemap: true,
      google_console: true,
      sitemap_submission: true,
      google_analytics: true,
      local_seo: true,
      preview_snippets: true,
      multi_language_seo: true,

      // add-ons
      image_optimizer: null,
      ai_optimizer: null,
    },
  },
  {
    name: "Enterprise",
    subtitle: "Designed for large-scale businesses with unlimited product optimization",
    type: PRO,
    interval: ANNUALLY,
    price: 2499.99,
    discount: 0,
    status: ACTIVE,
    featured: false,
    order: 3,
    coupon_code: "TREAT18",
    rules: {
      products: null,

      // common features
      seo_analysis: true,
      control_sitemap: true,
      fix_instruction: true,
      unlimited_tags: true,
      alt_text: true,
      json_ld: true,
      keyword_analytics: true,
      seo_reports: true,
      bulk_seo: true,
      redirect_out_of_stock: true,

      // pro features
      instant_indexing: true,
      html_sitemap: true,
      google_console: true,
      sitemap_submission: true,
      google_analytics: true,
      local_seo: true,
      preview_snippets: true,
      multi_language_seo: true,

      // add-ons
      image_optimizer: null,
      ai_optimizer: null,
    },
  },
  {
    name: "Visionary",
    subtitle: "Get the AI-driven SEO and predictive analytics for your eCommerce store",
    type: PRO,
    interval: USAGE,
    price: 2.99,
    discount: 0,
    status: ACTIVE,
    featured: false,
    order: 1,
    meta: { isTest: false, trialDays: 0, cappedAmount: 100, duration: 18, isSpecial: true },
    rules: {
      products: 200,

      // common features
      seo_analysis: true,
      control_sitemap: true,
      fix_instruction: true,
      unlimited_tags: true,
      alt_text: true,
      json_ld: true,
      keyword_analytics: true,
      seo_reports: true,
      bulk_seo: true,
      redirect_out_of_stock: true,

      // pro features
      instant_indexing: true,
      html_sitemap: true,
      google_console: true,
      sitemap_submission: true,
      google_analytics: true,
      local_seo: true,
      preview_snippets: true,
      multi_language_seo: false,

      // add-ons
      image_optimizer: null,
      ai_optimizer: null,
    },
  },
];
