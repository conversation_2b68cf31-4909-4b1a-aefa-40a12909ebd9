module.exports = {
  port: parseInt(process.env.PORT, 10) || 3000,
  environment: process.env.NODE_ENV !== "production",
  url: process.env.HOST,
  /*
   * Product will be set to optimized over this value
   * */
  optimizePercent: 75,
  /*
   * Optimization rules will be set to true over this value
   * */
  ruleOptimizePercent: 75,
  bulkOptimizationBatchSize: 3000,

  freeProductLimit: 50,
  freeProductTagLimit: 5,
  isSubscriptionTestMode: process.env.SHOPIFY_SUBSCRIPTION_TEST_MODE === "true",

  mailchimp: {
    apiKey: process.env.MAILCHIMP_API_KEY || "************************************",
    listId: process.env.MAILCHIMP_LIST_ID || "2e6e32458a",
    serverPrefix: process.env.MAILCHIMP_SERVER_PREFIX || "us8",
  },

  fluentcrm: {
    username: process.env.FLUENT_CRM_API_USER || "shamayel",
    password: process.env.FLUENT_CRM_API_PASSWORD || "UWje 9CNQ pPNv fTDJ yCEQ ICfx",
    baseURL: process.env.FLUENT_CRM_API_BASE_URL || "https://storeseo.com/wp-json/fluent-crm/v2",
  },

  currency: "USD",

  webhookPath: "/webhooks/v1",

  betterdocsWebhookPath: "/better-docs/webhooks/v1",

  // Shopify report timezone
  report: { tz: "Asia/Dhaka" },

  pubSub: {
    projectId: "storeseo-webhook",
    topicId: "storeseo-webhook",
  },
};
