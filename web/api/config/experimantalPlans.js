const { PRO } = require("storeseo-enums/planType");
const { MONTHLY } = require("storeseo-enums/planInterval");
const { ACTIVE, SPECIAL } = require("storeseo-enums/planStatus");

const freeFeatures = {
  seo_analysis: true,
  control_sitemap: true,
  fix_instruction: true,
  unlimited_tags: true,
  alt_text: true,
  json_ld: true,
  keyword_analytics: true,
  seo_reports: true,
  bulk_seo: true,
  redirect_out_of_stock: true,
};

const proFeatures = {
  instant_indexing: true,
  html_sitemap: true,
  google_console: true,
  sitemap_submission: true,
  google_analytics: true,
  local_seo: true,
  preview_snippets: true,
};

const addons = {
  image_optimizer: null,
  ai_optimizer: null,
};

module.exports = [
  {
    name: "Essential",
    subtitle: "Affordable start for small businesses to build their small and mid size stores",
    type: PRO,
    interval: MONTHLY,
    price: 39.99,
    discount: 0,
    status: ACTIVE,
    featured: false,
    order: 1,
    coupon_code: null,
    rules: {
      products: 250,

      // common features
      ...freeFeatures,

      // pro features
      ...proFeatures,
      multi_language_seo: false,

      // add-ons
      ...addons,
    },
  },
  {
    name: "Growth",
    subtitle: "For businesses & established stores with significant global reach and content needs",
    type: PRO,
    interval: MONTHLY,
    price: 99.99,
    discount: 0,
    status: ACTIVE,
    featured: false,
    order: 2,
    coupon_code: null,
    rules: {
      products: 1000,

      // common features
      ...freeFeatures,

      // pro features
      ...proFeatures,
      multi_language_seo: true,

      // add-ons
      ...addons,
    },
  },
  {
    name: "Advanced",
    subtitle: "Perfect for multi-vendor stores, global reach, advanced SEO & AI content needs",
    type: PRO,
    interval: MONTHLY,
    price: 249.99,
    discount: 0,
    status: ACTIVE,
    featured: false,
    order: 3,
    coupon_code: null,
    rules: {
      products: 10000,

      // common features
      ...freeFeatures,

      // pro features
      ...proFeatures,
      multi_language_seo: true,

      // add-ons
      ...addons,
    },
  },
  {
    name: "Elite",
    subtitle: "Perfect for multi-vendor stores, global reach, advanced SEO & AI content needs",
    type: PRO,
    interval: MONTHLY,
    price: 499.99,
    discount: 0,
    status: SPECIAL,
    featured: false,
    order: 4,
    coupon_code: null,
    rules: {
      products: 20000,

      // common features
      ...freeFeatures,

      // pro features
      ...proFeatures,
      multi_language_seo: true,

      // add-ons
      ...addons,
    },
  },
];
