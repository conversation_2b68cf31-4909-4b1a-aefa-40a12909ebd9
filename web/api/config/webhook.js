const app = require("../config/app");
const { DeliveryMethod } = require("@shopify/shopify-api");
const { NAMESPACE } = require("storeseo-enums/metafields");

const TOPICS = require("storeseo-enums/webhook-topics");
const PRIORITY = {
  MANDATORY: 1,
  ADDITIONAL: 2,
};

const PATH_PREFIX = app.webhookPath;

const PATH = {
  [TOPICS.PRODUCTS_CREATE]: `${PATH_PREFIX}/product/create`,
  [TOPICS.PRODUCTS_UPDATE]: `${PATH_PREFIX}/product/update`,
  [TOPICS.PRODUCTS_DELETE]: `${PATH_PREFIX}/product/delete`,
  [TOPICS.APP_SUBSCRIPTIONS_UPDATE]: `${PATH_PREFIX}/app-subscriptions/update`,
  [TOPICS.APP_PURCHASES_ONE_TIME_UPDATE]: `${PATH_PREFIX}/app-purchase/update`,
  [TOPICS.THEMES_PUBLISH]: `${PATH_PREFIX}/themes/publish`,
  [TOPICS.SHOP_UPDATE]: `${PATH_PREFIX}/shop/update`,
  [TOPICS.APP_UNINSTALLED]: `${PATH_PREFIX}/app/uninstalled`,
  [TOPICS.LOCATIONS_CREATE]: `${PATH_PREFIX}/locations/create`,
  [TOPICS.LOCATIONS_UPDATE]: `${PATH_PREFIX}/locations/update`,
  [TOPICS.LOCATIONS_DELETE]: `${PATH_PREFIX}/locations/delete`,
  [TOPICS.BULK_OPERATIONS_FINISH]: `${PATH_PREFIX}/bulk-operation-finish`,
  // Collection
  [TOPICS.COLLECTIONS_CREATE]: `${PATH_PREFIX}/collection/create`,
  [TOPICS.COLLECTIONS_UPDATE]: `${PATH_PREFIX}/collection/update`,
  [TOPICS.COLLECTIONS_DELETE]: `${PATH_PREFIX}/collection/delete`,
  // GDPR
  [TOPICS.CUSTOMERS_DATA_REQUEST]: `${PATH_PREFIX}/customer-data`,
  [TOPICS.CUSTOMERS_REDACT]: `${PATH_PREFIX}/customer-redact`,
  [TOPICS.SHOP_REDACT]: `${PATH_PREFIX}/shop-redact`,
  // Locales
  [TOPICS.LOCALES_CREATE]: `${PATH_PREFIX}/locales/create`,
  [TOPICS.LOCALES_UPDATE]: `${PATH_PREFIX}/locales/update`,
};

// const DeliveryMethod.Http = process.env.WEBHOOK === "http" ? DeliveryMethod.Http : DeliveryMethod.PubSub;

const WEBHOOKS = [
  {
    priority: PRIORITY.ADDITIONAL,
    topic: TOPICS.PRODUCTS_CREATE,
    deliveryMethod: DeliveryMethod.Http,
    path: PATH[TOPICS.PRODUCTS_CREATE],
  },
  {
    priority: PRIORITY.ADDITIONAL,
    topic: TOPICS.PRODUCTS_UPDATE,
    path: PATH[TOPICS.PRODUCTS_UPDATE],
    includeFields: [
      "id",
      "handle",
      "title",
      "body_html",
      "metafields",
      "media",
      "admin_graphql_api_id",
      "tags",
      "defaultCursor",
      "onlineStoreUrl",
      "product_type",
      "vendor",
      "tags",
      "status",
      "published_at",
      "published_scope",
    ],
    metafieldNamespaces: [NAMESPACE.SEO, NAMESPACE.GLOBAL, NAMESPACE.STORE_SEO],
    deliveryMethod: DeliveryMethod.Http,
  },
  {
    priority: PRIORITY.ADDITIONAL,
    topic: TOPICS.PRODUCTS_DELETE,
    path: PATH[TOPICS.PRODUCTS_DELETE],
    deliveryMethod: DeliveryMethod.Http,
  },
  {
    priority: PRIORITY.ADDITIONAL,
    topic: TOPICS.THEMES_PUBLISH,
    path: PATH[TOPICS.THEMES_PUBLISH],
    deliveryMethod: DeliveryMethod.Http,
  },
  {
    priority: PRIORITY.MANDATORY,
    topic: TOPICS.SHOP_UPDATE,
    path: PATH[TOPICS.SHOP_UPDATE],
    deliveryMethod: DeliveryMethod.Http,
  },
  {
    priority: PRIORITY.ADDITIONAL,
    topic: TOPICS.LOCATIONS_CREATE,
    path: PATH[TOPICS.LOCATIONS_CREATE],
    deliveryMethod: DeliveryMethod.Http,
  },
  {
    priority: PRIORITY.ADDITIONAL,
    topic: TOPICS.LOCATIONS_UPDATE,
    path: PATH[TOPICS.LOCATIONS_UPDATE],
    deliveryMethod: DeliveryMethod.Http,
  },
  {
    priority: PRIORITY.ADDITIONAL,
    topic: TOPICS.LOCATIONS_DELETE,
    path: PATH[TOPICS.LOCATIONS_DELETE],
    deliveryMethod: DeliveryMethod.Http,
  },
  {
    priority: PRIORITY.MANDATORY,
    topic: TOPICS.BULK_OPERATIONS_FINISH,
    path: PATH[TOPICS.BULK_OPERATIONS_FINISH],
    deliveryMethod: DeliveryMethod.Http,
  },
  {
    priority: PRIORITY.MANDATORY,
    topic: TOPICS.APP_SUBSCRIPTIONS_UPDATE,
    path: PATH[TOPICS.APP_SUBSCRIPTIONS_UPDATE],
    deliveryMethod: DeliveryMethod.Http,
  },
  {
    priority: PRIORITY.MANDATORY,
    topic: TOPICS.APP_PURCHASES_ONE_TIME_UPDATE,
    path: PATH[TOPICS.APP_PURCHASES_ONE_TIME_UPDATE],
    deliveryMethod: DeliveryMethod.Http,
  },
  {
    priority: PRIORITY.MANDATORY,
    topic: TOPICS.APP_UNINSTALLED,
    path: PATH[TOPICS.APP_UNINSTALLED],
    deliveryMethod: DeliveryMethod.Http,
  },
  // Collection
  {
    priority: PRIORITY.ADDITIONAL,
    topic: TOPICS.COLLECTIONS_CREATE,
    path: PATH[TOPICS.COLLECTIONS_CREATE],
    deliveryMethod: DeliveryMethod.Http,
  },
  {
    priority: PRIORITY.ADDITIONAL,
    topic: TOPICS.COLLECTIONS_UPDATE,
    path: PATH[TOPICS.COLLECTIONS_UPDATE],
    deliveryMethod: DeliveryMethod.Http,
  },
  {
    priority: PRIORITY.ADDITIONAL,
    topic: TOPICS.COLLECTIONS_DELETE,
    path: PATH[TOPICS.COLLECTIONS_DELETE],
    deliveryMethod: DeliveryMethod.Http,
  },
  // Locales
  {
    priority: PRIORITY.MANDATORY,
    topic: TOPICS.LOCALES_CREATE,
    path: PATH[TOPICS.LOCALES_CREATE],
    deliveryMethod: DeliveryMethod.Http,
  },
  {
    priority: PRIORITY.MANDATORY,
    topic: TOPICS.LOCALES_UPDATE,
    path: PATH[TOPICS.LOCALES_UPDATE],
    deliveryMethod: DeliveryMethod.Http,
  },
];

module.exports = {
  TOPICS,
  PATH,
  PRIORITY,
  WEBHOOKS,
};
