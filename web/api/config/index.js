require("dotenv").config();
const fs = require("fs");
const path = require("path");

const configPath = __dirname;
const defaultConfigFile = "app.js";
const exceptFiles = ["home.js"];
let config = {};

const scanDir = fs.readdirSync(configPath).filter((f) => exceptFiles.indexOf(f) === -1);

scanDir.forEach((file) => {
  const isJsFile = Boolean(file.match(/\.js+$/i));

  if (isJsFile) {
    const configFile = path.join(configPath, file);
    if (file === defaultConfigFile) {
      const dConfig = require(configFile);
      config = { ...dConfig };
    } else {
      const fConfig = require(configFile);
      const fileKey = file.replace(".js", "");
      config[fileKey] = { ...fConfig };
    }
  }
});

global.config = (key, defaultValue = null) => {
  const value = key.split(".").reduce((prev, next) => prev?.[next], config);

  if (value !== undefined) {
    return value;
  }
  return defaultValue;
};

module.exports = config;
