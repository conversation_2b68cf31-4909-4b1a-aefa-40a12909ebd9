module.exports = [
  // Title Block
  {
    key: "TITLE_CHARACTER_LIMIT",
    value: "Write a product title within 70 characters",
    point: 1,
  },
  {
    key: "TITLE_FOCUS_KEYWORD_AT_THE_BEGINNING",
    value: "Add focus keyword at the beginning of your meta title",
    point: 1,
  },
  {
    key: "TITLE_FOCUS_KEYWORD_IN_PAGE_TITLE",
    value: "Set a focus keyword for your product page",
    point: 1,
  },
  {
    key: "TITLE_UNIQUE_PRODUCT_TITLE",
    value: "Product title is unique",
    point: 1,
  },
  // Description Block
  {
    key: "PRODUCT_DESCRIPTION_LIMIT",
    value: "Write a product description of 50 to 300 words",
    point: 1,
  },
  {
    key: "FOCUS_KEYWORD_IN_DESCRIPTION",
    value: "Use focus keyword and combination in your product description",
    point: 1,
  },
  {
    key: "KEYWORD_DENSITY",
    value: "Focus keyword density is 1-2% focus keyword repetition on product description",
    point: 1,
  },
  {
    key: "UNIQUE_PRODUCT_DESCRIPTION",
    value: "Product description is unique",
    point: 1,
  },
  {
    key: "PRODUCT_META_DESCRIPTION",
    value: "Add a meta description for your product",
    point: 1,
  },
  {
    key: "META_DESCRIPTION_LENGTH",
    value: "Meta description length is within 165 characters",
    point: 1,
  },
  // Alt-text block
  {
    key: "IMAGE_ALT_TEXT",
    value: "Add alt text to all images",
    point: 1,
  },
  {
    key: "IMAGE_ALT_TEXT_HAS_FOCUS_KEYWORD",
    value: "Add at least one image with focus keyword as alt text",
    point: 1,
  },
];

// KEYWORDS: "Add up to ___ tags",
// FOCUS_KEYWORD_IN_H1: "Add focus keyword in the H1 tag of your product page",
// FOCUS_KEYWORD_IN_IMG_ALT_TAG: "Add at least one image with focus keyword as alt text",
// ACTIVE_VOICE: "Use of active voice: ____% ",
// READABILITY_SCORE: "Readability Score: _____ ",
// INTERNAL_LINK: "Add 1 or 2 internal links",
// NO_BROKEN_LINK: "No broken links",
// CHECK_JSON_LD: "Check JSON-LD",
// INSTANT_INDEXING_ENABLED: "Instant Indexing is enabled",
// XML_SITEMAP_ENABLED: "XML Sitemaps have been enabled",
// CONNECTED_GOOGLE_CONSOLE: "Connect With Google Console",
// CONNECTED_GOOGLE_ANALYTICS: "Connect With Google Analytics",
