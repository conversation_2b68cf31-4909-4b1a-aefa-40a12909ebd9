const { loadJSONFileContent } = require("./driver");

module.exports = {
  mapsApiKey: process.env?.GOOGLE_MAPS_API_KEY || null,
  geoCodingApiUrl: "https://maps.googleapis.com/maps/api/geocode/json",
  placeAutocompleteApiUrl: "https://maps.googleapis.com/maps/api/place/autocomplete/json",
  placeDetailsApiUrl: "https://maps.googleapis.com/maps/api/place/details/json",
  oauth: loadJSONFileContent(process.env.GOOGLE_OAUTH_JSON_KEY_FILE),
  bucket: loadJSONFileContent(process.env.GOOGLE_BUCKET_JSON_KEY_FILE),
  bucketName: process.env?.GOOGLE_BUCKET_NAME || "",
  ads: {
    customerId: "9100081966",
    developerToken: "9e8nXTcZiy2CU1OebEgQKw",
    refreshToken:
      "1//0gw78g48WAK_DCgYIARAAGBASNwF-L9IrTTXQjvDvCtPVRVPIrFS6nk2Tgj06SJLFNnTCi6rvdWOOW3D8RwEOUoCSb9a8jri-JSU",
  },
};
