//@ts-check
const {
  OPTIONS,
  EMAIL_NOTIFICATION,
  EMAIL_NOTIFICATION_REPORT,
} = require("storeseo-enums/settings/email-notification");

const blockedEmailDomain = process.env?.BLOCKED_EMAIL_DOMAINS?.split(",") ?? [];

const emailNotificationSettings = Object.keys(OPTIONS).reduce(
  (previousValue, key) => ({
    ...previousValue,
    [key]: {
      enabled: true,
      day: OPTIONS[key]?.configurable ? "monday" : "",
      time: OPTIONS[key]?.instantNotification ? "" : 12,
      configurable: OPTIONS[key]?.configurable,
      instantNotification: OPTIONS[key]?.instantNotification,
    },
  }),
  {}
);

const defaultEmailNotificationSettings = {
  key: EMAIL_NOTIFICATION,
  value: JSON.stringify({
    [EMAIL_NOTIFICATION_REPORT]: true,
    items: emailNotificationSettings,
  }),
  value_type: "json",
};

const imageOptimizerUsageNotification = {
  FIFTY_PERCENT: 50,
  EIGHTY_PERCENT: 80,
  NINETY_FIVE_PERCENT: 95,
};

const aiOptimizerUsageNotification = {
  FIFTY_PERCENT: 50,
  EIGHTY_PERCENT: 80,
  NINETY_FIVE_PERCENT: 95,
};

module.exports = {
  blockedEmailDomain,
  defaultEmailNotificationSettings,
  imageOptimizerUsageNotification,
  aiOptimizerUsageNotification,
};
