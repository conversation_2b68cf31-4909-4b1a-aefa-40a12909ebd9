const formData = require("form-data");
const Mailgun = require("mailgun.js");

let instance;
class MailGunClient {
  #UNSUBSCRIBES = "unsubscribes";
  #mailgun;
  #mg;
  constructor(formData, MAILGUN_API_KEY, MAILGUN_DOMAIN, MAILGUN_FROM_EMAIL, MAILGUN_FROM_NAME) {
    if (instance) {
      throw new Error("You can only create one instance of MailGunClient!");
    }
    instance = this;

    this.domain = MAILGUN_DOMAIN;
    this.fromEmail = MAILGUN_FROM_EMAIL;
    this.fromName = MAILGUN_FROM_NAME;
    this.#mailgun = new Mailgun(formData);
    this.#mg = this.#mailgun.client({
      username: "api",
      key: MAILGUN_API_KEY,
    });
  }

  send = async (message) => {
    return await this.#mg.messages.create(this.domain, { ...message, from: `${this.fromName} <${this.fromEmail}>` });
  };
  getUnsubscribeList = async () => {
    return await this.#mg.suppressions.list(this.domain, this.#UNSUBSCRIBES);
  };
  getUnsubscribeByAddress = async (address) => {
    return await this.#mg.suppressions.get(this.domain, this.#UNSUBSCRIBES, address);
  };
  addUnsubscribeByAddress = async (address) => {
    return await this.#mg.suppressions.create(this.domain, this.#UNSUBSCRIBES, { address });
  };
  deleteUnsubscribeByAddress = async (address) => {
    return await this.#mg.suppressions.destroy(this.domain, this.#UNSUBSCRIBES, address);
  };
}

const MAILGUN_API_KEY = process.env.MAILGUN_API_KEY;
const MAILGUN_DOMAIN = process.env.MAILGUN_DOMAIN;
const MAILGUN_FROM_EMAIL = process.env.MAILGUN_FROM_EMAIL;
const MAILGUN_FROM_NAME = process.env.MAILGUN_FROM_NAME;

const mailGunClient = Object.freeze(
  new MailGunClient(formData, MAILGUN_API_KEY, MAILGUN_DOMAIN, MAILGUN_FROM_EMAIL, MAILGUN_FROM_NAME)
);

module.exports = mailGunClient;
