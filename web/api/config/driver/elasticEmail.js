const ElasticEmail = require("@elasticemail/elasticemail-client");

let instance;
class ElasticEmailClient {
  #defaultClient;
  #apikey;
  #emailApi;
  // #suppressionsApi;
  constructor(ELASTICEMAIL_API_KEY, ELASTICEMAIL_FROM_EMAIL, ELASTICEMAIL_FROM_NAME) {
    if (instance) {
      throw new Error("You can only create one instance of ElasticEmailClient!");
    }
    instance = this;

    this.fromEmail = ELASTICEMAIL_FROM_EMAIL;
    this.fromName = ELASTICEMAIL_FROM_NAME;
    this.#defaultClient = ElasticEmail.ApiClient.instance;
    this.#apikey = this.#defaultClient.authentications["apikey"];
    this.#apikey.apiKey = ELASTICEMAIL_API_KEY;
    this.#emailApi = new ElasticEmail.EmailsApi();
    // this.#suppressionsApi = new ElasticEmail.SuppressionsApi();
  }

  /**
   *
   * @param {{to: string, subject: string, html: string}} message
   * @returns {Promise<{status: number}>}
   */
  send = async (message) => {
    const { to, subject, html } = message;

    const email = ElasticEmail.EmailMessageData.constructFromObject({
      Recipients: [new ElasticEmail.EmailRecipient(to)],
      Content: {
        Body: [
          ElasticEmail.BodyPart.constructFromObject({
            ContentType: "HTML",
            Content: html,
          }),
        ],
        Subject: subject,
        From: `${this.fromName} <${this.fromEmail}>`,
      },
    });

    return new Promise((resolve, reject) => {
      this.#emailApi.emailsPost(email, (error, _data, response) => {
        if (error) {
          reject({
            details: response.body?.Error ?? "Something went wrong",
            status: response.status,
          });
        } else {
          resolve({ status: response.status });
        }
      });
    });
  };
  // getUnsubscribeList = async () => {
  //   this.#suppressionsApi.suppressionsUnsubscribesGet({}, (error, data, response) => {
  //     if (error) {
  //       console.error(error);
  //     } else {
  //       console.log(data);
  //     }
  //   });
  // };
  // getUnsubscribeByAddress = async (address) => {
  //   return await this.#mg.suppressions.get(this.fromEmail, this.#UNSUBSCRIBES, address);
  // };
  // addUnsubscribeByAddress = async (address) => {
  //   return await this.#mg.suppressions.create(this.fromEmail, this.#UNSUBSCRIBES, { address });
  // };
  deleteUnsubscribeByAddress = async (address) => {
    return null;
  };
}

const ELASTICEMAIL_API_KEY = process.env.ELASTICEMAIL_API_KEY;
const ELASTICEMAIL_FROM_EMAIL = process.env.ELASTICEMAIL_FROM_EMAIL;
const ELASTICEMAIL_FROM_NAME = process.env.ELASTICEMAIL_FROM_NAME;

const elasticMailClient = Object.freeze(
  new ElasticEmailClient(ELASTICEMAIL_API_KEY, ELASTICEMAIL_FROM_EMAIL, ELASTICEMAIL_FROM_NAME)
);

module.exports = elasticMailClient;
