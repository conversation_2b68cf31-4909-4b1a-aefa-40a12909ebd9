const cacheKeys = require("storeseo-enums/cacheKeys");

const redisClient = require("./client");

/**
 * Set/read the boolean status flag indicating whether product sync process is currently running or not.
 * @param {string} shop myshopify domain of the shop
 * @param {boolean} [status] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<boolean | null>}
 */
const syncOngoing = async (shop, status) => {
  const key = `${shop}:${cacheKeys.PRODUCT_SYNC_ONGOING}`;

  // if 'status' is provided, update the flag
  if (status !== undefined && status !== null) {
    await redisClient.set(key, String(status));
    return status;
  }

  // return the current flag status
  const flag = await redisClient.get(key);
  return flag === "true";
};

/**
 * Set/read the input file downloaded from Shopify for product sync
 * @param {string} shop myshopify domain of the shop
 * @param {string} [inputFile] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<string | null>}
 */
const syncFile = async (shop, inputFile) => {
  const key = `${shop}:${cacheKeys.PRODUCT_SYNC_FILE}`;

  // if 'inputFile' is provided, update the flag
  if (inputFile !== undefined && inputFile !== null) {
    await redisClient.set(key, String(inputFile));
    return inputFile;
  }

  // return the current flag cursor
  return await redisClient.get(key);
};

/**
 * Set/read the file byte position cursor of the product data file downloaded from Shopify for product sync
 * @param {string} shop myshopify domain of the shop
 * @param {number} [cursor] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<number | null>}
 */
const syncFileCursor = async (shop, cursor) => {
  const key = `${shop}:${cacheKeys.PRODUCT_SYNC_FILE_CURSOR}`;

  // if 'cursor' is provided, update the flag
  if (cursor !== undefined && cursor !== null) {
    await redisClient.set(key, String(cursor));
    return cursor;
  }

  // return the current flag cursor
  const flag = await redisClient.get(key);
  return Number(flag);
};

/**
 * Deletes the cursor of the product sync releated keys
 * @param {string} shop myshopify domain of the shop
 * @returns {Promise<number | null>}
 */
const resetSync = async (shop) => {
  await redisClient.del(`${shop}:${cacheKeys.PRODUCT_SYNC_ONGOING}`);
  await redisClient.del(`${shop}:${cacheKeys.PRODUCT_SYNC_FILE}`);
  await redisClient.del(`${shop}:${cacheKeys.PRODUCT_SYNC_FILE_CURSOR}`);
};

/**
 * Set/read the selected app to migrate data
 * @param {string} shop myshopify domain of the shop
 * @param {string} [appName] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<string | null>}
 */
const migrateDataFromApp = async (shop, appName) => {
  const key = `${shop}:${cacheKeys.MIGRATE_DATA_FROM_APP}`;

  // if 'appName' is provided, update the flag
  if (appName !== undefined && appName !== null) {
    await redisClient.set(key, String(appName));
    return appName;
  }

  // return the current flag appName
  return await redisClient.get(key);
};

/**
 * Set/read the boolean status flag indicating whether mulit-language product sync process is currently running or not.
 * @param {string} shop myshopify domain of the shop
 * @param {string} languageCode language code
 * @param {boolean} [status] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<boolean | null>}
 */
const mulitLanguageSyncOngoing = async (shop, languageCode, status) => {
  const key = `${shop}:${cacheKeys.MULTI_LANGUAGE}:${languageCode}:${cacheKeys.PRODUCT_SYNC_ONGOING}`;

  // if 'status' is provided, update the flag
  if (status !== undefined && status !== null) {
    await redisClient.set(key, String(status));
    return status;
  }

  // return the current flag status
  const flag = await redisClient.get(key);
  return flag === "true";
};

/**
 * Set/read the file byte position cursor of the product data file downloaded from Shopify for product sync
 * @param {string} shop myshopify domain of the shop
 * @param {string} languageCode language code
 * @param {number} [cursor] db id of the last synced product. Leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<number | null>}
 */
const multiLanugageSyncCursor = async (shop, languageCode, cursor) => {
  const key = `${shop}:${cacheKeys.MULTI_LANGUAGE}:${languageCode}:${cacheKeys.PRODUCT_SYNC_CURSOR}`;

  // if 'cursor' is provided, update the flag
  if (cursor !== undefined && cursor !== null) {
    await redisClient.set(key, String(cursor));
    return cursor;
  }

  // return the current flag cursor
  const flag = await redisClient.get(key);
  return Number(flag);
};

module.exports = {
  syncOngoing,
  syncFile,
  syncFileCursor,
  resetSync,
  mulitLanguageSyncOngoing,
  multiLanugageSyncCursor,

  migrateDataFromApp,
};
