const {
  IMAGE_OPTIMIZER,
  USAGE_LIMIT,
  USAGE_COUNT,
  TOTAL_USAGE_COUNT,
  EMAIL_NOTIFICATION,
  LAST_SENT_EMAIL,
} = require("storeseo-enums/cacheKeys");
const redisClient = require("./client");

/**
 *
 * @param {string} shop
 * @param {{limit?: number, addon?: string}} param1
 * @returns {Promise<number>}
 */
const usageLimit = async (shop, { limit, addon = IMAGE_OPTIMIZER }) => {
  const key = `${shop}:${addon}:${USAGE_LIMIT}`;

  // if 'value' is provided, update the key
  if (limit !== undefined && limit !== null) {
    await redisClient.set(key, String(limit));
    return Number(limit);
  }

  // return the current value
  const value = await redisClient.get(key);
  return Number(value);
};

/**
 *
 * @param {string} shop
 * @param {{count?: number, addon?: string}} param1
 * @returns {Promise<number>}
 */
const usageCount = async (shop, { count, addon = IMAGE_OPTIMIZER }) => {
  const key = `${shop}:${addon}:${USAGE_COUNT}`;

  // if 'value' is provided, update the key
  if (count !== undefined && count !== null) {
    await redisClient.set(key, String(count > 0 ? count : 0));
    return addon === IMAGE_OPTIMIZER ? Number(count) : Number(count).toFixed(1);
  }

  // return the current value
  const value = await redisClient.get(key);
  return addon === IMAGE_OPTIMIZER ? Number(value) : Number(value).toFixed(1);
};

/**
 * @param {string} shop domain of the shop
 * @param {{addon: string, incrementBy: string | number}} options additional options
 * @returns {Promise<string>}
 */
const incrementUsageCount = async (shop, { addon = IMAGE_OPTIMIZER, incrementBy = 1 }) => {
  const key = `${shop}:${addon}:${USAGE_COUNT}`;
  const value = await redisClient.incrByFloat(key, Number(incrementBy));
  return addon === IMAGE_OPTIMIZER ? Number(value) : Number(value).toFixed(1);
};

/**
 * @param {string} shop domain of the shop
 * @param {{addon: string, decrementBy: string|number}} options additional options
 * @returns {Promise<string>}
 */
const decrementUsageCount = async (shop, { addon = IMAGE_OPTIMIZER, decrementBy = 1 }) => {
  const key = `${shop}:${addon}:${USAGE_COUNT}`;
  const value = await redisClient.incrByFloat(key, -Number(decrementBy));
  return addon === IMAGE_OPTIMIZER ? Number(value) : Number(value).toFixed(1);
};

const totalUsageCount = async (shop, { addon = IMAGE_OPTIMIZER, count }) => {
  const key = `${shop}:${addon}:${TOTAL_USAGE_COUNT}`;

  // if 'value' is provided, update the key
  if (count !== undefined && count !== null) {
    await redisClient.set(key, String(count));
    return addon === IMAGE_OPTIMIZER ? Number(count) : Number(count).toFixed(1);
  }

  // return the current value
  const value = await redisClient.get(key);
  return addon === IMAGE_OPTIMIZER ? Number(value) : Number(value).toFixed(1);
};

/**
 * @param {string} shop domain of the shop
 * @param {{addon: string, incrementBy: string | number}} options additional options
 * @returns {Promise<string>}
 */
const incrementTotalUsageCount = async (shop, { addon = IMAGE_OPTIMIZER, incrementBy = 1 }) => {
  const key = `${shop}:${addon}:${TOTAL_USAGE_COUNT}`;

  const value = await redisClient.incrByFloat(key, Number(incrementBy));
  return value;
};

/**
 * @param {string} shop domain of the shop
 * @param {{addon: string, decrementBy: string|number}} options additional options
 * @returns {Promise<string>}
 */
const decrementTotalUsageCount = async (shop, { addon = IMAGE_OPTIMIZER, decrementBy = 1 }) => {
  const key = `${shop}:${addon}:${TOTAL_USAGE_COUNT}`;

  const value = await redisClient.incrByFloat(key, -Number(decrementBy));
  return value;
};

const totalAppUsageCount = async (count, { addon = IMAGE_OPTIMIZER }) => {
  const key = `${addon}:${TOTAL_USAGE_COUNT}`;

  // if 'value' is provided, update the key
  if (count !== undefined && count !== null) {
    await redisClient.set(key, String(count));
    return count;
  }

  // return the current value
  const value = await redisClient.get(key);
  return Number(value);
};

/**
 * @param {string} addon name of the addon
 * @param {string} incrementBy increment amount
 * @returns {Promise<string>}
 */
const incrementTotalAppUsageCount = async (addon = IMAGE_OPTIMIZER, incrementBy = 1) => {
  const key = `${addon}:${TOTAL_USAGE_COUNT}`;

  const value = await redisClient.incrByFloat(key, Number(incrementBy));
  return value;
};

/**
 * @param {string} addon name of the addon
 * @param {Number} decrementBy decrement amount
 * @returns {Promise<string>}
 */
const decrementTotalAppUsageCount = async (addon = IMAGE_OPTIMIZER, decrementBy = 1) => {
  const key = `${addon}:${TOTAL_USAGE_COUNT}`;

  const value = await redisClient.incrByFloat(key, -Number(decrementBy));
  return value;
};

const lastEmailSentForUsagePercentage = async (shop, { addon = IMAGE_OPTIMIZER, usagePerecentage }) => {
  const key = `${shop}:${addon}:${EMAIL_NOTIFICATION}:${LAST_SENT_EMAIL}`;

  // if 'value' is provided, update the key
  if (usagePerecentage !== undefined) {
    const value = await redisClient.set(key, usagePerecentage);
    return value;
  }

  // return the current value
  const value = await redisClient.get(key);
  return value;
};

module.exports = {
  usageLimit,
  incrementUsageCount,
  decrementUsageCount,
  usageCount,
  totalUsageCount,
  incrementTotalUsageCount,
  decrementTotalUsageCount,
  totalAppUsageCount,
  incrementTotalAppUsageCount,
  decrementTotalAppUsageCount,
  lastEmailSentForUsagePercentage,
};
