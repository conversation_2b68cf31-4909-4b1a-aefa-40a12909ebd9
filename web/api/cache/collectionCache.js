// @ts-check
const cacheKeys = require("storeseo-enums/cacheKeys");
const redisClient = require("./client");

/**
 * Set/read the boolean status flag indicating whether collection sync process is currently running or not.
 * @param {string} shop myshopify domain of the shop
 * @param {boolean} [status] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<boolean | null>}
 */
const collectionSyncOngoingStatus = async (shop, status) => {
  const key = `${shop}:${cacheKeys.COLLECTION_SYNC}:${cacheKeys.COLLECTION_SYNC_ONGOING}`;
  // if 'status' is provided, update the flag
  if (status !== undefined && status !== null) {
    await redisClient.set(key, String(status));
    return status;
  }

  // return the current flag status
  const flag = await redisClient.get(key);
  return flag === "true";
};

/**
 * Set the last sync cursor for collection by shop domain. It's used for pagination in the sync process.
 * @param {object} shop
 * @param {string} cursor
 * @returns  {Promise<string>}
 */
const setCollectionSyncCursor = async (shop, cursor) => {
  const key = `${shop}:${cacheKeys.COLLECTION_SYNC}:${cacheKeys.COLLECTION_SYNC_CURSOR}`;
  await redisClient.set(key, cursor);
  return cursor;
};

/**
 * Get the last sync cursor for collection by shop domain. It's used for pagination in the sync process.
 * @param {object} shop
 * @returns {Promise<string|null>}
 */
const getCollectionSyncCursor = async (shop) => {
  const key = `${shop}:${cacheKeys.COLLECTION_SYNC}:${cacheKeys.COLLECTION_SYNC_CURSOR}`;
  return redisClient.get(key);
};

/**
 * Remove the last sync cursor for collection by shop domain. It's used for pagination in the sync process.
 * @param {object} shop
 * @returns {Promise<number>}
 */
const removeCollectionSyncCursor = async (shop) => {
  const key = `${shop}:${cacheKeys.COLLECTION_SYNC}:${cacheKeys.COLLECTION_SYNC_CURSOR}`;
  return redisClient.del(key);
};

// Collection products related cache methods

/**
 * Set/read the boolean status flag indicating whether collection products sync process is currently running or not.
 * @param {string} shop myshopify domain of the shop
 * @param {boolean} [status] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<boolean | null>}
 */
const setCollectionProductsSyncOngoingStatus = async (shop, status) => {
  const key = `${shop}:${cacheKeys.COLLECTION_PRODUCTS_SYNC}:${cacheKeys.COLLECTION_PRODUCTS_SYNC_ONGOING}`;
  // if 'status' is provided, update the flag
  if (status !== undefined && status !== null) {
    await redisClient.set(key, String(status));
    return status;
  }

  // return the current flag status
  const flag = await redisClient.get(key);
  return flag === "true";
};

/**
 * Set the last sync cursor for collection products by shop domain. It's used for pagination in the sync process.
 * @param {object} shop
 * @param {string?} keySuffix
 * @param {string} cursor
 * @returns  {Promise<string>}
 */
const setCollectionProductsSyncCursor = async (shop, keySuffix = null, cursor) => {
  const key = `${shop}:${cacheKeys.COLLECTION_PRODUCTS_SYNC}:${cacheKeys.COLLECTION_PRODUCTS_SYNC_CURSOR}${
    keySuffix && `-${keySuffix}`
  }`;
  await redisClient.set(key, cursor);
  return cursor;
};

/**
 * Get the last sync cursor for collection by shop domain. It's used for pagination in the sync process.
 * @param {object} shop
 * @param {string?} keySuffix
 * @returns {Promise<string|null>}
 */
const getCollectionProductsSyncCursor = async (shop, keySuffix = null) => {
  const key = `${shop}:${cacheKeys.COLLECTION_PRODUCTS_SYNC}:${cacheKeys.COLLECTION_PRODUCTS_SYNC_CURSOR}${
    keySuffix && `-${keySuffix}`
  }`;
  return redisClient.get(key);
};

/**
 * Remove the last sync cursor for collection by shop domain. It's used for pagination in the sync process.
 * @param {object} shop
 * @param {string?} keySuffix
 * @returns {Promise<number>}
 */
const removeCollectionProductsSyncCursor = async (shop, keySuffix = null) => {
  const key = `${shop}:${cacheKeys.COLLECTION_PRODUCTS_SYNC}:${cacheKeys.COLLECTION_PRODUCTS_SYNC_CURSOR}${
    keySuffix && `-${keySuffix}`
  }`;
  return redisClient.del(key);
};

module.exports = {
  collectionSyncOngoingStatus,
  setCollectionSyncCursor,
  getCollectionSyncCursor,
  removeCollectionSyncCursor,
  setCollectionProductsSyncOngoingStatus,
  setCollectionProductsSyncCursor,
  getCollectionProductsSyncCursor,
  removeCollectionProductsSyncCursor,
};
