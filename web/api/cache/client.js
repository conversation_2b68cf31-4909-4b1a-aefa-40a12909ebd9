const { createClient } = require("redis");

const { host, port, tls, username, password } = require("../config/redis");

const redisClient = createClient({
  socket: { host, port, tls, connectTimeout: 10000 },
  username,
  password,
});

(async () => {
  redisClient.on("error", (err) => console.log(`Error connecting to redis: `, err));
  redisClient.on("ready", async () => {
    // console.log(`> Redis client listening => ${host}:${port}`);
    await redisClient.configSet("notify-keyspace-events", "KEA");
  });

  await redisClient.connect();
})();

module.exports = redisClient;
