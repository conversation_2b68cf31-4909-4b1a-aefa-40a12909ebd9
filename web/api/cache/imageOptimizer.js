const redisClient = require("./client");
const cacheKeys = require("storeseo-enums/cacheKeys");
const moment = require("moment");

const HOURS_PER_DAY = 24;
const MINUTES_PER_HOUR = 60;
const SECONDS_PER_MINUTE = 60;
const SECONDS_PER_DAY = HOURS_PER_DAY * MINUTES_PER_HOUR * SECONDS_PER_MINUTE;

const DEFAULT_TTL_DAYS = 5;
const DEFAULT_TTL_SECONDS = DEFAULT_TTL_DAYS * SECONDS_PER_DAY;

/**
 * @param {string} date Format: "YYYY-MM-DD"
 */
async function addToPendingSyncStatisticDates(date) {
  const key = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.STATISTICS}:${cacheKeys.PENDING_SYNC}:${cacheKeys.DATES}`;
  return redisClient.sAdd(key, date);
}

async function popOneDateFromPendingSyncStatisticsDates() {
  const key = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.STATISTICS}:${cacheKeys.PENDING_SYNC}:${cacheKeys.DATES}`;
  return redisClient.sPop(key);
}

/**
 * @param {string} date Format: "YYYY-MM-DD"
 */
async function getMaxMinQueueUsageCount(date = moment().format("YYYY-MM-DD")) {
  const maxKey = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.QUEUE_USAGE}:${cacheKeys.MAX}:${date}`;
  const max = await redisClient.get(maxKey);

  const minKey = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.QUEUE_USAGE}:${cacheKeys.MIN}:${date}`;
  const min = await redisClient.get(minKey);

  return {
    max: Number(max),
    min: Number(min),
  };
}

/**
 * @param {string} date Format: "YYYY-MM-DD"
 * @param {number} currentQueueUsageCount
 */
async function setMaxMinQueueUsageCount(date, currentQueueUsageCount) {
  const maxKey = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.QUEUE_USAGE}:${cacheKeys.MAX}:${date}`;

  const currentMax = await redisClient.get(maxKey);
  if (!currentMax) {
    await redisClient.set(maxKey, currentQueueUsageCount, {
      EX: DEFAULT_TTL_SECONDS,
    });
  } else {
    await redisClient.set(maxKey, Math.max(currentMax, currentQueueUsageCount));
  }

  const minKey = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.QUEUE_USAGE}:${cacheKeys.MIN}:${date}`;

  const currentMin = await redisClient.get(minKey);
  if (!currentMin) {
    await redisClient.set(minKey, currentQueueUsageCount, {
      EX: DEFAULT_TTL_SECONDS,
    });
  } else if (currentQueueUsageCount) {
    await redisClient.set(minKey, Math.min(currentMin, currentQueueUsageCount));
  }
}

/**
 * @param {string} date Format: "YYYY-MM-DD"
 */
async function getMaxMinStoreCount(date = moment().format("YYYY-MM-DD")) {
  const maxKey = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.STORES_QUEUED}:${cacheKeys.MAX}:${date}`;
  const max = await redisClient.get(maxKey);

  const minKey = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.STORES_QUEUED}:${cacheKeys.MIN}:${date}`;
  const min = await redisClient.get(minKey);

  return {
    max: Number(max),
    min: Number(min),
  };
}

/**
 * @param {string} date Format: "YYYY-MM-DD"
 * @param {number} storeCount
 */
async function setMaxMinStoreCount(date, storeCount) {
  const maxKey = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.STORES_QUEUED}:${cacheKeys.MAX}:${date}`;

  const currentMax = await redisClient.get(maxKey);
  if (!currentMax) {
    await redisClient.set(maxKey, storeCount, {
      EX: DEFAULT_TTL_SECONDS,
    });
  } else {
    await redisClient.set(maxKey, Math.max(currentMax, storeCount));
  }

  const minKey = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.STORES_QUEUED}:${cacheKeys.MIN}:${date}`;

  const currentMin = await redisClient.get(minKey);

  if (!currentMin) {
    await redisClient.set(minKey, storeCount, {
      EX: DEFAULT_TTL_SECONDS,
    });
  } else if (storeCount) {
    await redisClient.set(minKey, Math.min(currentMin, storeCount));
  }
}

/**
 * @typedef {Object} ImageOptimizerCache.MaxMinUsageStats.UpdateInput
 *
 * @property {string} [date] Format: "YYYY-MM-DD", Default: current date
 *
 * @property {number} queueUsageCount
 * @property {number} pendingStoresCount
 */

/**
 *
 * @param {ImageOptimizerCache.MaxMinUsageStats.UpdateInput}
 */
async function updateMaxMinQueueUsageStats({
  date = moment().format("YYYY-MM-DD"),
  queueUsageCount,
  pendingStoresCount,
} = {}) {
  try {
    await Promise.all([setMaxMinQueueUsageCount(date, queueUsageCount), setMaxMinStoreCount(date, pendingStoresCount)]);
  } catch (err) {
    console.log("cache err: ", err);
  }
}

const numberOfStoresPendingImageOptimization = async () => {
  return redisClient.sCard(`${cacheKeys.PENDING_IMAGE_OPTIMIZATION_QUEUE}:set`);
};

/**
 *
 * @param {string} [date] **Format**: `YYYY-MM-DD`, **Default**: today
 * @returns
 */
const imagesQueuedByDate = async (date = moment().format("YYYY-MM-DD")) => {
  const key = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.IMAGES_QUEUED}:${date}`;
  const count = await redisClient.get(key);

  return Number(count);
};

/**
 *
 * @param {number} value
 * @param {string} [date] **Format**: `YYYY-MM-DD`, **Default**: today
 */
const incrementImagesQueuedByDate = async (value, date = moment().format("YYYY-MM-DD")) => {
  const key = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.IMAGES_QUEUED}:${date}`;

  return redisClient.incrBy(key, value);
};

/**
 *
 * @param {string} [date] **Format**: `YYYY-MM-DD`, **Default**: today
 * @returns
 */
const imagesProcessedByDate = async (date = moment().format("YYYY-MM-DD")) => {
  const key = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.IMAGES_PROCESSED}:${date}`;
  const count = await redisClient.get(key);

  return Number(count);
};

/**
 *
 * @param {number} value
 * @param {string} [date] **Format**: `YYYY-MM-DD`, **Default**: today
 */
const incrementImagesProcessedByDate = async (value, date = moment().format("YYYY-MM-DD")) => {
  const key = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.IMAGES_PROCESSED}:${date}`;

  return redisClient.incrBy(key, value);
};

/**
 *
 * @param {string} [date] **Format**: `YYYY-MM-DD`, **Default**: today
 * @returns
 */
const processingFailedByDate = async (date = moment().format("YYYY-MM-DD")) => {
  const key = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.PROCESSING_FAILED}:${date}`;
  const count = await redisClient.get(key);

  return Number(count);
};

/**
 *
 * @param {number} value
 * @param {string} [date] **Format**: `YYYY-MM-DD`, **Default**: today
 */
const incrementProcessingFailedByDate = async (value, date = moment().format("YYYY-MM-DD")) => {
  const key = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.PROCESSING_FAILED}:${date}`;

  return redisClient.incrBy(key, value);
};

/**
 *
 * @param {string} [date] **Format**: `YYYY-MM-DD`, **Default**: today
 * @returns
 */
const imagesFailedByDate = async (date = moment().format("YYYY-MM-DD")) => {
  const key = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.IMAGES_FAILED}:${date}`;
  const count = await redisClient.get(key);

  return Number(count);
};

/**
 *
 * @param {number} value
 * @param {string} [date] **Format**: `YYYY-MM-DD`, **Default**: today
 */
const incrementImagesFailedByDate = async (value, date = moment().format("YYYY-MM-DD")) => {
  const key = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.IMAGES_FAILED}:${date}`;

  return redisClient.incrBy(key, value);
};

/**
 *
 * @param {string} [date] **Format**: `YYYY-MM-DD`, **Default**: today
 * @returns
 */
const imageOptimizeWebhooksCountByDate = async (date = moment().format("YYYY-MM-DD")) => {
  const key = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.WEBHOOKS_COUNT}:${date}`;
  const count = await redisClient.get(key);

  return Number(count);
};

/**
 *
 * @param {number} value
 * @param {string} [date] **Format**: `YYYY-MM-DD`, **Default**: today
 */
const incrementImageOptimizeWebhooksCountByDate = async (value, date = moment().format("YYYY-MM-DD")) => {
  const key = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.WEBHOOKS_COUNT}:${date}`;
  const updatedCount = await redisClient.incrBy(key, value);

  await redisClient.expire(key, DEFAULT_TTL_SECONDS);

  return updatedCount;
};

module.exports = {
  addToPendingSyncStatisticDates,
  popOneDateFromPendingSyncStatisticsDates,

  getMaxMinQueueUsageCount,
  getMaxMinStoreCount,
  updateMaxMinQueueUsageStats,

  imagesQueuedByDate,
  incrementImagesQueuedByDate,

  imagesProcessedByDate,
  incrementImagesProcessedByDate,

  processingFailedByDate,
  incrementProcessingFailedByDate,

  imagesFailedByDate,
  incrementImagesFailedByDate,

  imageOptimizeWebhooksCountByDate,
  incrementImageOptimizeWebhooksCountByDate,

  numberOfStoresPendingImageOptimization,
};
