const { SHOPS_PENDING_WEBHOOK_REGISTRATION } = require("storeseo-enums/cacheKeys");
const redisClient = require("./client");

/**
 * Check if a shop is already in the pending webhook registration list
 * @param {string} shop myshopify domain of the shop
 * @returns {Promise<boolean>} True if the shop is in the list, false otherwise
 */
const isShopInPendingWebhookRegistrationList = async (shop) => {
  return redisClient.sIsMember(`${SHOPS_PENDING_WEBHOOK_REGISTRATION}:set`, shop);
};

/**
 * Add a shop to the pending webhook registration list
 * @param {string} shop myshopify domain of the shop
 * @returns {Promise<number>} The length of the list after the push operation
 */
const addShopToPendingWebhookRegistrationList = async (shop) => {
  // First check if the shop is already in the set to avoid duplicates
  const alreadyAddedToQueue = await isShopInPendingWebhookRegistrationList(shop);
  if (alreadyAddedToQueue) return 1;

  // Add to set for quick membership check
  await redisClient.sAdd(`${SHOPS_PENDING_WEBHOOK_REGISTRATION}:set`, shop);
  
  // Add to list for queue processing
  return redisClient.rPush(SHOPS_PENDING_WEBHOOK_REGISTRATION, shop);
};

/**
 * Get the next shop from the pending webhook registration list
 * @returns {Promise<string|null>} The shop domain or null if the list is empty
 */
const getNextShopFromPendingWebhookRegistrationList = async () => {
  const shop = await redisClient.lPop(SHOPS_PENDING_WEBHOOK_REGISTRATION);
  if (!shop) return null;

  // Remove from the set as well
  await redisClient.sRem(`${SHOPS_PENDING_WEBHOOK_REGISTRATION}:set`, shop);
  return shop;
};

module.exports = {
  addShopToPendingWebhookRegistrationList,
  getNextShopFromPendingWebhookRegistrationList,
  isShopInPendingWebhookRegistrationList,
};
