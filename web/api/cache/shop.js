const cacheKeys = require("storeseo-enums/cacheKeys");
const redisClient = require("./client");
const DEFAULT_LANGUAGE_VALUE = { locale: "en", name: "English" };

/**
 * Set/read the boolean status flag indicating whether store hase multiple languages in Shopify.
 * @param {string} shop myshopify domain of the shop
 * @param {boolean} [status] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<boolean | null>}
 */
const hasMultipleLanguages = async (shop, status) => {
  const key = `${shop}:${cacheKeys.HAS_MULTIPLE_LANGUAGES}`;

  // if 'status' is provided, update the flag
  if (status !== undefined && status !== null) {
    await redisClient.set(key, String(status));
    return status;
  }

  // return the current flag status
  const flag = await redisClient.get(key);
  return flag === "true";
};

/**
 * Set/read the boolean status flag indicating whether store has enabled multi-languages in the app.
 * @param {string} shop myshopify domain of the shop
 * @param {boolean} [status] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<boolean | null>}
 */
const enabledMultiLanguage = async (shop, status) => {
  const key = `${shop}:${cacheKeys.ENABLED_MULTI_LANGUAGE}`;

  // if 'status' is provided, update the flag
  if (status !== undefined && status !== null) {
    await redisClient.set(key, String(status));
    return status;
  }

  // return the current flag status
  const flag = await redisClient.get(key);
  return flag === "true";
};

/**
 * Set/read the default language of the store.
 * @param {string} shop myshopify domain of the shop
 * @param {object} [locale] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<{ locale: string, name: string } | null>}
 */
const defaultLanguage = async (shop, locale) => {
  const key = `${shop}:${cacheKeys.DEFAULT_LANGUAGE}`;

  // if 'locale' is provided, update the flag
  if (locale !== undefined && locale !== null) {
    await redisClient.set(key, JSON.stringify(locale));
    return locale;
  }

  // return the current flag status
  const flag = await redisClient.get(key);
  return flag ? JSON.parse(flag) : DEFAULT_LANGUAGE_VALUE;
};

/**
 * Set/read the language for generating ai content of the store.
 * @param {string} shop myshopify domain of the shop
 * @param {object} [locale] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<{ locale: string, name: string }>}
 */
const aiContentGeneratorLanguage = async (shop, locale) => {
  const key = `${shop}:${cacheKeys.AI_CONTENT_GENERATOR_LANG}`;

  // if 'locale' is provided, update the flag
  if (locale !== undefined && locale !== null) {
    await redisClient.set(key, JSON.stringify(locale));
    return locale;
  }

  // return the current flag status
  const flag = await redisClient.get(key);
  return flag ? JSON.parse(flag) : DEFAULT_LANGUAGE_VALUE;
};

module.exports = {
  hasMultipleLanguages,
  enabledMultiLanguage,
  defaultLanguage,
  aiContentGeneratorLanguage,
};
