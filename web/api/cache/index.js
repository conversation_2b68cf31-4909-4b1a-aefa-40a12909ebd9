require("dotenv").config();
const cacheKeys = require("storeseo-enums/cacheKeys");
const moment = require("moment");

const redisClient = require("./client");

const imageOptimizer = require("./imageOptimizer");
const altTextOptimizer = require("./altTextOptimizer");
const aiOptimizer = require("./aiOptimizer");
const collectionCache = require("./collectionCache");
const collectionAiOptimizer = require("./collectionAiOptimizer");
const addons = require("./addons");
const product = require("./product");
const shop = require("./shop");
const webhooks = require("./webhooks");

const hoursPerDay = 24;
const minutesPerHour = 60;
const secondsPerMinute = 60;
const secondsInADay = hoursPerDay * minutesPerHour * secondsPerMinute;

/**
 * **Clear** all cache related to the shop
 * @param {string} shopDomain
 */
const clear = async (shop) => {
  const keys = [
    `${shop}:${cacheKeys.HIDE_PRO_PACKAGE_AD}`,
    `${shop}:${cacheKeys.PRODUCT_SYNC_ONGOING}`,
    `${shop}:${cacheKeys.PAGE_SYNC_ONGOING}`,
    `${shop}:${cacheKeys.BLOG_SYNC_ONGOING}`,
    `${shop}:${cacheKeys.BLOGS_PENDING_IN_QUEUE}`,
    `${shop}:${cacheKeys.API_RATE_LIMIT_EXCEEDED}:${cacheKeys.SHOPIFY_GRAPHQL_API}`,
    `${shop}:${cacheKeys.API_RATE_LIMIT_EXCEEDED}:${cacheKeys.SHOPIFY_HTTP_API}`,
    `${shop}:${cacheKeys.AUTO_OPTIMIZATION_TASK}:${cacheKeys.PRODUCTS_TO_OPTIMIZE}`,
    `${shop}:${cacheKeys.AUTO_OPTIMIZATION_TASK}:${cacheKeys.PRODUCTS_OPTIMIZED}`,
    `${shop}:${cacheKeys.AUTO_OPTIMIZATION_TASK}:${cacheKeys.TOTAL_BATCHES}`,
    `${shop}:${cacheKeys.AUTO_OPTIMIZATION_TASK}:${cacheKeys.LAST_BATCH_NUMBER}`,
    `${shop}:${cacheKeys.AUTO_OPTIMIZATION_TASK}:${cacheKeys.RUNNING}`,
    `${shop}:${cacheKeys.BANNER_DEAL}`,
    `${shop}:${cacheKeys.BANNER_GET_STARTED}`,
    `${shop}:${cacheKeys.BANNER_GOOGLE_SITEMAP}`,
    `${shop}:${cacheKeys.BANNER_HOMEPAGE_HINT}`,
    `${shop}:${cacheKeys.BANNER_SEO_GUIDE}`,
    `${shop}:${cacheKeys.BANNER_WHATS_NEW}`,
    `${shop}:${cacheKeys.BANNER_BETTERDOCS}`,
    `${shop}:${cacheKeys.BANNER_SCHEDULE_A_CALL}`,
    `${shop}:${cacheKeys.BANNER_PARTNERSHIPS}`,
  ];

  for (let key of keys) {
    await redisClient.del(key);
  }

  return true;
};

/**
 * Deletes data associated with a cache key
 * @param {string} shop myshopify domain of the shop
 * @returns {Promise<boolean>}
 */
const deleteAllCache = async (shop) => {
  const keys = await redisClient.keys(`${shop}:*`);

  if (keys.length === 0) return true;

  for (let key of keys) {
    await redisClient.del(key);
  }

  return true;
};

/**
 * Deletes data associated with a cache key
 * @param {string} key
 * @returns {Promise<boolean>}
 */
const deleteCacheValue = async (key) => {
  await redisClient.del(key);
  return true;
};

/**
 * Set a flag to hide the pro package upgrade advertisement
 * @param {string} shopDomain myshopify domain of the shop
 * @returns {string} "1" indicates that the flag was set successfully.
 */
const setHideProPackageAd = async (shopDomain) => {
  const expireInDays = 30;
  const key = `${shopDomain}:${cacheKeys.HIDE_PRO_PACKAGE_AD}`;
  await redisClient.set(key, "1", {
    EX: expireInDays * hoursPerDay * minutesPerHour * secondsPerMinute,
  });
  return "1";
};

/**
 * Check if the flag to hide pro package upgrade advertisement is still active
 * @param {string} shopDomain myshopify domain of the shop
 * @returns {string | null}
 */
const checkProPackageAdHideValidity = async (shopDomain) => {
  const key = `${shopDomain}:${cacheKeys.HIDE_PRO_PACKAGE_AD}`;
  return redisClient.get(key);
};

const saveProductSyncCursor = async (shopDomain, cursor) => {
  const key = `${shopDomain}:${cacheKeys.PRODUCT_SYNC_CURSOR}`;
  await redisClient.set(key, cursor);
  return cursor;
};

const getProductSyncCursor = async (shopDomain) => {
  const key = `${shopDomain}:${cacheKeys.PRODUCT_SYNC_CURSOR}`;
  return redisClient.get(key);
};

const removeProductSyncCursor = async (shopDomain) => {
  const key = `${shopDomain}:${cacheKeys.PRODUCT_SYNC_CURSOR}`;
  return redisClient.del(key);
};

/**
 * Set/read the boolean status flag indicating whether product sync process is currently running or not.
 * @param {string} shop myshopify domain of the shop
 * @param {boolean} [status] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<boolean | null>}
 */
const productSyncOngoing = async (shop, status) => {
  const key = `${shop}:${cacheKeys.PRODUCT_SYNC_ONGOING}`;

  // if 'status' is provided, update the flag
  if (status !== undefined && status !== null) {
    await redisClient.set(key, String(status));
    return status;
  }

  // return the current flag status
  const flag = await redisClient.get(key);
  return flag === "true";
};

/**
 * Set/read the boolean status flag indicating whether shopify page sync process is currently running or not.
 * @param {string} shop myshopify domain of the shop
 * @param {boolean} [status] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<boolean | null>}
 */
const pageSyncOngoing = async (shop, status) => {
  const key = `${shop}:${cacheKeys.PAGE_SYNC_ONGOING}`;

  // if 'status' is provided, update the flag
  if (status !== undefined && status !== null) {
    await redisClient.set(key, String(status));
    return status;
  }

  // return the current flag status
  const flag = await redisClient.get(key);
  return flag === "true";
};

/**
 * Set/read the boolean status flag indicating whether shopify blog posts sync process is currently running or not.
 * @param {string} shop myshopify domain of the shop
 * @param {boolean} [status] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<boolean | null>}
 */
const blogSyncOngoing = async (shop, status) => {
  const key = `${shop}:${cacheKeys.BLOG_SYNC_ONGOING}`;

  // if 'status' is provided, update the flag
  if (status !== undefined && status !== null) {
    await redisClient.set(key, String(status));
    return status;
  }

  // return the current flag status
  const flag = await redisClient.get(key);
  return flag === "true";
};

/**
 * Set/read the array of blog ids that are waiting in the queue to be processed
 * @param {string} shop myshopify domain of the shop
 * @param {number | string} [id] DB id of the blog to add to the list. **Leave this empty to read the current list**
 * @returns {Promise<string[]>}
 */
const blogsPendingInQueue = async (shop, id) => {
  const key = `${shop}:${cacheKeys.BLOGS_PENDING_IN_QUEUE}`;

  // update the cache if 'ids' array is provided
  if (id !== undefined && id !== null) {
    await redisClient.sAdd(key, String(id));
  }

  // return the current list from cache
  const value = await redisClient.sMembers(key);

  return value;
};

/**
 * Delete blog from the list of pending blog ids queue
 * @param {string} shop myshopify domain of the shop
 * @param {number | string} id DB id of the blog
 */
const delBlogFromPendingQueueList = async (shop, id) => {
  const key = `${shop}:${cacheKeys.BLOGS_PENDING_IN_QUEUE}`;

  return redisClient.sRem(key, String(id));
};

const addToActiveShopifyShops = async (shopDomain) => {
  await redisClient.sAdd(cacheKeys.ACTIVE_SHOPIFY_SHOPS, shopDomain);
  return true;
};

const isActiveShop = async (shopDomain) => {
  try {
    const result = await redisClient.sIsMember(cacheKeys.ACTIVE_SHOPIFY_SHOPS, shopDomain);
    return result;
  } catch (err) {
    console.log(`Error checking active shop in redis for ${shopDomain}`, err);
    return false;
  }
};

const delFromActiveShopifyShops = async (shopDomain) => {
  return redisClient.sRem(cacheKeys.ACTIVE_SHOPIFY_SHOPS, shopDomain);
};

const resetActiveShopifyShops = async () => {
  return redisClient.del(cacheKeys.ACTIVE_SHOPIFY_SHOPS);
};

/**
 * Read or update the boolean flag indicating api rate limit status when accessing third party APIs.
 * @param {string} shop myshopify domain of the shop
 * @param {import("storeseo-enums/cacheKeys").apiNames} api name of the API
 * @param {boolean} [status] leave it empty to read the current flag status, provide value to update the flag
 * @param {number} [period] - for how long the rate limit flag will be active (in seconds, default: 60 seconds)
 */
const apiRateLimitExceeded = async (shop, api, status, period) => {
  const key = `${shop}:${cacheKeys.API_RATE_LIMIT_EXCEEDED}:${api}`;
  if (status !== undefined && status !== null) {
    await redisClient.set(key, String(status), {
      EX: period || secondsPerMinute,
    });

    return status;
  }

  const res = await redisClient.get(key);
  return res === "true";
};

/**
 * Set/read the number of products that are pending in the auto-optimization task
 * @param {string} shop myshopify domain of the shop
 * @param {number} [count] number of products pending in the task. **Leave this parameter empty to read the current count**
 */
const productsPendingInAutoOptimizationTask = async (shop, count) => {
  const key = `${shop}:${cacheKeys.AUTO_OPTIMIZATION_TASK}:${cacheKeys.PRODUCTS_TO_OPTIMIZE}`;

  if (count !== null && count !== undefined) {
    await redisClient.set(key, count);
    return count;
  }

  const res = await redisClient.get(key);
  return Number(res);
};

/**
 * Increment/decrement the number of products pending in the auto-optimization task
 * @param {string} shop myshopify domain of the shop
 * @param {"INCR" | "DECR"} step step to increment or decrement total count
 * @param {number} [amount] increment or decrement amount. **`Default: 1`**
 */
const updateProductsCountPendingInAutoOptimizationTask = async (shop, step, amount = 1) => {
  const key = `${shop}:${cacheKeys.AUTO_OPTIMIZATION_TASK}:${cacheKeys.PRODUCTS_TO_OPTIMIZE}`;

  if (step === "INCR") return redisClient.incrBy(key, amount);
  else return redisClient.decrBy(key, amount);
};

/**
 * Read/Increment/decrement the number of products optimized by the currently running auto-optimization task.
 *
 * *Leave `step` & `amount` empty `to read` the current value*
 * ***
 * @param {string} shop myshopify domain of the shop
 * @param {"INCR" | "DECR"} step step to increment or decrement total count
 * @param {number} [amount] increment or decrement amount. **`Default: 1`**
 */
const productsOptimizedByAutoOptimizationTask = async (shop, step, amount = 1) => {
  const key = `${shop}:${cacheKeys.AUTO_OPTIMIZATION_TASK}:${cacheKeys.PRODUCTS_OPTIMIZED}`;

  if (!step) return Number(await redisClient.get(key));

  if (step === "INCR") return redisClient.incrBy(key, amount);
  else return redisClient.decrBy(key, amount);
};

/**
 * Set/read the number of total batches in the auto-optimization task
 * @param {string} shop myshopify domain of the shop
 * @param {number} [count] number of total batches in the task. **Leave this parameter empty to read the current count**
 */
const totalBatchesInOptimizationTask = async (shop, count) => {
  const key = `${shop}:${cacheKeys.AUTO_OPTIMIZATION_TASK}:${cacheKeys.TOTAL_BATCHES}`;

  if (count !== null && count !== undefined) {
    await redisClient.set(key, count);
    return count;
  }

  const res = await redisClient.get(key);
  return Number(res);
};

/**
 * Set/read the last batch number in the auto-optimization task
 * @param {string} shop myshopify domain of the shop
 * @param {number} [batchNo] number of products pending in the task. **Leave this parameter empty to read the current count**
 */
const lastRunningBatchInOptimizationTask = async (shop, batchNo) => {
  const key = `${shop}:${cacheKeys.AUTO_OPTIMIZATION_TASK}:${cacheKeys.LAST_BATCH_NUMBER}`;

  if (batchNo !== null && batchNo !== undefined) {
    await redisClient.set(key, batchNo);
    return batchNo;
  }

  const res = await redisClient.get(key);
  return Number(res);
};

/**
 * Set/read the boolean status flag indicating whether auto-optimization process is currently running or not.
 * @param {string} shop myshopify domain of the shop
 * @param {boolean} [status] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<boolean | null>}
 */
const optimizationTaskRunning = async (shop, status) => {
  const key = `${shop}:${cacheKeys.AUTO_OPTIMIZATION_TASK}:${cacheKeys.RUNNING}`;

  // if 'status' is provided, update the flag
  if (status !== undefined && status !== null) {
    await redisClient.set(key, String(status));
    return status;
  }

  // return the current flag status
  const flag = await redisClient.get(key);
  return flag === "true";
};

/**
 * Store optimization task start info in cache
 * @param {string} shop myshopify domain of the shop
 * @param {number} totalBatches number of batches to run in the optimization task
 */
const storeOptimizationTaskStartInfo = async (shop, totalBatches) => {
  await totalBatchesInOptimizationTask(shop, totalBatches);
  await lastRunningBatchInOptimizationTask(shop, 0);
  await optimizationTaskRunning(shop, true);
};

const resetOptimizationTaskInfo = async (shop) => {
  const baseKey = `${shop}:${cacheKeys.AUTO_OPTIMIZATION_TASK}`;

  await redisClient.del(`${baseKey}:${cacheKeys.PRODUCTS_OPTIMIZED}`);
  await redisClient.del(`${baseKey}:${cacheKeys.PRODUCTS_TO_OPTIMIZE}`);
  await optimizationTaskRunning(shop, false);
  await totalBatchesInOptimizationTask(shop, 0);
  await lastRunningBatchInOptimizationTask(shop, 0);
};

const hideBetterDocsPromo = async (shopDomain) => {
  const expireInDays = 30;
  const key = `${shopDomain}:${cacheKeys.BANNER_BETTERDOCS}`;
  await redisClient.set(key, "1", {
    EX: expireInDays * hoursPerDay * minutesPerHour * secondsPerMinute,
  });
  return "1";
};
const checkBetterDocsPromo = async (shopDomain) => {
  const key = `${shopDomain}:${cacheKeys.BANNER_BETTERDOCS}`;
  return await redisClient.get(key);
};

/**
 * Set/read the boolean status flag indicating the onboarding step.
 * @param {string} shop myshopify domain of the shop
 * @param {number | string} [step] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<number | null>}
 */
const onboardStep = async (shop, step) => {
  const key = `${shop}:${cacheKeys.ONBOARD_STEP}`;

  // if 'step' is provided, update the flag
  if (step !== undefined && step !== null) {
    await redisClient.set(key, String(step));
    return step;
  }

  // return the current value
  const value = await redisClient.get(key);
  return Number(value);
};

/**
 * Set/read the boolean status flag indicating the onboarding step.
 * @param {string} shop myshopify domain of the shop
 * @param {number | string} [step] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<number | null>}
 */
const isOnboardingCompleted = async (shop, isCompleted) => {
  const key = `${shop}:IS_ONBOARDING_COMPLETED`;

  // if 'isCompleted' is provided, update the flag
  if (isCompleted !== undefined && isCompleted !== null) {
    await redisClient.set(key, String(isCompleted));
    return isCompleted;
  }

  // return the current value
  const value = await redisClient.get(key);
  return value ? value === "true" : true;
};

/**
 * Set/read the boolean status flag indicating the plan id of the shop.
 * @param {string} shop myshopify domain of the shop
 * @param {string | number} [planId] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<number | null>}
 */
const planId = async (shop, planId) => {
  const key = `${shop}:${cacheKeys.PLAN_ID}`;

  // if 'planId' is provided, update the value
  if (planId !== undefined && planId !== null) {
    await redisClient.set(key, String(planId));
    return planId;
  }

  // return the current value
  const value = await redisClient.get(key);
  return Number(value);
};

/**
 * Set/read the boolean status flag indicating the app embed is enabled of the shop.
 * @param {string} shop myshopify domain of the shop
 * @param {string | number} [status] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<boolean | null>}
 */
const appEmbedStatus = async (shop, status) => {
  const key = `${shop}:${cacheKeys.APP_EMBED_STATUS}`;

  // if 'status' is provided, update the value
  if (status !== undefined) {
    await redisClient.set(key, String(status));
    return status;
  }

  // return the current value
  const value = await redisClient.get(key);
  return value === "true";
};

/**
 * Set value to any key
 * @param {String} shop myshopify domain
 * @param {String} key
 * @param {String|Number} value
 * @param {Number} expireInDays
 * @returns {Promise<string>}
 */
const setCache = async (shop, key, value, expireInDays = null) => {
  return await redisClient.set(`${shop}:${key}`, String(value), {
    EX: expireInDays ? expireInDays * secondsInADay : undefined,
  });
};

/**
 * Get value of any key
 * @param {String} shop myshopify domain
 * @param {String} key
 * @returns {Promise<string>}
 */
const getCache = async (shop, key) => {
  return await redisClient.get(`${shop}:${key}`);
};

const tempSubsctiptionSet = async (shop, { id, plan, addons = [], couponCode = undefined }) => {
  const expireInDays = 2;
  await setCache(
    shop,
    cacheKeys.TEMP_SUBSCRIPTION_DATA,
    JSON.stringify({
      id,
      plan,
      addons,
      couponCode,
    }),
    expireInDays
  );
};

const tempSubsctiptionGet = async (shop) => {
  const subscriptionData = await getCache(shop, cacheKeys.TEMP_SUBSCRIPTION_DATA);
  return JSON.parse(subscriptionData);
};

const tempSubsctiptionReset = async (shop) => {
  await redisClient.del(`${shop}:${cacheKeys.TEMP_SUBSCRIPTION_DATA}`);
};

const tempOnetimeAddon = async (shop, { addons = [] } = {}) => {
  if (addons?.length > 0) {
    const expireInDays = 2;
    await Promise.allSettled([setCache(shop, cacheKeys.TEMP_ONETIME_ADDONS, addons.join(","), expireInDays)]);
    return;
  }

  [addons] = await Promise.allSettled([getCache(shop, cacheKeys.TEMP_ONETIME_ADDONS)]);

  return {
    addons: addons.value ? addons.value.split(",") : [],
  };
};

const tempOnetimeAddonReset = async (shop) => {
  await Promise.allSettled([redisClient.del(`${shop}:${cacheKeys.TEMP_ONETIME_ADDONS}`)]);
};

/**
 * Set/read the usage limit for image optimisation.
 * @param {string} shop myshopify domain of the shop
 * @param {number} [limit] leave it empty to read the current value, provide a value to update the current value.
 * @returns {Promise<number | null>}
 */
const imageOptimizerUsageLimit = async (shop, limit) => {
  const key = `${shop}:${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.USAGE_LIMIT}`;

  // if 'value' is provided, update the key
  if (limit !== undefined && limit !== null) {
    await redisClient.set(key, String(limit));
    return limit;
  }

  // return the current value
  const value = await redisClient.get(key);
  return Number(value);
};

/**
 * Set/read the usage count for image optimisation.
 * @param {string} shop myshopify domain of the shop
 * @param {number} [count] leave it empty to read the current value, provide a value to update the current value.
 * @returns {Promise<number | null>}
 */
const imageOptimizerUsageCount = async (shop, count) => {
  const key = `${shop}:${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.USAGE_COUNT}`;

  // if 'value' is provided, update the key
  if (count !== undefined && count !== null) {
    await redisClient.set(key, String(count > 0 ? count : 0));
    return count;
  }

  // return the current value
  const value = await redisClient.get(key);
  return Number(value);
};

/**
 * increment the usage count for image optimisation.
 * @param {string} shop myshopify domain of the shop
 * @returns {Promise<number>}
 */
const incrementImageOptimizerUsageCount = async (shop) => {
  const key = `${shop}:${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.USAGE_COUNT}`;

  const value = await redisClient.incr(key);
  return value;
};

/**
 * increment the usage count for image optimisation.
 * @param {string} shop myshopify domain of the shop
 * @returns {Promise<number>}
 */
const decrementImageOptimizerUsageCount = async (shop) => {
  const key = `${shop}:${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.USAGE_COUNT}`;

  const value = await redisClient.decr(key);
  return value;
};

/**
 * Set/read the total usage count for image optimisation.
 * @param {string} shop myshopify domain of the shop
 * @param {number} [count] leave it empty to read the current value, provide a value to update the current value.
 * @returns {Promise<number | null>}
 */
const imageOptimizerTotalUsageCount = async (shop, count) => {
  const key = `${shop}:${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.TOTAL_USAGE_COUNT}`;

  // if 'value' is provided, update the key
  if (count !== undefined && count !== null) {
    await redisClient.set(key, String(count));
    return count;
  }

  // return the current value
  const value = await redisClient.get(key);
  return Number(value);
};

/**
 * increment the total usage count for image optimisation by **1**.
 * @param {string} shop myshopify domain of the shop
 * @returns {Promise<number>}
 */
const incrementImageOptimizerTotalUsageCount = async (shop) => {
  const key = `${shop}:${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.TOTAL_USAGE_COUNT}`;

  const value = await redisClient.incr(key);
  return value;
};

/**
 * decrement the total usage count for image optimisation by **1**.
 * @param {string} shop myshopify domain of the shop
 * @returns {Promise<number>}
 */
const decrementImageOptimizerTotalUsageCount = async (shop) => {
  const key = `${shop}:${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.TOTAL_USAGE_COUNT}`;

  const value = await redisClient.decr(key);
  return value;
};

/**
 * Set/read the total app wide usage count for image optimisation.
 * @param {number} [count] leave it empty to read the current value, provide a value to update the current value.
 * @returns {Promise<number | null>}
 */
const imageOptimizerTotalAppUsageCount = async (count) => {
  const key = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.TOTAL_USAGE_COUNT}`;

  // if 'value' is provided, update the key
  if (count !== undefined && count !== null) {
    await redisClient.set(key, String(count));
    return count;
  }

  // return the current value
  const value = await redisClient.get(key);
  return Number(value);
};

/**
 * increment the total app wide usage count for image optimisation by **1**.
 * @param {string} shop myshopify domain of the shop
 * @returns {Promise<number>}
 */
const incrementImageOptimizerTotalAppUsageCount = async () => {
  const key = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.TOTAL_USAGE_COUNT}`;

  const value = await redisClient.incr(key);
  return value;
};

/**
 * decrement the total app wide usage count for image optimisation by **1**.
 * @param {string} shop myshopify domain of the shop
 * @returns {Promise<number>}
 */
const decrementImageOptimizerTotalAppUsageCount = async () => {
  const key = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.TOTAL_USAGE_COUNT}`;

  const value = await redisClient.decr(key);
  return value;
};

/**
 *
 * @param {string} shop myshopify domain of the shop
 * @param {boolean} incrementUsage **Default:** `true`, a `false` value means the usage count will be decremented
 */
const updateImageOptimizerUsageCount = async (shop, incrementUsage = true) => {
  if (incrementUsage) {
    await incrementImageOptimizerUsageCount(shop);
    await incrementImageOptimizerTotalUsageCount(shop);
    await incrementImageOptimizerTotalAppUsageCount();

    return;
  }

  await decrementImageOptimizerUsageCount(shop);
  await decrementImageOptimizerTotalUsageCount(shop);
  await decrementImageOptimizerTotalAppUsageCount();
};

/**
 * increment the invalid image count for image optimisation by **1**.
 * @param {string} shop myshopify domain of the shop
 * @returns {Promise<number>}
 */
const incrementImageOptimizerInvalidImageCount = async (shop) => {
  const totalKey = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.INVALID_IMAGE_COUNT}`;
  await redisClient.incr(totalKey);

  const dateKey = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.INVALID_IMAGE_COUNT}:${moment().format("YYYY-MM-DD")}`;
  await redisClient.incr(dateKey);

  const shopKey = `${shop}:${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.INVALID_IMAGE_COUNT}`;

  const valueByShop = await redisClient.incr(shopKey);
  return valueByShop;
};

const invalidImagesCountByDate = async (date = moment().format("YYYY-MM-DD")) => {
  const key = `${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.INVALID_IMAGE_COUNT}:${date}`;

  // return the current value
  const value = await redisClient.get(key);
  return Number(value);
};

const getHiddenBanners = async (shop) => {
  const keys = Object.keys(cacheKeys).filter((ck) => ck.includes("BANNER_"));

  const obj = {};
  for (let i = 0; i < keys.length; i++) {
    let key = keys[i];
    let value = !(await getCache(shop, key));
    obj[key] = value;
    // console.log(key, value);
  }

  return obj;
};

const autoOptimizationMedia = async (shop, status = null, stagedTarget = null, batchNo = null) => {
  const prefix = `${shop}:${cacheKeys.AUTO_OPTIMIZATION_TASK}`;
  const statusKey = `${prefix}:${cacheKeys.MEDIA_STATUS}`;
  const jsonKey = `${prefix}:${cacheKeys.MEDIA_STAGED_TARGET}`;
  const batchKey = `${prefix}:${cacheKeys.MEDIA_BATCH_NUMBER}`;

  if (status === 201) {
    await redisClient.set(statusKey, status);
    await redisClient.set(jsonKey, JSON.stringify(stagedTarget));
    await redisClient.set(batchKey, batchNo);
    return { status, stagedTarget, batchNo };
  }

  status = Number(await redisClient.get(statusKey));
  stagedTarget = JSON.parse(await redisClient.get(jsonKey));
  batchNo = Number(await redisClient.get(batchKey));
  return { status, stagedTarget, batchNo };
};

const resetAutoOptimizationMedia = async (shop) => {
  const prefix = `${shop}:${cacheKeys.AUTO_OPTIMIZATION_TASK}`;
  const statusKey = `${prefix}:${cacheKeys.MEDIA_STATUS}`;
  const jsonKey = `${prefix}:${cacheKeys.MEDIA_STAGED_TARGET}`;
  const batchKey = `${prefix}:${cacheKeys.MEDIA_BATCH_NUMBER}`;

  await redisClient.del(statusKey);
  await redisClient.del(jsonKey);
  await redisClient.del(batchKey);
  return true;
};

/**
 *
 * @param {string} shop myshopify domain of the shop
 */
const addStoreToPendingImageOptimizationQueue = async (shop) => {
  const alreadyAddedToQueue = await redisClient.sIsMember(`${cacheKeys.PENDING_IMAGE_OPTIMIZATION_QUEUE}:set`, shop);
  if (alreadyAddedToQueue) return 1;

  redisClient.sAdd(`${cacheKeys.PENDING_IMAGE_OPTIMIZATION_QUEUE}:set`, shop);
  return redisClient.rPush(cacheKeys.PENDING_IMAGE_OPTIMIZATION_QUEUE, shop);
};

const getNextStoreFromPendingImageOptimizationQueue = async () => {
  const shop = await redisClient.lPop(cacheKeys.PENDING_IMAGE_OPTIMIZATION_QUEUE);
  if (!shop) return null;

  redisClient.sRem(`${cacheKeys.PENDING_IMAGE_OPTIMIZATION_QUEUE}:set`, shop);
  return shop;
};

const betterDocInstallationStatus = async (shop, status) => {
  const key = `${shop}:${cacheKeys.BETTERDOCS_IS_INSTALLED}`;

  // if status is provided, update the flag
  if (status !== undefined && status !== null) {
    await redisClient.set(key, String(status));
    return status;
  }

  // return the current flag status
  const flag = await redisClient.get(key);
  return flag === "true";
};

const removeBetterDocsInstallationStatus = async (shop) => {
  const key = `${shop}:${cacheKeys.BETTERDOCS_IS_INSTALLED}`;
  return redisClient.del(key);
};

/**
 * Set/read the boolean status flag indicating the onboarding step.
 * @param {string} shop myshopify domain of the shop
 * @param {boolean} [isNew] leave it empty to read the current flag, provide a value to update the flag.
 * @returns {Promise<boolean>}
 */
const isNewStore = async (shop, isNew) => {
  const key = `${shop}:IS_NEW_STORE`;

  // if 'isNew' is provided, update the flag
  if (isNew !== undefined && isNew !== null) {
    await redisClient.set(key, String(isNew));
    return isNew;
  }

  // return the current value
  const value = await redisClient.get(key);

  return value ? value === "true" : false;
};

module.exports = {
  setHideProPackageAd,
  checkProPackageAdHideValidity,
  saveProductSyncCursor,
  getProductSyncCursor,
  removeProductSyncCursor,
  productSyncOngoing,
  pageSyncOngoing,
  blogSyncOngoing,
  blogsPendingInQueue,
  delBlogFromPendingQueueList,
  addToActiveShopifyShops,
  isActiveShop,
  delFromActiveShopifyShops,
  resetActiveShopifyShops,
  apiRateLimitExceeded,
  productsPendingInAutoOptimizationTask,
  updateProductsCountPendingInAutoOptimizationTask,
  productsOptimizedByAutoOptimizationTask,
  totalBatchesInOptimizationTask,
  lastRunningBatchInOptimizationTask,
  optimizationTaskRunning,
  storeOptimizationTaskStartInfo,
  resetOptimizationTaskInfo,
  hideBetterDocsPromo,
  checkBetterDocsPromo,
  clear,
  onboardStep,
  isOnboardingCompleted,
  planId,
  deleteCacheValue,
  deleteAllCache,
  setCache,
  getCache,
  tempSubsctiptionGet,
  tempSubsctiptionSet,
  tempSubsctiptionReset,
  tempOnetimeAddon,
  tempOnetimeAddonReset,
  imageOptimizerUsageLimit,
  imageOptimizerUsageCount,
  incrementImageOptimizerUsageCount,
  imageOptimizerTotalUsageCount,
  incrementImageOptimizerTotalUsageCount,
  decrementImageOptimizerTotalUsageCount,
  decrementImageOptimizerUsageCount,
  imageOptimizerTotalAppUsageCount,
  incrementImageOptimizerTotalAppUsageCount,
  decrementImageOptimizerTotalAppUsageCount,
  updateImageOptimizerUsageCount,

  incrementImageOptimizerInvalidImageCount,
  invalidImagesCountByDate,

  getHiddenBanners,
  autoOptimizationMedia,
  resetAutoOptimizationMedia,

  addStoreToPendingImageOptimizationQueue,
  getNextStoreFromPendingImageOptimizationQueue,

  appEmbedStatus,

  imageOptimizer,
  altTextOptimizer,
  aiOptimizer,
  collectionAiOptimizer,
  ...collectionCache,
  shop,
  webhooks,
  addons,
  product,
  keys: cacheKeys,

  betterDocInstallationStatus,
  removeBetterDocsInstallationStatus,
  isNewStore,
};
