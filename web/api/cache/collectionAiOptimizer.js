//@ts-check
const redisClient = require("./client");
const cacheKeys = require("storeseo-enums/cacheKeys");
const moment = require("moment");

const HOURS_PER_DAY = 24;
const MINUTES_PER_HOUR = 60;
const SECONDS_PER_MINUTE = 60;
const SECONDS_PER_DAY = HOURS_PER_DAY * MINUTES_PER_HOUR * SECONDS_PER_MINUTE;

const DEFAULT_TTL_DAYS = 5;
const DEFAULT_TTL_SECONDS = DEFAULT_TTL_DAYS * SECONDS_PER_DAY;

/**
 * @param {string} date Format: "YYYY-MM-DD"
 */
async function addToPendingSyncStatisticDates(date) {
  const key = `${cacheKeys.COLLECTION_AI_OPTIMIZER}:${cacheKeys.STATISTICS}:${cacheKeys.PENDING_SYNC}:${cacheKeys.DATES}`;
  return redisClient.sAdd(key, date);
}

async function popOneDateFromPendingSyncStatisticsDates() {
  const key = `${cacheKeys.COLLECTION_AI_OPTIMIZER}:${cacheKeys.STATISTICS}:${cacheKeys.PENDING_SYNC}:${cacheKeys.DATES}`;
  return redisClient.sPop(key);
}

/**
 * @param {string} date Format: "YYYY-MM-DD"
 */
async function getMaxMinQueueUsageCount(date = moment().format("YYYY-MM-DD")) {
  const maxKey = `${cacheKeys.COLLECTION_AI_OPTIMIZER}:${cacheKeys.QUEUE_USAGE}:${cacheKeys.MAX}:${date}`;
  const max = await redisClient.get(maxKey);

  const minKey = `${cacheKeys.COLLECTION_AI_OPTIMIZER}:${cacheKeys.QUEUE_USAGE}:${cacheKeys.MIN}:${date}`;
  const min = await redisClient.get(minKey);

  return {
    max: Number(max),
    min: Number(min),
  };
}

/**
 * @param {string} date Format: "YYYY-MM-DD"
 * @param {number} currentQueueUsageCount
 */
async function setMaxMinQueueUsageCount(date, currentQueueUsageCount) {
  const maxKey = `${cacheKeys.COLLECTION_AI_OPTIMIZER}:${cacheKeys.QUEUE_USAGE}:${cacheKeys.MAX}:${date}`;

  const currentMax = await redisClient.get(maxKey);
  if (!currentMax) {
    await redisClient.set(maxKey, currentQueueUsageCount, {
      EX: DEFAULT_TTL_SECONDS,
    });
  } else {
    await redisClient.set(maxKey, Math.max(Number(currentMax), currentQueueUsageCount));
  }

  const minKey = `${cacheKeys.COLLECTION_AI_OPTIMIZER}:${cacheKeys.QUEUE_USAGE}:${cacheKeys.MIN}:${date}`;

  const currentMin = await redisClient.get(minKey);
  if (!currentMin) {
    await redisClient.set(minKey, currentQueueUsageCount, {
      EX: DEFAULT_TTL_SECONDS,
    });
  } else if (currentQueueUsageCount) {
    await redisClient.set(minKey, Math.min(Number(currentMin), currentQueueUsageCount));
  }
}

/**
 * @param {string} date Format: "YYYY-MM-DD"
 */
async function getMaxMinStoreCount(date = moment().format("YYYY-MM-DD")) {
  const maxKey = `${cacheKeys.COLLECTION_AI_OPTIMIZER}:${cacheKeys.STORES_QUEUED}:${cacheKeys.MAX}:${date}`;
  const max = await redisClient.get(maxKey);

  const minKey = `${cacheKeys.COLLECTION_AI_OPTIMIZER}:${cacheKeys.STORES_QUEUED}:${cacheKeys.MIN}:${date}`;
  const min = await redisClient.get(minKey);

  return {
    max: Number(max),
    min: Number(min),
  };
}

/**
 * @param {string} date Format: "YYYY-MM-DD"
 * @param {number} storeCount
 */
async function setMaxMinStoreCount(date, storeCount) {
  const maxKey = `${cacheKeys.COLLECTION_AI_OPTIMIZER}:${cacheKeys.STORES_QUEUED}:${cacheKeys.MAX}:${date}`;

  const currentMax = await redisClient.get(maxKey);
  if (!currentMax) {
    await redisClient.set(maxKey, storeCount, {
      EX: DEFAULT_TTL_SECONDS,
    });
  } else {
    await redisClient.set(maxKey, Math.max(Number(currentMax), storeCount));
  }

  const minKey = `${cacheKeys.COLLECTION_AI_OPTIMIZER}:${cacheKeys.STORES_QUEUED}:${cacheKeys.MIN}:${date}`;

  const currentMin = await redisClient.get(minKey);

  if (!currentMin) {
    await redisClient.set(minKey, storeCount, {
      EX: DEFAULT_TTL_SECONDS,
    });
  } else if (storeCount) {
    await redisClient.set(minKey, Math.min(Number(currentMin), storeCount));
  }
}

/**
 * @typedef {Object} AiOptimizerCache.MaxMinUsageStats.UpdateInput
 *
 * @property {string} [date] Format: "YYYY-MM-DD", Default: current date
 *
 * @property {number} queueUsageCount
 * @property {number} pendingStoresCount
 */

/**
 *
 * @param {AiOptimizerCache.MaxMinUsageStats.UpdateInput} param0
 */
async function updateMaxMinQueueUsageStats({
  date = moment().format("YYYY-MM-DD"),
  queueUsageCount,
  pendingStoresCount,
}) {
  try {
    await Promise.all([setMaxMinQueueUsageCount(date, queueUsageCount), setMaxMinStoreCount(date, pendingStoresCount)]);
  } catch (err) {
    console.log("cache err: ", err);
  }
}

/**
 *
 * @param {string} [date] **Format**: `YYYY-MM-DD`, **Default**: today
 * @returns
 */
const collectionsQueuedByDate = async (date = moment().format("YYYY-MM-DD")) => {
  const key = `${cacheKeys.COLLECTION_AI_OPTIMIZER}:${cacheKeys.COLLECTIONS_QUEUED}:${date}`;
  const count = await redisClient.get(key);

  return Number(count);
};

/**
 *
 * @param {number} value
 * @param {string} [date] **Format**: `YYYY-MM-DD`, **Default**: today
 */
const incrementCollectionsQueuedByDate = async (value, date = moment().format("YYYY-MM-DD")) => {
  const key = `${cacheKeys.COLLECTION_AI_OPTIMIZER}:${cacheKeys.COLLECTIONS_QUEUED}:${date}`;

  return redisClient.incrBy(key, value);
};

/**
 *
 * @param {string} [date] **Format**: `YYYY-MM-DD`, **Default**: today
 * @returns
 */
const collectionsProcessedByDate = async (date = moment().format("YYYY-MM-DD")) => {
  const key = `${cacheKeys.COLLECTION_AI_OPTIMIZER}:${cacheKeys.COLLECTIONS_PROCESSED}:${date}`;
  const count = await redisClient.get(key);

  return Number(count);
};

/**
 *
 * @param {number} value
 * @param {string} [date] **Format**: `YYYY-MM-DD`, **Default**: today
 */
const incrementCollectionsProcessedByDate = async (value, date = moment().format("YYYY-MM-DD")) => {
  const key = `${cacheKeys.COLLECTION_AI_OPTIMIZER}:${cacheKeys.COLLECTIONS_PROCESSED}:${date}`;

  return redisClient.incrBy(key, value);
};

/**
 *
 * @param {string} [date] **Format**: `YYYY-MM-DD`, **Default**: today
 * @returns
 */
const processingFailedByDate = async (date = moment().format("YYYY-MM-DD")) => {
  const key = `${cacheKeys.COLLECTION_AI_OPTIMIZER}:${cacheKeys.PROCESSING_FAILED}:${date}`;
  const count = await redisClient.get(key);

  return Number(count);
};

/**
 *
 * @param {number} value
 * @param {string} [date] **Format**: `YYYY-MM-DD`, **Default**: today
 */
const incrementProcessingFailedByDate = async (value, date = moment().format("YYYY-MM-DD")) => {
  const key = `${cacheKeys.COLLECTION_AI_OPTIMIZER}:${cacheKeys.PROCESSING_FAILED}:${date}`;

  return redisClient.incrBy(key, value);
};

/**
 *
 * @param {string} shop myshopify domain of the shop
 */
const addStoreToPendingOptimizationQueue = async (shop) => {
  const alreadyAddedToQueue = await redisClient.sIsMember(
    `${cacheKeys.PENDING_COLLECTION_AI_OPTIMIZATION_QUEUE}:set`,
    shop
  );
  if (alreadyAddedToQueue) return 1;

  redisClient.sAdd(`${cacheKeys.PENDING_COLLECTION_AI_OPTIMIZATION_QUEUE}:set`, shop);
  return redisClient.rPush(cacheKeys.PENDING_COLLECTION_AI_OPTIMIZATION_QUEUE, shop);
};

const getNextStoreFromPendingOptimizationQueue = async () => {
  const shop = await redisClient.lPop(cacheKeys.PENDING_COLLECTION_AI_OPTIMIZATION_QUEUE);
  if (!shop) return null;

  redisClient.sRem(`${cacheKeys.PENDING_COLLECTION_AI_OPTIMIZATION_QUEUE}:set`, shop);
  return shop;
};

const numberOfStoresPendingOptimization = async () => {
  return redisClient.sCard(`${cacheKeys.PENDING_COLLECTION_AI_OPTIMIZATION_QUEUE}:set`);
};

module.exports = {
  addToPendingSyncStatisticDates,
  popOneDateFromPendingSyncStatisticsDates,

  getMaxMinQueueUsageCount,
  getMaxMinStoreCount,
  updateMaxMinQueueUsageStats,

  collectionsQueuedByDate,
  incrementCollectionsQueuedByDate,

  collectionsProcessedByDate,
  incrementCollectionsProcessedByDate,

  processingFailedByDate,
  incrementProcessingFailedByDate,

  addStoreToPendingOptimizationQueue,
  getNextStoreFromPendingOptimizationQueue,
  numberOfStoresPendingOptimization,
};
