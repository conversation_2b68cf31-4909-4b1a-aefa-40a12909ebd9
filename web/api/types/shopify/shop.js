/**
 * @typedef {Object} BillingAddress
 * @property {string} id - Unique identifier for the billing address
 * @property {string} address1 - Primary address line
 * @property {string} address2 - Secondary address line
 * @property {string} city - City name
 * @property {string} country - Country name
 * @property {string} zip - Postal/ZIP code
 * @property {string} company - Company name
 * @property {string} formatted - Full formatted address
 * @property {string} formattedArea - Formatted area
 * @property {string} phone - Phone number
 */

/**
 * @typedef {Object} ShopPlan
 * @property {string} displayName - Display name of the plan
 * @property {boolean} partnerDevelopment - Whether the shop is in partner development
 * @property {boolean} shopifyPlus - Whether the shop is on Shopify Plus
 */

/**
 * @typedef {Object} MetafieldNode
 * @property {string} id - Unique identifier for the metafield
 * @property {string} key - Key of the metafield
 * @property {string} description - Description of the metafield
 * @property {string} value - Value of the metafield
 * @property {string} type - Type of the metafield
 * @property {string} updatedAt - Last update timestamp
 * @property {string} namespace - Namespace of the metafield
 */

/**
 * @typedef {Object} ShopNode
 * @property {string} id - Unique identifier for the shop
 * @property {string} name - Name of the shop
 * @property {string} email - Email address of the shop
 * @property {string} description - Description of the shop
 * @property {string} url - URL of the shop
 * @property {string} myshopifyDomain - Myshopify domain of the shop
 * @property {string} currencyCode - Currency code used by the shop
 * @property {string} ianaTimezone - IANA timezone of the shop
 * @property {BillingAddress} billingAddress - Billing address information
 * @property {ShopPlan} plan - Shop plan information
 * @property {Object} metafields - Shop metafields information
 * @property {Array<{node: MetafieldNode}>} metafields.edges - Array of metafield edges
 */

/**
 * @typedef {Object} SerializedShop
 * @property {string} [shop_id]
 * @property {string} name
 * @property {string} email
 * @property {string} url
 * @property {string} domain
 * @property {string} [description]
 * @property {string} [access_token]
 * @property {string} currency_code
 * @property {string} ianaTimezone
 * @property {BillingAddress} [billing_address]
 * @property {MetafieldNode[]} [meta]
 * @property {string} shopify_plan_name
 * @property {boolean} [shopify_plus_subscription]
 * @property {string} [logo_path]
 * @property {string} [status]
 */

/**
 * @typedef {Object} WebhookShopData
 * @property {string} name
 * @property {string} email
 * @property {string} domain
 * @property {string} myshopify_domain
 * @property {string} currency
 * @property {string} plan_name
 * @property {string} plan_display_name
 * @property {string} iana_timezone
 */
