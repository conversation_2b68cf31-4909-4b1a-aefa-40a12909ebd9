// @ts-check
const router = require("express").Router();

// Controllers
const AiController = require("../controllers/AiController");
const AnalyticsController = require("../controllers/AnalyticsController");
const ArticleController = require("../controllers/ArticleController");
const BackupRestoreController = require("../controllers/BackupRestoreController");
const BlogController = require("../controllers/BlogController");
const CollectionController = require("../controllers/collections/CollectionController");
const CommonController = require("../controllers/CommonController");
const DocController = require("../controllers/DocController");
const ImageController = require("../controllers/ImageController");
const LocalSEOController = require("../controllers/LocalSEOController");
const NotificationController = require("../controllers/NotificationController");
const OnboardingController = require("../controllers/OnboardingController");
const PageController = require("../controllers/PageController");
const PartnerController = require("../controllers/PartnerController");
const ProductController = require("../controllers/ProductController");
const ReportController = require("../controllers/ReportController");
const SEOController = require("../controllers/SEOController");
const SettingController = require("../controllers/SettingController");
const ShopController = require("../controllers/ShopController");
const SitemapController = require("../controllers/SitemapController");
const SubscriptionController = require("../controllers/SubscriptionController");
const UserController = require("../controllers/UserController");
const UtilityController = require("../controllers/UtilityController");

// Middleware
const authMiddleware = require("../middleware/auth");
const { hasPaidSubscription } = require("../middleware/subscription");

router.use(authMiddleware);

// Auth routes
router.get("/auth", UserController.findAuthUser);

// Onboarding routes
router.put("/onboarding/migrate-data", OnboardingController.migrateDataFromApp);
router.get("/onboarding/status", OnboardingController.getOnboardingStatus);
router.put("/onboarding/status", OnboardingController.updateOnboardingStatus);
router.get("/onboarding/analysis", OnboardingController.getAnalysisData);

// Dashboard routes
router.get("/shop", ShopController.getShop);
router.put("/shop/logo", ShopController.updateShopLogo);
router.get("/shop/google-service-json", ShopController.getGoogleServiceJson);
router.put("/shop/google-service-json", ShopController.updateGoogleServiceJson);
router.post("/shop/migrate-data", ShopController.handleDataMigrateReq);
router.get("/dashboard-stats", ShopController.getDashboardStats);
router.get("/is-onboarded", ShopController.getIsOnboarded);

// Product routes
router.get("/products", ProductController.products);
router.get("/products/:id", ProductController.productDetails);
router.post("/products/:id/optimization", ProductController.getProductOptimization);
router.put("/products/restore-optimized-alt-tags", ProductController.restoreOptimizeImageAltTags);
router.put("/products/:id", ProductController.updateProduct);
router.put("/products/:id/update-alt-tags", ProductController.updateImageAltTags);
router.put("/products/:id/optimize-alt-tags", ProductController.optimizeImageAltTags);
router.put("/products/:id/no-index", ProductController.toggleNoIndexStatus);
router.put("/products/:id/no-follow", ProductController.toggleNoFollowStatus);
router.put("/products/:id/social-media-images", ProductController.updateSocialMediaPreviewImage);
router.put("/products/:id/canonical-url", ProductController.updateCanonicalUrl);
router.post("/products/sync", ProductController.syncProducts);
router.post("/products/sync/:id", ProductController.syncProduct);
router.put("/products/:id/translations/:languageCode", ProductController.updateTranslation);
router.post("/products/sync/:id/translations/:languageCode", ProductController.syncTranslation);

// Sitemap routes
router.get("/sitemaps", ProductController.getSitemaps); // Deprecated
router.post("/sitemaps", ProductController.submitSitemaps); // Deprecated
router.get("/sitemaps/info", SitemapController.getShopSitemapInfo);
router.get("/sitemaps/submit-to-google", SitemapController.submitSitemapsToGoogle);
router.get("/sitemaps/:resourceType", SitemapController.getSitemaps);
router.post("/sitemaps/:resourceType", SitemapController.submitSitemaps);

// Common routes
router.get("/industries", CommonController.industries);
router.get("/packages", CommonController.packages);

// Image routes
router.get("/images/settings", ImageController.getAutomateSettings);
router.get("/images/:type", ImageController.getImages);
router.post("/images/:type/optimize", ImageController.optimizeImage);
router.post("/images/optimize/queue", ImageController.optimizeImagesViaQueue);
router.put("/images/optimize/save", ImageController.saveOptimizedImages);
router.post("/images/:type/restore", ImageController.restoreImage);
router.post("/images/settings", ImageController.updateAutomateSettings);

// Report routes
router.get("/reports", ReportController.reports);

// Setting routes
router.get("/setting/seo-setting", SettingController.getSeoSettingData);
router.post("/setting/seo-setting", SettingController.storeSeoSetting);
router.get("/setting/google-integration", SettingController.getGoogleIntegration);
router.post("/setting/google-integration", SettingController.storeGoogleIntegration);
router.get("/setting/google-service-json", SettingController.getGoogleServiceJSON);
router.post("/setting/google-service-json", SettingController.storeGoogleServiceJSON);
router.get("/setting/google-connect", SettingController.connectToGoogle);
router.put("/setting/google-connect", SettingController.updateAccessTokens);
router.get("/setting/jsonld", SettingController.getJSONLDStatus);
router.put("/setting/jsonld", hasPaidSubscription, SettingController.toggleJSONLDStatus);
router.get("/setting/redirect-out-of-stock", SettingController.getRedirectOutOfStockInfo);
router.put("/setting/redirect-out-of-stock", hasPaidSubscription, SettingController.updateRedirectOutOfStockInfo);
router.get("/setting/google-indexing", SettingController.getGoogleIndexingStatus);
router.put("/setting/google-indexing", hasPaidSubscription, SettingController.toggleGoogleIndexingStatus);
router.get("/setting/local-seo", SEOController.getLocalSEOData);
router.put("/setting/local-seo", hasPaidSubscription, SEOController.updateLocalSEOData);
router.get("/setting/google-integration-info", SettingController.getGoogleIntegrationInfo);
router.put("/setting/google-integration-info", SettingController.updateGoogleIntegrationInfo);
router.put("/setting/google-integration-info/reset", SettingController.resetGoogleIntegrationInfo);
router.post(
  "/setting/google-integration-info/verify-analytics-property-id",
  SettingController.verifyGoogleAnalyticsPropertyId
);
router.get("/setting/html-sitemap", SettingController.getHtmlSitemapSetting);
router.post("/setting/html-sitemap", SettingController.storeHtmlSitemapSetting);
router.get("/setting/html-sitemap/setup", SettingController.getHtmlSitemapSetupInfo);
router.put("/setting/html-sitemap/setup", SettingController.updateHtmlSitemapSetupInfo);
router.get("/setting/image-optimizer", SettingController.getImageOptimizerSetting);
router.put("/setting/image-optimizer", SettingController.updateImageOptimizerSetting);
router.put("/setting/image-optimizer/auto-optimization", SettingController.toggleAutoImageOptimization);
router.get("/setting/email-notification", SettingController.getEmailNotificationSetting);
router.post("/setting/email-notification", SettingController.storeEmailNotificationSetting);
router.get("/setting/multi-language", SettingController.getMultiLanguageSetting);
router.put("/setting/multi-language/toggle", SettingController.toggleMultiLanguageSupport);
router.post("/setting/multi-language/sync", SettingController.syncMultiLanguageData);
router.get("/subscription", SettingController.getSubscriptions);
router.get("/settings/transaction-history", SettingController.getSubscriptionTransactions);
router.post("/subscribe-to-plan", SettingController.subscribeToPlan);
router.post("/subscription-cancel", SettingController.cancelSubscription);
router.get("/checkout/:slug", SettingController.getCheckoutPageData);
router.post("/validate-coupon", SettingController.validateCoupon);
router.put("/settings/hide-ad", SettingController.hideAdForFreeUser);
router.put("/settings/hide-betterdocs-promo", SettingController.hideBetterDocsPromo);
router.put("/settings/hide-banner", SettingController.hideBanner);
router.get("/settings/app-embed-status", SettingController.checkAppEmbedStatus);
router.get("/settings/bulk-seo-optimization", SettingController.getBulkSEOUpdateData);
router.post("/settings/bulk-seo-optimization", SettingController.submitBulkSEOUpdateData);

// Subscription routes
router.get("/credit-bundles", SubscriptionController.creditAddons);
router.post("/credit-purchase", SubscriptionController.creditAddonsPurchase);

// Utility routes
router.get("/utility/address-autocomplete-suggestions", UtilityController.getAddressAutocompleteSuggestions);
router.get("/utility/address-details", UtilityController.getAddressDetailsByPlaceId);
router.get("/utility/keyword-metrics", UtilityController.getKeywordMetrics);
router.get("/utility/installed-apps", UtilityController.getListOfInstalledSeoApps);
router.post("/utility/trigger-event", UtilityController.triggerEvent);

// Page routes
router.get("/pages", PageController.getPages);
router.get("/pages/count", PageController.getShopifyPageCount);
router.get("/pages/:id", PageController.pageDetails);
router.put("/pages/:id/update", PageController.updatePage);
router.post("/pages/:id/optimization", PageController.getPageOptimization);
router.put("/pages/:id/social-media-images", PageController.updateSocialMediaPreviewImage);
router.put("/pages/:id/canonical-url", PageController.updateCanonicalUrl);
router.put("/pages/:id/no-follow", PageController.toggleNoFollowStatus);
router.put("/pages/:id/no-index", PageController.toggleNoIndexStatus);
router.post("/pages/sync", PageController.syncPagesFromShopify);
router.post("/pages/sync/:page_id", PageController.syncPageFromShopify);

// Article routes
router.get("/articles", BlogController.getAllArticles);
router.post("/articles/sync", BlogController.syncBlogsFromShopify);
router.put("/articles/:id/sync", ArticleController.syncArticleFromShopify);
router.get("/articles/:id", ArticleController.getArticle);
router.post("/articles/:id/optimization", ArticleController.getArticleOptimization);
router.put("/articles/:id", ArticleController.updateArticle);
router.put("/articles/:id/no-follow", ArticleController.toggleNoFollowStatus);
router.put("/articles/:id/no-index", ArticleController.toggleNoIndexStatus);
router.put("/articles/:id/social-media-images", ArticleController.updateSocialMediaPreviewImage);
router.put("/articles/:id/canonical-url", ArticleController.updateCanonicalUrl);
router.put("/articles/:id/image-alt-texts", ArticleController.updateFeaturedImageAltText);
router.get("/articles/count", ArticleController.countShopifyArticles);

// Collection routes
router.get("/collections", CollectionController.getCollections);
router.get("/collections/count", CollectionController.getCollectionCount);
router.post("/collections/sync", CollectionController.syncCollections);
router.get("/collections/:id", CollectionController.getCollection);
router.post("/collections/:id/optimization", CollectionController.getCollectionOptimization);
router.put("/collections/:id", CollectionController.updateCollection);
router.put("/collections/:id/no-index", CollectionController.toggleNoIndexStatus);
router.put("/collections/:id/no-follow", CollectionController.toggleNoFollowStatus);

// Doc routes
router.get("/docs", DocController.getDocs);
router.post("/docs/sync", DocController.syncDocs);
router.get("/docs/installStatus", DocController.checkAppInstall);
router.get("/docs/count", DocController.docsCount);
router.get("/docs/:id", DocController.getDoc);
router.post("/docs/:id/optimization", DocController.getDocOptimization);
router.put("/docs/:id", DocController.updateDoc);
router.post("/docs/sync/:id", DocController.syncDoc);
router.get("/docsCount", DocController.docsCount);
router.put("/docs/:id/meta-robots", DocController.toggleNoIndexNoFollow);

// Analytics routes
router.post("/analytics/run-report-by-date", AnalyticsController.runReportByDate);
router.post("/analytics/run-top-products-report-by-date", AnalyticsController.runProductsReport);
router.post("/analytics/run-keywords-report-by-date", AnalyticsController.runKeywordsReport);
router.post("/analytics/search-console-queries-report", AnalyticsController.runSearchConsoleQueriesPerformanceReport);

// Notification routes
router.get("/notifications", NotificationController.getNotifications);
router.put("/notifications/read-all", NotificationController.readAllNotifications);
router.put("/notifications/:id", NotificationController.updateNotification);

// AI routes
router.post("/ai/generate-product-content", AiController.generateProductAiContent);
router.post("/ai/generate-collection-content", AiController.generateCollectionAiContent);
router.post("/ai/generate-page-content", AiController.generatePageAiContent);
router.post("/ai/generate-article-content", AiController.generateArticleAiContent);
router.post("/ai/generate-image-alt-text", AiController.generateImageAltText);
router.post("/ai/generate-image-alt-text/queue", AiController.generateImageAltTextByQueue);
router.post("/ai/:type/generate-ai-content/queue", AiController.generateAiContentByQueue);
router.post("/ai/:type/restore-ai-content", AiController.restoreAiContentByQueue);
router.get("/ai/auto-optimizer-settings", AiController.getAutoAiContentGenerationSettings);
router.put("/ai/auto-optimizer-settings", AiController.updateAutoAiContentGenerationSettings);

// Partner routes
router.get("/partners", PartnerController.getPartnersList);

// SEO Schema routes
router.get("/local-seo/breadcrumb-schema", LocalSEOController.getBreadcrumbSchemaSettings);
router.put("/local-seo/breadcrumb-schema", LocalSEOController.updateBreadcrumbSchemaSettings);
router.get("/local-seo/collection-schema", LocalSEOController.getCollectionSchemaSettings);
router.put("/local-seo/collection-schema", LocalSEOController.updateCollectionSchemaSettings);
router.get("/local-seo/blog-schema", LocalSEOController.getBlogSchemaSettings);
router.put("/local-seo/blog-schema", LocalSEOController.updateBlogSchemaSettings);
router.get("/local-seo/product-schema", LocalSEOController.getProductSchemaSettings);
router.put("/local-seo/product-schema", LocalSEOController.updateProductSchemaSettings);
router.get("/local-seo/product-merchant-schema", LocalSEOController.getProductMerchantSchemaSettings);
router.put("/local-seo/product-merchant-schema", LocalSEOController.updateProductMerchantSchemaSettings);
router.get("/local-seo/article-schema", LocalSEOController.getArticleSchemaSettings);
router.put("/local-seo/article-schema", LocalSEOController.updateArticleSchemaSettings);
router.get("/local-seo/organization-schema", LocalSEOController.getOrganizationSchemaSettings);
router.put("/local-seo/organization-schema", LocalSEOController.updateOrganizationSchemaSettings);
router.get("/local-seo/local-business-schema", LocalSEOController.getLocalBusinessSchemaSettings);
router.put("/local-seo/local-business-schema", LocalSEOController.updateLocalBusinessSchemaSettings);

// Backup and Restore routes
router.get("/backup-restore/status", BackupRestoreController.getStoreBackupRestoreInProgressStatus);
router.get("/backup-restore", BackupRestoreController.getBackupRestoreData);
router.put("/backup-restore", BackupRestoreController.createBackup);
router.post("/backup-restore/:id", BackupRestoreController.restoreBackup);
router.delete("/backup-restore/:id", BackupRestoreController.deleteBackup);

module.exports = router;
