const WebhookController = require("../controllers/WebhookController");
const CollectionsWebhookController = require("../controllers/collections/CollectionsWebhookController");
const { verifyWebhook } = require("../middleware/verifyShopifyRequests");

const router = require("express").Router();

router.use(verifyWebhook);

router.post("/product/create", WebhookController.productUpdateOrCreate);
router.post("/product/update", WebhookController.productUpdateOrCreate);
router.post("/product/delete", WebhookController.productDelete);
router.post("/app-subscriptions/update", WebhookController.appSubscriptionUpdate);
router.post("/app-purchase/update", WebhookController.appPurchaseUpdate);
router.post("/themes/publish", WebhookController.handleThemeChange);
router.post("/shop/update", WebhookController.handleShopUpdate);
router.post("/app/uninstalled", WebhookController.handleAppUninstall);
router.post("/locations/create", WebhookController.handleLocationCreate);
router.post("/locations/update", WebhookController.handleLocationUpdate);
router.post("/locations/delete", WebhookController.handleLocationDelete);
router.post("/bulk-operation-finish", WebhookController.handleBulkOperationFinish);

router.post("/customer-data", WebhookController.customerDataRequest);
router.post("/customer-redact", WebhookController.customerRedact);
router.post("/shop-redact", WebhookController.shopRedact);

// Collection related routes
router.post("/collection/create", CollectionsWebhookController.collectionCreate);
router.post("/collection/update", CollectionsWebhookController.collectionUpdate);
router.post("/collection/delete", CollectionsWebhookController.collectionDelete);

// locales
router.post("/locales/create", WebhookController.handleLocaleCreate);
router.post("/locales/update", WebhookController.handleLocaleUpdate);

module.exports = router;
