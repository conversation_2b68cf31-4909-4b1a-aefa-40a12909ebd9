const AppProxyController = require("../controllers/AppProxyController");

const router = require("express").Router();

router.get("/", AppProxyController.generateHtmlSitemap);

router.get("/products", AppProxyController.generateProductsHtmlSitemap);
router.get("/collections", AppProxyController.generateCollectionsHtmlSitemap);
router.get("/pages", AppProxyController.generatePagesHtmlSitemap);
router.get("/blogs", AppProxyController.generateBlogsHtmlSitemap);

module.exports = router;
