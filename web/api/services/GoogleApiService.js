const { google } = require("googleapis");
const ShopifyService = require("../services/ShopifyService");
const integrationService = require("../services/IntegrationService");
const {
  isValidAddress,
  buildQueryStringForGoogleMapsAPI,
  extractAddressObjFromGooglePlaceDetailAPIResponse,
  generatePayloadForGoogleKeywordIdeasAPI,
} = require("../utils/helper");
const logger = require("storeseo-logger");
const axios = require("axios");
const integrationType = require("storeseo-enums/integrationType");
const GoogleAuthService = require("./GoogleAuthService");
const settingKeys = require("storeseo-enums/settingKeys");

const googleConfig = require("../config/google");

const SCOPES = ["https://www.googleapis.com/auth/webmasters", "https://www.googleapis.com/auth/indexing"];

class GoogleApiService {
  oAuthClient = async (user) => {
    const ShopService = require("../services/ShopService");
    const {
      configs: { client_id, client_secret },
    } = await integrationService.getConfig(user.shopId, integrationType.GOOGLE);

    const shop = await ShopService.getShopById(user.shopId);
    const handle = await ShopifyService.getAppHandle(user.shop);
    const redirect_uri = `https://${shop.domain}/admin/apps/${handle}/sitemap`;

    return new google.auth.OAuth2({
      clientId: client_id,
      clientSecret: client_secret,
      redirectUri: redirect_uri,
    });
  };

  #adsApiHttpClient = async () => {
    const refreshToken = googleConfig.ads.refreshToken;
    const { token: accessToken } = await GoogleAuthService.getOAuthClient(refreshToken).getAccessToken();

    return axios.create({
      baseURL: "https://googleads.googleapis.com/v17",
      headers: {
        "developer-token": googleConfig.ads.developerToken,
        Authorization: `Bearer ${accessToken}`,
      },
    });
  };

  getOAuthUrl = async (user) => {
    const gClient = await this.oAuthClient(user);

    return gClient.generateAuthUrl({
      access_type: "offline",
      scope: SCOPES,
    });
  };

  getAccessTokens = async (user, code) => {
    const gClient = await this.oAuthClient(user);
    const { tokens } = await gClient.getToken(code);
    return tokens;
  };

  getSitemap = async (user) => {
    const { tokens } = await integrationService.getConfig(user.shopId, integrationType.GOOGLE);
    const gClient = await this.oAuthClient(user);
    gClient.setCredentials({ access_token: tokens.access_token });
    const webmaster = google.webmasters({
      version: "v3",
      auth: await gClient,
    });

    return await webmaster.sitemaps.get({
      siteUrl: `https://${user.shop}`,
      feedpath: `https://${user.shop}/sitemap.xml`,
    });
  };

  submitSitemap = async (user) => {
    const ShopService = require("../services/ShopService");
    const { tokens } = await integrationService.getConfig(user.shopId, integrationType.GOOGLE);
    const gClient = await this.oAuthClient(user);
    gClient.setCredentials({ access_token: tokens.access_token });
    const webmaster = google.webmasters({
      version: "v3",
      auth: await gClient,
    });

    const shop = await ShopService.getShop(user.shop);

    // const params = {
    //   siteUrl: `https://nahian.dev`,
    //   feedpath: `https://nahian.dev/sitemap.xml`,
    // };
    const params = {
      siteUrl: shop.url,
      feedpath: `${shop.url}/sitemap.xml`,
    };
    return await webmaster.sitemaps.submit(params);
  };

  submitGoogleIndexing = async (user, data) => {
    const ShopService = require("./ShopService");

    const { value: google_integration_info } = await ShopService.getShopSetting(
      user.shopId,
      settingKeys.GOOGLE_INTEGRATION_INFO
    );
    const authenticatedUser = await GoogleAuthService.getAuthenticatedUser(google_integration_info?.googleUserEmail);
    const oAuthClient = GoogleAuthService.getOAuthClient(authenticatedUser.refresh_token);

    // const gClient = await this.googleAuth(user);
    const indexing = google.indexing({
      version: "v3",
      auth: oAuthClient,
    });

    try {
      return await indexing.urlNotifications.publish({
        requestBody: {
          // url: "https://nahian.dev",
          // type: urlNotificationType.URL_UPDATED,
          ...data,
        },
      });
    } catch (err) {
      return err.response;
    }
  };

  googleAuth = async (user) => {
    const { configs } = await integrationService.getConfig(user.shopId, integrationType.GOOGLE_SERVICE_JSON);

    const options = {
      keyFile: `./uploads/${configs.file_path}`,
      scopes: SCOPES,
    };

    return new google.auth.GoogleAuth(options);
  };

  getLatLongFromAddress = async (addressObj) => {
    try {
      if (isValidAddress(addressObj)) {
        const queryString = buildQueryStringForGoogleMapsAPI(addressObj);
        const url = `${googleConfig.geoCodingApiUrl}?${queryString}`;
        const { data } = await axios.get(url);
        return data?.results?.[0]?.geometry?.location;
      }
    } catch (err) {
      console.error(`Error generating latitude longitude for address: ${addressObj} => err: ${err.message}`);
      return null;
    }
  };

  getPlaceAutocompleteSuggestions = async (inputText) => {
    try {
      const url = `${googleConfig.placeAutocompleteApiUrl}?key=${googleConfig.mapsApiKey}&input=${inputText}`;
      const {
        data: { predictions: suggestions },
      } = await axios.get(url);
      return suggestions;
    } catch (err) {
      console.error(`Error fetching place autocomplete suggestions, inputText=${inputText}: ${err}`);
      return null;
    }
  };

  getPlaceDetailByPlaceId = async (placeId) => {
    try {
      const fields = ["formatted_address", "geometry", "address_component"].join(",");
      const url = `${googleConfig.placeDetailsApiUrl}?key=${googleConfig.mapsApiKey}&fields=${fields}&place_id=${placeId}`;
      const {
        data: { result },
      } = await axios.get(url);
      return extractAddressObjFromGooglePlaceDetailAPIResponse(result);
    } catch (err) {
      console.log(err);
      console.error(`Error fetching place details, placeId=${placeId}: ${err}`);
      return null;
    }
  };

  getListOfVerifiedSitesForUser = async (authenticatedUser) => {
    try {
      const { refresh_token } = authenticatedUser;
      const oAuthClient = GoogleAuthService.getOAuthClient(refresh_token);
      const siteverification = google.siteVerification({
        version: "v1",
        auth: oAuthClient,
      });

      const { data } = await siteverification.webResource.list();
      console.log(`sites data res: `, JSON.stringify(data));
      return data;
    } catch (err) {
      console.error(`Error fetching list of verified sites for user ${authenticatedUser}: ${err}`);
      return null;
    }
  };

  isSiteVerified = async (domain, authenticatedUser) => {
    try {
      const { items } = await this.getListOfVerifiedSitesForUser(authenticatedUser);
      return items?.reduce((status, item) => {
        if (item.site?.identifier.includes(domain)) return true;
        else return status;
      }, false);
    } catch (err) {
      console.error(`Error checking site verification status for ${domain} for user ${authenticatedUser}: ${err}`);
      return null;
    }
  };

  getSiteVerificationToken = async (url, authenticatedUser, verificationMethod = "META") => {
    try {
      const { refresh_token } = authenticatedUser;
      const oauthClient = GoogleAuthService.getOAuthClient(refresh_token);
      const siteVerification = google.siteVerification({
        version: "v1",
        auth: oauthClient,
      });

      url = url.startsWith("https://") ? url : `https://${url}`;
      url = url.endsWith("/") ? url : `${url}/`;

      console.log("url for site verification token: ", url);

      const { data } = await siteVerification.webResource.getToken({
        requestBody: {
          verificationMethod,
          site: {
            identifier: url,
            type: "SITE",
          },
        },
      });
      return data;
    } catch (err) {
      console.log(
        `Error getting site verification token for url ${url}, authenticatedUser: ${authenticatedUser}. Error: ${err}`
      );
      return null;
    }
  };

  /**
   *
   * @param {string} url
   * @param {*} authenticatedUser
   * @param {*} verificationMethod
   * @returns
   */
  verifySiteOwnership = async (url, authenticatedUser, verificationMethod = "META") => {
    try {
      const { refresh_token } = authenticatedUser;
      const oauthClient = GoogleAuthService.getOAuthClient(refresh_token);
      const siteVerification = google.siteVerification({
        version: "v1",
        auth: oauthClient,
      });

      url = url.includes("https://") ? url : `https://${url}`;
      url = url.endsWith("/") ? url : `${url}/`;
      const { data } = await siteVerification.webResource.insert({
        verificationMethod,
        requestBody: {
          site: {
            identifier: url,
            type: "SITE",
          },
        },
      });
      return data;
    } catch (err) {
      console.log(`Error verifying site ownership of ${url} for user ${authenticatedUser?.email}. Error: ${err}`);
      return null;
    }
  };

  addSiteToSearchConsole = async (url, authenticatedUser) => {
    try {
      const { refresh_token } = authenticatedUser;
      const oauthClient = GoogleAuthService.getOAuthClient(refresh_token);
      const webmaster = google.webmasters({
        version: "v3",
        auth: oauthClient,
      });

      url = url.includes("https://") ? url : `https://${url}`;
      await webmaster.sites.add({
        siteUrl: url,
      });
      return true;
    } catch (err) {
      console.error(`Error adding site ${url} to search console for user ${authenticatedUser?.email}. Error: ${err}`);
      return null;
    }
  };

  submitSitemapForShop = async (shopDomain, authenticatedUser) => {
    try {
      const { refresh_token } = authenticatedUser;
      const oauthClient = GoogleAuthService.getOAuthClient(refresh_token);
      const webmaster = google.webmasters({
        version: "v3",
        auth: oauthClient,
      });

      const url = shopDomain.includes("https://") ? shopDomain : `https://${shopDomain}`;
      await webmaster.sitemaps.submit({
        siteUrl: url,
        feedpath: `${url}/sitemap.xml`,
      });
      return true;
    } catch (err) {
      console.log("err: ", err);
      console.error(
        `Error submitting sitemap for ${shopDomain} to search console for user ${authenticatedUser?.email}. Error: ${err}`
      );
      return null;
    }
  };

  generateKeywordIdeas = async (customerId, keywords) => {
    try {
      const payload = generatePayloadForGoogleKeywordIdeasAPI({ keywords });
      const { data } = await (
        await this.#adsApiHttpClient()
      ).post(`/customers/${customerId}:generateKeywordIdeas`, payload);
      return data.results;
    } catch (err) {
      console.warn(
        `GoogleApiService -> Error generating keyword ideas for customerId ${customerId}. ${JSON.stringify(err)}\n\n`,
        err.response?.headers,
        JSON.stringify(err.response?.data || null, null, 2)
      );
      return null;
    }
  };

  #mutateKeywordPlans = async (customerId, operations) => {
    const Client = await this.#adsApiHttpClient();
    return await Client.post(`/customers/${customerId}/keywordPlans:mutate`, {
      operations,
    });
  };

  createKeywordPlan = async ({ customerId, planName }) => {
    try {
      const keywordPlanOperation = {
        create: {
          name: planName,
          forecastPeriod: {
            dateInterval: "NEXT_QUARTER",
          },
        },
      };

      const { data } = await this.#mutateKeywordPlans(customerId, [keywordPlanOperation]);
      return data.results[0].resourceName;
    } catch (err) {
      console.error(
        `GoogleApiService -> Error creating keyword plan ${planName} for customer ${customerId}. ${JSON.stringify(err)}`
      );
      return null;
    }
  };

  deleteKeywordPlan = async ({ customerId, planResourceName }) => {
    try {
      const keywordPlanOperation = {
        remove: planResourceName,
      };

      const { data } = await this.#mutateKeywordPlans(customerId, [keywordPlanOperation]);
      return data.results[0].resourceName;
    } catch (err) {
      console.table([]);
      console.error(
        `GoogleApiService -> Error deleting keyword plan ${planResourceName} for customerId ${customerId}. ${JSON.stringify(
          err
        )}`
      );
      return null;
    }
  };

  #mutateKeywordPlanCampaigns = async (customerId, operations) => {
    const Client = await this.#adsApiHttpClient();
    return await Client.post(`/customers/${customerId}/keywordPlanCampaigns:mutate`, {
      operations,
    });
  };

  createKeywordPlanCampaign = async ({ customerId, campaignName, keywordPlanResourceName }) => {
    try {
      const keywordPlanCampaignOperation = {
        create: {
          keywordPlanNetwork: "GOOGLE_SEARCH_AND_PARTNERS",
          keywordPlan: keywordPlanResourceName,
          name: campaignName,
          cpcBidMicros: "50000",
        },
      };

      const { data } = await this.#mutateKeywordPlanCampaigns(customerId, [keywordPlanCampaignOperation]);
      return data.results[0].resourceName;
    } catch (err) {
      console.error(
        `GoogleApiService -> Error creating keyword plan campaign ${campaignName} for keyword plan ${keywordPlanResourceName} & customer ${customerId}. ${JSON.stringify(
          err
        )}`
      );
      return null;
    }
  };

  deleteKeywordPlanCampaign = async ({ customerId, campaignResourceName }) => {
    try {
      const keywordPlanCampaignOperation = {
        remove: campaignResourceName,
      };

      const { data } = await this.#mutateKeywordPlanCampaigns(customerId, [keywordPlanCampaignOperation]);
      return data.results[0].resourceName;
    } catch (err) {
      console.error(
        `GoogleApiService -> error deleting keyword plan campaign ${campaignResourceName} for customer ${customerId}. ${JSON.stringify(
          err
        )}`
      );
      return null;
    }
  };

  #mutateKeywordPlanAdGroups = async (customerId, operations) => {
    const Client = await this.#adsApiHttpClient();
    return await Client.post(`/customers/${customerId}/keywordPlanAdGroups:mutate`, {
      operations,
    });
  };

  createKeywordPlanAdGroup = async ({ customerId, adGroupName, campaignResourceName }) => {
    try {
      const adGroupOperation = {
        create: {
          keywordPlanCampaign: campaignResourceName,
          name: adGroupName,
          cpcBidMicros: "100000000",
        },
      };

      const { data } = await this.#mutateKeywordPlanAdGroups(customerId, [adGroupOperation]);
      return data.results[0].resourceName;
    } catch (err) {
      console.error(
        `GoogleApiService -> Error creating ad group ${adGroupName} for keyword campaign ${campaignResourceName} & customer ${customerId}. ${JSON.stringify(
          err
        )}`
      );
      return null;
    }
  };

  deleteKeywordPlanAdGroup = async ({ customerId, adGroupResourceName }) => {
    try {
      const adGroupOperation = {
        remove: adGroupResourceName,
      };

      const { data } = await this.#mutateKeywordPlanAdGroups(customerId, [adGroupOperation]);
      return data.results[0].resourceName;
    } catch (err) {
      console.error(
        `GoogleApiService -> error deleting ad group ${adGroupResourceName} for customer ${customerId}. ${JSON.stringify(
          err
        )}`
      );
      return null;
    }
  };

  #mutateKeywordPlanAdGroupKeywords = async (customerId, operations) => {
    const Client = await this.#adsApiHttpClient();
    return await Client.post(`/customers/${customerId}/keywordPlanAdGroupKeywords:mutate`, {
      operations,
    });
  };

  createKeywordPlanAdGroupKeyword = async ({ customerId, keyword, adGroupResourceName }) => {
    try {
      const adGroupKeywordOperation = {
        create: {
          matchType: "PHRASE",
          keywordPlanAdGroup: adGroupResourceName,
          text: keyword,
          cpcBidMicros: "10000000",
        },
      };

      const { data } = await this.#mutateKeywordPlanAdGroupKeywords(customerId, [adGroupKeywordOperation]);
      return data.results[0].resourceName;
    } catch (err) {
      console.error(
        `GoogleApiService - error creating ad group keyword '${keyword}' for keyword plan ad group ${adGroupResourceName} & customerId ${customerId}. ${JSON.stringify(
          err
        )}`
      );
      return null;
    }
  };

  deleteKeywordPlanAdGroupKeyword = async ({ customerId, keywordResourceName }) => {
    try {
      const adGroupKeywordOperation = {
        remove: keywordResourceName,
      };

      const { data } = await this.#mutateKeywordPlanAdGroupKeywords(customerId, [adGroupKeywordOperation]);
      return data.results[0].resourceName;
    } catch (err) {
      console.error(
        `GoogleApiService -> error deleting ad group keyword '${keywordResourceName}' for customerId ${customerId}. ${JSON.stringify(
          err
        )}`
      );
      return null;
    }
  };
}

module.exports = new GoogleApiService();
