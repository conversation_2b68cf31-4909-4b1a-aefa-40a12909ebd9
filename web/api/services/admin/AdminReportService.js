// @ts-check
const moment = require("moment");
const { QueryTypes } = require("sequelize");
const { Op, sequelize, AdminReport } = require("../../../sequelize");
const EventType = require("storeseo-enums/shopify/appEventTypes");
const appSubscriptionInterval = require("storeseo-enums/appSubscriptionInterval");
const AdminReportTypes = require("storeseo-enums/admin/AdminReportTypes");
const app = require("../../config/app");
const { sumStatsWithExclusions } = require("../../utils/helper");

class AdminReportService {
  constructor() {
    this.tz = app.report.tz;
    this.format = "YYYY-MM-DD HH:mm:ss";
  }

  /**
   * Prepares the report and comparison dates
   * @param {string | undefined} startDate
   * @param {string | undefined} endDate
   * @returns {{startDate:string,endDate:string,compareStartDate:string,compareEndDate:string,interval:number,formattedDate:string}} dates
   */
  prepareDates = (startDate = undefined, endDate = undefined) => {
    const startDay = moment(startDate).subtract(1, "day").startOf("D").subtract(6, "hours");
    const endDay = moment(endDate || startDate)
      .subtract(1, "day")
      .endOf("D")
      .subtract(6, "hours");

    const interval = Math.ceil(endDay.diff(startDay, "days", true));

    const compareStartDate = startDay.clone().subtract(interval, "days");
    const compareEndDate = endDay.clone().subtract(interval, "days");

    return {
      startDate: startDay.format(this.format),
      endDate: endDay.format(this.format),
      compareStartDate: compareStartDate.format(this.format),
      compareEndDate: compareEndDate.format(this.format),
      interval,
      formattedDate: this.#formattedDate(startDay, endDay, interval),
    };
  };

  #formattedDate = (startDate, endDate, interval) => {
    if (startDate && endDate && interval > 1) {
      return moment(startDate).format("Do MMM") + " - " + moment(endDate).format("Do MMM, YYYY");
    }
    return moment(startDate).format("Do MMM (dddd), YYYY");
  };

  /**
   * status report based on date
   * @param {string | undefined} startDate
   * @param {string | undefined} endDate
   * @returns {Promise<any>} data
   */
  dailyStatusReport = async (startDate = undefined, endDate = undefined) => {
    const newFreeStores = await this.countNewFreeStores(startDate, endDate);
    const returningFreeStores = await this.countReturningFreeStores(startDate, endDate);

    const newProStores = await this.countNewProStores(startDate, endDate);
    const freeToProStores = await this.countFreeToProStores(startDate, endDate);
    const proToProStores = await this.countProToProStores(startDate, endDate);

    const uninstalledFromFreeStores = await this.countUninstalledFromFreeStores(startDate, endDate);
    const downgradeToFreeStores = await this.countDowndradeToFreeStores(startDate, endDate);
    const cancelledStores = await this.countCancelledStores(startDate, endDate);

    const closedFromFreeStores = await this.countClosedFromFreeStores(startDate, endDate);
    const closedFromProStores = await this.countClosedFromProStores(startDate, endDate);

    const reopendFromFreeStores = await this.countReOpenedFreeStores(startDate, endDate);
    const reopendFromProStores = await this.countReOpenedProStores(startDate, endDate);

    const yearlyActiveStores = await this.countYearlyActiveStores(endDate);
    const visionaryAciveStores = await this.countVisionaryActiveStores(endDate);

    const monthlyNotPaidStores = await this.countMonthlyNotPaidActiveStores(endDate);
    const monthlyOnetimePaidStores = await this.countMonthlyOnetimePaidActiveStores(endDate);
    const monthlyMultiplePaidStores = await this.countMonthlyMultiplePaidActiveStores(endDate);

    const onetimeCharges = await this.countOnetimeCharges(startDate, endDate);
    const onetimeChargesAll = await this.countOnetimeCharges("2023-01-01", endDate);

    return {
      newFreeStores,
      returningFreeStores,
      newProStores,
      freeToProStores,
      proToProStores,
      uninstalledFromFreeStores,
      downgradeToFreeStores,
      cancelledStores,
      closedFromFreeStores,
      closedFromProStores,
      reopendFromFreeStores,
      reopendFromProStores,
      yearlyActiveStores,
      visionaryAciveStores,
      monthlyActiveStores: [
        { title: "No Payment (New)", key: "monthlyNotPaidStores", count: monthlyNotPaidStores },
        { title: "1 Payment Made", key: "monthlyOnetimePaidStores", count: monthlyOnetimePaidStores },
        { title: "More than 1 Payment Made", key: "monthlyMultiplePaidStores", count: monthlyMultiplePaidStores },
      ],
      onetimeCharges,
      onetimeChargesAll,
    };
  };

  /**
   * New free stores: installed, not subscribed to any pro plan
   * @param {string | undefined} startDate
   * @param {string | undefined} endDate
   * @returns {Promise<number>} count
   */
  countNewFreeStores = async (startDate = undefined, endDate = undefined) => {
    const [result] = await sequelize.query(
      `
        SELECT COUNT(e1.shop_domain) AS count
        FROM shopify_app_events e1
        WHERE e1.occurred_at BETWEEN :startDate AND :endDate
          AND e1.event_type = :installedEventType
          AND NOT EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.event_type = :activatedEventType
                AND e2.occurred_at BETWEEN :startDate AND :endDate
          )
          AND NOT EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.event_type = :uninstalledEventType
                AND e2.occurred_at <= :startDate
          )
        `,
      {
        replacements: {
          startDate: startDate,
          endDate: endDate,
          installedEventType: EventType.RELATIONSHIP_INSTALLED,
          activatedEventType: EventType.SUBSCRIPTION_CHARGE_ACTIVATED,
          uninstalledEventType: EventType.RELATIONSHIP_UNINSTALLED,
        },
        type: QueryTypes.SELECT,
      }
    );

    return result?.count || 0;
  };

  /**
   * Returning free stores: previously installed as free user and uninstalled. installed again
   * @param {string | undefined} startDate
   * @param {string | undefined} endDate
   * @returns {Promise<number>} count
   */
  countReturningFreeStores = async (startDate = undefined, endDate = undefined) => {
    const [result] = await sequelize.query(
      `
        SELECT COUNT(DISTINCT e1.shop_domain) AS count
        FROM shopify_app_events e1
        WHERE e1.occurred_at BETWEEN :startDate AND :endDate
          AND e1.event_type = :installedEventType
          AND EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.event_type = :uninstalledEventType
                AND e2.occurred_at < :startDate
          )
          AND NOT EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.event_type = :activatedEventType
                AND e2.occurred_at BETWEEN :startDate AND :endDate
          )
      `,
      {
        replacements: {
          startDate: startDate,
          endDate: endDate,
          installedEventType: EventType.RELATIONSHIP_INSTALLED,
          uninstalledEventType: EventType.RELATIONSHIP_UNINSTALLED,
          activatedEventType: EventType.SUBSCRIPTION_CHARGE_ACTIVATED,
        },
        type: QueryTypes.SELECT,
      }
    );

    return result?.count || 0;
  };

  /**
   * New pro stores: installed & subscribed to plan
   * @param {string | undefined} startDate
   * @param {string | undefined} endDate
   * @returns {Promise<Array>} result
   */
  countNewProStores = async (startDate = undefined, endDate = undefined) => {
    const result = await sequelize.query(
      `
        SELECT
            INITCAP(e1.charge_name) as title,
            e1.charge_amount as key,
            COUNT(e1.id) as count
        FROM shopify_app_events e1
        WHERE e1.event_type = :activatedEventType
          AND e1.is_test != true
          AND e1.occurred_at BETWEEN :startDate AND :endDate
          AND EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.occurred_at BETWEEN :startDate AND :endDate
                AND e2.event_type = :installedEventType
          )
          AND NOT EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.occurred_at <= :startDate
                AND e2.event_type = :activatedEventType
          )
          AND NOT EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.occurred_at BETWEEN :startDate AND :endDate
                AND (
                    e2.event_type = :uninstalledEventType OR
                    e2.event_type = :deactivatedEventType
                )
          )
          GROUP BY e1.charge_name, e1.charge_amount
          ORDER BY e1.charge_amount
        `,
      {
        replacements: {
          startDate: startDate,
          endDate: endDate,
          activatedEventType: EventType.SUBSCRIPTION_CHARGE_ACTIVATED,
          installedEventType: EventType.RELATIONSHIP_INSTALLED,
          uninstalledEventType: EventType.RELATIONSHIP_UNINSTALLED,
          deactivatedEventType: EventType.RELATIONSHIP_DEACTIVATED,
        },
        type: QueryTypes.SELECT,
      }
    );

    return result;
  };

  /**
   * Free to pro stores: installed previously and subscribed to plan
   * @param {string | undefined} startDate
   * @param {string | undefined} endDate
   * @returns {Promise<Array>} result
   */
  countFreeToProStores = async (startDate = undefined, endDate = undefined) => {
    const result = await sequelize.query(
      `
        SELECT
            INITCAP(e1.charge_name) as title,
            e1.charge_amount as key,
            COUNT(e1.id) as count
        FROM shopify_app_events e1
        WHERE e1.event_type = :activatedEventType
          AND e1.is_test != true
          AND e1.occurred_at BETWEEN :startDate AND :endDate
          AND EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.occurred_at <= :startDate
                AND e2.event_type = :installedEventType
          )
          AND NOT EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.occurred_at <= :startDate
                AND e2.event_type = :canceledEventType
          )
          GROUP BY e1.charge_name, e1.charge_amount
          ORDER BY e1.charge_amount
        `,
      {
        replacements: {
          startDate: startDate,
          endDate: endDate,
          activatedEventType: EventType.SUBSCRIPTION_CHARGE_ACTIVATED,
          installedEventType: EventType.RELATIONSHIP_INSTALLED,
          canceledEventType: EventType.SUBSCRIPTION_CHARGE_CANCELED,
        },
        type: QueryTypes.SELECT,
      }
    );

    return result;
  };

  /**
   * Pro to pro stores: installed previously and upgraded/downgraded from another pro plan
   * @param {string | undefined} startDate
   * @param {string | undefined} endDate
   * @returns {Promise<number>} count
   */
  countProToProStores = async (startDate = undefined, endDate = undefined) => {
    const [result] = await sequelize.query(
      `
        SELECT COUNT(DISTINCT e1.shop_domain) AS count
        FROM shopify_app_events e1
        WHERE e1.event_type = :activatedEventType
          AND e1.occurred_at BETWEEN :startDate AND :endDate
          AND EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.occurred_at <= :endDate
                AND e2.event_type = :installedEventType
          )
          AND EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.occurred_at BETWEEN :startDate AND :endDate
                AND e2.event_type = :canceledEventType
          )
          AND NOT EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.occurred_at BETWEEN :startDate AND :endDate
                AND (
                    e2.event_type = :uninstalledEventType OR
                    e2.event_type = :deactivatedEventType
                )
          )
        `,
      {
        replacements: {
          startDate: startDate,
          endDate: endDate,
          activatedEventType: EventType.SUBSCRIPTION_CHARGE_ACTIVATED,
          installedEventType: EventType.RELATIONSHIP_INSTALLED,
          canceledEventType: EventType.SUBSCRIPTION_CHARGE_CANCELED,
          uninstalledEventType: EventType.RELATIONSHIP_UNINSTALLED,
          deactivatedEventType: EventType.RELATIONSHIP_DEACTIVATED,
        },
        type: QueryTypes.SELECT,
      }
    );

    return result?.count || 0;
  };

  /**
   * Uninstallation: uninstalled & subscription charge not activated
   * @param {string | undefined} startDate
   * @param {string | undefined} endDate
   * @returns {Promise<number>} count
   */
  countUninstalledFromFreeStores = async (startDate = undefined, endDate = undefined) => {
    const [result] = await sequelize.query(
      `
        SELECT COUNT(DISTINCT e1.shop_domain) AS count
        FROM shopify_app_events e1
        WHERE e1.occurred_at BETWEEN :startDate AND :endDate
          AND e1.event_type = :uninstalledEventType
          AND NOT EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.event_type = :activatedEventType
                AND e2.occurred_at <= :endDate
          )
        `,
      {
        replacements: {
          startDate: startDate,
          endDate: endDate,
          uninstalledEventType: EventType.RELATIONSHIP_UNINSTALLED,
          activatedEventType: EventType.SUBSCRIPTION_CHARGE_ACTIVATED,
        },
        type: QueryTypes.SELECT,
      }
    );

    return result?.count || 0;
  };

  /**
   * Downgrade to free
   * @param {string | undefined} startDate
   * @param {string | undefined} endDate
   * @returns {Promise<number>} count
   */
  countDowndradeToFreeStores = async (startDate = undefined, endDate = undefined) => {
    const [result] = await sequelize.query(
      `
        SELECT COUNT(DISTINCT e1.shop_domain) AS count
        FROM shopify_app_events e1
        WHERE e1.occurred_at BETWEEN :startDate AND :endDate
          AND e1.event_type = :canceledEventType
          AND EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.event_type = :activatedEventType
                AND e2.occurred_at <= :endDate
          )
          AND NOT EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND (
                    e2.event_type = :uninstalledEventType OR
                    e2.event_type = :deactivatedEventType
                )
                AND e2.occurred_at <= :endDate
          )
        `,
      {
        replacements: {
          startDate: startDate,
          endDate: endDate,
          canceledEventType: EventType.SUBSCRIPTION_CHARGE_CANCELED,
          activatedEventType: EventType.SUBSCRIPTION_CHARGE_ACTIVATED,
          uninstalledEventType: EventType.RELATIONSHIP_UNINSTALLED,
          deactivatedEventType: EventType.RELATIONSHIP_DEACTIVATED,
        },
        type: QueryTypes.SELECT,
      }
    );

    return result?.count || 0;
  };

  /**
   * Cancelled stores: Subcription cancelled & uninstalled
   * @param {string | undefined} startDate
   * @param {string | undefined} endDate
   * @returns {Promise<number>} count
   */
  countCancelledStores = async (startDate = undefined, endDate = undefined) => {
    const [result] = await sequelize.query(
      `
        SELECT COUNT(DISTINCT e1.shop_domain) AS count
        FROM shopify_app_events e1
        WHERE e1.occurred_at BETWEEN :startDate AND :endDate
          AND e1.event_type = :canceledEventType
          AND EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.event_type = :uninstalledEventType
                AND e2.occurred_at BETWEEN :startDate AND :endDate
          )
          AND NOT EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.event_type = :deactivatedEventType
                AND e2.occurred_at <= :endDate
          )
        `,
      {
        replacements: {
          startDate: startDate,
          endDate: endDate,
          canceledEventType: EventType.SUBSCRIPTION_CHARGE_CANCELED,
          uninstalledEventType: EventType.RELATIONSHIP_UNINSTALLED,
          deactivatedEventType: EventType.RELATIONSHIP_DEACTIVATED,
        },
        type: QueryTypes.SELECT,
      }
    );

    return result?.count || 0;
  };

  /**
   * Stores Closed from free plan
   * @param {string | undefined} startDate
   * @param {string | undefined} endDate
   * @returns {Promise<number>} count
   */
  countClosedFromFreeStores = async (startDate = undefined, endDate = undefined) => {
    const [result] = await sequelize.query(
      `
        SELECT COUNT(DISTINCT e1.shop_domain) AS count
        FROM shopify_app_events e1
        WHERE e1.occurred_at BETWEEN :startDate AND :endDate
          AND e1.event_type = :deactivatedEventType
          AND NOT EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.event_type = :activatedEventType
                AND e2.occurred_at <= :endDate
          )
        `,
      {
        replacements: {
          startDate: startDate,
          endDate: endDate,
          deactivatedEventType: EventType.RELATIONSHIP_DEACTIVATED,
          activatedEventType: EventType.SUBSCRIPTION_CHARGE_ACTIVATED,
        },
        type: QueryTypes.SELECT,
      }
    );

    return result?.count || 0;
  };

  /**
   * Stores Closed from PRO plan
   * @param {string | undefined} startDate
   * @param {string | undefined} endDate
   * @returns {Promise<number>} count
   */
  countClosedFromProStores = async (startDate = undefined, endDate = undefined) => {
    const [result] = await sequelize.query(
      `
        SELECT COUNT(DISTINCT e1.shop_domain) AS count
        FROM shopify_app_events e1
        WHERE e1.occurred_at BETWEEN :startDate AND :endDate
          AND e1.event_type = :deactivatedEventType
          AND EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.event_type = :activatedEventType
                AND e2.occurred_at <= :endDate
          )
        `,
      {
        replacements: {
          startDate: startDate,
          endDate: endDate,
          deactivatedEventType: EventType.RELATIONSHIP_DEACTIVATED,
          activatedEventType: EventType.SUBSCRIPTION_CHARGE_ACTIVATED,
        },
        type: QueryTypes.SELECT,
      }
    );

    return result?.count || 0;
  };

  /**
   * Store Reopened: installed & store closed previously, store re-opened
   * @param {string | undefined} startDate
   * @param {string | undefined} endDate
   * @returns {Promise<number>} count
   */
  countReOpenedFreeStores = async (startDate = undefined, endDate = undefined) => {
    const [result] = await sequelize.query(
      `
        SELECT COUNT(DISTINCT e1.shop_domain) AS count
        FROM shopify_app_events e1
        WHERE e1.occurred_at BETWEEN :startDate AND :endDate
          AND e1.event_type = :reactivatedEventType
          AND EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.event_type = :deactivatedEventType
                AND e2.occurred_at <= :startDate
          )
          AND NOT EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.event_type = :unfrozenEventType
                AND e2.occurred_at BETWEEN :startDate AND :endDate
          )
        `,
      {
        replacements: {
          startDate: startDate,
          endDate: endDate,
          reactivatedEventType: EventType.RELATIONSHIP_REACTIVATED,
          deactivatedEventType: EventType.RELATIONSHIP_DEACTIVATED,
          unfrozenEventType: EventType.SUBSCRIPTION_CHARGE_UNFROZEN,
        },
        type: QueryTypes.SELECT,
      }
    );

    return result?.count || 0;
  };

  /**
   * Store Reopened Pro: installed, subscribed & store closed previously, store re-opened
   * @param {string | undefined} startDate
   * @param {string | undefined} endDate
   * @returns {Promise<number>} count
   */
  countReOpenedProStores = async (startDate = undefined, endDate = undefined) => {
    const [result] = await sequelize.query(
      `
        SELECT COUNT(DISTINCT e1.shop_domain) AS count
        FROM shopify_app_events e1
        WHERE e1.occurred_at BETWEEN :startDate AND :endDate
          AND e1.event_type = :reactivatedEventType
          AND EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.event_type = :deactivatedEventType
                AND e2.occurred_at <= :startDate
          )
          AND EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.event_type = :unfrozenEventType
                AND e2.occurred_at BETWEEN :startDate AND :endDate
          )
        `,
      {
        replacements: {
          startDate: startDate,
          endDate: endDate,
          reactivatedEventType: EventType.RELATIONSHIP_REACTIVATED,
          deactivatedEventType: EventType.RELATIONSHIP_DEACTIVATED,
          unfrozenEventType: EventType.SUBSCRIPTION_CHARGE_UNFROZEN,
        },
        type: QueryTypes.SELECT,
      }
    );

    return result?.count || 0;
  };

  /**
   * Yearly active stores
   * @param {string | undefined} endDate
   * @returns {Promise<number>} count
   */
  countYearlyActiveStores = async (endDate = undefined) => {
    const [result] = await sequelize.query(
      `
        SELECT COUNT(DISTINCT e1.shop_domain) AS count
        FROM shopify_app_events e1
        LEFT JOIN shopify_transactions t1 ON e1.shop_domain = t1.shop_domain
        WHERE e1.event_type = :activatedEventType
          AND e1.occurred_at <= :endDate
          AND NOT EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.occurred_at <= :endDate
                AND (e2.event_type = :uninstalledEventType OR e2.event_type = :deactivatedEventType)
          )
          AND NOT EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND t1.charge_id = e2.charge_id
                AND e2.occurred_at <= :endDate
                AND (e2.event_type = :canceledEventType OR e2.event_type = :frozenEventType)
          )
          AND t1.billing_interval = :yearlyBillingInterval
        `,
      {
        replacements: {
          endDate: endDate,
          activatedEventType: EventType.SUBSCRIPTION_CHARGE_ACTIVATED,
          uninstalledEventType: EventType.RELATIONSHIP_UNINSTALLED,
          deactivatedEventType: EventType.RELATIONSHIP_DEACTIVATED,
          canceledEventType: EventType.SUBSCRIPTION_CHARGE_CANCELED,
          frozenEventType: EventType.SUBSCRIPTION_CHARGE_FROZEN,
          yearlyBillingInterval: appSubscriptionInterval.ANNUAL,
        },
        type: QueryTypes.SELECT,
      }
    );

    return result?.count || 0;
  };

  /**
   * Count visionary active stores
   * @param {string | undefined} endDate
   * @returns {Promise<number>} count
   */
  countVisionaryActiveStores = async (endDate = undefined) => {
    const [result] = await sequelize.query(
      `
        SELECT COUNT(DISTINCT e1.shop_domain) AS count
        FROM shopify_app_events e1
        WHERE e1.event_type = :activatedEventType
          AND e1.occurred_at <= :endDate
          AND e1.is_test != true
          AND NOT EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.occurred_at <= :endDate
                AND (
                  e2.event_type = :uninstalledEventType
                  OR e2.event_type = :deactivatedEventType
                  OR e2.event_type = :canceledEventType
                  OR e2.event_type = :frozenEventType
                )
          )
        `,
      {
        replacements: {
          endDate: endDate,
          activatedEventType: EventType.USAGE_CHARGE_APPLIED,
          uninstalledEventType: EventType.RELATIONSHIP_UNINSTALLED,
          deactivatedEventType: EventType.RELATIONSHIP_DEACTIVATED,
          canceledEventType: EventType.SUBSCRIPTION_CHARGE_CANCELED,
          frozenEventType: EventType.SUBSCRIPTION_CHARGE_FROZEN,
        },
        type: QueryTypes.SELECT,
      }
    );

    return result?.count || 0;
  };

  /**
   * Count monthly one time paid active stores
   * @param {string | undefined} endDate
   * @returns {Promise<number>} count
   */
  countMonthlyOnetimePaidActiveStores = async (endDate = undefined) => {
    const [result] = await sequelize.query(
      `
        SELECT COUNT(*) AS count
        FROM (
            SELECT t1.shop_domain
            FROM shopify_app_events e1
            JOIN shopify_transactions t1 ON e1.shop_domain = t1.shop_domain
            WHERE e1.event_type = :activatedEventType
              AND e1.is_test != true
              AND t1.billing_interval = :monthlyBillingInterval
              AND t1.transaction_created_at <= :endDate
              AND NOT EXISTS (
                  SELECT 1
                  FROM shopify_app_events e2
                  WHERE t1.shop_domain = e2.shop_domain
                    AND e2.occurred_at <= :endDate
                    AND (e2.event_type = :uninstalledEventType OR e2.event_type = :deactivatedEventType)
              )
              AND NOT EXISTS (
                  SELECT 1
                  FROM shopify_app_events e2
                  WHERE t1.shop_domain = e2.shop_domain
                    AND t1.charge_id = e2.charge_id
                    AND e2.occurred_at <= :endDate
                    AND e2.event_type = :frozenEventType
              )
            GROUP BY t1.shop_domain
            HAVING COUNT(*) = 1
        ) AS subquery
      `,
      {
        replacements: {
          endDate: endDate,
          activatedEventType: EventType.SUBSCRIPTION_CHARGE_ACTIVATED,
          monthlyBillingInterval: appSubscriptionInterval.EVERY_30_DAYS,
          uninstalledEventType: EventType.RELATIONSHIP_UNINSTALLED,
          deactivatedEventType: EventType.RELATIONSHIP_DEACTIVATED,
          frozenEventType: EventType.SUBSCRIPTION_CHARGE_FROZEN,
        },
        type: QueryTypes.SELECT,
      }
    );

    return result?.count || 0;
  };

  /**
   * Count monthly multiple time paid active stores
   * @param {string | undefined} endDate
   * @returns {Promise<number>} count
   */
  countMonthlyMultiplePaidActiveStores = async (endDate = undefined) => {
    const [result] = await sequelize.query(
      `SELECT COUNT(*) AS count
        FROM (
            SELECT t1.shop_domain
            FROM shopify_app_events e1
            JOIN shopify_transactions t1 ON e1.shop_domain = t1.shop_domain
            WHERE e1.event_type = :activatedEventType
              AND e1.is_test != true
              AND t1.billing_interval = :monthlyBillingInterval
              AND t1.transaction_created_at <= :endDate
              AND NOT EXISTS (
                  SELECT 1
                  FROM shopify_app_events e2
                  WHERE t1.shop_domain = e2.shop_domain
                    AND e2.occurred_at <= :endDate
                    AND (e2.event_type = :uninstalledEventType OR e2.event_type = :deactivatedEventType)
              )
              AND NOT EXISTS (
                  SELECT 1
                  FROM shopify_app_events e2
                  WHERE t1.shop_domain = e2.shop_domain
                    AND t1.charge_id = e2.charge_id
                    AND e2.occurred_at <= :endDate
                    AND e2.event_type = :frozenEventType
              )
            GROUP BY t1.shop_domain
            HAVING COUNT(t1.shop_domain) > 1
        ) AS subquery`,
      {
        replacements: {
          endDate: endDate,
          activatedEventType: EventType.SUBSCRIPTION_CHARGE_ACTIVATED,
          monthlyBillingInterval: appSubscriptionInterval.EVERY_30_DAYS,
          uninstalledEventType: EventType.RELATIONSHIP_UNINSTALLED,
          deactivatedEventType: EventType.RELATIONSHIP_DEACTIVATED,
          frozenEventType: EventType.SUBSCRIPTION_CHARGE_FROZEN,
        },
        type: QueryTypes.SELECT,
      }
    );

    return result?.count || 0;
  };

  /**
   * Count monthly not paid active stores
   * @param {string | undefined} endDate
   * @returns {Promise<number>} count
   */
  countMonthlyNotPaidActiveStores = async (endDate = undefined) => {
    const [result] = await sequelize.query(
      `
        SELECT COUNT(*) AS count
        FROM shopify_app_events e1
        WHERE e1.event_type = :activatedEventType
          AND e1.is_test != true
          AND e1.occurred_at <= :endDate
          AND NOT EXISTS (
              SELECT 1
              FROM shopify_app_events e2
              WHERE e1.shop_domain = e2.shop_domain
                AND e2.occurred_at <= :endDate
                AND (
                    e2.event_type = :canceledEventType
                    OR e2.event_type = :uninstalledEventType
                    OR e2.event_type = :deactivatedEventType
                    OR e2.event_type = :declinedEventType
                )
          )
          AND NOT EXISTS (
              SELECT 1
              FROM shopify_transactions t
              WHERE t.transaction_created_at <= :endDate
                AND t.shop_domain = e1.shop_domain
          )
        `,
      {
        replacements: {
          endDate: endDate,
          activatedEventType: EventType.SUBSCRIPTION_CHARGE_ACTIVATED,
          canceledEventType: EventType.SUBSCRIPTION_CHARGE_CANCELED,
          uninstalledEventType: EventType.RELATIONSHIP_UNINSTALLED,
          deactivatedEventType: EventType.RELATIONSHIP_DEACTIVATED,
          declinedEventType: EventType.SUBSCRIPTION_CHARGE_DECLINED,
        },
        type: QueryTypes.SELECT,
      }
    );

    return result?.count || 0;
  };

  /**
   * Onetime charges count
   * @param {string | undefined} startDate
   * @param {string | undefined} endDate
   * @returns {Promise<Array>} data
   */
  countOnetimeCharges = async (startDate, endDate) => {
    const query = `
        WITH title_table AS (
            SELECT charge_name, charge_amount
            FROM shopify_app_events
            WHERE event_type = 'ONE_TIME_CHARGE_ACTIVATED'
            GROUP BY charge_name, charge_amount
        )
        SELECT
            tt.charge_name as title,
            tt.charge_amount as key,
            COUNT(CASE WHEN sae.charge_name = tt.charge_name THEN sae.id END) AS count
        FROM title_table tt
        CROSS JOIN shopify_app_events sae
        WHERE sae.event_type = 'ONE_TIME_CHARGE_ACTIVATED'
            AND sae.is_test is FALSE
            AND sae.occurred_at BETWEEN :startDate AND :endDate
        GROUP BY tt.charge_name, tt.charge_amount
        ORDER BY tt.charge_amount::FLOAT`;

    const replacements = { startDate: startDate, endDate: endDate };

    const result = await sequelize.query(query, {
      replacements: replacements,
      type: QueryTypes.SELECT,
    });
    return result;
  };

  /**
   *
   * @param {{date: Date, type: string, stats: object}} param0
   * @returns
   */
  createOrUpdateReportData = async ({ date, type, stats }) => {
    /**
     * @type {import('sequelize').Model}
     */
    const [ar] = await AdminReport.upsert({
      date,
      type,
      stats,
    });

    return ar.toJSON();
  };

  /**
   * Prepares the report and comparison dates
   * @param {string | undefined} startDate
   * @param {string | undefined} endDate
   * @returns {{startDate:string,endDate:string,compareStartDate:string,compareEndDate:string,interval:number,formattedDate:string}} dates
   */
  prepareAdminReportDates = (startDate = undefined, endDate = undefined) => {
    const startDay = moment(startDate).subtract(1, "day").startOf("D");
    const endDay = moment(endDate || startDate)
      .subtract(1, "day")
      .endOf("D");

    const interval = Math.ceil(endDay.diff(startDay, "days", true));

    const compareStartDate = startDay.clone().subtract(interval, "days");
    const compareEndDate = endDay.clone().subtract(interval, "days");

    return {
      startDate: startDay.format(this.format),
      endDate: endDay.format(this.format),
      compareStartDate: compareStartDate.format(this.format),
      compareEndDate: compareEndDate.format(this.format),
      interval,
      formattedDate: this.#formattedDate(startDay, endDay, interval),
    };
  };

  /**
   *
   * @param {string | undefined} startDate
   * @param {string | undefined} endDate
   */
  getDailyStatusReportData = async (type = AdminReportTypes.DAILY_STATUS_REPORT, startDate = "", endDate = "") => {
    const reports = await AdminReport.findAll({
      where: {
        type,
        date: { [Op.between]: [startDate, endDate] },
      },
    });
    return sumStatsWithExclusions(reports.map((r) => r.toJSON()));
  };
}

module.exports = new AdminReportService();
