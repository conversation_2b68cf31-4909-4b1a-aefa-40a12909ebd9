const { Op, QueryTypes, where } = require("sequelize");
const { AddonUsage, sequelize } = require("../../sequelize");
const SubscriptionAddonService = require("./SubscriptionAddonService");
const { without, add, includes } = require("lodash");
const cache = require("../cache");
const subscriptionAddonGroup = require("storeseo-enums/subscriptionAddonGroup");
const subscriptionAddonInterval = require("storeseo-enums/subscriptionAddonInterval");
const SubscriptionAddonType = require("storeseo-enums/subscriptionAddonType");

class AddonUsageService {
  /**
   *
   * @param {{shopId: number, shopDomain: string purchaseDate: Date, addons: import("../sequelize/models/subscriptionaddon").SubscriptionAddon[]}} param
   *
   * @returns {Promise<import("../sequelize/models/addonusage").AddonUsage[]>}
   */
  updateRecurringUsagePermissions = async ({ shopId, shopDomain, purchaseDate, addons = [] }) => {
    // console.info("addons =", addons);
    if (addons.length === 0) return null;

    await this.#decativateRecurringAddonsThatAreNotPurchased(shopId, addons);

    const records = [];
    for (let addon of addons) {
      const currentlyActiveRecord = await this.getActiveRecord(shopId, addon.group, addon.type);
      const newRecord = await this.registerUsageRecord({
        shopId,
        purchaseDate,
        lastResetDate: purchaseDate,
        addon,
        activeRecord: currentlyActiveRecord,
      });
      records.push(newRecord);

      await this.updateUsageLimitInCache(shopDomain, shopId, newRecord.addon_group);

      if (currentlyActiveRecord) await this.deactivateRecordById(currentlyActiveRecord?.id);
    }

    return records;
  };

  /**
   *
   * @param {{shopId: number, shopDomain: string purchaseDate: Date, addons: import("../sequelize/models/subscriptionaddon").SubscriptionAddon[]}} param
   *
   * @returns {Promise<import("../sequelize/models/addonusage").AddonUsage[]>}
   */
  updateCreditUsagePermissions = async ({ shopId, shopDomain, purchaseDate, addons }) => {
    const records = [];
    for (let addon of addons) {
      const currentlyActiveRecord = await this.getActiveRecord(shopId, addon.group, addon.type);
      const newRecord = await this.registerUsageRecord({
        shopId,
        purchaseDate,
        lastResetDate: purchaseDate,
        addon,
        activeRecord: currentlyActiveRecord,
      });
      records.push(newRecord);

      if (currentlyActiveRecord) await this.deactivateRecordById(currentlyActiveRecord?.id);

      await this.updateUsageLimitInCache(shopDomain, shopId, newRecord.addon_group);
    }

    return records;
  };

  /**
   *
   * @param {number} shopId
   * @param {import("../sequelize/models/subscriptionaddon").SubscriptionAddon[]} addonsPurchased
   */
  #decativateRecurringAddonsThatAreNotPurchased = async (shopId, addonsPurchased) => {
    /** @type {import("../sequelize/models/subscriptionaddon").SubscriptionAddon[]} */
    const allRecurringAddons = await SubscriptionAddonService.getRecurringAddons();

    const allRecurringAddonIds = allRecurringAddons?.map((a) => a.id);
    const purchasedAddonIds = addonsPurchased?.map((a) => a.id) || [];
    const excludedAddonIds = without(allRecurringAddonIds, ...purchasedAddonIds);

    // console.log("allRecurringAddonIds", allRecurringAddonIds);
    // console.log("purchasedAddonIds", purchasedAddonIds);
    // console.log("excludedAddonIds", excludedAddonIds);

    await AddonUsage.update(
      { is_active: false },
      {
        where: {
          shop_id: shopId,
          addon_id: {
            [Op.in]: excludedAddonIds,
          },
        },
      }
    );
  };

  /**
   *
   * @param {string} shopDomain
   * @param {number} shopId
   * @param {keyof import("storeseo-enums/subscriptionAddonGroup")} addonGroup
   * @param {number} currentLimit
   */
  updateUsageLimitInCache = async (shopDomain, shopId, addonGroup) => {
    const activeRecords = await this.getActiveRecordsByAddonGroup(shopId, addonGroup);
    const currentLimit = activeRecords.reduce((limit, record) => limit + record.current_limit, 0);

    await cache.addons.usageLimit(shopDomain, {
      limit: currentLimit,
      addon: cache.keys[addonGroup],
    });

    return true;
  };

  /**
   *
   * @param {{shopId: number, purchaseDate: Date, lastResetDate: Date, addon: import("../../sequelize/models/subscriptionaddon").SubscriptionAddon, activeRecord: import("../../sequelize/models/addonusage").AddonUsage}} params
   *
   * @returns {Promise<import("../../sequelize/models/addonusage").AddonUsage>}
   */
  registerUsageRecord = async ({ shopId, purchaseDate, lastResetDate, addon, activeRecord = undefined }) => {
    /** @type {import("../sequelize/models/addonusage").AddonUsage} */
    const payload = {
      shop_id: shopId,
      addon_id: addon.id,

      addon_group: addon.group,
      addon_type: addon.type,
      is_active: true,

      purchase_date: purchaseDate,
      last_reset_date: lastResetDate,

      limit: addon.limit,
      current_limit:
        addon.limit +
        ((activeRecord && addon.interval === subscriptionAddonInterval.CREDIT && Number(activeRecord?.current_limit)) ||
          0),
      current_usage: (addon.interval === subscriptionAddonInterval.CREDIT && Number(activeRecord?.current_usage)) || 0,
    };

    return await this.#addNewRecord(payload);
  };

  /**
   *
   * @param {number} shopId
   * @param {keyof import("storeseo-enums/subscriptionAddonGroup")} addonGroup
   *
   * @returns {Promise<import("../sequelize/models/addonusage").AddonUsage | undefined>}
   */
  getActiveRecord = async (shopId, addonGroup, addonType) => {
    if (!shopId || !addonGroup) return null;

    const whereClause = {
      shop_id: shopId,
      addon_group: addonGroup,
      is_active: true,
    };

    if (addonType) {
      whereClause.addon_type = addonType;
    }

    const record = await AddonUsage.findOne({
      where: whereClause,
    });

    return record?.toJSON();
  };

  /**
   *
   * @param {number} shopId
   * @param {keyof import("storeseo-enums/subscriptionAddonGroup")} addonGroup
   *
   * @returns {Promise<import("../../sequelize/models/addonusage").AddonUsage[]>}
   */
  getActiveRecordsByAddonGroup = async (shopId, addonGroup) => {
    const records = await AddonUsage.findAll({
      where: { shop_id: shopId, addon_group: addonGroup, is_active: true },
    });

    return records?.map((r) => r.toJSON());
  };

  /**
   *
   * @param {number} shopId
   *
   * @returns {Promise<import("../../sequelize/models/addonusage").AddonUsage[]>}
   */
  getActiveRecords = async (shopId) => {
    const records = await AddonUsage.findAll({
      where: { shop_id: shopId, is_active: true },
      include: ["addon"],
    });

    return records?.map((r) => r.toJSON());
  };

  /**
   *
   * @param {number} id
   * @returns {Promise<true>}
   */
  deactivateRecordById = async (id) => {
    await AddonUsage.update({ is_active: false }, { where: { id } });
    return true;
  };

  /**
   *
   * @param {import("../sequelize/models/addonusage").AddonUsage} payload
   * @returns {Promise<import("../sequelize/models/addonusage").AddonUsage>}
   */
  #addNewRecord = async (payload) => {
    const record = await AddonUsage.create(payload);
    return record.toJSON();
  };

  /**
   *
   * @param {number} id
   * @param {import("../sequelize/models/addonusage").AddonUsage} data
   *
   * @returns {Promise<import("../sequelize/models/addonusage").AddonUsage | undefined>}
   */
  updateRecordById = async (id, data) => {
    const [affectedRows, records] = await AddonUsage.update(data, {
      where: { id },
    });

    return records?.[0]?.toJSON();
  };

  /**
   *
   * @param {number} id
   * @param {keyof import("storeseo-enums/subscriptionAddonType")} addonType
   * @param {import("../sequelize/models/addonusage").AddonUsage} data
   *
   * @returns {Promise<import("../sequelize/models/addonusage").AddonUsage | undefined>}
   */
  updateRecordByIdAndAddonType = async (id, addonType, data) => {
    const [affectedRows, records] = await AddonUsage.update(data, {
      where: { id, addon_type: addonType },
    });

    return records?.[0]?.toJSON();
  };
}

module.exports = new AddonUsageService();
