const path = require("path");
const ShopService = require("./ShopService");
const fsPromise = require("fs/promises");
const fs = require("fs");
const readline = require("readline");
const ProductService = require("./ProductService");
const PageService = require("./PageService");
const ArticleService = require("./ArticleService");
const FileService = require("./FileService");
const logger = require("storeseo-logger");
const GoogleBucketService = require("./GoogleBucketService");
const { Backup } = require("../../sequelize");
const backupStatus = require("storeseo-enums/backupStatus");
const moment = require("moment");
const { dispatchQueue } = require("../queue/queueDispatcher");
const { QUEUE_NAMES } = require("../queue/config");
const { backupAndRestoreSchema, backupAndRestoreDatabaseSchema } = require("storeseo-schema/settings/backupAndRestore");
const CollectionService = require("./collections/CollectionService");
const pageType = require("storeseo-enums/pageType");
const resourceType = require("storeseo-enums/resourceType");
const ShopifyService = require("./ShopifyService");
const bulkOperationTypes = require("storeseo-enums/bulkOperationTypes");
const BulkOperationService = require("./BulkOperationService");
const updateArticleMutation = require("../queries/bulk-operation/mutation.article-update.gql");
const updatePageMutation = require("../queries/bulk-operation/mutation.page-update.gql");
const updateCollectionMutation = require("../queries/bulk-operation/mutation.collection-update.gql");
const { sleep, getMetaTitleAndDescription } = require("../utils/helper");
const { NAMESPACE, METAFIELD_KEYS, METAFIELD_TYPES } = require("storeseo-enums/metafields");
const productUpdateMutation = require("../queries/bulk-operation/mutation.product-update.gql");
const metaFragment = require("../queries/fragment.metafield.gql");
const metafieldsSetMutation = require("../queries/mutations/mutation.set-metafields.gql");
const { prepareQueryString } = require("../utils/shopify.clients");
const { Op } = require("sequelize");
const ProductImageService = require("./ProductImageService");
const AiOptimizationStatus = require("storeseo-enums/aiOptimization");

const metafieldsSet = prepareQueryString(metafieldsSetMutation, [metaFragment]);

class BackupService {
  #fileNames = {
    SHOP: "shop.json",
    SHOP_SETTINGS: "shop-settings.json",
    PRODUCTS: "products.jsonl",
    PAGES: "pages.jsonl",
    ARTICLES: "articles.jsonl",
    COLLECTIONS: "collections.jsonl",
  };
  #bulOpMutationMap = {
    [resourceType.PRODUCT]: productUpdateMutation,
    [resourceType.PRODUCT_META]: metafieldsSet,
    [resourceType.PAGE]: updatePageMutation,
    [resourceType.ARTICLE]: updateArticleMutation,
    [resourceType.COLLECTION]: updateCollectionMutation,
  };

  #zipFileName = "backup.zip";
  #backupDirectory = path.resolve(__dirname, `../../uploads`);

  /**
   *
   * @param {object} shop
   * @param {string} domain
   */
  #backupShopTableData = async (shop, domain) => {
    const {
      jsonld_enabled,
      jsonld_data,
      logo_path,
      industry_id,
      out_of_stock_redirect_enabled,
      out_of_stock_redirect_url,
    } = shop;

    const data = {
      domain,
      jsonld_enabled,
      jsonld_data,
      logo_path,
      industry_id,
      out_of_stock_redirect_enabled,
      out_of_stock_redirect_url,
    };

    const filePath = `${this.#backupDirectory}/${domain}/${this.#fileNames.SHOP}`;
    await fsPromise.writeFile(filePath, JSON.stringify(data));
  };

  /**
   *
   * @param {id} shopId
   * @param {string} domain
   */
  #backupShopSettingsTable = async (shopId, domain) => {
    const settings = await ShopService.getShopSettings(shopId);

    const filePath = `${this.#backupDirectory}/${domain}/${this.#fileNames.SHOP_SETTINGS}`;
    await fsPromise.writeFile(
      filePath,
      JSON.stringify(
        settings.map((s) => {
          delete s.id;
          delete s.shop_id;
          delete s.created_at;
          delete s.updated_at;

          return s;
        })
      )
    );
  };

  /**
   *
   * @param {number} shopId
   * @param {string} domain
   */
  #backupProductsTable = async (shopId, domain) => {
    const filePath = `${this.#backupDirectory}/${domain}/${this.#fileNames.PRODUCTS}`;
    const wsStream = fs.createWriteStream(filePath, { encoding: "utf-8" });

    for await (let products of ProductService.iterateOverProducts(shopId)) {
      for (let p of products) {
        wsStream.write(JSON.stringify(p) + "\n");
      }
    }
    wsStream.end();
  };

  /**
   *
   * @param {number} shopId
   * @param {string} domain
   */
  #backupPagesTable = async (shopId, domain) => {
    const filePath = `${this.#backupDirectory}/${domain}/${this.#fileNames.PAGES}`;
    const wsStream = fs.createWriteStream(filePath, { encoding: "utf-8" });

    for await (let pages of PageService.iterateOverPages(shopId)) {
      for (let p of pages) {
        wsStream.write(JSON.stringify(p) + "\n");
      }
    }
    wsStream.end();
  };

  /**
   *
   * @param {number} shopId
   * @param {string} domain
   */
  #backupArticlesTable = async (shopId, domain) => {
    const filePath = `${this.#backupDirectory}/${domain}/${this.#fileNames.ARTICLES}`;
    const wsStream = fs.createWriteStream(filePath, { encoding: "utf-8" });

    for await (let articles of ArticleService.iterateOverArticles(shopId)) {
      for (let a of articles) {
        wsStream.write(JSON.stringify(a) + "\n");
      }
    }
    wsStream.end();
  };

  /**
   *
   * @param {number} shopId
   * @param {string} domain
   */
  #backupCollectionsTable = async (shopId, domain) => {
    const filePath = `${this.#backupDirectory}/${domain}/${this.#fileNames.COLLECTIONS}`;
    const wsStream = fs.createWriteStream(filePath, { encoding: "utf-8" });

    for await (let collections of CollectionService.iterateOverCollections(shopId)) {
      for (let c of collections) {
        wsStream.write(JSON.stringify(c) + "\n");
      }
    }
    wsStream.end();
  };

  #cleanup = async (domain, zipFileName = this.#zipFileName) => {
    console.info(`Backup service, ${domain}, Cleaning up local disk...`);

    try {
      const basePath = `${this.#backupDirectory}/${domain}`;
      for (let fileName of Object.values(this.#fileNames)) {
        if (fs.existsSync(`${basePath}/${fileName}`)) {
          await FileService.deleteFile(`${basePath}/${fileName}`);
        }
      }

      await FileService.deleteFile(`${basePath}/${zipFileName}`);
    } catch (error) {
      console.error(error);
    }
  };

  /**
   *
   * @param {string} domain
   * @returns {Promise<number>} shop id (database)
   */
  #restoreShopData = async (domain) => {
    console.info(`Restoring shop data from backup...`);

    const content = await fsPromise.readFile(`${this.#backupDirectory}/${domain}/${this.#fileNames.SHOP}`);
    const { jsonld_data, ...data } = JSON.parse(content);
    const updatedShop = await ShopService.updateShopByCondition({ domain }, data);
    await ShopService.upsertJsonldSetting(updatedShop.id, jsonld_data);
    return updatedShop.id;
  };

  /**
   *
   * @param {string} domain myshopify domain
   * @param {number} shopId shop id (database)
   */
  #restoreShopSettings = async (domain, shopId) => {
    console.info(`Restoring shop settings from backup...`);

    const content = await fsPromise.readFile(`${this.#backupDirectory}/${domain}/${this.#fileNames.SHOP_SETTINGS}`);

    for (let setting of JSON.parse(content)) {
      await ShopService.updateShopSetting(shopId, { ...setting, value: JSON.stringify(setting.value) });
    }
  };

  /**
   *
   * @param {string} domain myshopify domain
   * @param {number} shopId shop id (database)
   */
  #restoreProductsData = async (domain, shopId) => {
    console.info(`Restoring product data from backup...`);
    console.info(`----`);

    const rl = readline.createInterface({
      input: fs.createReadStream(`${this.#backupDirectory}/${domain}/${this.#fileNames.PRODUCTS}`),
      crlfDelay: Infinity,
    });

    for await (const line of rl) {
      const { product_id, focus_keyword } = JSON.parse(line);
      console.info(`Restoring product: ${product_id}...`);
      await ProductService.updateProductByCondition({ shop_id: shopId, product_id }, { focus_keyword });
    }

    console.info(`----`);
  };

  /**
   *
   * @param {string} domain myshopify domain
   * @param {number} shopId shop id (database)
   */
  #restorePagesData = async (domain, shopId) => {
    console.info(`Restoring page data from backup...`);
    console.info(`----`);

    const rl = readline.createInterface({
      input: fs.createReadStream(`${this.#backupDirectory}/${domain}/${this.#fileNames.PAGES}`),
      crlfDelay: Infinity,
    });

    for await (const line of rl) {
      const { page_id, focus_keyword } = JSON.parse(line);
      console.info(`Restoring page: ${page_id}...`);
      await PageService.updatePageByCondition({ shop_id: shopId, page_id }, { focus_keyword });
    }

    console.info(`----`);
  };

  /**
   *
   * @param {string} domain myshopify domain
   * @param {number} shopId shop id (database)
   */
  #restoreArticlesData = async (domain, shopId) => {
    console.info(`Restoring article data from backup...`);
    console.info(`----`);

    const rl = readline.createInterface({
      input: fs.createReadStream(`${this.#backupDirectory}/${domain}/${this.#fileNames.ARTICLES}`),
      crlfDelay: Infinity,
    });

    for await (const line of rl) {
      const { article_id, focus_keyword } = JSON.parse(line);
      console.info(`Restoring article: ${article_id}...`);
      await ArticleService.updateArticleByCondition(
        { shop_id: shopId, article_id, is_deleted: false },
        { focus_keyword }
      );
    }

    console.info(`----`);
  };

  /**
   *
   * @param {string} domain
   * @param {boolean} timestamp
   */
  storeBackup = async (domain, timestamp = false) => {
    try {
      const basePath = `${this.#backupDirectory}/${domain}`;
      await fsPromise.mkdir(basePath, { recursive: true });

      console.info("Creating backup files...");

      const shop = await ShopService.getShopIncludingAssociations({ where: { domain } });
      await this.#backupShopTableData(shop, domain);
      await this.#backupShopSettingsTable(shop.id, domain);
      await this.#backupProductsTable(shop.id, domain);
      await this.#backupCollectionsTable(shop.id, domain);
      await this.#backupPagesTable(shop.id, domain);
      await this.#backupArticlesTable(shop.id, domain);

      console.info("Compressing backup files into zip...");

      const fileName = this.zipFileName(timestamp);

      const filePaths = Object.values(this.#fileNames).map((n) => `${basePath}/${n}`);
      const zipFilePath = await FileService.createZip(filePaths, `${basePath}/${fileName}`);

      console.info("Uploading backup zip into Google bucket...");
      await GoogleBucketService.uploadMedia({
        folder: `shops/${domain}`,
        originalFilePath: zipFilePath,
      });

      await this.#cleanup(domain, fileName);
    } catch (err) {
      logger.error(err, { domain });
    }
  };

  zipFileName = (timestamp = false) => {
    if (timestamp) {
      let nameArr = this.#zipFileName.split(".");

      return nameArr[0] + "-" + +new Date() + "." + nameArr[1];
    }

    return this.#zipFileName;
  };

  /**
   *
   * @param {string} domain myshopify domain of the shop
   * @param {string} fileName?
   */
  restoreData = async (domain, fileName = this.#zipFileName) => {
    const backupFile = `shops/${domain}/${fileName}`;
    const downloadDir = `${this.#backupDirectory}/${domain}`;
    await fsPromise.mkdir(downloadDir, { recursive: true });
    const downloadPath = `${downloadDir}/${this.#zipFileName}`;

    console.info(`Backup service, ${domain}, Downloading bakcup file from Google bucket...`);
    await GoogleBucketService.downloadMedia(backupFile, downloadPath);

    console.info(`Backup service, ${domain}, Extracting downloaded backup zip file...`);
    await FileService.extractZip(downloadPath, downloadDir);

    const shopId = await this.#restoreShopData(domain);
    await this.#restoreShopSettings(domain, shopId);
    await this.#restoreProductsData(domain, shopId);
    await this.#restorePagesData(domain, shopId);
    await this.#restoreArticlesData(domain, shopId);

    await this.#cleanup(domain, fileName);
  };

  /**
   * @param {string} domain
   * @param {string} localFilePath
   */
  backupShopsInitialProducts = async (domain, localFilePath) => {
    const baseFilePath = localFilePath.split("/").slice(1).join("/");
    const filePath = `${this.#backupDirectory}/${baseFilePath}`;

    //check if the file exists in the filepath
    if (!fs.existsSync(filePath)) {
      throw new Error(`Bulk products file not found: ${filePath}`);
    }

    const backupFilename = "backup-products.jsonl";
    const backupFolderDir = `shops/${domain}/backups`;

    // Check if the backup file already exists in the Google bucket
    const isFileAlreadyExist = await GoogleBucketService.isFileExists(`${backupFolderDir}/${backupFilename}`);
    if (isFileAlreadyExist) {
      console.info(`Backup file already exists in the Google bucket: ${backupFolderDir}/${backupFilename}`);
      return;
    }
    // Upload the backup file to the Google bucket
    await GoogleBucketService.uploadMedia({
      folder: backupFolderDir,
      newFileName: backupFilename,
      originalFilePath: filePath,
    });
  };

  /**
   *
   * @param {number} shopId
   */
  manualBackupList = async (shopId, { sortBy = "created_at", sortOrder = "DESC" }) => {
    let order = [[sortBy, sortOrder]];

    const backups = await Backup.findAll({
      where: { shop_id: shopId },
      order,
      limit: 5,
    });

    return backups.map((item) => item.toJSON());
  };

  /**
   *
   * @param {number} shopId
   * @param {object} options
   * @returns {Promise<import("yup").InferType<backupAndRestoreDatabaseSchema> | null>}
   */
  getManualBackupByCondition = async (shopId, options) => {
    const backup = await Backup.findOne({ where: { shop_id: shopId, ...options } });

    if (!backup) {
      return null;
    }
    return backup?.toJSON();
  };

  /**
   *
   * @param {number} shopId
   * @returns {Promise<import("yup").InferType<backupAndRestoreDatabaseSchema>[]>}
   */
  getAllManualBackupByShop = async (shopId) => {
    const backups = await Backup.findAll({ where: { shop_id: shopId } });
    return backups.map((item) => item.toJSON());
  };

  deleteManualBackup = async (shopId, backupId) => {
    const backup = await Backup.findOne({ where: { id: backupId, shop_id: shopId } });
    if (!backup) {
      throw new Error("Backup not found");
    }

    const shop = await ShopService.getShopByCondition({ id: shopId });
    if (!shop) {
      throw new Error("Shop not found");
    }

    // Delete backup file from Google bucket
    const backupFileName = `shops/${shop.domain}/backups/${backup.title}.zip`;
    const deleted = await GoogleBucketService.removeMedia(backupFileName);
    if (!deleted) {
      console.log(`Failed to delete backup file from Google bucket: ${backupFileName}`);
    }

    // Delete backup record from database
    await backup.destroy();
  };

  /**
   *
   * @param {number} shopId
   * @param {import("yup").InferType<backupAndRestoreSchema>} data
   * @returns
   */
  createManualBackup = async (shopId, data) => {
    const EventService = require("./EventService");
    const shop = await ShopService.getShopByCondition({ id: shopId });

    if (!shop) {
      throw new Error("Shop not found for the given shop id");
    }

    const localTime = moment().tz(shop.ianaTimezone).format("ll LTS");
    const resources = Object.keys(data).filter((key) => data[key] === true); // e.g. ["PRODUCT, PAGE, ARTICLE"]

    const backupData = {
      shop_id: shopId,
      title: `Backup - ${localTime}`,
      resources,
      backup_status: backupStatus.PENDING,
    };

    /**
     * @type {import("yup").InferType<backupAndRestoreDatabaseSchema> | undefined}
     */
    const backup = await Backup.create(backupData);

    if (!backup) {
      throw new Error("Failed to create backup");
    }

    EventService.handleBackupInitialized({ shop: shop.domain });
    dispatchQueue({
      queueName: QUEUE_NAMES.BACKUP_QUEUE,
      message: {
        shopId,
        backupId: backup.id,
        resources: data,
      },
    });
    return backup;
  };

  /**
   * Create backup jsonl file for each resource type in our database data format
   * @param {import("yup").InferType<backupAndRestoreSchema>} resources
   * @param {number} shopId
   * @param {string} shopDomain
   */
  async createManualBackupJsonlFiles(resources, shopId, shopDomain) {
    const basePath = `${this.#backupDirectory}/${shopDomain}`;
    await fsPromise.mkdir(basePath, { recursive: true });

    if (resources.PRODUCT) {
      await this.#backupProductsTable(shopId, shopDomain);
    }
    // if (resources.PAGE) {
    //   await this.#backupPagesTable(shopId, shopDomain);
    // }
    // if (resources.ARTICLE) {
    //   await this.#backupArticlesTable(shopId, shopDomain);
    // }
    if (resources.COLLECTION) {
      await this.#backupCollectionsTable(shopId, shopDomain);
    }
  }

  /**
   * Upload the backup jsonl file to the google cloud storage
   * @param {object} shop
   * @param {number} backupId
   * @returns {Promise<Awaited<ReturnType<typeof GoogleBucketService.uploadMedia>>>}
   */
  async uploadToGoogleCloudStorage(shop, backupId) {
    const { domain: shopDomain, id: shopId } = shop;

    const backup = await this.getManualBackupByCondition(shopId, { id: backupId });
    if (!backup) {
      throw new Error("Backup not found");
    }

    const basePath = `${this.#backupDirectory}/${shopDomain}`;

    console.info("Compressing backup files into zip...");

    const fileName = `${backup.title}.zip`;

    const filePaths = [this.#fileNames.PRODUCTS, this.#fileNames.COLLECTIONS]
      .map((n) => `${basePath}/${n}`)
      .filter((p) => fs.existsSync(p));

    const zipFilePath = await FileService.createZip(filePaths, `${basePath}/${fileName}`);

    if (fs.existsSync(zipFilePath)) {
      const uploadResult = await GoogleBucketService.uploadMedia({
        folder: `shops/${shopDomain}/backups`,
        originalFilePath: zipFilePath,
      });

      await this.#cleanup(shopDomain, fileName);
      return uploadResult;
    }

    await this.#cleanup(shopDomain, fileName);

    throw new Error("Failed to create backup zip file");
  }

  /**
   * Update the backup status to completed
   * @param {number} backupId
   * @param {Partial<import("yup").InferType<backupAndRestoreDatabaseSchema>>} data
   */
  async updateManualBackup(backupId, data) {
    const result = await Backup.update(data, { where: { id: backupId } });
    if (!result) {
      throw new Error("Failed to update backup status");
    }

    return result;
  }

  /**
   *
   * @param {number} shopId
   * @returns {Promise<boolean>}
   */
  async hasAnyBackupInProgress(shopId) {
    const backups = await Backup.findAll({
      where: {
        shop_id: shopId,
        backup_status: {
          [Op.or]: [backupStatus.PENDING, backupStatus.PROCESSING],
        },
      },
    });

    return backups.length > 0;
  }

  /**
   *
   * @param {number} shopId
   * @returns {Promise<boolean>}
   */
  async hasAnyRestoreInProgress(shopId) {
    const backups = await Backup.findAll({
      where: {
        shop_id: shopId,
        restore_status: {
          [Op.or]: [backupStatus.PENDING, backupStatus.PROCESSING],
        },
      },
    });

    return backups.length > 0;
  }

  /**
   *
   * @param {number} shopId
   * @param {string} shop
   * @param {number} backupId
   */
  async restoreManualBackup(shopId, shop, backupId) {
    const EventService = require("./EventService");
    await this.updateManualBackup(backupId, { restore_status: backupStatus.PENDING });

    EventService.handleBackupRestoreInitialized({ shop });
    dispatchQueue({
      queueName: QUEUE_NAMES.RESTORE_QUEUE,
      message: {
        shopId,
        backupId,
      },
    });
  }

  /**
   * Download and extract backup from Google Cloud Storage
   * @param {string} domain
   * @param {string} downloadFileName
   */
  async downloadAndExtractManualBackupFile(domain, downloadFileName) {
    const fileName = downloadFileName.split("/").pop();

    const downloadDir = `${this.#backupDirectory}/${domain}/restore`;
    await fsPromise.mkdir(downloadDir, { recursive: true });

    await this.deleteAllFilesInDirectory(downloadDir);

    const downloadPath = `${downloadDir}/${fileName}`;

    await GoogleBucketService.downloadMedia(downloadFileName, downloadPath);
    await FileService.extractZip(downloadPath, downloadDir);
  }

  /**
   * create input files for restore bulk operation
   * @param {object} shop
   * @param {string} downloadFileName
   */
  async createInputFilesForRestoreBulkOperation(shop, downloadFileName) {
    const domain = shop.domain;
    const fileName = downloadFileName.split("/").pop();
    const basePath = `${this.#backupDirectory}/${domain}/restore`;

    const filePaths = Object.values(this.#fileNames)
      .map((fileName) => `${basePath}/${fileName}`)
      .filter((filePath) => fs.existsSync(filePath));

    for (let filePath of filePaths) {
      if (filePath.includes(this.#fileNames.PRODUCTS)) {
        await this.#generateManualProductInput(filePath, shop);
        await this.#generateManualProductImageInput(filePath, shop);
        await this.#generateManualProductMetafieldsInput(filePath, shop);
      }

      if (filePath.includes(this.#fileNames.COLLECTIONS)) {
        await this.#generateManualCollectionInput(filePath, shop);
      }

      if (filePath.includes(this.#fileNames.PAGES)) {
        await this.#generateManualPageInput(filePath, shop);
      }

      if (filePath.includes(this.#fileNames.ARTICLES)) {
        await this.#generateManualArticleInput(filePath, shop);
      }
    }

    await this.#cleanup(`${domain}/restore`, fileName);
  }
  /**
   * Restore backup data to database
   * @param {object} shop
   * @param {number} backupId
   */
  async restoreManualBackupData(shop, backupId) {
    const domain = shop.domain;
    const basePath = `${this.#backupDirectory}/${domain}/restore`;

    let restoreSteps = {
      bulk_mutations: [],
      manual_mutations: [],
    };
    const files = await fsPromise.readdir(basePath);
    for (const file of files) {
      if (file === ".DS_Store") continue; // Explicitly skip .DS_Store
      const filePath = path.join(basePath, file);
      const stat = await fsPromise.lstat(filePath);

      if (stat.isFile()) {
        if (file.includes(resourceType.PRODUCT_IMAGE)) {
          restoreSteps.manual_mutations.push({
            filePath,
            complete: false,
            resourceType: file.split("-").shift(),
          });
        } else {
          restoreSteps.bulk_mutations.push({
            filePath,
            complete: false,
            resourceType: file.split("-").shift(),
            bulk_op_gql: null,
          });
        }
      }
    }

    const session = { shop: domain, accessToken: shop.access_token };

    const initialStep = restoreSteps.bulk_mutations[0];
    const { status, stagedTarget } = await ShopifyService.uploadBulkOperationInputFileIntoShopify({
      shop: session.shop,
      fileName: initialStep.filePath.split("/").pop(),
      filePath: initialStep.filePath,
    });

    if (status === 201) {
      console.table(["file upload successful..."]);
      await sleep(3000);

      const bulkOpMutation = this.#bulOpMutationMap[initialStep.resourceType];
      const bulkOp = await BulkOperationService.processFileAndStartBulkMutation(session, {
        shopId: shop.id,
        stagedTarget,
        mutation: bulkOpMutation,
        opType: bulkOperationTypes.BULK_RESTORE,
      });

      let bulkSteps = restoreSteps.bulk_mutations.map((step, index) => {
        if (index === 0) {
          return {
            ...step,
            bulk_op_gql: bulkOp.id,
          };
        }
        return step;
      });

      await this.updateManualBackup(backupId, {
        restore_status: backupStatus.PROCESSING,
        restore_steps: {
          ...restoreSteps,
          bulk_mutations: bulkSteps,
        },
      });
    }
  }

  /**
   *
   * @param {string} filePath
   * @param {any} shop
   */
  async #generateManualProductInput(filePath, shop) {
    console.info(`Generating product input files for restore...`);
    console.info(`----`);

    const CHUNK_SIZE = 700;
    let productCount = 0;
    let fileCount = 1;
    let wsStream = null;

    const rl = readline.createInterface({
      input: fs.createReadStream(filePath),
      crlfDelay: Infinity,
    });

    for await (let line of rl) {
      if (productCount % CHUNK_SIZE === 0) {
        // Close previous stream if exists
        if (wsStream) {
          wsStream.end();
        }
        // Create new stream for next chunk
        const productInputFilePath = `${this.#backupDirectory}/${shop.domain}/restore/${resourceType.PRODUCT}-INPUT-${fileCount}.jsonl`;
        wsStream = fs.createWriteStream(productInputFilePath, { encoding: "utf-8" });
        fileCount++;
      }

      const product = JSON.parse(line);

      // Update product focus keyword and ai optimization status
      await ProductService.updateProductByCondition(
        {
          shop_id: product.shop_id,
          id: product.id,
        },
        {
          focus_keyword: product.focus_keyword,
          ai_optimization_status:
            AiOptimizationStatus.OPTIMIZED === product.ai_optimization_status
              ? AiOptimizationStatus.OPTIMIZED
              : AiOptimizationStatus.NOT_OPTIMIZED,
          ai_optimized_at: product.ai_optimized_at,
        }
      );

      const productUpdateInputData = {
        product: {
          id: product.product_id,
          handle: product.handle,
          redirectNewHandle: true,
          tags: product?.tags || [],
        },
      };

      const productInputData = JSON.stringify(productUpdateInputData);
      wsStream.write(productInputData + "\n");
      productCount++;
    }

    if (wsStream) {
      wsStream.end();
    }

    console.info(`Created ${fileCount - 1} input files with ${CHUNK_SIZE} products each`);
    console.info(`----`);
  }

  async #generateManualProductImageInput(filePath, shop) {
    console.info(`Generating product image input files for restore...`);
    console.info(`----`);

    const session = { shop: shop.domain, accessToken: shop.access_token };

    const CHUNK_SIZE = 5000;
    let imageCount = 0;
    let fileCount = 1;
    let wsStream = null;

    const rl = readline.createInterface({
      input: fs.createReadStream(filePath),
      crlfDelay: Infinity,
    });

    for await (let line of rl) {
      if (imageCount % CHUNK_SIZE === 0) {
        // Close previous stream if exists
        if (wsStream) {
          wsStream.end();
        }
        // Create new stream for next chunk
        const productInputFilePath = `${this.#backupDirectory}/${shop.domain}/restore/${resourceType.PRODUCT_IMAGE}-INPUT-${fileCount}.jsonl`;
        wsStream = fs.createWriteStream(productInputFilePath, { encoding: "utf-8" });
        fileCount++;
      }

      const product = JSON.parse(line);

      // Update product image optimization status
      for (let image of product.images) {
        const {
          id,
          alt_text_optimization_status,
          alt_text_optimized_at,
          optimization_status,
          optimization_meta,
          optimization_setting,
        } = image;
        const data = {
          alt_text_optimization_status,
          alt_text_optimized_at,
          optimization_status,
          optimization_meta,
          optimization_setting,
        };
        await ProductImageService.updateImage(id, data);
      }

      const images = await ProductService.updateProductMediaData(session, {
        productGqlId: product.product_id,
        images: product.images,
      });

      for (let image of images) {
        const imageUpdateInput = {
          owner: {
            id: product.id,
          },
          image: {
            id: image.media_id,
            alt: image.alt_text || "",
            originalSource: image.src,
          },
        };
        const imageInputData = JSON.stringify(imageUpdateInput);
        wsStream.write(imageInputData + "\n");

        imageCount++;
      }
    }

    if (wsStream) {
      wsStream.end();
    }

    console.info(`Created ${fileCount - 1} input files with ${CHUNK_SIZE} product images each`);
    console.info(`----`);
  }

  async #generateManualProductMetafieldsInput(filePath, shop) {
    console.info(`Generating product metafields input files for restore...`);
    console.info(`----`);

    const CHUNK_SIZE = 700;
    let metafieldsCount = 0;
    let fileCount = 1;
    let wsStream = null;

    const rl = readline.createInterface({
      input: fs.createReadStream(filePath),
      crlfDelay: Infinity,
    });

    for await (let line of rl) {
      if (metafieldsCount % CHUNK_SIZE === 0) {
        // Close previous stream if exists
        if (wsStream) {
          wsStream.end();
        }
        // Create new stream for next chunk
        const productInputFilePath = `${this.#backupDirectory}/${shop.domain}/restore/${resourceType.PRODUCT_META}-INPUT-${fileCount}.jsonl`;
        wsStream = fs.createWriteStream(productInputFilePath, { encoding: "utf-8" });
        fileCount++;
      }

      const product = JSON.parse(line);
      let metafields = [];

      const defaultMetafields = this.#createDefaultMetafields({ ...product, owner_id: product.product_id });
      const otherMetafields =
        product?.meta.filter((meta) => !defaultMetafields.some((defaultMeta) => defaultMeta.key === meta.key)) || [];
      const restMetafields = this.#mapMetaToMetafields(product.product_id, otherMetafields);

      metafields = [...defaultMetafields, ...restMetafields];

      const productUpdateInputData = {
        metafields,
      };
      const productInputData = JSON.stringify(productUpdateInputData);
      wsStream.write(productInputData + "\n");
      metafieldsCount++;
    }

    if (wsStream) {
      wsStream.end();
    }

    console.info(`Created ${fileCount - 1} input files with ${CHUNK_SIZE} product metafields each`);
    console.info(`----`);
  }

  /**
   *
   * @param {string} filePath
   * @param {any} shop
   */
  async #generateManualCollectionInput(filePath, shop) {
    console.info(`Generating collection input files for restore...`);
    console.info(`----`);

    const CHUNK_SIZE = 700;
    let collectionCount = 0;
    let fileCount = 1;
    let wsStream = null;

    const rl = readline.createInterface({
      input: fs.createReadStream(filePath),
      crlfDelay: Infinity,
    });

    for await (const line of rl) {
      if (collectionCount % CHUNK_SIZE === 0) {
        // Close previous stream if exists
        if (wsStream) {
          wsStream.end();
        }
        // Create new stream for next chunk
        const collectionInputFilePath = `${this.#backupDirectory}/${shop.domain}/restore/${resourceType.COLLECTION}-INPUT-${fileCount}.jsonl`;
        wsStream = fs.createWriteStream(collectionInputFilePath, { encoding: "utf-8" });
        fileCount++;
      }

      const collection = JSON.parse(line);
      let metafields = [];

      const defaultMetafields = this.#createDefaultMetafields({ ...collection, owner_id: undefined });
      const otherMetafields =
        collection?.meta.filter((meta) => !defaultMetafields.some((defaultMeta) => defaultMeta.key === meta.key)) || [];
      const restMetafields = this.#mapMetaToMetafields(undefined, otherMetafields);

      metafields = [...defaultMetafields, ...restMetafields];

      // Update collection focus keyword and ai optimization status
      await CollectionService.update(shop.id, collection.id, {
        focus_keyword: collection.focus_keyword,
        ai_optimization_status:
          AiOptimizationStatus.OPTIMIZED === collection.ai_optimization_status
            ? AiOptimizationStatus.OPTIMIZED
            : AiOptimizationStatus.NOT_OPTIMIZED,
        ai_optimized_at: collection.ai_optimized_at,
      });

      const collectionUpdateInputData = {
        input: {
          id: collection.collection_id,
          handle: collection.handle,
          redirectNewHandle: true,
          image: collection?.img?.src ? { src: collection?.img?.src } : null,
          metafields,
        },
      };

      const collectionInputData = JSON.stringify(collectionUpdateInputData);
      wsStream.write(collectionInputData + "\n");
      collectionCount++;
    }

    if (wsStream) {
      wsStream.end();
    }

    console.info(`Created ${fileCount - 1} input files with ${CHUNK_SIZE} collections each`);
    console.info(`----`);
  }

  /**
   *
   * @param {string} filePath
   * @param {any} shop
   * @param {any} session
   */
  async #generateManualPageInput(filePath, shop) {
    const productInputFilePath = `${this.#backupDirectory}/${shop.domain}/restore/${resourceType.PAGE}-INPUT.jsonl`;
    const wsStream = fs.createWriteStream(productInputFilePath, { encoding: "utf-8" });
    console.info(`Restoring page data from manual backup...`);
    console.info(`----`);

    const rl = readline.createInterface({
      input: fs.createReadStream(filePath),
      crlfDelay: Infinity,
    });

    for await (const line of rl) {
      const page = JSON.parse(line);
      if (page.page_type === pageType.HOMEPAGE) {
        continue;
      }
      console.info(`Restoring page: ${page.page_id}...`);
      const metafields = this.#mapMetaToMetafields(page.meta);
      const pageUpdateInputData = {
        id: `gid://shopify/Page/${page.page_id}`,
        page: {
          handle: page.handle,
          redirectNewHandle: true,
          tags: page?.tags || [],
          metafields,
        },
      };

      const pageInputData = JSON.stringify(pageUpdateInputData);
      wsStream.write(pageInputData + "\n");
    }
    wsStream.end();

    console.info(`----`);
  }

  /**
   *
   * @param {string} filePath
   * @param {any} shop
   */
  async #generateManualArticleInput(filePath, shop) {
    const productInputFilePath = `${this.#backupDirectory}/${shop.domain}/restore/${resourceType.ARTICLE}-INPUT.jsonl`;
    const wsStream = fs.createWriteStream(productInputFilePath, { encoding: "utf-8" });
    console.info(`Restoring article data from manual backup...`);
    console.info(`----`);

    const rl = readline.createInterface({
      input: fs.createReadStream(filePath),
      crlfDelay: Infinity,
    });

    for await (const line of rl) {
      const article = JSON.parse(line);
      console.info(`Restoring article: ${article.article_id}...`);
      const metafields = this.#mapMetaToMetafields(article.meta);
      const image = article.img ? { altText: article.img.alt_text, url: article.img.src } : null;

      const articleUpdateInputData = {
        id: `gid://shopify/Article/${article.article_id}`,
        article: {
          handle: article.handle,
          redirectNewHandle: true,
          tags: article?.tags || [],
          metafields,
          image,
        },
      };

      const articleInputData = JSON.stringify(articleUpdateInputData);
      wsStream.write(articleInputData + "\n");
    }
    wsStream.end();

    console.info(`----`);
  }

  /**
   * Find all file paths in the selected directory and delete them
   * @param {string} directoryPath
   */
  async deleteAllFilesInDirectory(directoryPath) {
    try {
      const files = await fsPromise.readdir(directoryPath);
      for (const file of files) {
        const filePath = path.join(directoryPath, file);
        const stat = await fsPromise.lstat(filePath);
        if (stat.isDirectory()) {
          await this.deleteAllFilesInDirectory(filePath);
        } else {
          await fsPromise.unlink(filePath);
        }
      }
      console.info(`All files in directory ${directoryPath} have been deleted.`);
    } catch (error) {
      // console.error(`Error deleting files in directory ${directoryPath}:`, error);
    }
  }

  async dispatchNextRestoreStep(shopId, backupId) {
    const shop = await ShopService.getShopByCondition({ id: shopId });
    const backup = await this.getManualBackupByCondition(shopId, { id: backupId });

    if (!backup) {
      throw new Error("Backup not found");
    }

    let restoreSteps = backup.restore_steps.bulk_mutations;
    const session = { shop: shop.domain, accessToken: shop.access_token };

    const nextStep = restoreSteps.find((step) => !step.complete);
    if (!nextStep) {
      const manual_steps = backup.restore_steps.manual_mutations;
      this.#dispatchNextManualRestoreSteps(shopId, backupId, manual_steps);
      return;
    }

    const { status, stagedTarget } = await ShopifyService.uploadBulkOperationInputFileIntoShopify({
      shop: session.shop,
      fileName: nextStep.filePath.split("/").pop(),
      filePath: nextStep.filePath,
    });

    if (status === 201) {
      console.table(["file upload successful..."]);
      await sleep(3000);

      const bulkOpMutation = this.#bulOpMutationMap[nextStep.resourceType];
      const bulkOp = await BulkOperationService.processFileAndStartBulkMutation(session, {
        shopId: shop.id,
        stagedTarget,
        mutation: bulkOpMutation,
        opType: bulkOperationTypes.BULK_RESTORE,
      });

      restoreSteps = restoreSteps.map((step) => {
        if (step.filePath === nextStep.filePath) {
          return {
            ...step,
            bulk_op_gql: bulkOp.id,
          };
        }
        return step;
      });

      await this.updateManualBackup(backupId, {
        restore_steps: {
          ...backup.restore_steps,
          bulk_mutations: restoreSteps,
        },
      });
    }
  }

  /**
   * @param {number} shopId
   * @param {number} backupId
   * @param {Array<{filePath: string, complete: boolean, resourceType: string, completed_at: string}>} steps
   */
  #dispatchNextManualRestoreSteps = async (shopId, backupId, steps) => {
    // Dispatch manual mutations
    for (const step of steps) {
      const { filePath, resourceType } = step;
      const rl = readline.createInterface({
        input: fs.createReadStream(filePath),
        crlfDelay: Infinity,
      });

      let data = [];
      for await (let line of rl) {
        data.push(JSON.parse(line));
        if (data.length === 5) {
          dispatchQueue({
            queueName: QUEUE_NAMES.RESTORE_MANUALLY_QUEUE,
            message: {
              shopId,
              backupId,
              data,
              filePath: filePath,
              resourceType: resourceType,
              isLastItem: false,
            },
          });
          data = [];
        }
      }

      // Handle remaining data
      dispatchQueue({
        queueName: QUEUE_NAMES.RESTORE_MANUALLY_QUEUE,
        message: {
          shopId,
          backupId,
          data,
          filePath: filePath,
          resourceType: resourceType,
          isLastItem: true,
        },
      });
    }
  };

  /**
   * Creates default metafields for title and description
   * @param {object} resourceItem
   * @returns {Array<Object>}
   */
  #createDefaultMetafields(resourceItem) {
    const { owner_id } = resourceItem;
    const { metaTitle, metaDescription } = getMetaTitleAndDescription(resourceItem);

    return [
      {
        ownerId: owner_id,
        key: METAFIELD_KEYS.TITLE_TAG,
        type: METAFIELD_TYPES.STRING,
        namespace: NAMESPACE.GLOBAL,
        value: metaTitle,
      },
      {
        ownerId: owner_id,
        key: METAFIELD_KEYS.DESCRIPTION_TAG,
        type: METAFIELD_TYPES.STRING,
        namespace: NAMESPACE.GLOBAL,
        value: metaDescription,
      },
    ];
  }

  /**
   * Maps meta items to metafields format
   * @param {number} owner_id
   * @param {Array<object>} meta
   * @returns {Array<Object>}
   */
  #mapMetaToMetafields(owner_id, meta) {
    return meta.map((item) => ({
      ownerId: owner_id,
      key: item.key,
      type: item.type,
      namespace: item.namespace,
      value: item.value,
    }));
  }

  /**
   *
   * @param {number} shopId
   * @param {string} shop
   * @param {number} backupId
   * @returns {Promise<boolean>}
   */
  async markedManualRestoreAsComplete(shopId, shop, backupId) {
    const EventService = require("./EventService");
    const backup = await this.getManualBackupByCondition(shopId, { id: backupId });
    if (!backup) {
      throw new Error("Backup not found");
    }
    const bulk_steps = backup.restore_steps.bulk_mutations;
    const manual_steps = backup.restore_steps.manual_mutations;
    const restore_steps = [...bulk_steps, ...manual_steps];
    const isAllRestoreStepsComplete = restore_steps.every((step) => step.complete);
    if (isAllRestoreStepsComplete) {
      await this.updateManualBackup(backupId, { restore_status: backupStatus.COMPLETE });
      EventService.handleBackupRestoreComplete({ shopId, shop, backupId });
    }
    return isAllRestoreStepsComplete;
  }

  async downloadBackupFile(domain, fileName) {
    const backupFile = `shops/${domain}/backups/${fileName}`;
    const downloadDir = `${this.#backupDirectory}/${domain}`;
    await fsPromise.mkdir(downloadDir, { recursive: true });
    const downloadPath = `${downloadDir}/${fileName}`;

    console.info(`Backup service, ${domain}, Downloading bakcup file from Google bucket...`);
    await GoogleBucketService.downloadMedia(backupFile, downloadPath);

    return downloadPath;
  }
}

module.exports = new BackupService();
