const elasticMailClient = require("../config/driver/elasticEmail");
const mailGunClient = require("../config/driver/mailgun");

let instance;
const provider = process.env.MAIL_PROVIDER;
let mailProvider =
  provider === "mailgun" ? mailGunClient : provider === "elasticmail" ? elasticMailClient : { send: () => {} };

class MailService {
  /**
   * @type {mailProvider}
   */
  #provider;
  constructor(provider) {
    if (instance) {
      throw new Error("You can only create one instance of MailService!");
    }
    instance = this;

    this.#provider = provider;
  }

  /**
   * Send email
   * @param {{to: string, subject: string, text?: string, html: string}} data
   * @returns {Promise<{status: number}>}
   */
  send = async (data) => {
    const { to, subject, text, html } = data;
    const message = {
      to,
      subject,
      html,
      text,
    };
    return await this.#provider.send(message);
  };
  /**
   * Get a unsubscribe user by email address
   * @param {string} address - email address
   * @returns {Promise<any>}
   */
  getUnsubscribeByAddress = async (address) => {
    return await this.#provider.getUnsubscribeByAddress(address);
  };
  /**
   * Remove a user from the email notification unsubscribe list. This unsubscribes list of users are stored by email providers. (e.g. Mailgun, Sendgrid, etc. )
   * @param {string} address - email address
   * @returns {Promise<any>}
   */
  removeFromUnsubscribeList = async (address) => {
    return await this.#provider.deleteUnsubscribeByAddress(address);
  };
}

const mailService = Object.freeze(new MailService(mailProvider));
module.exports = mailService;
