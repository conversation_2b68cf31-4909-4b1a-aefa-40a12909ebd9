require("graphql-import-node/register");
const { removeTrailingSlash, getUrl } = require("../utils/helper");
const { graphQLClient } = require("../utils/shopify.clients");
const { pubSub } = require("../config/app");

const webhookSubscriptionNodeFragment = require("../queries/webhooks/fragment.webhook-subscription.gql");
const webhookSubscriptionDeleteMutation = require("../queries/webhooks/mutation.subscription-delete.gql");
const webhookSubscriptionCreateMutation = require("../queries/webhooks/mutation.subscription-create.gql");
const webhookSubscriptionUpdateMutation = require("../queries/webhooks/mutation.subscription-update.gql");
const webhookSubscriptionsQuery = require("../queries/webhooks/query.subscriptions.gql");
const webhookSubscriptionQuery = require("../queries/webhooks/query.subscription.gql");

const pubSubWebhookSubscriptionCreateMutation = require("../queries/webhooks/pubsub/mutation.subscription-create.gql");
const pubSubWebhookSubscriptionUpdateMutation = require("../queries/webhooks/pubsub/mutation.subscription-update.gql");

class ShopifyWebhookService {
  /**
   * @param {string} shop - The shop myshopify domain
   * @param {Object} option
   * @returns {Promise<*>}
   */
  pubSubWebhookSubscriptionCreate = async (shop, option) => {
    const variables = {
      topic: option.topic,
      webhookSubscription: {
        pubSubProject: pubSub.projectId,
        pubSubTopic: pubSub.topicId,
        format: "JSON",
        includeFields: option?.includeFields || undefined,
        metafieldNamespaces: option?.metafieldNamespaces || undefined,
      },
    };

    const {
      data: {
        pubSubWebhookSubscriptionCreate: { userErrors, webhookSubscription },
      },
    } = await graphQLClient(shop, {
      query: pubSubWebhookSubscriptionCreateMutation,
      variables,
      fragments: [webhookSubscriptionNodeFragment],
    });

    if (userErrors.length > 0) {
      throw Error(userErrors[0]?.message);
    }

    return webhookSubscription;
  };

  /**
   * @param {string} shop - The shop myshopify domain
   * @param {string} whSubsId
   * @param {Object} option
   * @returns {Promise<*>}
   */
  pubSubWebhookSubscriptionUpdate = async (shop, whSubsId, option) => {
    const variables = {
      id: whSubsId,
      webhookSubscription: {
        pubSubProject: pubSub.projectId,
        pubSubTopic: pubSub.topicId,
        format: "JSON",
        includeFields: option?.includeFields || undefined,
        metafieldNamespaces: option?.metafieldNamespaces || undefined,
      },
    };

    const {
      data: {
        pubSubWebhookSubscriptionUpdate: { userErrors, webhookSubscription },
      },
    } = await graphQLClient(shop, {
      query: pubSubWebhookSubscriptionUpdateMutation,
      variables,
      fragments: [webhookSubscriptionNodeFragment],
    });

    if (userErrors.length > 0) {
      throw Error(userErrors[0]?.message);
    }

    return webhookSubscription;
  };

  /**
   * @param {string} shop - The shop myshopify domain
   * @param {Object} option
   * @returns {Promise<*>}
   */
  webhookSubscriptionCreate = async (shop, option) => {
    const variables = {
      topic: option.topic,
      webhookSubscription: {
        callbackUrl: await getUrl(option.path),
        includeFields: option?.includeFields || undefined,
        metafieldNamespaces: option?.metafieldNamespaces || undefined,
        format: "JSON",
      },
    };

    const {
      data: {
        webhookSubscriptionCreate: { userErrors, webhookSubscription },
      },
    } = await graphQLClient(shop, {
      query: webhookSubscriptionCreateMutation,
      variables,
      fragments: [webhookSubscriptionNodeFragment],
    });

    if (userErrors.length > 0) {
      throw Error(userErrors[0]?.message);
    }

    return webhookSubscription;
  };

  /**
   * @param {string} shop - The shop myshopify domain
   * @param {string} whSubsId
   * @param {Object} option
   * @returns {Promise<*>}
   */
  webhookSubscriptionUpdate = async (shop, whSubsId, option) => {
    const variables = {
      id: whSubsId,
      webhookSubscription: {
        callbackUrl: await getUrl(option.path),
        includeFields: option?.includeFields || undefined,
        metafieldNamespaces: option?.metafieldNamespaces || undefined,
        format: "JSON",
      },
    };

    const {
      data: {
        webhookSubscriptionUpdate: { userErrors, webhookSubscription },
      },
    } = await graphQLClient(shop, {
      query: webhookSubscriptionUpdateMutation,
      variables,
      fragments: [webhookSubscriptionNodeFragment],
    });

    if (userErrors.length > 0) {
      throw Error(userErrors[0]?.message);
    }

    return webhookSubscription;
  };

  /**
   * @param {string} shop - The shop myshopify domain
   * @param {string} whSubsId
   * @returns {Promise<*>}
   */
  webhookSubscriptionDelete = async (shop, whSubsId) => {
    const variables = { id: whSubsId };

    const {
      data: {
        webhookSubscriptionDelete: { userErrors, deletedWebhookSubscriptionId },
      },
    } = await graphQLClient(shop, {
      query: webhookSubscriptionDeleteMutation,
      variables,
    });

    if (userErrors.length > 0) {
      throw Error(userErrors[0]?.message);
    }

    return deletedWebhookSubscriptionId;
  };

  /**
   * Fetches webhook subscriptions for a given shop.
   *
   * @async
   * @function
   * @param {string} shop - The shop identifier.
   * @param {Object} options - Options for fetching webhook subscriptions.
   * @param {number} [options.first=50] - The maximum number of webhook subscriptions to fetch.
   * @param {string[]|null} [options.topics=null] - Specific topics to filter the webhook subscriptions.
   * @param {string|null} [options.callbackUrl=null] - A callback URL to filter the webhook subscriptions.
   * @returns {Promise<Object[]>} A promise that resolves to an array of webhook subscription nodes.
   * @throws {Error} Throws an error if the request fails.
   */
  webhookSubscriptions = async (shop, { first = 50, topics = null, callbackUrl = null } = {}) => {
    try {
      const {
        data: { webhookSubscriptions },
      } = await graphQLClient(shop, {
        query: webhookSubscriptionsQuery,
        variables: {
          first,
          topics,
          callbackUrl,
        },
        fragments: [webhookSubscriptionNodeFragment],
      });
      return webhookSubscriptions.edges.map((n) => n.node);
    } catch (err) {
      throw err;
    }
  };

  /**
   * Retrieves a specific webhook subscription by its ID.
   *
   * @async
   * @function
   * @param {string} shop - The shop myshopify domain.
   * @param {Object} options - Options for fetching the webhook subscription.
   * @param {string} options.whSubsId - The ID of the webhook subscription to retrieve.
   * @returns {Promise<Object | null>} A promise that resolves to the webhook subscription object.
   * @throws {Error} Throws an error if the request fails.
   */
  webhookSubscription = async (shop, { whSubsId }) => {
    try {
      const {
        data: { webhookSubscription },
      } = await graphQLClient(shop, {
        query: webhookSubscriptionQuery,
        variables: { id: whSubsId },
        fragments: [webhookSubscriptionNodeFragment],
      });
      return webhookSubscription;
    } catch (err) {
      throw err;
    }
  };
}

module.exports = new ShopifyWebhookService();
