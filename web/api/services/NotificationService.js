const { Notification: N, Op } = require("../../sequelize");
const { preparePagination } = require("../utils/helper");

/**
 * @type {typeof import("sequelize").Model}
 */
const Notification = N;

class NotificationService {
  /**
   * Create a new notification entry in DB
   * @param {import("../../jsDocTypes").NotificationDetails} data
   * @returns {Promise<import("../../jsDocTypes").NotificationDetails>}
   */
  createNotification = async (data) => {
    const n = await Notification.create(data);
    return n.toJSON();
  };

  /**
   * Update an existing notificaiton in DB
   * @param {number} id database generated id of the notification
   * @param {import("../../jsDocTypes").NotificationDetails} data fields to update
   * @returns {Promise<import("../../jsDocTypes").NotificationDetails>} updated notification
   */
  updateNotification = async (id, data) => {
    const [count, notifications] = await Notification.update(data, {
      where: { id },
      returning: true,
    });

    return notifications[0].toJSON();
  };

  /**
   * Update notification(s) in DB by condition
   * @param {import("sequelize").WhereOptions} condition
   * @param {import("../../jsDocTypes").NotificationDetails} data
   * @returns {Promise<[affectedCount: number, affectedRows: Model<{}, {}>[]]>} sequelize update query result
   */
  #updateByCondition = async (condition, data) =>
    Notification.update(data, {
      where: condition,
      returning: true,
    });

  /**
   * Delete a notification by id
   * @param {number} id database generated id of the notification
   * @returns {Promise<number>} number of deleted records
   */
  deleteNotification = async (id) => {
    return Notification.destroy({
      where: { id },
    });
  };

  /**
   * Mark a notification as read by id
   * @param {number} id database generated id of the notification
   * @returns {Promise<boolean>}
   */
  markAsRead = async (id) => {
    await this.updateNotification(id, { is_read: true });
    return true;
  };

  /**
   * Mark a notification as unread by id
   * @param {number} id database generated id of the notification
   * @returns {Promise<boolean>}
   */
  markAsUnread = async (id) => {
    await this.updateNotification(id, { is_read: false });
    return true;
  };

  /**
   * Pull notifications from DB with pagination by applying filters
   * @param {number | string} shop_id relevant shop id (database id)
   * @param {import("../../jsDocTypes").queryParam & { is_read: boolean }} param1
   * @returns {Promise<{ notifications: import("../../jsDocTypes").NotificationDetails[], pagination: import("../../jsDocTypes").Pagination }>} list of notifications with pagination data
   */
  pullNotifications = async (shop_id, { is_read, limit = 10, page = 1 }) => {
    /**
     * @type {import("sequelize").WhereOptions}
     */
    const where = { shop_id };
    if (is_read !== undefined) where.is_read = is_read;

    const { count, rows: notifications } = await Notification.findAndCountAll({
      where,
      limit,
      offset: (page - 1) * limit,
      order: [["created_at", "DESC"]],
    });

    return {
      notifications: notifications.map((n) => n.toJSON()),
      pagination: preparePagination(count, page, limit),
    };
  };

  /**
   * Mark all unread notifications of a shop as read
   * @param {number | string} shop_id database id of the related shop
   * @returns {Promise<number>} number of updated notifications
   */
  markAllAsRead = async (shop_id) => {
    const [count] = await this.#updateByCondition({ shop_id, is_read: false }, { is_read: true });
    return count;
  };

  /**
   * Count number of unread notifications by shop id
   * @param {number | string} shop_id database id of the related shop
   * @returns {Promise<number>} count of unread notifications
   */
  countUnread = async (shop_id) =>
    Notification.count({
      where: { shop_id, is_read: false },
    });
}

module.exports = new NotificationService();
