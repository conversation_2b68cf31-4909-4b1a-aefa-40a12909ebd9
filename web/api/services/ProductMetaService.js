const { isEmpty } = require("lodash");
const { rejectOnEmpty } = require("../config/sequelize");
const { ProductMeta, Op } = require("../../sequelize");

class ProductMetaService {
  /**
   * Get product metas by shop id, product id
   * @param shopId
   * @param productId
   * @returns {Promise<U[]>}
   */
  getProductMetas = async (shopId, productId) => {
    const where = { shop_id: shopId, product_id: productId };
    const pms = await ProductMeta.findAll({ where });
    return pms.map((pm) => pm.toJSON());
  };

  /**
   * Get a single meta
   * @param shopId
   * @param productId
   * @param gqlId
   * @param reject
   * @returns {Promise<any>}
   */
  getSingleProductMeta = async (shopId, productId, gqlId, reject = rejectOnEmpty) => {
    const pm = await ProductMeta.findOne({
      where: { shop_id: shopId, product_id: productId, gql_id: gqlId },
      rejectOnEmpty: reject,
    });
    return pm?.toJSON() || null;
  };

  /**
   * Save or update shopify product meta to database
   * @param shopId
   * @param productId
   * @param shopifyMetas
   * @returns {Promise<void>}
   */
  saveOrUpdateProductMetas = async (shopId, productId, shopifyMetas) => {
    if (!isEmpty(shopifyMetas) && shopifyMetas.length > 0) {
      const dbMetas = [];
      for (let meta of shopifyMetas) {
        const metaData = {
          shop_id: shopId,
          product_id: productId,
          gql_id: meta.id,
          key: meta.key,
          type: meta.type,
          namespace: meta.namespace,
          value: meta.value,
          description: meta.description,
        };
        const [m] = await ProductMeta.upsert(metaData, { conflictFields: ["gql_id"] });
        dbMetas.push(m);
      }

      console.log(
        "Shop =>",
        shopId,
        "Product =>",
        productId,
        "Shopify Meta =>",
        shopifyMetas.length,
        "Inserted/Updated Meta =>",
        dbMetas.length
      );
    }
  };

  /**
   * Deletes product meta
   * @param shopId
   * @param productId
   * @param transaction
   * @returns {Promise<number|boolean>}
   */
  deleteProductMetas = async (shopId, productId, transaction = undefined) => {
    const where = { shop_id: shopId, product_id: productId };
    return await ProductMeta.destroy({ where, transaction });
  };

  /**
   * Deletes product meta by graphql ids array
   * @param shopId
   * @param productId
   * @param deletedIds
   * @returns {Promise<number|boolean>}
   */
  deleteProductMetasByGqlId = async (shopId, productId, deletedIds = []) => {
    if (deletedIds.length > 0) {
      const where = { shop_id: shopId, product_id: productId };
      where.gql_id = { [Op.in]: deletedIds };
      return await ProductMeta.destroy({ where });
    }
  };

  /**
   * Deletes product meta greater than the provided id
   * @param shopId
   * @param productId
   * @returns {Promise<number|boolean>}
   */
  deleteShopMetasGreaterThenProductId = async (shopId, productId, transaction = undefined) => {
    const where = { shop_id: shopId };
    if (productId) {
      where.product_id = { [Op.gte]: productId };
    }
    return await ProductMeta.destroy({ where, transaction });
  };

  /**
   * Deletes the deleted meta field data comparing shopify and database
   * @param shopId
   * @param productId
   * @param shopifyMetas
   * @returns {Promise<number|boolean>}
   */
  deleteDeletedProductMeta = async (shopId, productId, shopifyMetas = []) => {
    const dbMetas = await this.getProductMetas(shopId, productId);
    if (dbMetas.length > 0) {
      const deletedIds = this.#findDeleteMetaGqlIds(dbMetas, shopifyMetas);
      return await this.deleteProductMetasByGqlId(shopId, productId, deletedIds);
    }
  };

  /**
   * Find deleted meta ids comparing database & shopify meta
   * @param {Array} dbMetas
   * @param {Array} shopifyMetas
   * @returns {*}
   */
  #findDeleteMetaGqlIds = (dbMetas = [], shopifyMetas = []) => {
    return dbMetas.filter((dm) => !shopifyMetas.find((m) => m.id === dm.gql_id))?.map((m) => m.gql_id || null);
  };
}

module.exports = new ProductMetaService();
