const ShopService = require("./ShopService");
const ProductService = require("./ProductService");
const ProductAnalysisService = require("./ProductAnalysisService");
const ShopifyService = require("./ShopifyService");
const PageService = require("./PageService");
const BlogService = require("./BlogService");
const ArticleService = require("./ArticleService");
const LocationService = require("./LocationService");
const SubscriptionPlanService = require("./SubscriptionPlanService");

const { dispatchQueue } = require("../queue/queueDispatcher");
const { QUEUE_NAMES } = require("../queue/config");
const appSubscriptionStatus = require("storeseo-enums/appSubscriptionStatus");
const planType = require("storeseo-enums/planType");
const cache = require("../cache");
const { pick, isEmpty } = require("lodash");
const { ACTIVE, DELETED } = require("storeseo-enums/shopStatus");
const { UNINSTALLED } = require("storeseo-enums/mailchimp/staticTags");

const { IMAGE_OPTIMIZER } = require("storeseo-enums/subscriptionAddonGroup");
const subscriptionAddonType = require("storeseo-enums/subscriptionAddonType");
const { addonLimitLabels } = require("../config/addons");
const subscriptionAddonInterval = require("storeseo-enums/subscriptionAddonInterval");
const { serializeShopPlanData } = require("../serializers/SubscriptionSerializer");
const SubscriptionService = require("./SubscriptionService");
const ShopStatus = require("storeseo-enums/shopStatus");
const AddonUsageService = require("./AddonUsageService");

/**
 * @typedef {Object} Session
 * @property {string} shop - Shop domain
 * @property {string} accessToken - Access token
 */

/**
 * @typedef {Object} Shop
 * @property {string} id
 * @property {string} email
 * @property {string} domain
 * @property {string} url
 * @property {string} name
 * @property {string} [logo_path]
 * @property {string} access_token
 * @property {string} plan_id
 * @property {Date} plan_validity
 * @property {Date} subscribed_at
 * @property {Object} plan_rules
 * @property {string} plan_status
 * @property {string} industry_id
 * @property {boolean} is_verified
 * @property {Object} plan_info
 * @property {string} data_migrated_from_app
 */

/**
 * @typedef {Object} PlanInfo
 * @property {string} planType
 * @property {Array<Object>} addons
 */

/**
 * @typedef {Object} AuthToken
 * @property {string} email
 * @property {string} shopId
 * @property {string} shop
 * @property {boolean} productSyncOngoing
 * @property {boolean} collectionSyncOngoing
 * @property {boolean} betterDocsInstallationStatus
 * @property {string} url
 * @property {string} industryId
 * @property {string} shopName
 * @property {string} shopLogo
 * @property {string} accessToken
 * @property {string} planId
 * @property {Date} planValidity
 * @property {Date} subscribedAt
 * @property {Object} permission
 * @property {string} subscriptionStatus
 * @property {boolean} isSubscribed
 * @property {boolean} isPremium
 * @property {string} migrateDataFromApp
 * @property {boolean} isOnboarded
 * @property {boolean} isVerified
 * @property {Array} hiddenBanner
 * @property {Object} addons
 * @property {boolean} appEmbedEnabled
 * @property {Object} multiLanguage
 * @property {string} defaultLanguage
 */

class AuthService {
  /**
   * Register a new shop or update existing shop
   * @param {Session} session - Shop session data
   * @returns {Promise<Shop>}
   */
  register = async (session) => {
    const { shop: shopDomain, accessToken } = session;

    let shouldSyncProducts = false;
    // Get existing shop data
    let shop = await ShopService.getShop(shopDomain);
    const result = {
      shop: null,
      isNewShop: false,
    };

    // Check if shop is new
    if (![ShopStatus.ACTIVE, ShopStatus.CLOSED].includes(shop?.status)) {
      result.isNewShop = await cache.isNewStore(shopDomain, true);
    }

    // Create or update shop
    if (!shop) {
      const shopifyShop = await ShopifyService.getShopDetailsFromShopify(shopDomain);
      shop = await ShopService.storeShop({ ...shopifyShop, accessToken });
      shouldSyncProducts = true;
    } else {
      shop = await ShopService.updateShop(shop.id, {
        access_token: accessToken,
        status: ShopStatus.ACTIVE,
      });
    }

    // Handle free plan subscription
    if (!shop?.plan_id) {
      await this.subscribeToFreePlan(session, shop);
    }

    // Sync initial products for new shops
    if (shouldSyncProducts) {
      ProductService.storeProductsFromShopify(session, shop.id, null, 50);
    }

    result.shop = shop;
    return result;
  };

  subscribeToFreePlan = async (session, shop) => {
    // subscribe to free plan
    const subscriptionPlan = await SubscriptionPlanService.getFreeSubscriptionPlan();
    const combinedAddons = await SubscriptionPlanService.getCombinedAddons(subscriptionPlan.addons);
    await SubscriptionService.handleFreeSubscription(session, {
      shopId: shop.id,
      subscriptionPlan,
      shopData: shop,
      addons: combinedAddons || [],
    });

    await cache.planId(session.shop, subscriptionPlan.id);
    await cache.isOnboardingCompleted(session.shop, false);
  };

  /**
   * Serialize shop data into auth token
   * @param {Shop} shop - Shop data
   * @param {PlanInfo} [plan={}] - Plan information
   * @returns {Promise<AuthToken>}
   */
  serializeAuthToken = async (shop, plan = {}) => ({
    email: shop.email,
    shopId: shop.id,
    shop: shop.domain,
    productSyncOngoing: await cache.productSyncOngoing(shop.domain),
    collectionSyncOngoing: await cache.collectionSyncOngoingStatus(shop.domain),
    betterDocsInstallationStatus: await cache.betterDocInstallationStatus(shop.domain),
    url: shop.url,
    industryId: shop.industry_id,
    shopName: shop.name,
    shopLogo: shop?.logo_path || "",
    accessToken: shop.access_token,
    planId: shop.plan_id,
    planValidity: shop.plan_validity,
    subscribedAt: shop.subscribed_at,
    permission: shop.plan_rules,
    subscriptionStatus: shop.plan_status,
    isSubscribed: shop.plan_status === appSubscriptionStatus.ACTIVE || plan?.planType === planType.FREE,
    isPremium: !!(shop.plan_id && plan?.planType !== planType.FREE),
    migrateDataFromApp: shop.data_migrated_from_app,
    isOnboarded: await cache.isOnboardingCompleted(shop.domain),
    isVerified: shop.is_verified,
    ...plan,
    hiddenBanner: await cache.getHiddenBanners(shop.domain),
    addons: await this.serializeAddonData(shop), // moved image optimizer releated vars to addons
    appEmbedEnabled: await cache.appEmbedStatus(shop.domain),
    multiLanguage: {
      hasMultipleLanguages: await cache.shop.hasMultipleLanguages(shop.domain),
      enabledMultiLanguage: await cache.shop.enabledMultiLanguage(shop.domain),
    },
    defaultLanguage: await cache.shop.defaultLanguage(shop.domain),
  });

  /**
   * Serialize addon data for shop
   * @param {Shop} shop - Shop data
   * @returns {Promise<Array<Object>|null>}
   */
  serializeAddonData = async (shop) => {
    const addons = await AddonUsageService.getActiveRecords(shop.id);

    if (addons.length === 0) return null;

    return await Promise.all(
      addons.map(async (addon) => ({
        ...pick(addon.addon, ["id", "name", "group", "interval"]),
        isActive: addon.is_active,
        isFree: addon.addon.type === subscriptionAddonType.FREE,
        isMonthly: addon.addon.interval === subscriptionAddonInterval.MONTHLY,
        // usageLimit: await cache.addons.usageLimit(shop.domain, { addon: cache.keys[addon.group] }),
        usageLimit: addon.current_limit,
        usageCount: await cache.addons.usageCount(shop.domain, { addon: cache.keys[addon.addon.group] }),
        limitLabel: addonLimitLabels?.[addon.addon.group] || "",
      }))
    );
  };

  /**
   * Check if image optimizer is free
   * @param {Object} planInfo - Plan information
   * @returns {boolean}
   */
  checkIsFreeImageOptimizer = (planInfo) => {
    return planInfo?.addons?.find((addon) => addon.group === IMAGE_OPTIMIZER)?.type === subscriptionAddonType.FREE;
  };

  /**
   * Get user data from authorization token
   * @param {string} authorization - Authorization header
   * @returns {Promise<AuthToken|false>}
   */
  getUserFromToken = async (authorization) => {
    if (authorization !== undefined) {
      const token = authorization.replace("Bearer ", "");
      const { dest } = Shopify.Utils.decodeSessionToken(token);
      const shopDomain = dest.replace("https://", "");
      const shop = await ShopService.getShop(shopDomain);

      // Format plan_info data instead of calling SubscriptionPlanService
      const plan =
        shop.plan_id && shop.plan_info
          ? {
              planName: shop.plan_info.name,
              planSlug: shop.plan_info.slug,
              planType: shop.plan_info.type,
              planCoupon: shop.plan_info.coupon_code,
              planInterval: shop.plan_info.interval,
            }
          : {};

      return await this.serializeAuthToken(shop, plan);
    }

    return false;
  };

  /**
   * Get user data from session
   * @param {Session} session - Shop session
   * @returns {Promise<AuthToken|false>}
   */
  getUserFromSession = async (session) => {
    if (session !== undefined) {
      const { shop: shopDomain } = session;
      const shop = await ShopService.getShop(shopDomain);

      // Format plan_info data instead of calling SubscriptionPlanService
      const plan =
        shop.plan_id && shop.plan_info
          ? {
              planName: shop.plan_info.name,
              planSlug: shop.plan_info.slug,
              planType: shop.plan_info.type,
              planCoupon: shop.plan_info.coupon_code,
              planInterval: shop.plan_info.interval,
            }
          : {};

      return await this.serializeAuthToken(shop, plan);
    }

    return false;
  };

  /**
   * Remove access token for a shop
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<Shop>}
   */
  removeAccessToken = async (shopDomain) => {
    return await ShopService.updateShopByCondition({ domain: shopDomain }, { access_token: null });
  };

  /**
   * Permanently remove shop data
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<boolean>}
   */
  removeShopDataPermanently = async (shopDomain) => {
    try {
      const shop = await ShopService.getShop(shopDomain);

      dispatchQueue({
        queueName: QUEUE_NAMES.MAILCHIMP_ADD_MEMBER,
        message: {
          domain: shopDomain,
          tags: [UNINSTALLED],
        },
      });

      // await ShopService.deleteShop(shopDomain);
      await ShopService.updateShop(shop.id, { status: DELETED });
      await ProductService.deleteProductRelatedData(shop.id);
      await PageService.deletePageOfShop(shop.id);
      await BlogService.deleteBlogOfShop(shop.id);
      await ArticleService.deleteArticleOfShop(shop.id);
      await LocationService.deleteLocationsOfShop(shop.id);
      await ProductAnalysisService.deleteDataByShop(shop.id);
      console.log("all data removed.");
      return true;
    } catch (err) {
      console.log(err.message);
      return false;
    }
  };
}

module.exports = new AuthService();
