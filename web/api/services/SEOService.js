const GoogleApiService = require("./GoogleApiService");
const logger = require("storeseo-logger");
const { sanitizeInput } = require("../utils/helper");

class SEOService {
  generateDefaultLocalSEOData = async (shopObj) => {
    try {
      const { id, name, url, billing_address, logo_path } = shopObj;

      const { address1 = "", address2 = "", city = "", zip = "", country = "" } = billing_address || {};
      const { lat: latitude = "", lng: longitude = "" } =
        (await GoogleApiService.getLatLongFromAddress({ address1, address2, city, zip, country })) ?? {};

      const defaultLocalSEOdata = {
        name,
        url,
        telephone: "",
        priceRange: "",
        image: logo_path || "",
        address: { address1, address2, city, zip, country },
        geo: { latitude, longitude },
        socialMediaLinks: {
          facebookURL: "",
          instagramURL: "",
          twitterURL: "",
          linkedinURL: "",
          pinterestURL: "",
          youtubeURL: "",
        },
      };

      return defaultLocalSEOdata;
    } catch (err) {
      logger.error("Error generating default local seo data: " + JSON.stringify(err));
      return null;
    }
  };

  generateJSONLDSchemaForLocalSEO = (jsonldData, locations = []) => {
    const { name, url, telephone, priceRange, image, address, geo, socialMediaLinks } = sanitizeInput(jsonldData);
    const newLocations = sanitizeInput(locations);

    return {
      "@context": "https://schema.org",
      "@type": "Store",
      image: image,
      name,
      url,
      telephone,
      priceRange,
      address: {
        "@type": "PostalAddress",
        streetAddress: address?.address2,
        addressLocality: address?.address1,
        addressRegion: address?.city,
        postalCode: address?.zip,
        addressCountry: address?.country,
      },
      geo: {
        "@type": "GeoCoordinates",
        latitude: Number(geo.latitude),
        longitude: Number(geo.longitude),
      },
      sameAs: Object.values(socialMediaLinks).filter((url) => url),
      department: newLocations.map((loc) => ({
        "@type": "Store",
        name: `${loc.name} - ${name}`,
        image,
        telephone: loc.phone || telephone,
        priceRange,
        sameAs: Object.values(socialMediaLinks).filter((url) => url),
        address: {
          "@type": "PostalAddress",
          streetAddress: loc?.address2,
          addressLocality: loc?.address1,
          addressRegion: loc?.city,
          postalCode: loc?.zip,
          addressCountry: loc?.country,
        },
        geo: {
          "@type": "GeoCoordinates",
          latitude: Number(loc.latitude),
          longitude: Number(loc.longitude),
        },
      })),
    };
  };
}

module.exports = new SEOService();
