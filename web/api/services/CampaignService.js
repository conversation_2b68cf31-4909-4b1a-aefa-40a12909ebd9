const { Campaign, Op } = require("../../sequelize");
const moment = require("moment");

class CampaignService {
  /**
   * Get latest running campaign
   * @returns {Promise<*|null>}
   */
  getRunningCampaign = async () => {
    const date = moment();
    return await Campaign.findOne({
      where: {
        status: true,
        start_date: { [Op.lte]: date },
        end_date: { [Op.or]: { [Op.gte]: date, [Op.is]: null } },
      },
      rejectOnEmpty: false,
      order: [["created_at", "desc"]],
    });
  };

  #serialize = (campaign) => {};

  /**
   * Check is there any running campaign
   * @returns {Promise<boolean>}
   */
  hasRunningCampaign = async () => {
    const date = moment();
    return (
      (await Campaign.count({
        where: {
          status: true,
          start_date: { [Op.lte]: date },
          end_date: { [Op.or]: { [Op.gte]: date, [Op.is]: null } },
        },
        rejectOnEmpty: false,
        order: [["created_at", "desc"]],
      })) > 0
    );
  };
}

module.exports = new CampaignService();
