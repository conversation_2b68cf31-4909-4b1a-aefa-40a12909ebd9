const fetch = require("node-fetch");
const { stripSpecialChars } = require("../utils/helper");

class WebcrawlerService {
  crawlPage = async (url) => {
    try {
      const fetchClient = await fetch;
      const response = await fetchClient.default(url);
      const bodyHtml = await response.text();
      return bodyHtml;
    } catch (err) {
      console.log("err: ", err);
      return null;
    }
  };

  getPageTitleAndDescription = (bodyHtml) => {
    const head = bodyHtml.split("</head>")[0] || "";
    const title = head
      .match(
        /(?:(?:<title>[^<]*<\/title>)|(?:<meta property=(?:'|")og:title(?:'|")[^>]*>)|(?:<meta name="twitter:title[^>]*>))/gim
      )?.[0]
      .replace(/<title>|<\/title>|<meta.*content=(?:"|')|(?:"|')>?$/gim, "")
      .trim();
    const description = head
      .match(
        /<meta (?:(?:name=(?:"|')description(?:"|'))|(?:property=(?:"|')og:description(?:"|'))|(?:name=(?:"|')twitter:description(?:"|'))).*?(?:"|').*?(?:"|').*?>/gim
      )?.[0]
      .replace(/<meta.*content=(?:"|')|(?:"|')>?$/gim, "")
      .trim();

    return {
      title: stripSpecialChars(title),
      description: stripSpecialChars(description),
    };
  };
}

module.exports = new WebcrawlerService();
