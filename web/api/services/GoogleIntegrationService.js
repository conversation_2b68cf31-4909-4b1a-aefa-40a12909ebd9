const { METAFIELD_KEYS } = require("storeseo-enums/metafields");
const logger = require("storeseo-logger");
const GoogleApiService = require("./GoogleApiService");
const ShopifyService = require("./ShopifyService");
const ShopService = require("./ShopService");

class GoogleIntegrationService {
  THEME_ASSET_KEY = "layout/theme.liquid";
  SITE_VERIFICATION_START_COMMENT = "\n<!-- Site verification meta injected by StoreSEO - Start -->\n";
  SITE_VERIFICATION_END_COMMENT = "\n<!-- Site verification meta injected by StoreSEO - End -->\n";

  insertVerificationMetaIntoShopifySite = async (session, shopifyShopId, verificationToken) => {
    await ShopifyService.setMetafield(session.shop, {
      key: METAFIELD_KEYS.GOOGLE_SITE_VERIFICATION_META,
      ownerId: shopifyShopId,
      value: verificationToken,
    });
  };

  doSiteVerification = async (shopDomain, authenticatedUser) => {
    try {
      console.log("\n---");
      console.log("authenticatedUser: ", JSON.stringify(authenticatedUser));
      console.log("domain: ", shopDomain);
      console.log("---\n");
      const shop = await ShopService.getShop(shopDomain);
      const siteAlreadyVerified = await GoogleApiService.isSiteVerified(shop.url, authenticatedUser);
      let verficationStatus = siteAlreadyVerified;

      console.log("SHOP DOMAIN: ", shopDomain, "IS VERIFIED: ", verficationStatus);

      if (!siteAlreadyVerified) {
        const { access_token: accessToken, shop_id: shopifyShopId } = await ShopService.getShop(shopDomain);
        const session = { shop: shopDomain, accessToken };

        const { token } = await GoogleApiService.getSiteVerificationToken(shop.url, authenticatedUser);
        await this.insertVerificationMetaIntoShopifySite(session, shopifyShopId, token);

        const data = await GoogleApiService.verifySiteOwnership(shop.url, authenticatedUser);
        console.log("VERIFICATION DATA: ", data);
        if (data) verficationStatus = true;
      }

      await ShopService.updateGoogleIntegrationStatus(shopDomain, { siteVerified: verficationStatus });
    } catch (err) {
      logger.error(
        `Error completing site verification for ${shopDomain} for user ${authenticatedUser?.email}. Error: ${err}`
      );
      return null;
    }
  };
}

module.exports = new GoogleIntegrationService();
