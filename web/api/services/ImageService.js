const { ProductImage } = require("../../sequelize");
const { Op } = require("sequelize");
const { default: axios } = require("axios");
const { baseUrl, bearerToken } = require("../config/imageOptimizer");
const ResourceType = require("storeseo-enums/resourceType");
const ProductImageService = require("./ProductImageService");
const CollectionImageService = require("./collections/CollectionImageService");
const imageOptimization = require("storeseo-enums/imageOptimization");
const ShopifyService = require("./ShopifyService");
const ResourceOptimizationService = require("./resource/ResourceOptimizationService");
const ResourceOPType = require("storeseo-enums/resourceOPType");
const CollectionService = require("./collections/CollectionService");
const { formatCollectionImage } = require("../serializers/CollectionSerializer");
const ArticleImageService = require("./ArticleImageService");
const ArticleService = require("./ArticleService");
const { extractShopifyIdFromGqlId } = require("../utils/helper");
const { formatArticleImage } = require("../serializers/ArticleSerializer");

/**
 * @typedef {keyof Pick<typeof ResourceType, "PRODUCT" | "COLLECTION" | "PAGE" | "ARTICLE">} ImageResourceType
 */

class ImageService {
  /**
   *
   * @param {number} id
   * @param {{shop_id:number, resource_id?: string, optimization_status: keyof typeof imageOptimization, optimization_meta : object, optimization_setting: object, optimized_at: Date, media_id?: string, src?: string, file_size?: number }} data
   * @param {ImageResourceType} resourceType
   * @returns
   */
  updateCollectionImage = async (id, data, resourceType) => {
    const { shop_id, media_id, resource_id } = data;
    const collection = await CollectionService.getByCondition(
      shop_id,
      {
        collection_id: resource_id,
      },
      false,
      []
    );

    if (!collection) {
      throw new Error("Collection not found");
    }

    const updateImage = {
      ...data,
      collection_id: collection.id,
      gql_id: media_id,
    };
    const updatedImage = await CollectionImageService.upsert({ id, ...updateImage });
    // Update resource optimization
    const optimizationMeta = await ResourceOptimizationService.upsert({
      shop_id: data.shop_id,
      resource_id: id,
      resource_type: resourceType,
      resource_op_type: ResourceOPType.IMAGE_OPTIMIZATION,
      optimization_meta: data.optimization_meta,
      optimization_setting: data.optimization_setting,
    });

    return formatCollectionImage({ ...updatedImage, resourceOptimizationMeta: optimizationMeta });
  };

  /**
   *
   * @param {number} id
   * @param {{shop_id:number, resource_id?: string, optimization_status: keyof typeof imageOptimization, optimization_meta : object, optimization_setting: object, optimized_at: Date, media_id?: string, src?: string, file_size?: number }} data
   * @param {ImageResourceType} resourceType
   * @returns
   */
  updateArticleImage = async (id, data, resourceType) => {
    const { shop_id, resource_id, media_id } = data;
    const articleId = extractShopifyIdFromGqlId(resource_id);
    const article = await ArticleService.getArticleByShopifyId(shop_id, articleId);

    if (!article) {
      throw new Error("Article not found");
    }

    const mediaId = extractShopifyIdFromGqlId(media_id);
    const updateImage = {
      ...data,
      article_id: article.id,
      media_id: mediaId,
      blog_id: article.blog_id,
    };

    const updatedImage = await ArticleImageService.upsert({ id, ...updateImage });
    // Update resource optimization
    const optimizationMeta = await ResourceOptimizationService.upsert({
      shop_id: data.shop_id,
      resource_id: id,
      resource_type: resourceType,
      resource_op_type: ResourceOPType.IMAGE_OPTIMIZATION,
      optimization_meta: data.optimization_meta,
      optimization_setting: data.optimization_setting,
    });

    return formatArticleImage({ ...updatedImage, resourceOptimizationMeta: optimizationMeta });
  };

  /**
   * @param {ImageResourceType} resourceType - The type of resource to retrieve images for (e.g., "PRODUCT")
   * @param {number} id - The ID of the image to update
   * @param {{shop_id:number, resource_id?: string, optimization_status: keyof typeof imageOptimization, optimization_meta : object, optimization_setting: object, optimized_at: Date, media_id?: string, src?: string, file_size?: number }} data
   * @returns
   */
  updateImage = async (resourceType, id, data) => {
    switch (resourceType) {
      case "PRODUCT":
        return await ProductImageService.updateImage(id, data);
      case "COLLECTION":
        return await this.updateCollectionImage(id, data, resourceType);
      case "ARTICLE":
        return await this.updateArticleImage(id, data, resourceType);
      default:
        throw new Error("Invalid resource type");
    }
  };

  /**
   * Updates Shopify images based on the provided resource type and images.
   *
   * @param {string} shop
   * @param {ImageResourceType} resourceType
   * @param {Array<{id: string, src: string}>} imagePayload
   * @returns {Promise<Array<{url: string}>>} An array of updated image URLs
   */
  updateShopifyImages = async (shop, resourceType, imagePayload) => {
    switch (resourceType) {
      case "PRODUCT": {
        const inputPayloads = imagePayload.map((image) => ({
          id: image.id,
          originalSource: image.src,
        }));
        const {
          fileUpdate: { files },
        } = await ShopifyService.updateImageFiles(shop, inputPayloads);
        return files.map((item) => item.preview.image);
      }

      case "COLLECTION": {
        const collectionImages = [];
        for (const image of imagePayload) {
          const inputPayloads = {
            id: image.id,
            image: {
              src: image.src,
            },
          };
          const {
            collectionUpdate: { collection },
          } = await ShopifyService.updateCollection(shop, inputPayloads);
          collectionImages.push(collection.image);
        }

        return collectionImages;
      }

      case "ARTICLE": {
        const articleImages = [];
        for (const image of imagePayload) {
          const inputPayloads = {
            id: image.id,
            article: {
              image: {
                url: image.src,
              },
            },
          };

          const article = await ShopifyService.onlineStore.updateArticle(shop, inputPayloads);
          articleImages.push(article.image);
        }

        return articleImages;
      }
      default:
        throw new Error("Invalid resource type");
    }
  };

  getProductImageByIds = async (ids) => {
    return ProductImage.findAll({
      where: {
        id: {
          [Op.and]: ids,
        },
      },
    });
  };

  #imageOptimizerApiClient = axios.create({
    baseURL: baseUrl,
    headers: {
      Authorization: `Bearer ${bearerToken}`,
    },
  });

  optimizeImages = async (data) => {
    const { data: responseData } = await this.#imageOptimizerApiClient.post("/optimize", data);
    return responseData.image_info;
  };

  optimizeImagesViaQueue = async (data) => {
    await this.#imageOptimizerApiClient.post("/queue/optimize", data);
  };

  /**
   * Retrieves images based on the specified resource type and query parameters.
   *
   * @param {ImageResourceType} resourceType - The type of resource to retrieve images for (e.g., "PRODUCT", "COLLECTION").
   * @param {{
   *   shopId: string,
   *   page?: number,
   *   limit?: number,
   *   search?: string,
   *   status?: string,
   *   alt_status?: string,
   *   fileSize?: number,
   *   has_alt_text?: boolean,
   *   sortBy?: string,
   *   sortOrder?: string
   * }} options - The query parameters for retrieving images
   * @returns {Promise<Array>} A promise that resolves to an array of images
   */
  getImages = async (resourceType, options) => {
    switch (resourceType) {
      case "PRODUCT":
        return await ProductImageService.getImagesByShop(options.shopId, options);
      case "COLLECTION":
        return await CollectionImageService.getImagesByShop(options.shopId, options);
      case "ARTICLE":
        return await ArticleImageService.getImagesByShop(options.shopId, options);
      default:
        throw new Error("Invalid resource type");
    }
  };

  /**
   * Deletes an image based on resource type and ID.
   *
   * @param {ImageResourceType} resourceType - The type of resource (e.g., "PRODUCT", "COLLECTION").
   * @param {{id: number, shopId: number, resourceId: number}} option - The ID of the image to delete.
   * @returns {Promise<void>}
   */
  deleteImage = async (resourceType, option) => {
    const { id, shopId, resourceId } = option;

    switch (resourceType) {
      case "PRODUCT":
        return await ProductImageService.deleteImage(id);
      case "COLLECTION":
        return await CollectionImageService.deleteById(id, shopId, resourceId);
      case "ARTICLE": {
        const articleId = extractShopifyIdFromGqlId(resourceId);
        return await ArticleImageService.deleteById(id, shopId, articleId);
      }
      default:
        throw new Error("Invalid resource type");
    }
  };
}

module.exports = new ImageService();
