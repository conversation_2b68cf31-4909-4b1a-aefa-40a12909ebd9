const logEventTypes = require("storeseo-enums/logEventTypes");
const { AppLog: logModel } = require("../../sequelize");
const { pick, omit } = require("lodash");

/**
 * @typedef {import("../../jsDocTypes").AppLogDetails} AppLogDetails
 */

/**
 * @type {typeof import('sequelize').Model}
 */
const AppLog = logModel;

class AppLogService {
  /**
   * Create a new log entry in the DB for app uninstall event
   * @param {object} shop object from db
   * @returns {Promise<boolean>}
   */
  logAppUninstalled = async (shop) => {
    try {
      const logData = {
        shop: shop.domain,
        type: logEventTypes.APP_UNINSTALLED,
        uninstalled_at: new Date(),
        data: { ...omit(shop, ["mailchimp"]) },
      };
      await AppLog.create(logData, { returning: true });
      return true;
    } catch (err) {
      console.log("Error while creating log for app uninstalled: ", err);
      return false;
    }
  };

  /**
   * Update existing log in DB
   * @param {AppLogDetails} attrs
   * @param {import("sequelize").WhereAttributeHash} condition
   * @returns {Promise<AppLogDetails>}
   */
  updateLog = async (attrs, condition) => {
    try {
      // const updatedLog = await AppLog.where(condition).save(attrs, { patch: true });
      const [affectedRows, updatedLogs] = await AppLog.update(attrs, {
        returning: true,
        where: condition,
      });

      if (affectedRows === 1) return updatedLogs[0]?.toJSON();

      return updatedLogs.map((l) => l.toJSON());
    } catch (err) {
      console.log(
        `Error updating log by condition: ${JSON.stringify(condition, null, 2)} \n attrs: ${JSON.stringify(
          attrs,
          null,
          2
        )}. \n Error: `,
        err
      );
      return false;
    }
  };
}

module.exports = new AppLogService();
