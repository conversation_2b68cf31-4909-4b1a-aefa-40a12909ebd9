const { FluentCrmTags } = require("../../sequelize");
const { default: axios } = require("axios");
const { fluentcrm } = require("../config/app");

/**
 * @typedef {object} FluentCrmTagDb
 * @property {number} id
 * @property {number} tag_id
 * @property {string} title
 * @property {string} description
 * @property {string} slug
 */

/**
 * @typedef {object} FluentCrmContact
 * @property {string} email
 * @property {string} phone
 * @property {string} address_line_1
 * @property {string} address_line_2
 * @property {string} city
 * @property {string} state
 * @property {string} country
 * @property {string} postal_code
 * @property {number[]} tags id of tags
 * @property {{[field_slug]: string}} custom_values
 * @property {"subscribed" | "unsubscribed" | "pending" | "bounced" | "complained"} [status=subscribed]
 */

class FluentCrmService {
  /**
   * @type {typeof import("sequelize").Model}
   */
  Model = FluentCrmTags;
  /**
   * @type {import("axios").Axios}
   */
  Client;

  constructor() {
    const authToken = Buffer.from(`${fluentcrm.username}:${fluentcrm.password}`).toString("base64");

    this.Client = axios.create({
      baseURL: fluentcrm.baseURL,
      headers: {
        Authorization: `Basic ${authToken}`,
      },
    });
  }

  allTags = async () => {
    const { data } = await this.Client.get("/tags");
    return data;
  };

  /**
   * Create a new tag inside fluent crm
   * @param {string} title
   * @param {string} slug
   * @param {string} description
   */
  createTag = async (title, slug, description) => {
    const { data } = await this.Client.post("/tags", {
      title,
      slug,
      description,
    });

    return data.lists;
  };

  /**
   *
   * @param {FluentCrmTagDb} tag
   * @returns {Promise<FluentCrmTagDb>}
   */
  saveTagInDb = async (tag) => {
    const saved = await this.Model.create(tag, { returning: true });
    return saved.toJSON();
  };

  /**
   *
   * @param {string} slug
   * @returns {Promise<FluentCrmTagDb>}
   */
  getTagBySlugFromDb = async (slug) => {
    const tag = await this.Model.findOne({
      where: { slug },
    });

    return tag?.toJSON();
  };

  /**
   *
   * @param {FluentCrmContact} contact
   * @param {boolean} [forceUpdate]
   */
  createContact = async (contact, forceUpdate = true) => {
    const input = {
      ...contact,
      status: contact.status || "subscribed",
      __force_update: forceUpdate,
    };

    const { data } = await this.Client.post("/subscribers", input);

    return data.contact;
  };

  /**
   *
   * @param {string} email
   * @returns {Promise<FluentCrmContact | {tags: { id: string, slug: string }[]}>}
   */
  getContact = async (email) => {
    const { data } = await this.Client.get(`/subscribers/0?get_by_email=${email}&with[]=subscriber.custom_values`);

    return data.subscriber;
  };

  /**
   *
   * @param {string} id FluentCRM contact id
   * @param {{ subscriber: FluentCrmContact & { attach_tags: number[], detach_tags: number[]}}} input
   */
  updateContact = async (id, input) => {
    const { data } = await this.Client.put(`/subscribers/${id}`, input);

    return data.contact;
  };
}

module.exports = new FluentCrmService();
