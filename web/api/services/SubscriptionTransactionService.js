const { isEmpty } = require("lodash");
const { SubscriptionTransaction: ST, Op, sequelize: S } = require("../../sequelize");
const { createHmac } = require("../utils/crpyto");
const logger = require("storeseo-logger");
const { preparePagination } = require("../utils/helper");
const transTypes = require("storeseo-enums/transactionTypes");
const { FREE } = require("storeseo-enums/planType");
const moment = require("moment");
const { QueryTypes } = require("sequelize");

/**
 * @type {typeof import("sequelize").Model}
 */
const SubscriptionTransaction = ST;

/**
 * @type {import("sequelize").Sequelize}
 */
const sequelize = S;

class SubscriptionTransactionService {
  /**
   * Get Shops last transaction
   * @param {number} shopId
   * @returns {Promise<*|null>}
   */
  getLastTransaction = async (shopId) => {
    const lastTrans = await SubscriptionTransaction.findOne({
      where: { shop_id: shopId },
      order: [["created_at", "desc"]],
      rejectOnEmpty: false,
    });
    return lastTrans?.toJSON() || null;
  };

  /**
   * Get transactions with pagination
   * @param shopId
   * @param page
   * @param limit
   * @returns {Promise<{pagination: {pageCount: number, pageSize: number, page: number, rowCount: number}, transactions: (*&{hmac: string})[]}|null>}
   */
  getTransactionsByShopId = async (shopId, { page = 1, limit = 10 }) => {
    try {
      const offset = (page - 1) * limit;
      const { count, rows } = await SubscriptionTransaction.findAndCountAll({
        where: { shop_id: shopId },
        limit,
        offset,
        order: [["created_at", "desc"]],
      });

      return {
        transactions: rows.map((t) => ({ ...t.toJSON(), hmac: createHmac(t.get("id")) })),
        pagination: preparePagination(count, page, limit),
      };
    } catch (err) {
      console.log("error fetching transactions from database: ", err);
      return null;
    }
  };

  /**
   * Get subscription transaction by id
   * @param {number} id
   * @returns {Promise<null|any>}
   */
  getTransactionById = async (id) => {
    const transaction = await SubscriptionTransaction.findByPk(id, { rejectOnEmpty: false });
    return transaction?.toJSON() || null;
  };

  /**
   * Create subscription transaction
   * @param {object} shop
   * @param {Object} request
   * @returns {Promise<any>}
   */
  createTransaction = async (shop, request = undefined, couponCode) => {
    try {
      const subscriptionPlan = shop.plan_info;
      const { narration, transType } = await this.getTransTypeNarration(shop, subscriptionPlan);
      const data = {
        shop_id: shop.id,
        plan_id: subscriptionPlan.id,
        plan_validity: shop.plan_validity,
        plan_rules: shop.plan_rules,
        name: subscriptionPlan.name,
        slug: subscriptionPlan.slug,
        type: subscriptionPlan.type,
        interval: subscriptionPlan.interval,
        price: parseFloat(subscriptionPlan.price).toFixed(2),
        discount: parseFloat(subscriptionPlan.discount).toFixed(2),
        subtotal: parseFloat(subscriptionPlan.subtotal).toFixed(2),
        meta: {},
        request_data: request ? request : null,
        is_paid: subscriptionPlan.type === "FREE" || (request && request.status === "ACTIVE"),
        transaction_type: transType,
        transaction_narration: narration,
        coupon_code: couponCode || "",
      };

      const trans = await SubscriptionTransaction.create(data);
      return trans.toJSON();
    } catch (err) {
      console.log(err);
    }
  };

  /**
   * Get subscription transaction narration
   * @param {object} shop
   * @param {object} subscriptionPlan
   * @returns {Promise<{transType: string, narration: string}>}
   */
  getTransTypeNarration = async (shop, { id, name, price }) => {
    const lastTrans = await this.getLastTransaction(shop.id);

    // if there is no last transaction then it's new
    if (isEmpty(lastTrans)) {
      return {
        narration: `Subscribe to "${name}" plan.`,
        transType: transTypes.NEW,
      };
    }

    // if current plan id and new plan id is same then it's renewal
    if (parseInt(shop.plan_id) === parseInt(id) && lastTrans.price === price) {
      return {
        narration: `Plan "${name}" renewed.`,
        transType: transTypes.RENEWAL,
      };
    }

    if (price > lastTrans.price) {
      return {
        narration: `Plan upgrade from "${lastTrans.name}" to "${name}".`,
        transType: lastTrans.type === FREE ? transTypes.NEW : transTypes.UPGRADE,
      };
    }

    if (price < lastTrans.price) {
      return {
        narration: `Plan downgrade from "${lastTrans.name}" to "${name}".`,
        transType: transTypes.DOWNGRADE,
      };
    }

    return { narration: `Subscribe to "${name}" plan.`, transType: transTypes.RENEWAL };
  };

  /**
   * Count number of transactions in last 24 hours by transaction type
   * @param {"NEW" | "UPGRADE" | "DOWNGRADE"} transactionType
   * @returns {Promise<number>}
   */
  countTransactionsByTransactionType = async (transactionType) => {
    const yesterday = moment().subtract(1, "d").format("YYYY-MM-DD");

    return SubscriptionTransaction.count({
      where: {
        transaction_type: transactionType,
        type: {
          [Op.ne]: transactionType === "DOWNGRADE" ? null : FREE,
        },
        created_at: {
          [Op.between]: [`${yesterday} 00:00:00`, `${yesterday} 23:59:59`],
        },
      },
    });
  };

  /**
   * Count number of FREE transactions of new stores in last 24 hours
   * @returns {Promise<number>}
   */
  countFreeTransactions = async () => {
    const yesterday = moment().subtract(1, "d").format("YYYY-MM-DD");

    const result = await sequelize.query(
      `
      SELECT COUNT(id) FROM subscription_transactions s
      WHERE 
        type = 'FREE' AND 
        transaction_type = 'NEW' AND 
        created_at BETWEEN :yesterdayStart AND :yesterdayEnd AND
        NOT EXISTS (
          SELECT * FROM subscription_transactions s1 WHERE s.shop_id = s1.shop_id AND s1."type" != 'FREE' AND created_at BETWEEN :yesterdayStart AND :yesterdayEnd
        )
    `,
      {
        replacements: {
          yesterdayStart: `${yesterday} 00:00:00`,
          yesterdayEnd: `${yesterday} 23:59:59`,
        },
        type: QueryTypes.SELECT,
      }
    );

    return result[0].count;
  };
}

module.exports = new SubscriptionTransactionService();
