const { Partner } = require("../../sequelize");
const { ACTIVE } = require("storeseo-enums/partnerStatus");
const { pick } = require("lodash");

class PartnerService {
  /**
   * @function getPartnersList
   * @returns {Promise<count:number, partners: Array<Object>}
   *
   */
  getPartnersList = async () => {
    const { count, rows } = await Partner.findAndCountAll({
      where: { status: ACTIVE },
      order: [["order", "asc"]],
    });
    const partners = rows?.map((partner) => partner.toJSON());
    return { count, partners };
  };
}

module.exports = new PartnerService();
