const { User } = require("../../sequelize");

class UserService {
  getUsers = async () => {
    const users = await User.findAll();
    return users.map((u) => u.toJSON());
  };

  storeUser = async (data) => {
    const user = await User.create(data);
    return user.toJSON();
  };

  findUser = async (userId) => {
    const user = await User.findByPk(userId);
    return user.toJSON();
  };

  isUserExist = async (email) => {
    const userCount = await User.count({ where: { email } });
    return userCount > 0;
  };

  getUser = async (conditions) => {
    const user = await User.findOne({ where: conditions });
    return user.toJSON();
  };

  sanitizeUserData = (userData) => {
    return { ...userData, accessToken: undefined };
  };
}

module.exports = new UserService();
