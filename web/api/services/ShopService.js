const { Op, QueryTypes } = require("sequelize");
const { Shop, ShopSetting, sequelize } = require("../../sequelize");
const ProductService = require("./ProductService");
const ShopifyService = require("./ShopifyService");
const FileService = require("./FileService");
const { serializeShopifyShopData } = require("../serializers/ShopSerializer");
const cache = require("../cache");
const { rejectOnEmpty } = require("../config/sequelize");
const moment = require("moment");
const { ACTIVE, UNINSTALLED } = require("storeseo-enums/shopStatus");
const settingKeys = require("storeseo-enums/settingKeys");
const SEOService = require("./SEOService");
const googleIntegrationInfo = require("storeseo-enums/googleIntegrationInfo");
const GoogleAuthService = require("./GoogleAuthService");
const { METAFIELD_KEYS } = require("storeseo-enums/metafields");
const { defaultOnboardingSteps } = require("../config/defaultValues");

class ShopService {
  /**
   *
   * @param {import("sequelize").WhereOptions} conditions
   */
  iterateOverAllShops = async function* (conditions = {}, fields = []) {
    let limit = 30;
    let offset = 0;
    let shops = [1];

    while (shops.length > 0) {
      shops = await Shop.findAll({
        where: conditions,
        limit,
        offset,
        attributes: fields.length > 0 ? fields : undefined,
      });

      offset += limit;

      yield shops.map((s) => s.toJSON());
    }
  };

  /**
   * Read or update the boolean flag indicating api rate limit status when accessing third party APIs.
   * @param {*} shop
   * @param {import("storeseo-enums/cacheKeys").apiNames} api name of the API
   * @param {boolean} [status] leave it empty to read the current flag status, provide value to update the flag
   * @param {number} [period] for how long the rate limit flag will be active (in seconds)
   */
  apiRateLimitExceeded = async (shop, api, status, period) => cache.apiRateLimitExceeded(shop, api, status, period);

  /**
   * Count the number of new shop installs in last `24` hours
   * @returns {Promise<number>}
   */
  numberOfNewInstalls = async () => {
    const yesterday = moment().subtract(1, "d").format("YYYY-MM-DD");

    return Shop.count({
      where: {
        created_at: {
          [Op.between]: [`${yesterday} 00:00:00`, `${yesterday} 23:59:59`],
        },
      },
    });
  };

  /**
   * Shops that were installed in last`24` hours & `upgraded` to Pro plan
   * @returns {Promise<{ id: string, domain: string}[]>}
   */
  shopsWithPremiumSubscriptionOnTheSameDayOfInstall = async () => {
    const yesterday = moment().subtract(1, "d").format("YYYY-MM-DD");

    return sequelize.query(
      `
      SELECT * FROM shops s
      WHERE 
        created_at BETWEEN :yesterdayStart AND :yesterdayEnd AND
        EXISTS (
          SELECT type FROM subscription_transactions s1
          WHERE s.id = s1.shop_id AND
          s1.type != 'FREE' AND
          created_at BETWEEN :yesterdayStart AND :yesterdayEnd
        )
    `,
      {
        type: QueryTypes.SELECT,
        replacements: {
          yesterdayStart: `${yesterday} 00:00:00`,
          yesterdayEnd: `${yesterday} 23:59:59`,
        },
      }
    );
  };

  /**
   * Save the shopify shop data to database & subscribe to free package
   * @param {$ObjMap} session
   * @param {$ObjMap} shopifyShop
   * @returns {Promise<any>}
   */
  storeShopWithFreeSubscription = async (session, shopifyShop) => {
    try {
      const shop = await this.storeShop(session, shopifyShop);
      const SubscriptionService = require("./SubscriptionService");
      await SubscriptionService.subscribeToFreePlan({
        shopId: shop.id,
      });
      return shop;
    } catch (err) {
      return err;
    }
  };

  /**
   *
   * @param {number} shopId
   * @param {object} billing_address
   */
  upsertBillingAddress = async (shopId, billing_address) => {
    const data = {
      shop_id: shopId,
      key: settingKeys.BILLING_ADDRESS,
      value_type: "json",
      value: JSON.stringify(billing_address),
    };

    return await this.updateShopSetting(shopId, data);
  };

  /**
   *
   * @param {number} shopId
   * @param {object} jsonldSetting
   */
  upsertJsonldSetting = async (shopId, jsonldSetting) => {
    const data = {
      shop_id: shopId,
      key: settingKeys.JSONLD_DATA,
      value_type: "json",
      value: JSON.stringify(jsonldSetting),
    };

    return await this.updateShopSetting(shopId, data);
  };

  /**
   *
   * @param {number} shopId
   * @param {object} integration_setting
   */
  upsertGoogleIntegrationSetting = async (shopId, integration_setting = googleIntegrationInfo) => {
    const data = {
      shop_id: shopId,
      key: settingKeys.GOOGLE_INTEGRATION_INFO,
      value_type: "json",
      value: JSON.stringify(integration_setting),
    };

    return await this.updateShopSetting(shopId, data);
  };

  /**
   *
   * @param {number} shopId
   * @param {object} data
   */
  upsertMailchimpData = async (shopId, data) => {
    const settingData = {
      shop_id: shopId,
      key: settingKeys.MAILCHIMP,
      value_type: "json",
      value: JSON.stringify(data),
    };

    return await this.updateShopSetting(shopId, settingData);
  };

  /**
   *
   * @param {number} shopId
   * @param {object} data
   */
  upsertMetaData = async (shopId, data = {}) => {
    const settingData = {
      shop_id: shopId,
      key: settingKeys.META,
      value_type: "json",
      value: JSON.stringify(data),
    };

    return await this.updateShopSetting(shopId, settingData);
  };

  /**
   * Save the shopify shop data to database & subscribe to free package
   * @param {$ObjMap} session
   * @param {$ObjMap} shopifyShop
   * @returns {Promise<any>}
   */
  storeShop = async (shopifyShop) => {
    const { billing_address, meta, ...shopData } = serializeShopifyShopData(shopifyShop);
    const shop = await this.createNewShop(shopData);

    await this.upsertBillingAddress(shop.id, billing_address);
    await this.upsertMetaData(shop.id, meta);
    await this.upsertGoogleIntegrationSetting(shop.id);

    return { ...shop, billing_address };
  };

  /**
   * Create new shop to database
   * @param {$ObjMap} shopifyShop
   * @returns {Promise<any>}
   */
  createNewShop = async (shopData) => {
    const logoPath = (await FileService.generatePlaceholderImageForShop(shopData)).url;
    const [shop] = await Shop.upsert({
      ...shopData,
      logo_path: logoPath,
      status: ACTIVE,
    });

    return shop.toJSON();
  };

  /**
   * Check a shop is exists or not
   * @param conditions
   * @returns {Promise<boolean>}
   */
  hasShop = async (conditions) => {
    return (await Shop.count({ where: conditions })) > 0;
  };

  /**
   * Get a shop by domain
   * @param {string} shopDomain
   * @param {string[]=} fields
   * @returns {Promise<boolean|any>}
   */
  getShop = async (shopDomain, fields = undefined) => {
    try {
      const shop = await Shop.findOne({
        attributes: fields,
        where: { domain: shopDomain },
        rejectOnEmpty,
      });
      return shop.toJSON();
    } catch (e) {
      // console.error(e);
      return null;
    }
  };

  /**
   * Get a shop by domain
   * @param {{ where: import("sequelize").WhereOptions, fields?: string[] }}
   * @returns {Promise<boolean|any>}
   */
  getShopIncludingAssociations = async ({ where, fields = undefined }) => {
    try {
      let shop = await Shop.findOne({
        attributes: fields,
        where,
        include: [
          {
            model: ShopSetting,
            as: "jsonld_setting",
            where: { key: settingKeys.JSONLD_DATA },
            attributes: ["value"],
            required: false,
          },
          {
            model: ShopSetting,
            as: "meta_templates",
            where: { key: settingKeys.META_TEMPLATES },
            attributes: ["value"],
            required: false,
          },
          {
            model: ShopSetting,
            as: "billing_address_data",
            where: { key: settingKeys.BILLING_ADDRESS },
            attributes: ["value"],
            required: false,
          },
          {
            model: ShopSetting,
            as: "google_integration_setting",
            where: { key: settingKeys.GOOGLE_INTEGRATION_INFO },
            attributes: ["value"],
            required: false,
          },
          {
            model: ShopSetting,
            as: "mailchimp_data",
            where: { key: settingKeys.MAILCHIMP },
            attributes: ["value"],
            required: false,
          },
          {
            model: ShopSetting,
            as: "meta_data",
            where: { key: settingKeys.META },
            attributes: ["value"],
            required: false,
          },
          {
            model: ShopSetting,
            as: "onboarding_steps",
            where: { key: settingKeys.ONBOARD_STEPS },
            attributes: ["value"],
            required: false,
          },
        ],
        rejectOnEmpty,
      });

      shop = shop.toJSON();

      return {
        ...shop,
        jsonld_data: JSON.parse(shop.jsonld_setting?.value || null),
        meta_templates: JSON.parse(shop.meta_templates?.value || null),
        billing_address: JSON.parse(shop.billing_address_data?.value || null),
        google_integration_info: JSON.parse(shop.google_integration_setting?.value || null),
        mailchimp: JSON.parse(shop.mailchimp_data?.value || null),
        meta: JSON.parse(shop.meta_data?.value || null),
      };
    } catch (e) {
      console.log(e);
      return null;
    }
  };

  /**
   * Deletes a shop by domain
   * @param shopDomain
   * @returns {Promise<boolean|number>}
   */
  deleteShop = async (shopDomain) => {
    try {
      return await Shop.destroy({ where: { domain: shopDomain } });
    } catch (e) {
      console.log(e);
    }
    return false;
  };

  /**
   * Deletes a shop settings by shop_id
   * @param {number|string} shopId
   * @returns {Promise<boolean|number>}
   */
  deleteShopSettings = async (shopId) => {
    try {
      return await ShopSetting.destroy({ where: { shop_id: shopId } });
    } catch (e) {
      console.log(e);
    }
    return false;
  };

  /**
   * Get all shops | all shop in a plan
   * @param planId
   * @returns {Promise<*[]|*>}
   */
  getAllShop = async (planId = null) => {
    try {
      const shops = await Shop.findAll({
        attributes: ["id", "name", "shop_id", "url", "email", "plan_id"],
        where: planId ? { plan_id: planId } : undefined,
      });
      return shops.map((s) => s.toJSON());
    } catch (e) {
      return [];
    }
  };

  /**
   * Get all pro shops
   * @param {Array} fields
   * @returns {Promise<U[]|*[]>}
   */
  getAllProShop = async (fields = null) => {
    try {
      const shops = await Shop.findAll({
        attributes: fields || undefined,
        where: {
          plan_id: { [Op.gt]: 1 },
        },
      });
      return shops.map((s) => s.toJSON());
    } catch (e) {
      return [];
    }
  };

  /**
   * Get a shop by url
   * @param shopUrl
   * @returns {Promise<boolean|any>}
   */
  getShopByUrl = async (shopUrl) => {
    try {
      const shop = await Shop.findOne({ where: { url: shopUrl }, rejectOnEmpty });
      return shop.toJSON();
    } catch (e) {
      // console.log(e);
      return false;
    }
  };

  /**
   * Get a shop by url
   * @param {object} condition
   * @returns {Promise<boolean|any>}
   */
  getShopByCondition = async (condition) => {
    try {
      const shop = await Shop.findOne({ where: { ...condition }, rejectOnEmpty });
      return shop.toJSON();
    } catch (e) {
      // console.log(e);
      return false;
    }
  };

  /**
   * Get a shop by url
   * @param {object} condition
   * @returns {Promise<boolean|any>}
   */
  getAllShopByCondition = async (condition) => {
    try {
      const shops = await Shop.findAll({ where: { ...condition }, rejectOnEmpty });
      return shops.map((s) => s.toJSON());
    } catch (e) {
      // console.log(e);
      return [];
    }
  };

  /**
   * Get a shop by token
   * @param token
   * @returns {Promise<boolean|any>}
   */
  getShopByToken = async (token) => {
    try {
      const shop = await Shop.findOne({ where: { access_token: token }, rejectOnEmpty });
      return shop.toJSON();
    } catch (e) {
      // console.log(e);
    }
    return false;
  };

  /**
   * Get a shop by id
   * @param shopId
   * @param {import("sequelize").FindOptions} [options]
   * @returns {Promise<boolean|any>}
   */
  getShopById = async (shopId, options = {}) => {
    try {
      const where = { id: shopId, status: ACTIVE };
      if (options.where) {
        options.where = {
          ...options.where,
          ...where,
        };
      } else options.where = where;

      const shop = await Shop.findOne({
        ...options,
        rejectOnEmpty,
      });
      return shop.toJSON();
    } catch (e) {
      console.error(e);
      return false;
    }
  };

  /**
   * Get a shop by graphql id
   * @param gqlId
   * @returns {Promise<boolean|any>}
   */
  getShopByGqlId = async (gqlId) => {
    try {
      const shop = await Shop.findOne({ where: { shop_id: gqlId }, rejectOnEmpty });
      return shop.toJSON();
    } catch (e) {
      // console.log(e);
    }
    return false;
  };

  /**
   * Update a shop data by primary id
   * @param shopId
   * @param data
   * @returns {Promise<*>}
   */
  updateShop = async (shopId, data) => {
    const [affectedRows, shops] = await Shop.update(data, { where: { id: shopId }, returning: true });

    if (affectedRows === 0) {
      return [];
    }

    if (affectedRows === 1) {
      return shops[0].toJSON();
    }

    return shops.map((shop) => shop.toJSON());
  };

  /**
   * Update shop/s data by condition
   * @param conditions
   * @param data
   * @returns {Promise<*>}
   */
  updateShopByCondition = async (conditions, data) => {
    const [affectedRows, shops] = await Shop.update(data, {
      where: conditions,
      returning: true,
    });

    if (affectedRows === 1) {
      return shops[0].toJSON();
    }

    return shops.map((shop) => shop.toJSON());
  };

  /**
   * Updates shop settings
   * @param shopId
   * @param data
   * @returns {Promise<any>}
   */
  updateShopSetting = async (shopId, data) => {
    data.shop_id = shopId;
    data.updated_at = new Date();
    const [setting, created] = await ShopSetting.upsert(data, { conflictFields: ["shop_id", "key"] });
    return setting.toJSON();
  };

  /**
   *
   * @param {any} session
   * @param shopId
   * @param {any} value
   * @param {string} key
   * @description Update shop local SEO schema settings on shop settings database and update the metafield on shopify
   */
  updateShopLocalSEOSchema = async (session, shopId, value, key) => {
    const where = { shop_id: shopId, key };
    const hasThisSetting = await ShopSetting.findOne({ attributes: ["id"], where, rejectOnEmpty: false });

    const settings = {
      key,
      value: JSON.stringify(value),
      value_type: "json",
      shop_id: shopId,
      updated_at: new Date(),
    };

    if (hasThisSetting) {
      settings.id = hasThisSetting.id;
    }

    await ShopSetting.upsert(settings);

    const shop = await this.getShopById(shopId);
    await ShopifyService.setMetafield(session.shop, {
      ownerId: shop.shop_id,
      key: METAFIELD_KEYS[key],
      value: JSON.stringify(value),
    });
  };

  /**
   * Get shop setting by key
   * @param shopId
   * @param key
   * @returns {Promise<null|*>}
   */
  getShopSetting = async (shopId, key) => {
    try {
      const where = { shop_id: shopId, key };
      const setting = await ShopSetting.findOne({ where, rejectOnEmpty: false });
      return setting.toJSON();
    } catch (e) {
      // console.log(e);
      return null;
    }
  };

  /**
   *
   * @param {number} shopId
   * @param {string} key
   * @returns {Promise<boolean>}
   */
  hasShopSetting = async (shopId, key) => {
    try {
      const setting = await ShopSetting.findOne({
        where: { shop_id: shopId, key },
        rejectOnEmpty: true,
      });
      return !!setting;
    } catch (e) {
      return false;
    }
  };

  /**
   *
   * @param {number} shopId
   * @returns {Promise<any[]>}
   */
  getShopSettings = async (shopId) => {
    const settings = await ShopSetting.findAll({ where: { shop_id: shopId } });
    return settings?.map((s) => s.toJSON());
  };

  getOnboardingSteps = async (shopId) => {
    try {
      const { value } = await this.getShopSetting(shopId, settingKeys.ONBOARD_STEPS);
      return value;
    } catch (error) {
      return defaultOnboardingSteps;
    }
  };

  /**
   *
   * @param {number} shopId
   */
  generateDefaultJsonldSettings = async (shopId) => {
    const shop = await this.getShopById(shopId, {
      include: [
        {
          model: ShopSetting,
          as: "billing_address_data",
          where: { key: settingKeys.BILLING_ADDRESS },
          attributes: ["value"],
          required: false,
        },
      ],
    });

    shop.billing_address = JSON.parse(shop?.billing_address_data?.value || null);

    const jsonldData = await SEOService.generateDefaultLocalSEOData(shop);

    return this.upsertJsonldSetting(shopId, jsonldData);
  };

  /**
   *
   * @param {shopId: number} shopId
   */
  getJsonldSettings = async (shopId) => {
    const setting = await this.getShopSetting(shopId, settingKeys.JSONLD_DATA);
    if (setting) return setting.value;

    const { value: generatedSetting } = await this.generateDefaultJsonldSettings(shopId);
    return generatedSetting;
  };

  /**
   * Get shop sitemap related data
   * @param shopId
   * @returns {Promise<any>}
   */
  getShopSitemapData = async (shopId) => {
    const shop = await Shop.findByPk(shopId, {
      attributes: ["is_sitemap_submitted", "sitemap_submitted_at"],
      rejectOnEmpty,
    });

    const { value: google_integration_info } = await this.getShopSetting(shopId, settingKeys.GOOGLE_INTEGRATION_INFO);

    return { ...shop.toJSON(), google_integration_info };
  };

  /**
   * Update total product count in shop
   * @param shopId
   * @returns {Promise<*>}
   */
  updateTotalProductCount = async (shopId) => {
    const productCount = await ProductService.countProducts(shopId);
    return await this.updateShop(shopId, { total_products: productCount });
  };

  /**
   * Retrieve & update current theme id
   * @param shopDomain
   * @returns {Promise<null|*>}
   */
  retrieveCurrentThemeId = async (shopDomain) => {
    try {
      const { id, access_token: accessToken } = await this.getShop(shopDomain);
      const currentTheme = await ShopifyService.onlineStore.getCurrentTheme(shopDomain);

      const currentThemeId = currentTheme?.id;
      const data = { theme_id: currentThemeId };
      await this.updateShop(id, data);

      return currentThemeId;
    } catch (err) {
      console.error(`Error retrieving current theme id for shop ${shopDomain}: ${JSON.stringify(err)}`);
      return null;
    }
  };

  /**
   * Updates google integration user information to db
   * @param shopDomain
   * @param authenticatedUser
   * @returns {Promise<boolean|*>}
   */
  updateGoogleIntegrationUserInfo = async (shopDomain, authenticatedUser) => {
    try {
      const shop = await this.getShop(shopDomain);
      const { value: googleIntegrationInfo = {} } =
        (await this.getShopSetting(shop.id, settingKeys.GOOGLE_INTEGRATION_INFO)) || {};

      googleIntegrationInfo.googleUserEmail = authenticatedUser.email;
      googleIntegrationInfo.grantedScopes = authenticatedUser?.scopes || [];

      return await this.upsertGoogleIntegrationSetting(shop?.id, googleIntegrationInfo);
    } catch (err) {
      console.error(
        `Failed to update user info for google integration for shop ${shopDomain}. AuthenticatedUser: ${JSON.stringify(
          authenticatedUser
        )}. Error: ${err}`
      );
      return false;
    }
  };

  /**
   * Updates google integration status to db
   * @param {string} shopDomain
   * @param {object} updatedStatus
   * @returns {Promise<boolean>}
   */
  updateGoogleIntegrationStatus = async (shopDomain, updatedStatus) => {
    try {
      const shop = await this.getShop(shopDomain);
      const { value: google_integration_info } = await this.getShopSetting(
        shop.id,
        settingKeys.GOOGLE_INTEGRATION_INFO
      );
      const updatedData = this.#prepareGoogleIntegrationStatus(google_integration_info, updatedStatus);

      await this.upsertGoogleIntegrationSetting(shop.id, updatedData);
      return true;
    } catch (err) {
      console.error(`Failed to update site verification status for shop ${shopDomain}. Error: ${err}`);
      return false;
    }
  };

  /**
   * Updates google sitemap submit status to db
   * @param {string} shopDomain
   * @param {object} updatedStatus
   * @returns {Promise<boolean|*>}
   */
  updateSitemapSubmitStatus = async (shopDomain, updatedStatus) => {
    try {
      const shop = await this.getShop(shopDomain);
      const { value: google_integration_info } = await this.getShopSetting(
        shop.id,
        settingKeys.GOOGLE_INTEGRATION_INFO
      );
      const updatedIntegrationData = this.#prepareGoogleIntegrationStatus(google_integration_info, updatedStatus);

      const data = {
        is_sitemap_submitted: updatedStatus.sitemapSubmitted,
        sitemap_submitted_at: new Date().toISOString(),
      };

      await this.upsertGoogleIntegrationSetting(shop.id, updatedIntegrationData);
      await this.updateShop(shop?.id, data);

      return { ...shop, ...data, google_integration_info: updatedIntegrationData };
    } catch (err) {
      console.error(`Failed to update site verification status for shop ${shopDomain}. Error: ${err}`);
      return false;
    }
  };

  /**
   * @param {Object} googleIntegrationInfo
   * @param {Object} updatedStatus
   * @returns {*|{}}
   */
  #prepareGoogleIntegrationStatus = (googleIntegrationInfo = {}, updatedStatus) => {
    googleIntegrationInfo.steps = googleIntegrationInfo.steps
      ? { ...googleIntegrationInfo.steps, ...updatedStatus }
      : { updatedStatus };
    return googleIntegrationInfo;
  };

  /**
   * Get a shop by graphql id
   * @param gqlId
   * @returns {Promise<boolean|any>}
   */
  getShopDataForWebhook = async (gqlId) => {
    try {
      const shop = await Shop.findOne({
        attributes: [
          "id",
          "name",
          "domain",
          "url",
          "access_token",
          "plan_id",
          "plan_info",
          "plan_rules",
          "appSubscriptionId",
          "appSubscriptionData",
        ],
        where: { shop_id: gqlId },
        // include: [
        //   {
        //     model: ShopSetting,
        //     as: "meta_data",
        //     where: { key: settingKeys.META },
        //     attributes: ["value"],
        //     required: false,
        //   },
        // ],
        rejectOnEmpty,
      });

      return shop.toJSON();
      // return { ...shop.toJSON(), meta: JSON.parse(shop.meta_data?.value || null) };
    } catch (e) {
      // console.log(e);
    }
    return false;
  };

  markShopAsUninstalled = async (shopDomain) => {
    const updateData = {
      access_token: null,
      plan_id: null,
      // plan_info: null,
      plan_validity: null,
      plan_rules: null,
      plan_status: null,
      // onboard_step: 1,
      appSubscriptionId: null,
      appSubscriptionData: null,
      status: UNINSTALLED,
    };

    return await this.updateShopByCondition({ domain: shopDomain }, updateData);
  };

  getShopId = async (shopDomain) => {
    try {
      const shop = await Shop.findOne({
        attributes: ["id"],
        where: { domain: shopDomain },
        rejectOnEmpty,
      });
      return shop.id;
    } catch (e) {
      return null;
    }
  };

  getActiveShops = async () => {
    try {
      const shops = await Shop.findAll({
        attributes: ["id", "name", "shop_id", "url", "email", "plan_id", "plan_info", "access_token", "domain"],
        where: { status: ACTIVE },
        order: [["id", "desc"]],
        include: [
          {
            model: ShopSetting,
            as: "mailchimp_data",
            where: { key: settingKeys.MAILCHIMP },
            attributes: ["value"],
            required: false,
          },
        ],
      });

      return shops.map((s) => ({
        ...s.toJSON(),
        mailchimp: s.mailchimp_data ? JSON.parse(s.mailchimp_data?.value) : null,
      }));
    } catch (e) {
      return [];
    }
  };

  getActiveShopDomains = async () => {
    try {
      const shops = await Shop.findAll({
        attributes: ["id", "domain", "access_token", "plan_id"],
        where: { status: ACTIVE },
        order: [["id", "desc"]],
      });

      return shops.map((s) => ({
        ...s.toJSON(),
      }));
    } catch (e) {
      return [];
    }
  };

  getGoogleIntegrationInfoWithAuthUserDetails = async (shopId) => {
    const { value: googleIntegrationInfo } =
      (await this.getShopSetting(shopId, settingKeys.GOOGLE_INTEGRATION_INFO)) || {};
    if (googleIntegrationInfo) {
      const user = await GoogleAuthService.getAuthenticatedUser(googleIntegrationInfo.googleUserEmail);
      return {
        ...googleIntegrationInfo,
        ...user,
      };
    }

    return null;
  };

  getSession = async (conditions) => {
    const shop = await Shop.findOne({
      attributes: [
        ["id", "shopId"],
        ["domain", "shop"],
        ["access_token", "accessToken"],
      ],
      where: conditions,
      rejectOnEmpty: true,
    });
    return shop.toJSON();
  };

  getExpiredShops = async () => {
    const shops = await Shop.findAll({
      attributes: [
        "id",
        "shop_id",
        "name",
        "plan_id",
        "domain",
        "access_token",
        "plan_validity",
        "plan_status",
        "status",
      ],
      where: {
        plan_validity: {
          [Op.lte]: moment().startOf("day"),
        },
      },

      include: {
        as: "plan",
      },
    });

    return shops.map((s) => s.toJSON());
  };
}

module.exports = new ShopService();
