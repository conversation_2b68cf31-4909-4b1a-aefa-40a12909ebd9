const ShopifyService = require("../services/ShopifyService");
const boStatus = require("storeseo-enums/bulkOperationStatus");
const logger = require("storeseo-logger");

const { BulkOperation, Op } = require("../../sequelize");

/**
 * @typedef {import("../../jsDocTypes").BulkOperationDetails} BulkOperationDetails
 */

class BulkOperationService {
  /**
   *
   * @param {{ shopId: number, session: Session, operationType: keyof import("storeseo-enums/bulkOperationTypes"), limit?: number}} param0
   * @returns {Promise<*>}
   */
  startBulkQueryOperation = async ({ shopId, session, operationType, limit = null }) => {
    try {
      const bulkOperation = await ShopifyService.bulkProductRequest(session.shop, limit);
      const data = this.#serializeProductBulkOperationData(shopId, operationType, bulkOperation);
      return await this.saveBulkOperationData(data);
    } catch (err) {
      logger.error(err);
      throw Error("Bulk operation failed.");
    }
  };

  /**
   *
   * @param {number} shopId
   * @param {keyof import("storeseo-enums/bulkOperationTypes")} operationType
   * @param {*} bulkOperation
   * @returns
   */
  #serializeProductBulkOperationData = (shopId, operationType, bulkOperation) => {
    return {
      shop_id: shopId,
      gql_id: bulkOperation.id,
      op_type: operationType,
      status: bulkOperation.status,
      type: bulkOperation.type,
      url: bulkOperation.url,
      partial_url: bulkOperation.partialDataUrl,
      completed_at: bulkOperation.completedAt,
    };
  };

  /**
   * Insert bulk operation details in DB
   * @param {BulkOperationDetails} data
   * @returns {Promise<BulkOperationDetails>}
   */
  saveBulkOperationData = async (data) => {
    const res = await BulkOperation.create(data);
    return res.toJSON();
  };

  /**
   * Retrieve bulk operation details from DB
   * @param {import("sequelize").WhereAttributeHash} conditions
   * @returns {Promise<BulkOperationDetails | null >}
   */
  getBulkOperationDetails = async (conditions) => {
    try {
      const res = await BulkOperation.findOne({ where: conditions });
      return res?.toJSON() || null;
    } catch (err) {
      console.log(err);
      return null;
    }
  };

  /**
   * Get total count of a bulk operation
   * @param {import("sequelize").WhereAttributeHash} conditions
   * @returns {number}
   */
  getBulkOperationTotal = async (conditions) => {
    const res = await BulkOperation.findOne({ attributes: ["total"], where: conditions });

    return res.toJSON().total || 0;
  };

  /**
   * Get total count of a bulk operation
   * @param {import("sequelize").WhereAttributeHash} conditions
   * @param {*} data
   * @returns {Promise<*>}
   */
  updateBulkOperationByCondition = async (condition, data) => {
    const [affectedRows, bulkOperations] = await BulkOperation.update(data, {
      where: condition,
      returning: true,
    });

    return bulkOperations[0]?.toJSON();
  };

  /**
   * Insert/update existing bulk operation info in DB
   * @param {number} shopId relevant database id of the shop
   * @param {BulkOperationDetails} bulkOperationData
   * @returns {Promise<BulkOperationDetails>}
   */
  upsertBulkOperationData = async (shopId, bulkOperationData) => {
    const dbBO = await this.getBulkOperationDetails({ shop_id: shopId, gql_id: bulkOperationData.gql_id });
    if (dbBO) return this.updateBulkOperationData(dbBO.id, bulkOperationData);
    else return this.saveBulkOperationData(bulkOperationData);
  };

  /**
   * Update Existing bulk operation data
   * @param {number} bulkOperationId database id of the relevant bulk operation
   * @param {BulkOperationDetails} bulkOperationData
   * @returns {Promise<BulkOperationDetails>}
   */
  updateBulkOperationData = async (bulkOperationId, bulkOperationData) => {
    const [affectedRows, res] = await BulkOperation.update(bulkOperationData, {
      where: { id: bulkOperationId },
      returning: true,
    });
    return res[0].toJSON();
  };

  /**
   * Check running bulk operation status for a shop
   * @param {number} shopId database id of the relevant shop
   * @param {keyof import("storeseo-enums/bulkOperationTypes")} operationType
   * @returns {Promise<boolean>}
   */
  hasRunningBulkOperation = async (shopId, operationType) => {
    const { count } = await BulkOperation.findAndCountAll({
      where: { shop_id: shopId, status: { [Op.not]: boStatus.COMPLETED }, op_type: operationType },
    });

    return count > 0;
  };

  /**
   *
   * @param {object} session
   * @param {{shopId: number, stagedTarget: object, mutation: string, opType: string}} options
   * @returns {Promise<any>}
   */
  processFileAndStartBulkMutation = async (session, { shopId, stagedTarget, mutation, opType }) => {
    const stagedUploadPath = stagedTarget.parameters.find((p) => p.name === "key").value;
    // console.info("stagedUploadPath: ", stagedUploadPath);
    const bulkOp = await ShopifyService.startBulkOperationMutation(session.shop, stagedUploadPath, mutation);
    // console.info("\n---");
    // console.info("bulkOperation: ", bulkOp);
    // console.info("---\n");
    await this.saveBulkOperationData({
      shop_id: shopId,
      gql_id: bulkOp.id,
      op_type: opType,
      status: bulkOp.status,
      type: bulkOp.type,
      url: bulkOp.url,
    });
    return bulkOp;
  };
}

module.exports = new BulkOperationService();
