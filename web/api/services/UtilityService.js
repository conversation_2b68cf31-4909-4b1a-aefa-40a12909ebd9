const { crawler } = require("../utils/helper");
const experimentalFeatureSanitizer = require("../utils/experimentalFeatureSanitizer");

class UtilityService {
  listOfInstalledSeoApps = async (domain, url) => {
    const {
      data: { installedApps },
    } = await crawler().get(`/seo/apps`, {
      params: {
        url,
      },
    });
    // console.log("installedApps: ", installedApps);
    const sanitizedAppList = experimentalFeatureSanitizer.restrictInstalledAppsList(installedApps, domain);

    return sanitizedAppList;
  };
}

module.exports = new UtilityService();
