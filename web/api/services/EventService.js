const pusher = require("../pusher/serverClient");
const socketEvents = require("storeseo-enums/socketEvents");
const /* `backupStatus` is likely an enum object that contains predefined values for the status of a
backup process. It is being imported from the "storeseo-enums" module and used in the
`backupAndRestoreDatabaseSchema` schema to ensure that the `backup_status` field can only have
one of the predefined values specified in the `backupStatus` enum. This helps in enforcing
data integrity and ensuring that only valid backup status values are allowed in the schema. */
  ShopService = require("./ShopService");
const NotificationService = require("./NotificationService");
const cache = require("../cache");
const ArticleService = require("./ArticleService");
const CollectionEventService = require("./collections/CollectionEventService");
const DocEventService = require("./docs/DocEventService");
const BackupService = require("./BackupService");
const { dispatchQueue } = require("../queue/queueDispatcher");
const { QUEUE_NAMES } = require("../queue/config");
const eventTopics = require("storeseo-enums/eventTopics");
const { EMAIL_NOTIFICATION } = require("storeseo-enums/settings/email-notification");
const { defaultEmailNotificationSettings } = require("../config/email-notification");

/**
 * Handle product sync complete event by updating/resetting flags & sending push notifications
 * @param {import("../../jsDocTypes").ProductSyncCompleteEventDetails} eventDetails
 */
const handleProductSyncComplete = async (eventDetails) => {
  const { shop, deleteSyncCursor, total } = eventDetails;

  // cleanup necessary flags
  await cache.product.syncOngoing(shop, false);
  // if (deleteSyncCursor) await ProductService.deleteSyncCursor(shop);

  // create a notification entry for the event
  const shopDetails = await ShopService.getShop(shop);
  const notification = await NotificationService.createNotification({
    shop_id: shopDetails.id,
    title: "Product sync complete!",
    message: `Total ${total} products have been synced. You can now see & fix SEO issues for all of them inside StoreSEO.`,
  });
  const unreadNotifications = await NotificationService.countUnread(shopDetails.id);

  // send pusher event
  console.log("\n---\ntriggered product sync complete event for: ", shop, "\n---\n");
  pusher.trigger(shop, socketEvents.PRODUCT_SYNC_COMPLETE, eventDetails);
  pusher.trigger(shop, socketEvents.NEW_NOTIFICATION, { shop, notification, unreadNotifications });

  // check if email notification for product sync is enabled and trigger queue
  let emailNotificationSettings = await ShopService.getShopSetting(shopDetails.id, EMAIL_NOTIFICATION);
  if (!emailNotificationSettings) {
    emailNotificationSettings = await ShopService.updateShopSetting(shopDetails.id, defaultEmailNotificationSettings);
  }

  const settingsItems = emailNotificationSettings.value?.items ?? {};
  const productSyncNotificationSettings = settingsItems[eventTopics.PRODUCT_SYNC_COMPLETE] ?? null;

  if (productSyncNotificationSettings?.enabled) {
    dispatchQueue({
      queueName: QUEUE_NAMES.GENERATE_EMAIL_QUEUE,
      message: {
        shopDomain: shopDetails.domain,
        topic: eventTopics.PRODUCT_SYNC_COMPLETE,
        deliveryDate: new Date(),
      },
    });
  } else {
    console.log(`----------${eventTopics.PRODUCT_SYNC_COMPLETE} Notification Settings Disabled-------------`);
  }
};

/**
 * Handle product sync update event
 * @param {import("../../jsDocTypes").ProductSyncUpdateEventDetails} eventDetails
 */
const handleProductSyncUpdate = async (eventDetails) => {
  pusher.trigger(eventDetails.shop, socketEvents.PRODUCT_SYNC_UPDATE, eventDetails);
};

/**
 * Handle page sync update event
 * @param {import("../../jsDocTypes").PageSyncUpdateEventDetails} eventDetails
 */
const handlePageSyncUpdate = async (eventDetails) => {
  pusher.trigger(eventDetails.shop, socketEvents.PAGE_SYNC_UPDATE, eventDetails);
};

/**
 * Handle page sync complete event
 * @param {import("../../jsDocTypes").PageSyncCompleteEventDetails} eventDetails
 */
const handlePageSyncComplete = async (eventDetails) => {
  const { shop, total } = eventDetails;

  // cleanup related flags
  await cache.pageSyncOngoing(shop, false);

  // create a notification entry for the event
  const shopDetails = await ShopService.getShop(shop);
  const notification = await NotificationService.createNotification({
    shop_id: shopDetails.id,
    title: "Pages sync complete!",
    message: `Total ${total} pages have been synced. You can now see & fix SEO issues for all of them inside StoreSEO.`,
  });
  const unreadNotifications = await NotificationService.countUnread(shopDetails.id);

  pusher.trigger(shop, socketEvents.PAGE_SYNC_COMPLETE, eventDetails);
  pusher.trigger(shop, socketEvents.NEW_NOTIFICATION, { shop, notification, unreadNotifications });
};

/**
 * Handle blog articles sync update event
 * @param {import("../../jsDocTypes").BlogArticlesSyncUpdateEventDetails} eventDetails
 */
const handleBlogArticleSyncComplete = async (eventDetails) => {
  const { shop, total } = eventDetails;

  // send proccessed event to all client
  const shopDetails = await ShopService.getShop(shop);

  const notification = await NotificationService.createNotification({
    shop_id: shopDetails.id,
    title: "Blog posts sync complete!",
    message: `Total ${total} blog posts have been synced. You can now see & fix SEO issues for all of them inside StoreSEO.`,
  });
  const unreadNotifications = await NotificationService.countUnread(shopDetails.id);

  pusher.trigger(shop, socketEvents.BLOG_SYNC_COMPLETE, { ...eventDetails, articles: total });
  pusher.trigger(shop, socketEvents.NEW_NOTIFICATION, { shop, notification, unreadNotifications });
};

/**
 * handle single notification read event
 * @param {import("../../jsDocTypes").NotificationReadEventDetails} eventDetails
 */
const handleNotificationRead = async (eventDetails) => {
  const { id, shopId, room, socketId } = eventDetails;
  await NotificationService.markAsRead(id);
  const unreadNotifications = await NotificationService.countUnread(shopId);

  // send the event to rest of the clients
  pusher.trigger(
    room,
    socketEvents.NOTIFICATION_READ,
    { ...eventDetails, unreadNotifications },
    {
      socket_id: socketId,
    }
  );
};

/**
 * handle all notification read event for a shop
 * @param {import("../../jsDocTypes").AllNotificationReadEventDetails} eventDetails
 */
const handleAllNotificationRead = async (eventDetails) => {
  const { shopId, socketId, room } = eventDetails;
  await NotificationService.markAllAsRead(shopId);

  // send event to rest of the clients
  pusher.trigger(room, socketEvents.NOTIFICATION_ALL_READ, eventDetails, {
    socket_id: socketId,
  });
};

/**
 * Trigger notification for auto-optimization task finish
 * @param {import("../../jsDocTypes").OptimizationTaskFinishEvent} eventDetails
 */
const handleOptimizationTaskFinish = async (eventDetails) => {
  const { shopId, shop, productsOptimized, products } = eventDetails;
  const notification = await NotificationService.createNotification({
    shop_id: shopId,
    title: "Product auto-optimization task complete!",
    message: `Your request to auto-fix unoptimized products by applying custom SEO templates has been completed!`,
  });
  const unreadNotifications = await NotificationService.countUnread(shopId);

  pusher.trigger(shop, socketEvents.PRODUCT_OPTIMIZATION_COMPLETE, eventDetails);
  pusher.trigger(shop, socketEvents.NEW_NOTIFICATION, { shop, notification, unreadNotifications });
};

/**
 * Trigger notification for multi-language product sync finish
 * @param {{ shopId: number, shop: string, languageCode: string, languageName }} eventDetails
 */
const handleMultiLanguageProductSyncComplete = async (eventDetails) => {
  const { shopId, shop, languageName } = eventDetails;
  const notification = await NotificationService.createNotification({
    shop_id: shopId,
    title: `${languageName} products synced`,
    message: `Your request to sync product translations for '${languageName}' language has been completed!`,
  });
  const unreadNotifications = await NotificationService.countUnread(shopId);

  pusher.trigger(shop, socketEvents.MULTI_LANGUAGE_PRODUCTS_SYNCED, eventDetails);
  pusher.trigger(shop, socketEvents.NEW_NOTIFICATION, { shop, notification, unreadNotifications });
};

/**
 *
 * @param {{shop: string}} eventDetails
 */
const handleBackupInitialized = async (eventDetails) => {
  const { shop } = eventDetails;
  pusher.trigger(shop, socketEvents.BACKUP_INITIALIZED, eventDetails);
};

/**
 *
 * @param {{shopId: number, shop:string, backupId: number}} eventDetails
 */
const handleBackupCompleted = async (eventDetails) => {
  const { shopId, shop, backupId } = eventDetails;
  const backupDetails = await BackupService.getManualBackupByCondition(shopId, { id: backupId });

  const notification = await NotificationService.createNotification({
    shop_id: shopId,
    title: "Backup completed",
    message: `Your backup for ${backupDetails.resources.join(", ")} has been completed successfully!`,
  });
  const unreadNotifications = await NotificationService.countUnread(shopId);
  pusher.trigger(shop, socketEvents.BACKUP_COMPLETED, { ...eventDetails, type: socketEvents.BACKUP_COMPLETED });
  pusher.trigger(shop, socketEvents.NEW_NOTIFICATION, { shop, notification, unreadNotifications });
};

/**
 *
 * @param {{shop: string}} eventDetails
 */
const handleBackupRestoreInitialized = async (eventDetails) => {
  const { shop } = eventDetails;
  pusher.trigger(shop, socketEvents.BACKUP_RESTORE_INITIALIZED, eventDetails);
};

/**
 *
 *@param {{shopId: number, shop:string, backupId: number}} eventDetails
 */
const handleBackupRestoreComplete = async (eventDetails) => {
  const { shopId, shop, backupId } = eventDetails;
  const backupDetails = await BackupService.getManualBackupByCondition(shopId, { id: backupId });

  const notification = await NotificationService.createNotification({
    shop_id: shopId,
    title: "Backup file restore successful",
    message: `Your backup file restore for "${backupDetails.resources.map((resource) => resource.toLowerCase()).join(", ")}" has been completed successfully!`,
  });
  const unreadNotifications = await NotificationService.countUnread(shopId);
  pusher.trigger(shop, socketEvents.BACKUP_RESTORE_COMPLETED, {
    ...eventDetails,
    type: socketEvents.BACKUP_RESTORE_COMPLETED,
  });
  pusher.trigger(shop, socketEvents.NEW_NOTIFICATION, { shop, notification, unreadNotifications });
};

/**
 *
 *@param {{shopId: number, shop:string, type: string, notification:{title: string, message: string}}} eventDetails
 */
const handleBulOpComplete = async (eventDetails) => {
  const {
    shopId,
    shop,
    type,
    notification: { title, message },
  } = eventDetails;

  const notification = await NotificationService.createNotification({
    shop_id: shopId,
    title,
    message,
  });
  const unreadNotifications = await NotificationService.countUnread(shopId);
  pusher.trigger(shop, socketEvents.NEW_NOTIFICATION, { shop, notification, unreadNotifications });

  pusher.trigger(shop, type, eventDetails);
};

module.exports = {
  handleProductSyncComplete,
  handleProductSyncUpdate,
  handlePageSyncUpdate,
  handlePageSyncComplete,
  handleBlogArticleSyncComplete,
  handleNotificationRead,
  handleAllNotificationRead,
  handleOptimizationTaskFinish,
  handleMultiLanguageProductSyncComplete,
  handleBackupInitialized,
  handleBackupCompleted,
  handleBackupRestoreInitialized,
  handleBackupRestoreComplete,
  handleBulOpComplete,
  ...CollectionEventService,
  ...DocEventService,
};
