const PartnersService = require("./PartnersService");
const transactionsQuery = require("../../queries/partners/transactions.gql");
const { ShopifyTransaction } = require("../../../sequelize");
const { last } = require("lodash");

class TransactionService extends PartnersService {
  /**
   * Get's the shopify app event data based on the dates and save/update them in the database
   * @param {string} startDate
   * @param {string | null | undefined} endDate
   * @param {string | null | undefined} cursor
   * @param {number} limit
   * @returns {Promise<{hasNextPage: boolean, hasPreviousPage: boolean, lastCursor: string}>}
   */
  getTransactionsFromShopify = async (startDate, endDate, cursor, limit = 10) => {
    const {
      transactions: { edges, pageInfo },
    } = await this.apiData(transactionsQuery, {
      first: limit,
      createdAtMin: startDate,
      createdAtMax: endDate,
      cursor,
    });

    const serializedData = this.serializeShpoifyData(edges);

    return { pageInfo, lastCursor: last(edges)?.cursor, transactions: serializedData };
  };

  /**
   * Get's the shopify app event data based on the dates and save/update them in the database
   * @param {string} startDate
   * @param {string | null | undefined} endDate
   * @param {string | null | undefined} cursor
   * @returns {Promise<{hasNextPage: boolean, hasPreviousPage: boolean, lastCursor: string}>}
   */
  saveData = async (startDate, endDate, cursor = undefined) => {
    const { pageInfo, lastCursor, transactions } = await this.getTransactionsFromShopify(startDate, endDate, cursor);

    const conflictFields = ["shop_domain", "transaction_id"];
    const fields = [
      "shopify_shop_id",
      "transaction_created_at",
      "billing_interval",
      "charge_id",
      "gross_amount",
      "net_amount",
      "shopify_fee",
      "currency_code",
    ];
    const res = await Promise.allSettled(
      transactions.map((transaction) => ShopifyTransaction.upsert(transaction, { fields, conflictFields }))
    );

    return { ...pageInfo, lastCursor, res };
  };

  serializeShpoifyData = (edges) => {
    return edges.map(({ node }) => ({
      transaction_id: node.id,
      shop_domain: node.shop.myshopifyDomain,
      shopify_shop_id: node.shop.id,
      transaction_created_at: node.createdAt,
      billing_interval: node.billingInterval,
      charge_id: node?.chargeId,
      gross_amount: node?.grossAmount?.amount,
      net_amount: node?.netAmount?.amount,
      shopify_fee: node?.shopifyFee?.amount,
      currency_code: node?.grossAmount?.currencyCode,
    }));
  };
}

module.exports = new TransactionService();
