require("dotenv").config();
require("graphql-import-node/register");
const axios = require("axios");
const logger = require("storeseo-logger");
const { CustomError } = require("../../../errors");
const appQuery = require("../../queries/partners/app.gql");

class PartnersService {
  #client;
  #appID;

  constructor() {
    const BASE_URL = `https://partners.shopify.com/${process.env.PARTNER_ORG}/api/${process.env.PARTNER_API_VERSION}/graphql.json`;
    const TOKEN = process.env.PARTNER_ACCESS_TOKEN;

    this.#appID = `gid://partners/App/${process.env.PARTNER_APP_ID}`;

    this.#client = axios.create({
      baseURL: BASE_URL,
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": TOKEN,
      },
      transformResponse: [
        function (data) {
          const { data: parsedData, errors } = JSON.parse(data);

          if (errors?.length > 0) {
            throw new CustomError(errors[0].message);
          }

          return {
            ...parsedData,
          };
        },
      ],
    });
  }

  /**
   *
   * @param {string} query
   * @param {object} variables
   */
  apiData = async (query, variables = {}) => {
    try {
      const { status, statusText, data } = await this.#client.post("", {
        query: query.loc.source.body,
        variables: {
          appId: this.#appID,
          ...variables,
        },
      });

      if (status >= 400) {
        throw new CustomError(statusText);
      }

      return data;
    } catch (error) {
      logger.error(error);
    }
  };

  getAppDetail = async () => {
    const res = await this.apiData(appQuery);
    return res;
  };
}

module.exports = PartnersService;
