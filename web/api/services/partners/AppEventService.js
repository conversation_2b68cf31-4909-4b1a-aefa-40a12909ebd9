const PartnersService = require("./PartnersService");
const appEventQuery = require("../../queries/partners/app-events.gql");
const { ShopifyAppEvent } = require("../../../sequelize");
const { last } = require("lodash");

class AppEventService extends PartnersService {
  /**
   * Get's the shopify app event data based on the dates and save/update them in the database
   * @param {string} startDate
   * @param {string | null | undefined} endDate
   * @param {string | null | undefined} cursor
   * @param {number} limit
   * @returns {Promise<{hasNextPage: boolean, hasPreviousPage: boolean, lastCursor: string, events: Array<Object>}>}
   */
  getAppEvents = async (startDate, endDate, cursor = undefined, limit = 20) => {
    const {
      app: {
        events: { edges, pageInfo },
      },
    } = await this.apiData(appEventQuery, {
      first: limit,
      occurredAtMin: startDate,
      occurredAtMax: endDate,
      cursor: cursor,
    });

    const serializedData = this.serializeShopifyData(edges);

    return { pageInfo, lastCursor: last(edges)?.cursor, events: serializedData };
  };
  /**
   * Get's the shopify app event data based on the dates and save/update them in the database
   * @param {string} startDate
   * @param {string | null | undefined} endDate
   * @param {string | null | undefined} cursor
   * @returns {Promise<{hasNextPage: boolean, hasPreviousPage: boolean, lastCursor: string}>}
   */
  saveData = async (startDate, endDate, cursor = undefined) => {
    const { pageInfo, lastCursor, events } = await this.getAppEvents(startDate, endDate, cursor);

    const conflictFields = ["shop_domain", "event_type", "occurred_at", "shopify_shop_id"];
    const fields = ["charge_id", "charge_name", "charge_amount", "charge_currency_code", "billing_on", "is_test"];
    const res = await Promise.allSettled(
      events.map((data) => ShopifyAppEvent.upsert(data, { fields, conflictFields, returning: false }))
    );

    return { ...pageInfo, lastCursor, res };
  };

  serializeShopifyData = (eventEdges) => {
    return eventEdges.map(({ node }) => ({
      shop_domain: node.shop.myshopifyDomain,
      event_type: node.type,
      occurred_at: node.occurredAt,
      shopify_shop_id: node.shop.id,
      charge_id: node?.charge?.id,
      charge_name: node?.charge?.name,
      charge_amount: node?.charge?.amount?.amount,
      charge_currency_code: node?.charge?.amount?.currencyCode,
      billing_on: node?.charge?.billingOn,
      is_test: node?.charge?.test,
    }));
  };
}

module.exports = new AppEventService();
