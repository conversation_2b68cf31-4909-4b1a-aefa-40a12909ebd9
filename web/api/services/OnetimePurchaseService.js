const sequelize = require("../../sequelize");

/**
 * @type {typeof import('sequelize').Model}
 */

const OnetimePurchase = sequelize.OnetimePurchase;

class onetimePurchaseService {
  saveOnetimePurchase = async (data) => {
    const purchase = await OnetimePurchase.create(data);
    return purchase.toJSON();
  };

  createOnetimePurchase = async (shopId, onetimePurchase, addons = []) => {
    const data = {
      shop_id: shopId,
      purchase_id: onetimePurchase.admin_graphql_api_id,
      name: onetimePurchase.name,
      status: onetimePurchase.status,
      purchased_at: onetimePurchase.updated_at,
      meta: { addons },
    };

    return await this.saveOnetimePurchase(data);
  };
}

module.exports = new onetimePurchaseService();
