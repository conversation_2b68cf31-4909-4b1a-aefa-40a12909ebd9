const fs = require("node:fs");

const cache = require("../../cache");
const { BulkProductParsingError } = require("../../../errors");

const ASCII_CONTROL_CHARS_REGEX = /[\x00-\x1F\x7F]/g;

class ProductReadService {
  /** @type {string} */
  #shopDomain;
  /** @type {import("fs").ReadStream} */
  #productDataStream;
  /** @type {number} */
  #byteCursor;

  constructor(shopDomain) {
    this.#shopDomain = shopDomain;
  }

  /**
   *
   * @param {number} limit
   * @returns {Promise<*[]>}
   */
  async readNextShopifyProducts(limit) {
    await this.#startDataStreaming();
    const products = [];

    while (products.length <= limit) {
      const { line, startByte } = await this.#readNextLineFromStream();
      if (!line) break;

      let item = "";
      try {
        item = JSON.parse(line.replace(ASCII_CONTROL_CHARS_REGEX, ""));
      } catch (err) {
        throw new BulkProductParsingError("Failed to parse product data from JSONL file", { cause: err });
      }
      const itemType = this.#checkItemType(item);

      if (itemType === "PRODUCT" && products.length === limit) {
        this.#resetByteCursor(startByte);
        break;
      }
      if (itemType === "PRODUCT") products.push({ ...item, mediaImages: [], meta: [] });
      else if (itemType === "MEDIA_IMAGE") products[products.length - 1].mediaImages.push(item);
      else if (itemType === "METAFIELD") products[products.length - 1].meta.push(item);
    }

    await this.#stopDataStreaming();

    return products;
  }

  async #startDataStreaming() {
    const filePath = await cache.product.syncFile(this.#shopDomain);
    this.#byteCursor = (await cache.product.syncFileCursor(this.#shopDomain)) || 0;

    if (!fs.existsSync(filePath)) throw new Error(`Sync file not found in the disk. Path: ${filePath}`);

    this.#productDataStream = fs.createReadStream(filePath, {
      start: this.#byteCursor,
    });

    this.#productDataStream.on("error", (err) => {
      throw err;
    });

    await new Promise((r) => this.#productDataStream.once("readable", r));
  }

  async #stopDataStreaming() {
    this.#productDataStream.destroy();
    await cache.product.syncFileCursor(this.#shopDomain, this.#byteCursor);
  }

  /**
   * @returns {Promise<{ line: string|null, startByte: number, endByte: number}>}
   */
  async #readNextLineFromStream() {
    const startByte = this.#byteCursor;

    let line = [];
    let endOfLineReached = false;

    while (!endOfLineReached) {
      const byteData = this.#productDataStream.read(1);
      this.#byteCursor++;

      if (byteData === null && !line) break;
      else if (!byteData) break;
      else if (Buffer.from(byteData).toString() === "\n") endOfLineReached = true;
      else line.push(byteData);
    }

    return { line: Buffer.concat(line).toString(), startByte, endByte: this.#byteCursor };
  }

  /**
   *
   * @param {number} position
   */
  #resetByteCursor(position) {
    this.#byteCursor = position;
  }

  /**
   * @typedef {"PRODUCT" | "MEDIA_IMAGE" | "METAFIELD"} ItemType
   *
   * @param {any} item
   * @returns {ItemType}
   */
  #checkItemType(item) {
    if (item.id?.match(/\/Product\//gim)) return "PRODUCT";
    else if (item.id?.match(/\/MediaImage\//gim)) return "MEDIA_IMAGE";
    else if (item.id?.match(/\/Metafield\//gim)) return "METAFIELD";
  }
}

module.exports = ProductReadService;
