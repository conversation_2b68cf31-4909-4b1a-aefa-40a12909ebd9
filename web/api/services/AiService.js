// // @ts-check
const { default: axios } = require("axios");
const { PRODUCT, PAGE, ARTICLE } = require("storeseo-enums/analysisEntityTypes");
const { AI_OPTIMIZER } = require("storeseo-enums/subscriptionAddonGroup");
const cache = require("../cache");
const { inputTokenCost, creditUnit, outputTokenCost, perImageCredit, inputOutputRatio } = require("../config/ai");
const FileService = require("./FileService");
const { isEmpty } = require("lodash");
const { CONTENT, IMAGE } = require("storeseo-enums/aiContentTypes");
const ResourceType = require("storeseo-enums/resourceType");
const ProductService = require("./ProductService");
const { Op } = require("../../sequelize");
const { autoAiOptimizationSchema } = require("storeseo-schema/settings/autoAiOptimization");
const CollectionService = require("./collections/CollectionService");
const AiOptimizationStatus = require("storeseo-enums/aiOptimization");
const ResourceDataBackupService = require("./resource/ResourceDataBackupService");
const ResourceOptimizationService = require("./resource/ResourceOptimizationService");
const ResourceOPType = require("storeseo-enums/resourceOPType");

/**
 * @typedef {keyof Pick<typeof ResourceType, "PRODUCT" | "COLLECTION" | "PAGE" | "ARTICLE">} AiResourceType
 */

class AiService {
  #aiClient;

  constructor() {
    this.#aiClient = axios.create({
      baseURL: process.env.AI_BASE_URL,
      headers: { Authorization: `Bearer ${process.env?.AI_AUTH_TOKEN}` },
      timeout: 1000 * 60 * 5,
    });
  }

  /**
   * @param {{title: string, description: string, focusKeyword: string, type: PRODUCT | PAGE | ARTICLE, contentTypes?: Array<"meta" | "tags"| "product">}} input
   */
  generateProductContent = async ({
    title,
    description,
    focusKeyword,
    language = "English",
    contentTypes = ["meta", "tags"],
  }) => {
    return await this.#aiClient.post("/product-rewrite", {
      product_title: title,
      product_description: description,
      focus_keyword: focusKeyword,
      action_types: contentTypes,
      // custom_prompt: `Output in ${language}`,
      output_lang: language,
    });
  };

  /**
   * @param {string} domain domain of the shop
   * @param {{prompt_tokens: Number, completion_tokens: Number, total_tokens: Number}} usage domain of the shop
   * @param {string?} type - type of the content
   */
  increaseCreditUsage = async (domain, usage, type) => {
    const creditUsage = this.calculateCreditUsage(usage, type);
    await cache.addons.incrementUsageCount(domain, { addon: AI_OPTIMIZER, incrementBy: creditUsage });
    await cache.addons.incrementTotalUsageCount(domain, { addon: AI_OPTIMIZER, incrementBy: creditUsage });
    await cache.addons.incrementTotalAppUsageCount(AI_OPTIMIZER, creditUsage);
    return creditUsage;
  };

  /**
   * @param {{prompt_tokens: Number, completion_tokens: Number, total_tokens: Number}} usage domain of the shop
   * @param {string?} type - type of the content
   * @returns {number} credit usage
   */
  calculateCreditUsage = (usage, type = CONTENT) => {
    let creditUsage = 0;
    if (type === CONTENT) {
      creditUsage = ((usage.prompt_tokens + usage.completion_tokens * inputOutputRatio) / creditUnit).toFixed(1);
    } else if (type === IMAGE) {
      creditUsage = (perImageCredit / creditUnit).toFixed(1);
    }

    return Number(creditUsage);
  };

  logAiContentOptimization = async ({ shop, input, output, usage }) => {
    if (!output) return;
    const newData = {
      date: new Date().toISOString(),
      input_title: input.title,
      input_description: input.description,
      input_focus_keyword: input.focusKeyword,
      output_title: output.meta_title,
      output_description: output.product_description,
      output_tags: output?.tags?.join(",") || "",
      input_token: usage.prompt_tokens,
      input_cost: usage.prompt_tokens * inputTokenCost,
      output_token: usage.completion_tokens,
      output_cost: usage.completion_tokens * outputTokenCost,
    };

    await FileService.writeAiContentUsage(shop, newData, "ai-content.csv");
  };

  logAiImageAltTextOptimization = async ({ shop, input, output, usage }) => {
    if (!output) return;
    const newData = {
      date: new Date().toISOString(),
      input_title: input.title,
      input_description: input.description,
      input_focus_keyword: input.focusKeyword,
      input_images: input.images?.map((image) => JSON.stringify(image)).join(","),
      output_alt_text: output.alt_text,
      input_token: usage.prompt_tokens,
      input_cost: usage.prompt_tokens * inputTokenCost,
      output_token: usage.completion_tokens,
      output_cost: usage.completion_tokens * outputTokenCost,
    };

    await FileService.writeAiContentUsage(shop, newData, "ai-image-alt.csv");
  };

  hasAiLimit = (addons = []) => {
    const ai =
      addons
        .filter((a) => a.group === AI_OPTIMIZER)
        ?.reduce((acc, a) => ({ ...a, usageLimit: (acc?.usageLimit || 0) + a.usageLimit }), null) || {};

    if (isEmpty(ai) || !ai) return false;

    return Number(ai.usageLimit) - Number(ai.usageCount) > 0;
  };

  /**
   * @deprecated This method is deprecated. Please use the `generateCollectionContent` from OpenAiService instead.
   * @param {{title: string, description: string, focusKeyword: string, language: string}}
   */
  generateCollectionContent = async ({
    title,
    description,
    focusKeyword,
    language = "English",
    contentTypes = ["meta"],
  }) => {
    return await this.#aiClient.post("/generate-collection-content", {
      collection_title: title,
      collection_description: description,
      focus_keyword: focusKeyword,
      action_types: contentTypes,
      // custom_prompt: `Output in ${language}`,
      output_lang: language,
    });
  };

  /**
   * @param {{title: string, content: string, focusKeyword: string, language: string}}
   */
  generatePageContent = async ({ title, content, focusKeyword, language = "English" }) => {
    return await this.#aiClient.post("/generate-page-content", {
      page_title: title,
      page_content: content,
      focus_keyword: focusKeyword,
      action_types: ["meta"],
      // custom_prompt: `Output in ${language}`,
      output_lang: language,
    });
  };

  /**
   * @param {{title: string, content: string, focusKeyword: string, language: string}}
   */
  generateArticleContent = async ({ title, content, focusKeyword, language = "English" }) => {
    return await this.#aiClient.post("/generate-article-content", {
      article_title: title,
      article_content: content,
      focus_keyword: focusKeyword,
      action_types: ["meta", "tags"],
      // custom_prompt: `Output in ${language}`,
      output_lang: language,
    });
  };

  /**
   * @param {{images: Array<image>, title: string, description: string, focusKeyword: string, domain: string, language: string}}
   */
  generateImageAltTextGPT4o = async ({ images, title, description, focusKeyword, domain, language = "English" }) => {
    const imagesDetails = images.map((image) => ({
      image_url: image.src,
      image_id: String(image.id),
      product_title: title,
      product_description: description,
      focus_keyword: focusKeyword,
      // custom_prompt: `Output in ${language}`,
    }));

    console.info("language =", language);

    return await this.#aiClient.post("/generate-alt-text-openai-gpt4o", {
      image_details: imagesDetails,
      shop_domain: domain,
      output_lang: language,
    });
  };

  /**
   * @param {{meta:boolean,tags:boolean,imageAltText:"allImages" | "featuredImage"} | undefined} settings
   * @param {{title: string, description: string, focus_keyword: string, images: Array<*> | undefined}} resourceDetails
   * @returns {string}
   */
  calculateApproximateCreditUsage = (settings, resourceDetails) => {
    const { title = "", description = "", focus_keyword = "", images = [] } = resourceDetails;

    const perImageCreditUsage = this.calculateCreditUsage({}, IMAGE);
    const usage = this.#calculateApproximateAiUsage({
      title,
      description,
      focus_keyword,
    });
    let incrementValue = Math.ceil(this.calculateCreditUsage(usage)); // Approximate value. This will sync after AI content generation

    if (settings?.imageAltText === "featuredImage" && images.length > 0) {
      incrementValue += perImageCreditUsage;
    }

    if (settings?.imageAltText === "allImages" && images.length > 0) {
      incrementValue += perImageCreditUsage * images.length;
    }

    return incrementValue.toString();
  };

  /**
   *
   * @param {{title: string, description: string, focus_keyword: string}} param0
   */
  #calculateApproximateAiUsage = ({ title = "", description = "", focus_keyword = "" }) => {
    const completion_tokens = 100; // Estimated completion_tokens ≈ 100
    const titleWordCount = title.split(" ").length;
    const descriptionWordCount = description?.split(" ").length || 0;
    const keywordWordCount = focus_keyword?.split(" ").length || 0;
    const words_count = titleWordCount + descriptionWordCount + keywordWordCount;

    // prompt_tokens = a + b * words_count
    // a = 272, b = 1.65 (approximate values based some tests)
    // ref: https://grok.com/share/bGVnYWN5_42ec8c7e-a7de-47fc-a349-87d9825f6990

    const prompt_tokens = 272 + 1.65 * words_count;

    return {
      prompt_tokens,
      completion_tokens,
    };
  };

  /**
   * @param {{shopId: number, resourceList: Array<{id: number, gql_id: string}>, settings:import("yup").InferType<typeof autoAiOptimizationSchema>}} param
   * @param {AiResourceType} resourceType
   * @returns {Promise<{resources: Array<any>, totalApproximateCreditUsage: number, resourcesApproximateCreditUsageMap: Map<string, number>}>}
   */
  prepareBulkAiContentByType = async ({ shopId, resourceList, settings }, resourceType) => {
    let totalApproximateCreditUsage = 0;
    const resourcesApproximateCreditUsageMap = new Map();

    switch (resourceType) {
      case "PRODUCT": {
        const productsDetails = await ProductService.getFullProductsByCondition(shopId, {
          id: {
            [Op.in]: resourceList.map((product) => product.id),
          },
        });

        for (let product of productsDetails) {
          const approximateCreditUsage = this.calculateApproximateCreditUsage(settings, product);
          totalApproximateCreditUsage += Number(approximateCreditUsage);

          resourcesApproximateCreditUsageMap.set(product.id, approximateCreditUsage);
        }
        return { resources: productsDetails, totalApproximateCreditUsage, resourcesApproximateCreditUsageMap };
      }

      case "COLLECTION": {
        const collectionsDetails = await CollectionService.getFullCollectionsByCondition(shopId, {
          id: {
            [Op.in]: resourceList.map((collection) => collection.id),
          },
        });

        for (let collection of collectionsDetails) {
          const approximateCreditUsage = this.calculateApproximateCreditUsage(settings, collection);
          totalApproximateCreditUsage += Number(approximateCreditUsage);

          resourcesApproximateCreditUsageMap.set(collection.id, approximateCreditUsage);
        }
        return { resources: collectionsDetails, totalApproximateCreditUsage, resourcesApproximateCreditUsageMap };
      }

      default:
        throw new Error("Invalid resource type");
    }
  };

  /**
   * @param {number} shopId
   * @param {string} resourceId
   * @param {AiResourceType} resourceType
   */
  makedResourcesAsPending = async (shopId, resourceId, resourceType) => {
    switch (resourceType) {
      case "PRODUCT":
        await ProductService.updateProduct(shopId, resourceId, {
          ai_optimization_status: AiOptimizationStatus.PENDING,
        });
        break;

      case "COLLECTION":
        await CollectionService.update(shopId, resourceId, {
          ai_optimization_status: AiOptimizationStatus.PENDING,
        });
        break;

      default:
        break;
    }
  };

  /**
   * @param {string} shopDomain
   * @param {AiResourceType} resourceType
   */
  addStoreToPendingOptimizationQueue = async (shopDomain, resourceType) => {
    switch (resourceType) {
      case "PRODUCT":
        await cache.aiOptimizer.addStoreToPendingOptimizationQueue(shopDomain);
        break;

      case "COLLECTION":
        await cache.collectionAiOptimizer.addStoreToPendingOptimizationQueue(shopDomain);
        break;

      default:
        break;
    }
  };

  /**
   * @param {number} shopId
   * @param {string} resourceId
   * @param {AiResourceType} resourceType
   * @returns {Promise<{resourceDetails: any, backupData: import("./resource/ResourceDataBackupService").ResourceDataBackupType, resourceOptimization: import("./resource/ResourceOptimizationService").ResourceOptimizationType} | null>}
   */
  getResourceDetailsForRestore = async (shopId, resourceId, resourceType) => {
    let resourceDetails;
    let backupData;
    let resourceOptimization;

    if (resourceType === "PRODUCT") {
      resourceDetails = await ProductService.getProductByCondition(shopId, {
        id: resourceId,
        ai_optimization_status: AiOptimizationStatus.OPTIMIZED,
      });
    } else if (resourceType === "COLLECTION") {
      resourceDetails = await CollectionService.getByCondition(shopId, {
        id: resourceId,
        ai_optimization_status: AiOptimizationStatus.OPTIMIZED,
      });
    }

    if (!resourceDetails) return null;

    // Find the resource backup data for restore
    backupData = await ResourceDataBackupService.getByCondition({
      shop_id: shopId,
      resource_id: resourceDetails.id,
      resource_type: resourceType,
      resource_op_type: ResourceOPType.AI_OPTIMIZATION,
    });

    // Find the resource optimization data for stats
    resourceOptimization = await ResourceOptimizationService.getByCondition({
      shop_id: shopId,
      resource_id: resourceDetails.id,
      resource_type: resourceType,
      resource_op_type: ResourceOPType.AI_OPTIMIZATION,
    });

    return { resourceDetails, backupData, resourceOptimization };
  };

  /**
   * @param {number} shopId
   * @param {any} resourceDetails
   * @param {import("./resource/ResourceDataBackupService").ResourceDataBackupType} backupData
   * @param {import("./resource/ResourceOptimizationService").ResourceOptimizationType} resourceOptimization
   * @param {AiResourceType} resourceType
   * @param {Object} session
   * @returns {Promise<any>}
   */
  restoreResourceContent = async (shopId, resourceDetails, backupData, resourceOptimization, resourceType, session) => {
    const restoreData = {
      metaTitle: backupData?.data?.metaTitle ?? "",
      metaDescription: backupData?.data?.metaDescription ?? "",
      tags: backupData?.data?.tags ?? [],
    };

    const currentRestoreCount = resourceOptimization?.optimization_stats?.restore_count ?? 0;

    const optimization_stats = {
      optimization_count: resourceOptimization?.optimization_stats?.optimization_count ?? 0,
      restore_count: currentRestoreCount + 1,
      last_queued_date: resourceOptimization?.optimization_stats?.last_queued_date ?? null,
      last_queue_process_completed_date:
        resourceOptimization?.optimization_stats?.last_queue_process_completed_date ?? null,
    };

    await ResourceOptimizationService.upsert({
      shop_id: shopId,
      resource_id: resourceDetails.id,
      resource_type: resourceType,
      resource_op_type: ResourceOPType.AI_OPTIMIZATION,
      optimization_stats,
    });

    let restoredResource;

    switch (resourceType) {
      case "PRODUCT":
        restoredResource = await ProductService.restoreProductContent(shopId, resourceDetails, restoreData, session);
        break;

      case "COLLECTION":
        restoredResource = await CollectionService.restoreCollectionContent(
          shopId,
          resourceDetails,
          restoreData,
          session
        );
        break;

      default:
        throw new Error("Invalid resource type");
    }

    return {
      id: restoredResource.id,
      score: restoredResource.score,
      issues: restoredResource.issues,
      ai_optimization_status: restoredResource.ai_optimization_status,
    };
  };
}

module.exports = new AiService();
