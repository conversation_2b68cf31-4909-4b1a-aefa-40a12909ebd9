const fs = require("fs");
const { dirname, join, normalize } = require("path");

class XYZService {
  #fileName(topic) {
    return `${topic.replace("/", "-")}.json`;
  }

  #filePath(fileName, path = "uploads") {
    return normalize(join(__dirname, "../../", path, fileName));
  }

  async putWebhookId(webhookId, topic) {
    try {
      const data = await this.getFileData(this.#fileName(topic));

      data[webhookId] = data[webhookId] || 0;
      data[webhookId]++;

      return await this.writeFileData(this.#fileName(topic), data);
    } catch (e) {
      console.log("Error putting webhook id", e.message);
    }
  }

  async hasWebhookId(headers) {
    try {
      const topic = headers["x-shopify-topic"];
      const webhookId = headers["x-shopify-webhook-id"];
      const data = await this.getFileData(this.#fileName(topic));
      await this.putWebhookId(webhookId, topic);
      return data.hasOwnProperty(webhookId);
    } catch (e) {
      console.log("Error putting webhook id", e.message);
      return false;
    }
  }

  async getFileData(fileName, path = "uploads") {
    try {
      const file = this.#filePath(fileName, path);
      console.log(file);
      const data = fs.readFileSync(file, "utf8");
      return JSON.parse(data);
    } catch (e) {
      // console.log("File read error: ", e.message);
      return {};
    }
  }

  async writeFileData(fileName, data, path = "uploads") {
    try {
      const file = this.#filePath(fileName, path);
      console.log(file);
      await fs.writeFileSync(file, JSON.stringify(data, null, 2), "utf8");
      return await this.getFileData(fileName, path);
    } catch (e) {
      console.log("File write error: ", e.message);
      return {};
    }
  }
}

module.exports = new XYZService();
