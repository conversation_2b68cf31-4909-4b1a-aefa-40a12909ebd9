const moment = require("moment");
const { IncomingWebhook } = require("@slack/webhook");
const { sequelize, Sequelize, QueueStats } = require("../../sequelize");
const logger = require("storeseo-logger");
const AdminReportService = require("./admin/AdminReportService");
const {
  serializeDailyStatsData,
  prepareDailyStatusReportBlock,
} = require("../serializers/admin/DailyStatusDataSerializer");
const { QUEUE_BASE_NAME } = require("../queue/imageOptimizer/config");
const AdminReportTypes = require("storeseo-enums/admin/AdminReportTypes");
const AiOptimizationStatus = require("storeseo-enums/aiOptimization");
const { QueryTypes } = require("sequelize");

class SlackService {
  #config = {
    test: "*******************************************************************************",
    live: "*******************************************************************************",
  };

  #channels = {
    ALERT_CHANNEL: "ALERT_CHANNEL",
  };

  #incomingWebhookUrls = {
    [this.#channels.ALERT_CHANNEL]: {
      test: "*******************************************************************************",
      live: "*******************************************************************************",
    },
  };

  #client = () => {
    // Read a url from the environment variables
    const url = this.#config[process.env.SLACK_ENV || "test"];

    // Initialize
    const webhook = new IncomingWebhook(url);

    return webhook;
  };

  /**
   *
   * @param {string} channelName channel key defined in **#channels** config
   * @returns {string} incoming webhook url of the channel based on `.env -> SLACK_ENV` (default env: "test")
   */
  #getIncomingWebhookUrl = (channelName) => {
    return this.#incomingWebhookUrls[this.#channels.ALERT_CHANNEL][process.env.SLACK_ENV || "test"];
  };

  /**
   *
   * @param {string} channelName channel key defined in **#channels** config
   */
  #prepareClient = (channelName) => {
    const url = this.#getIncomingWebhookUrl(channelName);

    const webhook = new IncomingWebhook(url);
    return webhook;
  };

  sendImageOptimizerMissingWebhooksAlert = async ({ date, imagesQueued, webhooksReceived, imagesMissing }) => {
    const client = this.#prepareClient(this.#channels.ALERT_CHANNEL);

    const data = {
      blocks: [
        {
          type: "divider",
        },
        {
          type: "rich_text",
          elements: [
            {
              type: "rich_text_section",
              elements: [
                {
                  type: "emoji",
                  name: "x",
                },
                {
                  type: "text",
                  text: " ",
                },
                {
                  type: "text",
                  text: "Images are not returning via webhooks",
                  style: {
                    bold: true,
                  },
                },
                {
                  type: "text",
                  text: " ",
                },
                {
                  type: "emoji",
                  name: "cry",
                },
              ],
            },
          ],
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `---\n*Date: ${date}*\n--- \n • :alphabet-white-question:Images queued: _${imagesQueued}_ \n • :alphabet-yellow-question:Webhooks received: _${webhooksReceived}_ \n • :warning:Images not back yet: _${imagesMissing}_`,
          },
          accessory: {
            type: "image",
            image_url: "https://api.slack.com/img/blocks/bkb_template_images/notifications.png",
            alt_text: "calendar thumbnail",
          },
        },
        {
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `:alert: *\`${
                (imagesMissing * 100) / imagesQueued
              }%\`* images did not return from the image processing server.`,
            },
            {
              type: "mrkdwn",
              text: "*CC:* @channel",
            },
          ],
        },
      ],
    };

    client.send(data);
  };

  sendTestMessage = async () => {
    const res = await this.#client().send({ text: "this is a test message" });
    console.log("res", res);
  };

  sendImageOptimizerStats = async (date) => {
    const startTime = moment(date).tz("Asia/Dhaka").startOf("D").toISOString();
    const endTime = moment(date).tz("Asia/Dhaka").endOf("D").toISOString();
    const formattedDate = moment(date).format("Do MMM, YY");

    const subscription = await this.getIOSubscriptionData(startTime, endTime);
    const subscriptionElements = this.prepareIOSubsBlocks(subscription);

    const ioData = await this.imageOptimizerData(date);
    const queueStatElements = this.prepareIODataBlock(ioData);

    const aiContentData = await this.getAiContentOptimizationStats(date);
    const aiStatElements = this.prepareAiDataBlocks(aiContentData);

    const blocks = [
      {
        type: "header",
        text: {
          type: "plain_text",
          text: `Optimization Report - ${formattedDate}`,
          emoji: true,
        },
      },
      {
        type: "context",
        elements: [
          {
            type: "plain_text",
            text: `:calendar: ${formattedDate}`,
            emoji: true,
          },
        ],
      },
      { type: "divider" },
      {
        type: "rich_text",
        elements: subscriptionElements,
      },
      { type: "divider" },
      { ...queueStatElements },
      { type: "divider" },
      { ...aiStatElements },
    ];

    const res = await this.#client().send({ blocks });

    if (res.text === "ok") {
      logger.debug(`IO Stats send to slack`, res);
    } else {
      logger.error(`IO Stats faild send to slack`, res);
    }

    await AdminReportService.createOrUpdateReportData({
      date: moment(date).format("YYYY-MM-DD"),
      type: AdminReportTypes.DAILY_IMAGE_OPTIMIER_REPORT,
      stats: { subscriptions: subscription, imageOptimizerData: ioData, aiContentData },
    });

    return res;
  };

  sendDailyStatusReport = async (date = undefined) => {
    const { startDate, endDate, compareStartDate, compareEndDate, interval } = AdminReportService.prepareDates(date);

    const formattedDate = moment(endDate).format("Do MMM, YYYY (dddd)");
    console.log("Sending Daily Stats of Date =", formattedDate);
    const report = await AdminReportService.dailyStatusReport(startDate, endDate);

    const compareReport = await AdminReportService.dailyStatusReport(compareStartDate, compareEndDate);

    const data = serializeDailyStatsData(report, compareReport);

    const blocks = prepareDailyStatusReportBlock(formattedDate, data);

    const res = await this.#client().send({ blocks });

    if (res.text !== "ok") {
      logger.error(`Daily status report of ${formattedDate} faild send to slack`, res);
    }

    await AdminReportService.createOrUpdateReportData({
      date: moment(endDate).format("YYYY-MM-DD"),
      type: AdminReportTypes.DAILY_STATUS_REPORT,
      stats: report,
    });

    return res;
  };

  prepareIOSubsBlocks = (subscription) => {
    const freeIo = subscription.find((row) => Number(row.images) === 50)?.count || 0;
    const proIOs = subscription.filter((row) => Number(row.images) > 50);
    // const oneThousandIo = subscription.find((row) => Number(row.images) === 1000)?.count || 0;
    // const fiveThousandIo = subscription.find((row) => Number(row.images) === 5000)?.count || 0;
    // const twentyFiveThousandIo = subscription.find((row) => Number(row.images) === 25000)?.count || 0;

    const formattedData = [
      { images: 50, count: Number(freeIo) },
      // { images: 1000, count: Number(oneThousandIo) },
      // { images: 5000, count: Number(fiveThousandIo) },
      // { images: 25000, count: Number(twentyFiveThousandIo) },
      ...proIOs.map((row) => ({
        images: Number(row.images),
        count: Number(row.count),
      })),
    ];

    const total = formattedData.reduce((totalCount, row) => totalCount + row.count, 0);
    const proStores = formattedData.filter((r) => Number(r.images) > 50);
    const totalPro = proStores.reduce((totalCount, row) => totalCount + row.count, 0);

    const proStoresElements = proStores.map((row) => ({
      type: "rich_text_section",
      elements: [
        {
          type: "text",
          text: `${row.images} images - `,
        },
        {
          type: "text",
          text: `${row.count}`,
        },
      ],
    }));

    return [
      {
        type: "rich_text_section",
        elements: [
          {
            type: "text",
            text: "Subscribed to Image Optimizer - ",
            style: {
              bold: true,
            },
          },
          {
            type: "text",
            text: `${total}`,
          },
          {
            type: "text",
            text: "\n",
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 0,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: "Free User (50 images) - ",
              },
              {
                type: "text",
                text: `${freeIo}`,
              },
            ],
          },
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: "Pro User - ",
              },
              {
                type: "text",
                text: `${totalPro}`,
              },
            ],
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 1,
        elements: proStoresElements,
      },
    ];
  };

  prepareIODataBlock = (ioData) => ({
    type: "rich_text",
    elements: [
      {
        type: "rich_text_section",
        elements: [
          {
            type: "text",
            text: "Queue Status",
            style: {
              bold: true,
            },
          },
          {
            type: "text",
            text: "\n",
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 0,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: "Queue usage:",
                style: {
                  bold: true,
                },
              },
            ],
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 1,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Max - ${ioData?.maxQueueUsage || 0}`,
              },
            ],
          },
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Min - ${ioData?.minQueueUsage || 0}`,
              },
            ],
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 0,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: "Stores queued:",
                style: {
                  bold: true,
                },
              },
            ],
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 1,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Max - ${ioData?.maxStoreCount || 0}`,
              },
            ],
          },
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: "Min - 3",
                text: `Min - ${ioData?.minStoreCount || 0}`,
              },
            ],
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 0,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: "Images:",
                style: {
                  bold: true,
                },
              },
            ],
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 1,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Queued for optimization - ${ioData?.imagesQueued || 0}`,
              },
            ],
          },
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Successfully optimized - ${ioData?.imagesProcessed || 0}`,
              },
            ],
          },
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Failed to optimize - ${ioData?.imagesFailed || 0}`,
              },
            ],
          },

          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Failed to process - ${ioData?.imagesFailedToProcess || 0}`,
              },
            ],
          },

          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Webhooks received - ${ioData?.webhooksCount || 0}`,
              },
            ],
          },
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Invalid images found - ${ioData?.invalidImages || 0}`,
              },
            ],
          },
        ],
      },
    ],
  });

  prepareAiDataBlocks = (aiData) => ({
    type: "rich_text",
    elements: [
      {
        type: "rich_text_section",
        elements: [
          {
            type: "text",
            text: "AI Content Optimizer Stats",
            style: { bold: true },
          },
          { type: "text", text: "\n" },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 0,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: "Total Stores Tried - ",
                style: { bold: true },
              },
              {
                type: "text",
                text: `${aiData.aiContentSettingStats.total}`,
              },
            ],
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 1,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Currently Inactive - `,
              },
              {
                type: "text",
                text: `${aiData.aiContentSettingStats.inactive}`,
              },
            ],
          },
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: `Currently Active - `,
              },
              {
                type: "text",
                text: `${aiData.aiContentSettingStats.active}`,
              },
            ],
          },
        ],
      },
      {
        type: "rich_text_list",
        style: "bullet",
        indent: 0,
        elements: [
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: "Product AI Content Generated - ",
                style: { bold: true },
              },
              {
                type: "text",
                text: `${aiData.aiContentGenerated}`,
              },
            ],
          },
          {
            type: "rich_text_section",
            elements: [
              {
                type: "text",
                text: "Image alt-text Generated - ",
                style: { bold: true },
              },
              {
                type: "text",
                text: `${aiData.altTextGenerated}`,
              },
            ],
          },
        ],
      },
    ],
  });

  getIOSubscriptionData = async (startTime, endTime) => {
    const query = `
      WITH io_stats AS (
        SELECT
          id,
          DOMAIN,
          plan_id,
          plan_info ->> 'name' AS plan_name,
          (plan_rules ->> 'products')::DECIMAL AS products,
          (plan_rules ->> 'image_optimizer')::DECIMAL AS images,
          subscribed_at
        FROM
          shops
        WHERE
          subscribed_at BETWEEN '${startTime}' AND '${endTime}'
          AND plan_rules ->> 'image_optimizer' IS NOT NULL
          AND email NOT LIKE '%@wpdeveloper%'
        ORDER BY
          subscribed_at DESC
      )
      SELECT images, count(*) AS count FROM io_stats GROUP BY images ORDER BY images
    `;

    const [results] = await sequelize.query(query, { type: Sequelize.QueryTypes.RAW });

    return results;
  };

  imageOptimizerData = async (date, queueName = QUEUE_BASE_NAME) => {
    const data = await QueueStats.findOne({
      where: { date, queue_name: queueName },
      rejectOnEmpty: false,
    });

    return {
      maxQueueUsage: 0,
      minQueueUsage: 0,

      maxStoreCount: 0,
      minStoreCount: 0,

      imagesQueued: 0,
      imagesProcessed: 0,
      imagesFailedToProcess: 0,
      imagesFailed: 0,

      webhooksCount: 0,
      invalidImages: 0,

      ...(data?.statistics || {}),
    };
  };

  /**
   *
   * @param {string['YYYY-MM-DD']} date
   * @returns
   */
  getAiContentOptimizationStats = async (date) => {
    const [{ active, inactive }] = await sequelize.query(
      `SELECT
        SUM(CASE WHEN (value::json ->> 'status')::BOOLEAN THEN 1 ELSE 0 END) active,
        SUM(CASE WHEN (value::json ->> 'status')::BOOLEAN = FALSE THEN 1 ELSE 0 END) inactive
      FROM
        shop_settings
      WHERE
        "key" = 'ai_content_settings' and created_at <= DATE(:date) + 1;`,
      { type: QueryTypes.SELECT, replacements: { date } }
    );

    const aiContentSettingStats = {
      active: Number(active),
      inactive: Number(inactive),
      total: Number(active) + Number(inactive),
    };

    const [{ ai_content_generated }] = await sequelize.query(
      `SELECT
        COUNT(id) AS ai_content_generated
      FROM
        products
      WHERE
        ai_optimization_status = :optimizationStatus
        and DATE (ai_optimized_at) = :date
      GROUP BY
        DATE (ai_optimized_at);`,
      { type: QueryTypes.SELECT, replacements: { date, optimizationStatus: AiOptimizationStatus.OPTIMIZED } }
    );

    const [{ alt_text_generated }] = await sequelize.query(
      `SELECT
        COUNT(id) AS alt_text_generated
      FROM
        product_images
      WHERE
        alt_text_optimization_status = :optimizationStatus
        and DATE (alt_text_optimized_at) = :date
      GROUP BY
        DATE (alt_text_optimized_at)`,
      { type: QueryTypes.SELECT, replacements: { date, optimizationStatus: AiOptimizationStatus.OPTIMIZED } }
    );

    return {
      aiContentSettingStats,
      aiContentGenerated: Number(ai_content_generated),
      altTextGenerated: Number(alt_text_generated),
    };
  };
}

module.exports = new SlackService();
