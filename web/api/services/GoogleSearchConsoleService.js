const { google, webmasters_v3 } = require("googleapis");

const GoogleAuthService = require("./GoogleAuthService");
const searchConsoleDimensions = require("storeseo-enums/searchConsole/dimensions");
const { default: axios } = require("axios");

/**
 * @typedef {object} searchConsoleAnalyticsQuery.Options
 * @property {import("storeseo-enums/analytics/jsDocTypes").dateRange} dateRange,
 * @property {string[]} dimensions
 * @property {number} [rowLimit]
 * @property {webmasters_v3.Schema$ApiDimensionFilterGroup} [filters]
 */

/**
 * @typedef {object} IndividualHttpReqOptionForBatchQuery
 * @property {string} id
 * @property {string} url
 * @property {"POST"| "GET" | "PUT" | "DELETE"} method
 * @property {webmasters_v3.Schema$SearchAnalyticsQueryRequest} data
 */

/**
 * @typedef {object} IndividualHttpResOfBatchQuery
 * @property {string} id
 * @property {number} status
 * @property {object} data
 */

const SEARCH_ANALYTICS_URL = "https://www.googleapis.com/webmasters/v3/sites/siteUrl/searchAnalytics/query";

class GoogleSearchConsoleService {
  /**
   *
   * @param {import("./GoogleAnalyticsService").authenticatedUser} user
   */
  #authClient = (user) => {
    let authClient;

    if (user.serviceJson) {
      authClient = GoogleAuthService.getServiceJsonAuthClient(user.serviceJson);
    } else authClient = GoogleAuthService.getOAuthClient(user.refresh_token);

    return authClient;
  };

  /**
   *
   * @param {import("./GoogleAnalyticsService").authenticatedUser} user
   * @param {string} siteUrl
   * @param {searchConsoleAnalyticsQuery.Options} options
   *
   * @returns {Promise<webmasters_v3.Schema$ApiDataRow[]>}
   */
  runAnalyticsQuery = async (user, siteUrl, options) => {
    const authClient = this.#authClient(user);
    const webmaster = google.webmasters({ version: "v3", auth: authClient });

    const {
      dateRange: { startDate, endDate },
      dimensions,
      rowLimit = 5,
      filters,
    } = options;

    const { data } = await webmaster.searchanalytics.query({
      requestBody: {
        startDate,
        endDate,
        dimensions,
        rowLimit,
        dimensionFilterGroups: filters,
      },
      siteUrl,
    });

    return data.rows || [];
  };

  /**
   *
   * @param {import("./GoogleAnalyticsService").authenticatedUser} user
   * @param {string} siteUrl
   * @param {searchConsoleAnalyticsQuery.Options & { values: string []}} options
   */
  runAnalyticsQueryAgainstSepecificQueryValues = async (user, siteUrl, options) => {
    const { values } = options;

    /**
     * @type {webmasters_v3.Schema$ApiDimensionFilterGroup[]}
     */
    const filters = [
      {
        groupType: "and",
        filters: [
          {
            dimension: searchConsoleDimensions.QUERY,
            operator: "includingRegex",
            expression: `(?:${values.map((v) => `(?:${v})`).join("|")})`,
          },
        ],
      },
    ];

    return this.runAnalyticsQuery(user, siteUrl, { ...options, filters });
  };

  /**
   *
   * @param {IndividualHttpReqOptionForBatchQuery} request
   * @param {string} boundaryFlag
   *
   * @returns {string}
   */
  #generateHttpRequestString = (request, boundaryFlag) => {
    const parts = [
      `--${boundaryFlag}`,
      "Content-Type: application/http",
      `Content-Id: ${request.id}`,
      "",
      `${request.method} ${request.url}`,
      "",
      `${JSON.stringify(request.data, null, 2)}`,
    ];

    return parts.join("\n");
  };

  /**
   *
   * @param {string} resString
   * @returns {IndividualHttpResOfBatchQuery}
   */
  #constructHttpResFromString = (resString) => {
    const id = resString.match(/Content-ID:.*/gim)?.[0].split("response-")[1];
    const status = resString.match(/HTTP\/.*/gim)?.[0].split(" ")[1];

    const startPosOfData = resString.indexOf("{");
    const data = JSON.parse(resString.substring(startPosOfData), null, 2);

    return {
      id,
      status: Number(status),
      data,
    };
  };

  /**
   * @param {string} body
   * @returns {IndividualHttpResOfBatchQuery[]}
   */
  #parseBatchHttpResponseBody = (body) => {
    const responseBoundary = body.split(/\r\n/gim)[1];
    const responseBoundaryRegex = new RegExp(`\n?\r\n${responseBoundary}(?:--)?\r\n`, "gmi");

    const responses = body.split(responseBoundaryRegex).filter((r) => r.length);

    return responses.map((r) => this.#constructHttpResFromString(r));
  };

  /**
   *
   * @param {import("./GoogleAnalyticsService").authenticatedUser} user
   * @param {IndividualHttpReqOptionForBatchQuery[]} requests
   * @returns
   */
  runBatchRequests = async (user, requests) => {
    const authClient = this.#authClient(user);
    const { token } = await authClient.getAccessToken();

    const boundaryFlag = "batch_foo";

    const requestStrings = requests.map((r) => this.#generateHttpRequestString(r, boundaryFlag));
    requestStrings.push(`--${boundaryFlag}--`);

    const { data: response } = await axios.post(
      "https://www.googleapis.com/batch/webmasters/v3",
      requestStrings.join("\n"),
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": `multipart/mixed; boundary=${boundaryFlag}`,
        },
      }
    );

    return this.#parseBatchHttpResponseBody(response);
  };

  /**
   *
   * @param {{ sitUrl: string, dimensions: string[], filterBy: string, filterValue: string, dateRange: import("storeseo-enums/analytics/jsDocTypes").dateRange  }} param0
   *
   * @returns {IndividualHttpReqOptionForBatchQuery}
   */
  #craftHttpRequestOptionsForAnalyticsQuery = ({ siteUrl, dateRange, dimensions, filterBy, filterValue }) => {
    const encodedSiteUrl = encodeURIComponent(siteUrl);

    return {
      id: filterValue,
      url: SEARCH_ANALYTICS_URL.replace("siteUrl", encodedSiteUrl),
      method: "POST",
      data: {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        dimensions,
        dimensionFilterGroups: [
          {
            filters: [
              {
                dimension: filterBy,
                operator: "includingRegex",
                expression: `/${filterValue}/`,
              },
            ],
          },
        ],
      },
    };
  };

  /**
   *
   * @param {import("./GoogleAnalyticsService").authenticatedUser} user
   * @param {string} siteUrl
   * @param {{ dateRange: import("storeseo-enums/analytics/jsDocTypes").dateRange, values: string[] }} options
   *
   */
  runDateWiseAnalyticsAgainstSepecificQueryValues = async (user, siteUrl, options) => {
    const { dateRange, values } = options;

    if (values.length === 0) return [];

    const requests = values.map((v) =>
      this.#craftHttpRequestOptionsForAnalyticsQuery({
        siteUrl,
        dateRange,
        dimensions: [searchConsoleDimensions.DATE],
        filterBy: searchConsoleDimensions.QUERY,
        filterValue: v,
      })
    );

    return (await this.runBatchRequests(user, requests)).map((res) => ({
      ...res,
      query: res.id,
      ok: res.status === 200,
    }));
  };
}

module.exports = new GoogleSearchConsoleService();
