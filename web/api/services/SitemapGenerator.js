const fs = require("node:fs");
const path = require("node:path");
const ShopService = require("./ShopService");
const ProductService = require("./ProductService");
const PageService = require("./PageService");
const ArticleService = require("./ArticleService");
const settingKeys = require("storeseo-enums/settingKeys");
const cache = require("../cache");
const SubscriptionPlanService = require("./SubscriptionPlanService");
const planType = require("storeseo-enums/planType");
const pageType = require("storeseo-enums/pageType");
const sanitizeHtml = require("sanitize-html");
const { encode } = require("html-entities");
const CollectionService = require("./collections/CollectionService");

class SitemapGenerator {
  HTML_SITEMAP_CSS = fs.readFileSync(path.resolve(__dirname, `${process.cwd()}/frontend/assets/css/sitemap.css`));
  SITEMAP_CONTENT_COMMENT = "<!-- SITEMAP CONTENT -->";
  HTML_DOCUMENT = `
    <div class="sh-container">
      <style>${this.HTML_SITEMAP_CSS}</style>
      ${this.SITEMAP_CONTENT_COMMENT}
    </div>
  `;
  STORE_SEO_BRANDING = `
  <div class="sh-footer">
    <span style="color: #303030;">Powered by</span>
    <a href="https://storeseo.com/" target="_blank">
      <svg width="100" height="29" viewBox="0 0 100 29" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask id="mask0_46218_33038" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="100" height="29">
          <path d="M99.6867 0.0107422H0.0117188V28.9893H99.6867V0.0107422Z" fill="white"/>
        </mask>
        <g mask="url(#mask0_46218_33038)">
          <path d="M37.9148 21.1964C36.684 21.1964 35.6291 20.8754 35.0664 20.6079V18.9137C35.8753 19.2882 36.7719 19.5023 37.5104 19.5023C38.7763 19.5023 39.3565 18.9672 39.3565 17.8081C39.3565 16.5954 38.8994 16.2567 37.4401 15.4185C35.8753 14.509 34.9258 13.8314 34.9258 11.8519C34.9258 9.60495 36.2972 8.41016 38.8642 8.41016C39.7434 8.41016 40.6401 8.55282 41.4137 8.80244L41.1851 10.3718C40.6576 10.1934 39.9368 10.0865 39.2686 10.0865C38.3543 10.0865 37.4225 10.2648 37.4225 11.5487C37.4225 12.3691 37.7565 12.6544 39.2159 13.5639C40.9566 14.6339 41.8533 15.2936 41.8533 17.4336C41.8708 19.9837 40.5873 21.1964 37.9148 21.1964Z" fill="#26263F"/>
          <path d="M46.8623 21.1961C45.0513 21.1961 44.2425 20.3579 44.2425 18.4854V13.2961H43.0469V11.6733H44.3128L44.471 9.39062H46.581V11.6733H48.6205L48.5501 13.2961H46.581V18.3963C46.581 19.2701 46.792 19.7337 47.8821 19.7337C48.0931 19.7337 48.3041 19.6981 48.4798 19.6268L48.4095 20.9285C48.0227 21.1069 47.4601 21.1961 46.8623 21.1961Z" fill="#26263F"/>
          <path d="M53.7568 21.1963C50.4689 21.1963 50.0117 19.3239 50.0117 17.4692V15.1509C50.0117 12.6543 51.2425 11.4238 53.7568 11.4238C56.2887 11.4238 57.5194 12.6365 57.5194 15.1509V17.4692C57.5194 20.0906 56.3942 21.1963 53.7568 21.1963ZM53.7568 12.904C52.4909 12.904 52.3502 13.8848 52.3502 14.6159V18.0577C52.3502 18.771 52.4909 19.734 53.7568 19.734C55.0227 19.734 55.1634 18.771 55.1634 18.0577V14.6159C55.181 13.4568 54.7238 12.904 53.7568 12.904Z" fill="#26263F"/>
          <path d="M59.1758 20.9828V11.6739H61.3209V13.3324C61.778 11.9949 62.745 11.46 63.9055 11.46C63.9406 11.46 63.9758 11.46 63.9934 11.46V13.6177C63.9582 13.6177 63.9406 13.6177 63.9055 13.6177C63.0088 13.6177 62.0769 13.9566 61.5495 14.4916L61.5143 14.5272V21.0006H59.1758V20.9828Z" fill="#26263F"/>
          <path d="M69.3722 21.1968C66.7698 21.1968 65.293 19.8415 65.293 17.4875V15.1336C65.293 12.6904 66.5061 11.46 68.8799 11.46C71.2889 11.46 72.4136 12.5477 72.4136 14.9017V16.7564H67.5962V17.5767C67.5962 18.932 68.4933 19.6988 70.0403 19.6988C70.6733 19.6988 71.3414 19.574 72.1503 19.2708L72.0796 20.7331C71.4465 21.0185 70.3743 21.1968 69.3722 21.1968ZM68.8974 12.8331C67.7545 12.8331 67.5962 13.6712 67.5962 14.5094V15.6329H70.181V14.5094C70.181 13.6712 70.0228 12.8331 68.8974 12.8331Z" fill="#26263F"/>
          <path d="M76.9069 21.1964C75.6764 21.1964 74.6218 20.8754 74.0587 20.6079V18.9137C74.8676 19.2882 75.7647 19.5023 76.5028 19.5023C77.7689 19.5023 78.3488 18.9672 78.3488 17.8081C78.3488 16.5954 77.8915 16.2567 76.4327 15.4185C74.8676 14.509 73.918 13.8314 73.918 11.8519C73.918 9.60495 75.2898 8.41016 77.8565 8.41016C78.736 8.41016 79.6324 8.55282 80.4063 8.80244L80.1773 10.3718C79.65 10.1934 78.9293 10.0865 78.2612 10.0865C77.3467 10.0865 76.4146 10.2648 76.4146 11.5487C76.4146 12.3691 76.7493 12.6544 78.208 13.5639C79.949 14.6339 80.8454 15.2936 80.8454 17.4336C80.8636 19.9837 79.5624 21.1964 76.9069 21.1964Z" fill="#26263F"/>
          <path d="M82.5586 20.9824V8.67773H89.2227V10.3005H85.0552V13.9741H88.5364V15.5969H85.0552V19.324H89.3803V20.9824H82.5586Z" fill="#26263F"/>
          <path d="M95.1851 21.1961C92.2838 21.1961 90.6836 19.5199 90.6836 16.4882V13.1356C90.6836 10.1397 92.3188 8.42773 95.1851 8.42773C98.139 8.42773 99.686 10.0505 99.686 13.1356V16.5239C99.686 19.502 98.0333 21.1961 95.1851 21.1961ZM95.1851 10.104C93.8489 10.104 93.2859 10.8708 93.2859 12.672V17.0767C93.2859 18.753 93.8665 19.5199 95.1851 19.5199C96.8729 19.5199 97.0837 18.3072 97.0837 17.1302V12.672C97.0837 10.8708 96.5213 10.104 95.1851 10.104Z" fill="#26263F"/>
          <path d="M30.4431 1.66921C30.32 1.11639 29.9683 0.634898 29.4936 0.331736C29.1596 0.11774 28.7727 0.0107422 28.3859 0.0107422C27.6475 0.0107422 26.9617 0.385235 26.575 1.02722L15.1112 20.162C14.4958 21.1786 14.8123 22.4982 15.8145 23.1045C16.1485 23.3185 16.5353 23.4255 16.9222 23.4255C17.6606 23.4255 18.3464 23.051 18.7332 22.409L30.2145 3.30985C30.4958 2.81052 30.5838 2.23987 30.4431 1.66921Z" fill="#00CC76"/>
          <path d="M18.4893 5.25319C18.3662 4.70037 18.0146 4.21888 17.5398 3.91571C17.2058 3.70172 16.819 3.59473 16.4322 3.59473C15.6937 3.59473 15.008 3.96922 14.6211 4.6112L5.7596 19.1629C5.14421 20.1794 5.4607 21.499 6.4629 22.1053C6.79698 22.3194 7.18376 22.4263 7.5706 22.4263C8.30907 22.4263 8.99474 22.0518 9.38159 21.4098L18.2432 6.85816C18.5421 6.37667 18.6299 5.80601 18.4893 5.25319Z" fill="#00CC76"/>
          <path d="M8.51674 6.59108C8.39369 6.03826 8.042 5.55677 7.56726 5.25361C7.23322 5.03961 6.84637 4.93262 6.45959 4.93262C5.72113 4.93262 5.03541 5.30711 4.6486 5.94909L0.323315 12.7613C0.0244127 13.2428 -0.0634995 13.8313 0.07716 14.3841C0.200238 14.9369 0.534304 15.4184 1.00903 15.7216C1.3431 15.9355 1.72991 16.0426 2.11672 16.0426C2.85519 16.0426 3.5409 15.6681 3.92771 15.0261L8.27058 8.2139C8.56947 7.73238 8.65742 7.1617 8.51674 6.59108Z" fill="#00CC76"/>
          <path d="M6.1813 26.5643C6.04063 25.5122 5.14393 24.7275 4.10657 24.7275C4.00107 24.7275 3.91316 24.7275 3.80766 24.7453C2.66481 24.9059 1.85602 25.9936 2.03185 27.1706C2.1725 28.2228 3.0692 29.0074 4.10657 29.0074C4.21206 29.0074 4.29998 29.0074 4.40547 28.9896C5.53074 28.8291 6.33954 27.7413 6.1813 26.5643Z" fill="#00CC76"/>
          <path d="M15.568 26.5643C15.4274 25.5122 14.5307 24.7275 13.4933 24.7275C13.3878 24.7275 13.2999 24.7275 13.1944 24.7453C12.0515 24.9059 11.2427 25.9936 11.4186 27.1706C11.5593 28.2228 12.4559 29.0074 13.4933 29.0074C13.5988 29.0074 13.6867 29.0074 13.7922 28.9896C14.3549 28.9005 14.8471 28.6151 15.1812 28.1515C15.5153 27.7056 15.656 27.135 15.568 26.5643Z" fill="#00CC76"/>
          <path d="M21.8991 8.92736C21.2661 9.83681 20.053 10.1221 19.1035 9.53368C18.1541 8.94522 17.82 7.69692 18.3123 6.69824L18.1013 7.03708L11.332 18.1648C11.3497 18.147 11.3497 18.147 11.3673 18.1292C11.3848 18.1113 11.4024 18.0757 11.42 18.0578C11.4376 18.04 11.4551 18.0222 11.4727 18.0043C11.8595 17.5763 12.4222 17.3267 13.02 17.3267C14.198 17.3267 15.165 18.2896 15.165 19.5023C15.165 19.7876 15.1123 20.073 15.0068 20.3226L19.0332 13.6531L21.8991 8.92736Z" fill="#00AA95"/>
          <path d="M11.3633 18.1475C11.3809 18.1296 11.3984 18.094 11.416 18.0762C11.416 18.094 11.3809 18.1118 11.3633 18.1475Z" fill="#E54141"/>
        </g>
      </svg>
    </a>
  </div>
  `;

  #shouldShowBranding = async (shopDomain, branding) => {
    if (!branding) return false;
    const planId = await cache.planId(shopDomain);
    const plan = await SubscriptionPlanService.getSubscriptionPlanNameAndType(planId);
    return plan.planType === planType.FREE;
  };

  /**
   *
   * @param {string} title
   * @param {string} description
   * @param {{ textColor: string, headingColor: string }} styleSettings
   * @returns
   */
  #htmlSitemapHeader = (title, description, { textColor, headingColor }) => {
    const descriptionHtml = !description
      ? ""
      : `
    <p class="subtitle" style="color: ${textColor}">${sanitizeHtml(description)}</p>
    `;

    return `
    <div class="sh-header border-bottom">
        <h2 class="title" style="color: ${headingColor};">${sanitizeHtml(title)}</h2>
        ${sanitizeHtml(descriptionHtml)}
    </div>
    `;
  };

  /**
   *
   * @param {{ title: string, items: { content: string, href: string }[], viewMoreHref: string, config: any}} param0
   */
  #composeGridColumnContent = ({ title, items, config, viewMoreHref }) => {
    if (items?.length === 0) {
      return "";
    }
    const { textColor, linkColor, textAlign, buttonText, buttonColor, buttonTextColor } = config;
    const titleHtml = !title
      ? ""
      : `
    <h3 class="link-title" style="color: ${textColor};">${sanitizeHtml(title)}</h3>
    `;
    const loadMoreButton =
      !viewMoreHref || !items?.length
        ? ""
        : `
    <div class="load-more" style="text-align: ${textAlign};">
      <a
        href="${viewMoreHref}"
        class="sh-button"
        style="background: ${buttonColor}; color: ${buttonTextColor};"
      >
        ${sanitizeHtml(buttonText)}
      </a>
    </div>
    `;

    return `
    <div class="sh-link-group" style="text-align: ${textAlign};">
      ${titleHtml}
      <ul class="link-menu">
        ${items
          .map(
            (item) => `
          <li class="link-item">
            <a href="${item.href}" style="color: ${linkColor};">${sanitizeHtml(item.content)}</a>
          </li>`
          )
          .join("")}
      </ul>
      ${loadMoreButton}
    </div>
    `;
  };

  #composeShopSitemapContent = async ({ shopId, sitemapSettings, proxyPathPrefix }) => {
    const {
      homepageItemLimit,
      columns,
      productHeading,
      collectionHeading,
      pageHeading,
      blogHeading,
      hideProducts,
      hideCollections,
      hidePages,
      hideBlogs,
    } = sitemapSettings;

    const result = await Promise.allSettled([
      hideProducts
        ? []
        : ProductService.getSitemapEnabledProductsByShopId(shopId, ["handle", "title"], homepageItemLimit),
      hideCollections
        ? []
        : CollectionService.getSitemapEnabledCollectionsByShopId(shopId, ["handle", "title"], homepageItemLimit),
      hidePages
        ? []
        : PageService.getSitemapEnabledPagesByShopId(
            shopId,
            ["title", "handle", "published_at", "page_type"],
            homepageItemLimit
          ),
      hideBlogs
        ? []
        : ArticleService.getSitemapEnabledArticlesByShopId(
            shopId,
            ["title", "handle", "published_at"],
            homepageItemLimit
          ),
    ]);

    const products =
      result[0].status === "fulfilled"
        ? result[0].value.map((p) => ({ content: p.title, href: `/products/${p.handle}` }))
        : [];

    const productSitemap = this.#composeGridColumnContent({
      title: productHeading,
      items: products,
      config: sitemapSettings,
      viewMoreHref: `${proxyPathPrefix}/products`,
    });

    const collections =
      result[1].status === "fulfilled"
        ? result[1].value.map((c) => ({ content: c.title, href: `/collections/${c.handle}` }))
        : [];

    const collectionSitemap = this.#composeGridColumnContent({
      title: collectionHeading,
      items: collections,
      config: sitemapSettings,
      viewMoreHref: `${proxyPathPrefix}/collections`,
    });

    const pages =
      result[2].status === "fulfilled"
        ? result[2].value
            .filter((p) => p.published_at)
            .map((p) => ({ content: p.title, href: p.page_type === pageType.HOMEPAGE ? "/" : `/pages/${p.handle}` }))
        : [];

    const pagesSitemap = this.#composeGridColumnContent({
      title: pageHeading,
      items: pages,
      config: sitemapSettings,
      viewMoreHref: `${proxyPathPrefix}/pages`,
    });

    const articles =
      result[3].status === "fulfilled"
        ? result[3].value
            .filter((a) => a.published_at)
            .map((a) => ({ content: a.title, href: `/blogs/${a.blog.blog_handle}/${a.handle}` }))
        : [];

    const articlesSitemap = this.#composeGridColumnContent({
      title: blogHeading,
      items: articles,
      config: sitemapSettings,
      viewMoreHref: `${proxyPathPrefix}/blogs`,
    });

    return `
    <div class="sh-block" style="grid-template-columns: repeat(${columns}, 1fr);">
      ${hideProducts ? "" : productSitemap}
      ${hideCollections ? "" : collectionSitemap}
      ${hidePages ? "" : pagesSitemap}
      ${hideBlogs ? "" : articlesSitemap}
    </div>
    `;
  };

  #composeGridColumns = (items, columns, config) => {
    const columnsContent = [];

    for (let i = 1; i <= columns; i++) {
      const itemsPerColumn = Math.ceil(items.length / columns);
      const startIdx = (i - 1) * itemsPerColumn;
      const endIdx = itemsPerColumn * i;

      const itemsForCurrentColumn = items.slice(startIdx, endIdx);

      const list = this.#composeGridColumnContent({
        title: "",
        items: itemsForCurrentColumn,
        config,
      });

      columnsContent.push(list);
    }

    return columnsContent;
  };

  #composeProductSitemapContent = async ({ shopId, sitemapSettings, proxyPathPrefix }) => {
    const { columns } = sitemapSettings;
    const productsList = await ProductService.getSitemapEnabledProductsByShopId(shopId, ["handle", "title"], null);

    const products = productsList?.map((p) => ({ content: p.title, href: `/products/${p.handle}` })) || [];

    const productSitemapList = this.#composeGridColumns(products, columns, sitemapSettings);

    return `
    <div class="sh-block" style="grid-template-columns: repeat(${columns}, 1fr);">
      ${productSitemapList.join("")}
    </div>
    `;
  };

  #composeCollectionSitemapContent = async ({ shopId, sitemapSettings, proxyPathPrefix }) => {
    const { columns } = sitemapSettings;
    const collectionList = await CollectionService.getSitemapEnabledCollectionsByShopId(
      shopId,
      ["handle", "title"],
      null
    );

    const collections = collectionList?.map((c) => ({ content: c.title, href: `/collections/${c.handle}` })) || [];

    const collectionSitemapList = this.#composeGridColumns(collections, columns, sitemapSettings);

    return `
    <div class="sh-block" style="grid-template-columns: repeat(${columns}, 1fr);">
      ${collectionSitemapList.join("")}
    </div>
    `;
  };

  #composePageSitemapContent = async ({ shopId, sitemapSettings, proxyPathPrefix }) => {
    const { columns } = sitemapSettings;
    const pageList = await PageService.getSitemapEnabledPagesByShopId(shopId, [
      "title",
      "handle",
      "published_at",
      "page_type",
    ]);

    const pages =
      pageList
        ?.filter((p) => p.published_at)
        .map((p) => ({ content: p.title, href: p.page_type === pageType.HOMEPAGE ? "/" : `/pages/${p.handle}` })) || [];

    const pageSitemapList = this.#composeGridColumns(pages, columns, sitemapSettings);

    return `
    <div class="sh-block" style="grid-template-columns: repeat(${columns}, 1fr);">
      ${pageSitemapList.join("")}
    </div>
    `;
  };

  #composeBlogSitemapContent = async ({ shopId, sitemapSettings, proxyPathPrefix }) => {
    const { columns } = sitemapSettings;
    const articleList = await ArticleService.getSitemapEnabledArticlesByShopId(shopId, [
      "title",
      "handle",
      "published_at",
    ]);

    const articles =
      articleList
        ?.filter((p) => p.published_at)
        .map((p) => ({ content: p.title, href: `/blogs/${p.blog.blog_handle}/${p.handle}` })) || [];

    const articlesSitemapList = this.#composeGridColumns(articles, columns, sitemapSettings);

    return `
    <div class="sh-block" style="grid-template-columns: repeat(${columns}, 1fr);">
      ${articlesSitemapList.join("")}
    </div>
    `;
  };

  htmlSitemapForShop = async (shopDomain, proxyPathPrefix) => {
    const { id } = await ShopService.getShop(shopDomain);
    const {
      value: { setting: sitemapSettings, branding },
    } = await ShopService.getShopSetting(id, settingKeys.HTML_SITEMAP);

    const {
      mainHeading,
      description,

      homepageMetaTitle,
      homepageMetaDescription,

      headingColor,
      textColor,
      backgroundColor,
    } = sitemapSettings;

    const metaTitle = homepageMetaTitle || mainHeading || "";
    const metaDescription = homepageMetaDescription || description || "";
    const metaContent = this.#generateMetaContent(metaTitle, metaDescription);

    const sitemapHeader = this.#htmlSitemapHeader(mainHeading, description, {
      headingColor,
      textColor,
    });

    const sitemapContent = await this.#composeShopSitemapContent({
      shopId: id,
      sitemapSettings,
      proxyPathPrefix,
    });

    const brandingContent = (await this.#shouldShowBranding(shopDomain, branding)) ? this.STORE_SEO_BRANDING : "";

    const [part1, part2] = this.HTML_DOCUMENT.split(this.SITEMAP_CONTENT_COMMENT);

    return `
    ${metaContent}
    <div style="background-color: ${backgroundColor};">
      ${part1} 
      ${sitemapHeader}
      ${sitemapContent}
      ${brandingContent}
      ${part2}
    </div>
    `;
  };

  htmlSitemapForProducts = async (shopDomain, proxyPathPrefix) => {
    const { id } = await ShopService.getShop(shopDomain);
    const {
      value: { setting: sitemapSettings, branding },
    } = await ShopService.getShopSetting(id, settingKeys.HTML_SITEMAP);

    const {
      description,

      productHeading,

      productMetaTitle,
      productMetaDescription,

      headingColor,
      textColor,
      backgroundColor,
    } = sitemapSettings;

    const metaTitle = productMetaTitle || productHeading || "";
    const metaDescription = productMetaDescription || description || "";
    const metaContent = this.#generateMetaContent(metaTitle, metaDescription);

    const sitemapHeader = this.#htmlSitemapHeader(productHeading, "", {
      headingColor,
      textColor,
    });

    const sitemapContent = await this.#composeProductSitemapContent({
      shopId: id,
      sitemapSettings,
      proxyPathPrefix,
    });

    const brandingContent = (await this.#shouldShowBranding(shopDomain, branding)) ? this.STORE_SEO_BRANDING : "";

    const [part1, part2] = this.HTML_DOCUMENT.split(this.SITEMAP_CONTENT_COMMENT);

    return `
    ${metaContent}

    <div style="background-color: ${backgroundColor};">
      ${part1} 
      ${sitemapHeader}
      ${sitemapContent}
      ${brandingContent}
      ${part2}
    </div>
    `;
  };

  htmlSitemapForCollections = async (shopDomain, proxyPathPrefix) => {
    const { id } = await ShopService.getShop(shopDomain);
    const {
      value: { setting: sitemapSettings, branding },
    } = await ShopService.getShopSetting(id, settingKeys.HTML_SITEMAP);

    const {
      description,

      collectionHeading,

      collectionMetaTitle,
      collectionMetaDescription,

      headingColor,
      textColor,
      backgroundColor,
    } = sitemapSettings;

    const metaTitle = collectionMetaTitle || collectionHeading || "";
    const metaDescription = collectionMetaDescription || description || "";
    const metaContent = this.#generateMetaContent(metaTitle, metaDescription);

    const sitemapHeader = this.#htmlSitemapHeader(collectionHeading, "", {
      headingColor,
      textColor,
    });

    const sitemapContent = await this.#composeCollectionSitemapContent({
      shopId: id,
      sitemapSettings,
      proxyPathPrefix,
    });

    const brandingContent = (await this.#shouldShowBranding(shopDomain, branding)) ? this.STORE_SEO_BRANDING : "";

    const [part1, part2] = this.HTML_DOCUMENT.split(this.SITEMAP_CONTENT_COMMENT);

    return `
    ${metaContent}

    <div style="background-color: ${backgroundColor};">
      ${part1}
      ${sitemapHeader}
      ${sitemapContent}
      ${brandingContent}
      ${part2}
    </div>
    `;
  };

  htmlSitemapForPages = async (shopDomain, proxyPathPrefix) => {
    const { id } = await ShopService.getShop(shopDomain);
    const {
      value: { setting: sitemapSettings, branding },
    } = await ShopService.getShopSetting(id, settingKeys.HTML_SITEMAP);

    const {
      description,

      pageHeading,

      pageMetaTitle,
      pageMetaDescription,

      headingColor,
      textColor,
      backgroundColor,
    } = sitemapSettings;

    const metaTitle = pageMetaTitle || pageHeading || "";
    const metaDescription = pageMetaDescription || description || "";
    const metaContent = this.#generateMetaContent(metaTitle, metaDescription);

    const sitemapHeader = this.#htmlSitemapHeader(pageHeading, "", {
      headingColor,
      textColor,
    });

    const sitemapContent = await this.#composePageSitemapContent({
      shopId: id,
      sitemapSettings,
      proxyPathPrefix,
    });

    const brandingContent = (await this.#shouldShowBranding(shopDomain, branding)) ? this.STORE_SEO_BRANDING : "";

    const [part1, part2] = this.HTML_DOCUMENT.split(this.SITEMAP_CONTENT_COMMENT);

    return `
    ${metaContent}
    
    <div style="background-color: ${backgroundColor};">
      ${part1} 
      ${sitemapHeader}
      ${sitemapContent}
      ${brandingContent}
      ${part2}
    </div>
    `;
  };

  htmlSitemapForBlogs = async (shopDomain, proxyPathPrefix) => {
    const { id } = await ShopService.getShop(shopDomain);
    const {
      value: { setting: sitemapSettings, branding },
    } = await ShopService.getShopSetting(id, settingKeys.HTML_SITEMAP);

    const {
      description,

      blogHeading,

      blogMetaTitle,
      blogMetaDescription,

      headingColor,
      textColor,
      backgroundColor,
    } = sitemapSettings;

    const metaTitle = blogMetaTitle || blogHeading || "";
    const metaDescription = blogMetaDescription || description || "";
    const metaContent = this.#generateMetaContent(metaTitle, metaDescription);

    const sitemapHeader = this.#htmlSitemapHeader(blogHeading, "", {
      headingColor,
      textColor,
    });

    const sitemapContent = await this.#composeBlogSitemapContent({
      shopId: id,
      sitemapSettings,
      proxyPathPrefix,
    });

    const brandingContent = (await this.#shouldShowBranding(shopDomain, branding)) ? this.STORE_SEO_BRANDING : "";

    const [part1, part2] = this.HTML_DOCUMENT.split(this.SITEMAP_CONTENT_COMMENT);

    return `
    ${metaContent}

    <div style="background-color: ${backgroundColor};">
      ${part1} 
      ${sitemapHeader}
      ${sitemapContent}
      ${brandingContent}
      ${part2}
    </div>
    `;
  };

  #generateMetaContent = (title, description) => {
    return `
    {% assign title = "${encode(title)}" %}
    {% capture page_title %}
      {{ title }}
    {% endcapture %}

    {% assign description = "${encode(description)}" %}
    {% capture page_description %}
      {{ description }}
    {% endcapture %}
    
    `;
  };
}

module.exports = new SitemapGenerator();
