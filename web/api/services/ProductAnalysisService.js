const {
  calculateFocusKeywordInMetaTitle,
  calculateMetaDescLength,
  calculateMetaTitleLength,
  calculateFocusKeywordInAltText,
  calculateFocusKeywordInTitle,
  calculateFocusKeywordAtTheBeginningOfMetaTitle,
  calculateFocusKeywordInMetaDesc,
  calculateKeywordDensity,
  calculateDescWordLength,
  calculateAltTextInImg,
  calculateUniqueTitle,
  calculateUniqueFocusKeyword,
  totalScore,
  calculateAnalysisScores,
  calculateAllImageOptimized,
  calculateFocusKeywordInURL,
  calculateMulitLanguageUniqueFocusKeyword,
} = require("../utils/productScoreCalculations");
const SCORES = require("../config/seoScores");
const { serializeProductAnalysis } = require("../serializers/ProductAnalysisSerializer");
const { ProductAnalysis, sequelize, Op } = require("../../sequelize");
const { chunk, concat } = require("lodash/array");
const { sleep } = require("../utils/helper");
const { optimizePercent } = require("../config/app");

class ProductAnalysisService {
  /**
   * Get analysis data by shop and product id
   * @param shopId
   * @param productId
   * @returns {Promise<*>}
   */
  getAnalysisByShopProduct = async (shopId, productId) => {
    const analysisData = await ProductAnalysis.findOne({
      where: { shop_id: shopId, product_id: productId },
      rejectOnEmpty: false,
    });
    return analysisData?.toJSON();
  };

  /**
   * Get serialized analysis data by shop and product id
   * @param shopId
   * @param productId
   * @returns {Promise<{alt_text_in_all_img: (number|{defaultValue: number, type: SmallIntegerDataTypeConstructor}|*), meta_title_within_char_limit: (number|{defaultValue: number, type: SmallIntegerDataTypeConstructor}|*|number), focus_keyword_in_meta_title: (number|{defaultValue: number, type: SmallIntegerDataTypeConstructor}|*), focus_keyword_in_title: (number|{defaultValue: number, type: SmallIntegerDataTypeConstructor}|*), product_description_is_unique: (number|{defaultValue: number, type: SmallIntegerDataTypeConstructor}|*), focus_keyword_in_img_alt_text: (number|{defaultValue: number, type: SmallIntegerDataTypeConstructor}|*), focus_keyword_in_meta_desc: (number|{defaultValue: number, type: SmallIntegerDataTypeConstructor}|*), keyword_density_in_desc: (number|{defaultValue: number, type: SmallIntegerDataTypeConstructor}|*|number), unique_focus_keyword: (number|{defaultValue: number, type: SmallIntegerDataTypeConstructor}|*), meta_desc_within_char_limit: (number|{defaultValue: number, type: SmallIntegerDataTypeConstructor}|*|number), unique_title: (number|{defaultValue: number, type: SmallIntegerDataTypeConstructor}|*), focus_keyword_at_the_beginning_of_meta_title: (number|{defaultValue: number, type: SmallIntegerDataTypeConstructor}|*), desc_min_word_count_300: (number|number|{defaultValue: number, type: SmallIntegerDataTypeConstructor}|*)}>}
   */
  getSerializedAnalysisData = async (shopId, productId) => {
    const pa = await this.getAnalysisByShopProduct(shopId, productId);
    return serializeProductAnalysis(pa);
  };

  /**
   * Counts product analysis
   * @param shopId
   * @param productId
   * @returns {Promise<Promise<GroupedCountResultItem[]> | Promise<number>>}
   */
  countAnalysisByShopProduct = async (shopId, productId) => {
    return ProductAnalysis.count({ where: { shop_id: shopId, product_id: productId } });
  };

  /**
   *
   * @param shopId
   * @param productId
   * @param data
   * @returns {Promise<any>}
   */
  insertAnalysisForShopProduct = async (shopId, productId, data) => {
    const pa = await ProductAnalysis.create({
      shop_id: shopId,
      product_id: productId,
      ...data,
    });

    return pa.toJSON();
  };

  /**
   * Updates product analysis data
   * @param shopId
   * @param productId
   * @param data
   * @returns {Promise<*>}
   */
  updateAnalysisForShopProduct = async (shopId, productId, data) => {
    const [count, pa] = await ProductAnalysis.update(data, {
      where: { shop_id: shopId, product_id: productId },
      returning: true,
    });

    return pa[0]?.toJSON();
  };

  /**
   * Insert / Update product analysis data
   * @param shopId
   * @param productId
   * @param languageCode
   * @param data
   * @returns {Promise<*>}
   */
  upsertAnalysis = async (shopId, productId, lanugageCode, data) => {
    data = {
      ...data,
      shop_id: shopId,
      product_id: productId,
      language_code: lanugageCode,
    };
    const [analysis] = await ProductAnalysis.upsert(data);
    return analysis?.toJSON();
  };

  /**
   * Calculates products analysis scores
   * @param shopId
   * @param product
   * @param uniqueCheckEnable
   * @param oldProduct
   * @returns {Promise<{score: (number|number), is_analysed: boolean, is_optimized: boolean, passed, analysis: {alt_text_in_all_img: (number|number), meta_title_within_char_limit: (number|number|number), focus_keyword_in_meta_title: (number|number), focus_keyword_in_title: (number|number), product_description_is_unique: (number|undefined|{defaultValue: number, type: SmallIntegerDataTypeConstructor}|*), focus_keyword_in_img_alt_text: (number|number), focus_keyword_in_meta_desc: (number|number), keyword_density_in_desc: (number|number|number), unique_focus_keyword: (number|{defaultValue: number, type: SmallIntegerDataTypeConstructor}|*), meta_desc_within_char_limit: (number|number|number), unique_title: (number|{defaultValue: number, type: SmallIntegerDataTypeConstructor}|*),focus_keyword_in_url: (number|{defaultValue: number, type: SmallIntegerDataTypeConstructor}|*), focus_keyword_at_the_beginning_of_meta_title: (number|number), desc_min_word_count_300: (number|number|number)}, issues}>}
   */
  calculateProductScore = async ({ shopId, product, uniqueCheckEnable = false, oldProduct = null }) => {
    let checkUniqueFK = uniqueCheckEnable || !!(oldProduct && product.focus_keyword !== oldProduct.focus_keyword);
    let checkUniqueTitle = uniqueCheckEnable || !!(oldProduct && product.title !== oldProduct.title);
    // let checkUniqueDesc = uniqueCheckEnable || !!(oldProduct && product.description !== oldProduct.description);
    let checkUniqueDesc = uniqueCheckEnable;

    const analysis = {
      // Basic SEO
      unique_focus_keyword: checkUniqueFK
        ? await calculateUniqueFocusKeyword(shopId, product.focus_keyword, product?.id)
        : product.analysis?.unique_focus_keyword || SCORES.UNIQUE_FOCUS_KEYWORD,
      focus_keyword_in_meta_title: calculateFocusKeywordInMetaTitle(product, product.focus_keyword),
      meta_desc_within_char_limit: calculateMetaDescLength(product),
      meta_title_within_char_limit: calculateMetaTitleLength(product),
      unique_title: checkUniqueTitle
        ? await calculateUniqueTitle(shopId, product.title)
        : product.analysis?.unique_title || SCORES.UNIQUE_TITLE,

      // Detailed SEO
      focus_keyword_in_img_alt_text: calculateFocusKeywordInAltText(product.images, product.focus_keyword),
      focus_keyword_in_title: calculateFocusKeywordInTitle(product.title, product.focus_keyword),
      focus_keyword_in_meta_desc: calculateFocusKeywordInMetaDesc(product, product.focus_keyword),
      focus_keyword_at_the_beginning_of_meta_title: calculateFocusKeywordAtTheBeginningOfMetaTitle(
        product,
        product.focus_keyword
      ),
      keyword_density_in_desc: calculateKeywordDensity(product.description, product.focus_keyword),
      focus_keyword_in_url: calculateFocusKeywordInURL(product.handle, product.focus_keyword),
      desc_min_word_count_300: calculateDescWordLength(product.description),
      alt_text_in_all_img: calculateAltTextInImg(product.images),
      optimized_all_images: calculateAllImageOptimized(product.images),
    };

    const analysisScore = calculateAnalysisScores(analysis);
    // console.log("analysisScore: ", analysisScore);

    const score = Math.round((analysisScore.point * 100) / totalScore());

    return {
      analysis,
      score: score < 100 ? score : 100,
      issues: analysisScore.issues,
      passed: analysisScore.passed,
      is_analysed: true,
      is_optimized: score >= optimizePercent,
    };
  };

  /**
   * Products count with unique title
   * @param shopId
   * @returns {Promise<Promise<GroupedCountResultItem[]> | Promise<number>>}
   */
  productsCountHavingUniqueTitle = async (shopId) => {
    return ProductAnalysis.count({
      where: {
        shop_id: shopId,
        unique_title: { [Op.gt]: 0 },
      },
    });
  };

  /**
   * Products count with meta description
   * @param shopId
   * @returns {Promise<Promise<GroupedCountResultItem[]> | Promise<number>>}
   */
  productsCountHavingMetaDesc = async (shopId) => {
    return ProductAnalysis.count({
      where: {
        shop_id: shopId,
        meta_desc_within_char_limit: { [Op.gt]: 0 },
      },
    });
  };

  /**
   * Product count with meta title
   * @param shopId
   * @returns {Promise<Promise<GroupedCountResultItem[]> | Promise<number>>}
   */
  productsCountHavingMetaTitle = async (shopId) => {
    return ProductAnalysis.count({
      where: {
        shop_id: shopId,
        meta_title_within_char_limit: { [Op.gt]: 0 },
      },
    });
  };

  /**
   * Product count with image alt text
   * @param shopId
   * @returns {Promise<Promise<GroupedCountResultItem[]> | Promise<number>>}
   */
  productsCountHavingImgAltText = async (shopId) => {
    return ProductAnalysis.count({
      where: {
        shop_id: shopId,
        alt_text_in_all_img: { [Op.gt]: 0 },
      },
    });
  };

  /**
   * Get sum of analysis score by shop
   * @param shopId
   * @returns {Promise<any>}
   */
  getSumOfScoresByShop = async (shopId) => {
    const analysisData = await ProductAnalysis.findOne({
      attributes: [
        [sequelize.fn("SUM", sequelize.col("meta_title_within_char_limit")), "meta_title_within_char_limit"],
        [sequelize.fn("SUM", sequelize.col("meta_desc_within_char_limit")), "meta_desc_within_char_limit"],
        [sequelize.fn("SUM", sequelize.col("alt_text_in_all_img")), "alt_text_in_all_img"],
        [sequelize.fn("SUM", sequelize.col("unique_title")), "unique_title"],
        [sequelize.fn("SUM", sequelize.col("desc_min_word_count_300")), "desc_min_word_count_300"],
      ],
      where: { shop_id: shopId },
      rejectOnEmpty: false,
    });

    return analysisData.toJSON();
  };

  /**
   * Analyse all product of a shop
   * @param shopId
   * @returns {Promise<null>}
   */
  analyseShopAllProducts = async (shopId) => {
    const ProductService = require("./ProductService");
    const products = await ProductService.getAllProducts(shopId);
    const chunkSize = 50;

    if (products.length > 0) {
      const chunks = chunk(products, chunkSize);
      let analysedProducts = [];

      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];

        const analysis = await Promise.all(
          chunk.map(async (product) => await this.analyseEachProduct({ product, shopId }))
        );

        analysedProducts = concat(analysedProducts, analysis);
        if (i < chunks.length - 1) {
          await sleep(3000);
        }
      }
    }

    return null;
  };

  /**
   * Analysis of a single product with various checking and score calculation.
   * @param shopId
   * @param product
   * @param oldProduct
   * @param uniqueCheckEnable
   */
  analyseEachProduct = async ({ shopId, product, oldProduct = null, uniqueCheckEnable = false }) => {
    const ProductService = require("./ProductService");
    const { analysis, ...rest } = await this.calculateProductScore({
      shopId,
      product,
      oldProduct,
      uniqueCheckEnable,
    });
    // console.log("rest: ", rest);
    const pa = await this.upsertAnalysis(shopId, product.id, "default", analysis);
    return await ProductService.updateProduct(shopId, product.id, rest);
  };

  analyseMulitLanguageProduct = async ({ shopId, product, oldProduct = null, uniqueCheckEnable = false }) => {
    const ProductService = require("./ProductService");
    const { analysis, ...rest } = await this.calculateMulitLanguageProductScore({
      shopId,
      product,
      oldProduct,
      uniqueCheckEnable,
    });

    const pa = await this.upsertAnalysis(shopId, product.product_id, product.language_code, analysis);
    return ProductService.updateMulitLanguageProduct(shopId, product.id, rest);
  };

  calculateMulitLanguageProductScore = async ({ shopId, product, oldProduct, uniqueCheckEnable = false }) => {
    let checkUniqueFK = uniqueCheckEnable || !!(oldProduct && product.focus_keyword !== oldProduct.focus_keyword);
    let checkUniqueTitle = uniqueCheckEnable || !!(oldProduct && product.title !== oldProduct.title);
    let checkUniqueDesc = uniqueCheckEnable;

    const analysis = {
      // Basic SEO
      unique_focus_keyword: checkUniqueFK
        ? await calculateMulitLanguageUniqueFocusKeyword(
            shopId,
            product.focus_keyword,
            product.language_code,
            product?.id
          )
        : product.analysis?.unique_focus_keyword || SCORES.UNIQUE_FOCUS_KEYWORD,
      focus_keyword_in_meta_title: calculateFocusKeywordInMetaTitle(product, product.focus_keyword),
      meta_desc_within_char_limit: calculateMetaDescLength(product),
      meta_title_within_char_limit: calculateMetaTitleLength(product),
      unique_title: checkUniqueTitle
        ? await calculateUniqueTitle(shopId, product.title)
        : product.analysis?.unique_title || SCORES.UNIQUE_TITLE,

      // Detailed SEO
      focus_keyword_in_title: calculateFocusKeywordInTitle(product.title, product.focus_keyword),
      focus_keyword_in_meta_desc: calculateFocusKeywordInMetaDesc(product, product.focus_keyword),
      focus_keyword_at_the_beginning_of_meta_title: calculateFocusKeywordAtTheBeginningOfMetaTitle(
        product,
        product.focus_keyword
      ),
      keyword_density_in_desc: calculateKeywordDensity(product.description, product.focus_keyword),
      focus_keyword_in_url: calculateFocusKeywordInURL(product.handle, product.focus_keyword),
      desc_min_word_count_300: calculateDescWordLength(product.description),
    };

    const analysisScore = calculateAnalysisScores(analysis);

    const scoreConfig = { ...SCORES };
    delete scoreConfig.FOCUS_KEYWORD_IN_IMG_ALT_TEXT;
    delete scoreConfig.ALT_TEXT_IN_ALL_IMG;
    delete scoreConfig.OPTIMIZED_ALL_IMAGES;

    const score = Math.round((analysisScore.point * 100) / totalScore(scoreConfig));

    return {
      analysis,
      score: score < 100 ? score : 100,
      issues: analysisScore.issues,
      passed: analysisScore.passed,
      is_analysed: true,
      is_optimized: score >= optimizePercent,
    };
  };

  /**
   * Delete shop's all product analysis
   * @param shopId
   * @returns {Promise<number>}
   */
  deleteDataByShop = async (shopId) => {
    return ProductAnalysis.destroy({ where: { shop_id: shopId } });
  };

  /**
   * Delete single product analysis of a shop
   * @param shopId
   * @param productId
   * @param transaction
   * @returns {Promise<number>}
   */
  deleteProductAnalysis = async (shopId, productId, transaction = undefined) => {
    return ProductAnalysis.destroy({ where: { shop_id: shopId, product_id: productId }, transaction });
  };

  /**
   * Deletes all product analysis greater than product id
   * @param shopId
   * @param productId
   * @param transaction
   * @returns {Promise<number>}
   */
  deleteProductAnalysisGreaterThenProductId = async (shopId, productId, transaction = undefined) => {
    return ProductAnalysis.destroy({ where: { shop_id: shopId, product_id: { [Op.gte]: productId } }, transaction });
  };
}

module.exports = new ProductAnalysisService();
