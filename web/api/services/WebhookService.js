const { isEmpty } = require("lodash");
const { DeliveryMethod } = require("@shopify/shopify-api");
const { Webhook, Op } = require("../../sequelize");
const { WEBHOOKS, PRIORITY } = require("../config/webhook");
const ShopifyWebhookService = require("../services/ShopifyWebhookService");
const logger = require("storeseo-logger");
const { log } = require("handlebars");

class WebhookService {
  findWebhook = async (conditions) => {
    try {
      const webhook = await Webhook.findOne({ where: conditions, rejectOnEmpty: true });
      return webhook.toJSON();
    } catch (e) {
      // console.log(e);
    }
    return false;
  };

  hasWebhook = async (conditions) => {
    try {
      const count = await Webhook.count({ where: conditions });
      return count > 0;
    } catch (e) {
      // console.log(e);
    }
    return false;
  };

  getAllWebhooks = async (conditions, attributes) => {
    try {
      const webhooks = await Webhook.findAll({ where: conditions, attributes });
      return webhooks.map((wh) => wh.toJSON());
    } catch (e) {
      // console.log(e);
    }
    return false;
  };

  createWebhook = async (data) => {
    const webhook = await Webhook.create(data);
    return webhook.toJSON();
  };

  updateWebhook = async (id, data) => {
    const [affectedRows, webhooks] = await Webhook.update(data, { where: { id }, returning: true });

    if (affectedRows === 0) {
      return [];
    }

    if (affectedRows === 1) {
      return webhooks[0].toJSON();
    }

    return webhooks.map((wh) => wh.toJSON());
  };

  updateWebhookByCondition = async (condition, data) => {
    const [affectedRows, webhooks] = await Webhook.update(data, { where: { ...condition }, returning: true });

    if (affectedRows === 0) {
      return [];
    }

    if (affectedRows === 1) {
      return webhooks[0].toJSON();
    }

    return webhooks.map((wh) => wh.toJSON());
  };

  deleteWebhook = async (id) => {
    return await Webhook.destroy({ where: { id } });
  };

  deleteWebhookByConditions = async (conditions) => {
    return await Webhook.destroy({ where: conditions });
  };

  saveWebhookData = async (shopId, webhook, webhookSubscription) => {
    return await this.createWebhook({
      shop_id: shopId,
      topic: webhookSubscription.topic,
      delivery_method: webhook.deliveryMethod,
      wh_subs_id: webhookSubscription.id,
      response: webhookSubscription,
    });
  };

  updateWebhookData = async (shopId, webhook, webhookSubscription) => {
    return await this.updateWebhookByCondition(
      {
        shop_id: shopId,
        topic: webhookSubscription.topic,
      },
      {
        wh_subs_id: webhookSubscription.id,
        delivery_method: webhook.deliveryMethod,
        response: webhookSubscription,
      }
    );
  };

  /**
   * @param {Session} session
   * @param {Number} shopId
   * @param {Number} planId
   * @returns {Promise<void>}
   */
  registerAllWebhooks = async (session, shopId, planId = null) => {
    const shopifyWebhooks = await ShopifyWebhookService.webhookSubscriptions(session.shop);
    const webhooksToRegister = WEBHOOKS.filter((wh) => wh.priority === PRIORITY.ADDITIONAL);

    const results = [];
    for (let webhook of webhooksToRegister) {
      let res = await this.registerSingleWebhook(
        session,
        shopId,
        webhook,
        shopifyWebhooks.filter((wh) => wh.topic === webhook.topic)
      );
      results.push(res);
    }

    return results;
  };

  registerSingleWebhook = async (session, shopId, webhook, shopifyWebhooks = []) => {
    try {
      const shopifyWebhook = shopifyWebhooks[0] || null;

      // delete existing webhooks if there are multiple subscriptions
      if (shopifyWebhooks.length > 1) {
        for (let wh of shopifyWebhooks) {
          await ShopifyWebhookService.webhookSubscriptionDelete(session.shop, wh.id);
          console.log("⛔️", `DM: ${webhook.deliveryMethod}, Topic: ${webhook.topic} => Deleted.`);
        }
        await this.deleteWebhookByConditions({
          shop_id: shopId,
          topic: webhook.topic,
        });

        shopifyWebhooks = [];
      }

      if (webhook.deliveryMethod === DeliveryMethod.PubSub) {
        await this.pubsubWebhookRegister({
          shopId,
          session,
          webhook,
          shopifyWebhook,
        });
        return;
      }

      await this.httpWebhookRegister({
        shopId,
        session,
        webhook,
        shopifyWebhook,
      });
      return;
    } catch (err) {
      logger.error(err, {
        domain: session.shop,
        message: `Webhook registration failed for ${webhook.topic}`,
        webhook,
        shopifyWebhooks,
      });
      console.log("🚫", `DM: ${webhook.deliveryMethod}, Topic: ${webhook.topic} => Failed.`);
    }
  };

  httpWebhookRegister = async ({ shopId, session, webhook, shopifyWebhook }) => {
    if (isEmpty(shopifyWebhook)) {
      const whSubs = await ShopifyWebhookService.webhookSubscriptionCreate(session.shop, webhook);

      await this.saveWebhookData(shopId, webhook, whSubs);

      console.log(
        "✅",
        `DM: ${webhook.deliveryMethod}, Topic: ${webhook.topic} to URL: ${whSubs.endpoint.callbackUrl} --- Registered.`
      );
      return;
    }

    const whSubs = await ShopifyWebhookService.webhookSubscriptionUpdate(session.shop, shopifyWebhook.id, webhook);

    await this.updateWebhookData(shopId, webhook, whSubs);
    console.log(
      "🔼",
      `DM: ${webhook.deliveryMethod}, Topic: ${webhook.topic} to URL: ${whSubs.endpoint.callbackUrl} --- Updated.`
    );
    return;
  };

  pubsubWebhookRegister = async ({ shopId, session, webhook, shopifyWebhook }) => {
    if (isEmpty(shopifyWebhook)) {
      const whSubs = await ShopifyWebhookService.pubSubWebhookSubscriptionCreate(session.shop, webhook);
      await this.saveWebhookData(shopId, webhook, whSubs);

      console.log("✅", `DM: ${webhook.deliveryMethod}, Topic: ${webhook.topic} => Registered.`);
      return;
    }

    const whSubs = await ShopifyWebhookService.pubSubWebhookSubscriptionUpdate(
      session.shop,
      shopifyWebhook.id,
      webhook
    );
    await this.updateWebhookData(shopId, webhook, whSubs);
    console.log("🔼", `DM: ${webhook.deliveryMethod}, Topic: ${webhook.topic} => Updated.`);
    return;
  };

  unregisterAllWebhooks = async (session, shopId, deliveryMethod, mandatoryTopics = []) => {
    const { shop } = session;

    try {
      const shopifyWebhooks = await ShopifyWebhookService.webhookSubscriptions(session.shop);

      for (let i = 0; i < shopifyWebhooks.length; i++) {
        await this.unregisterWebhook(session, shopifyWebhooks[i]);
      }
    } catch (err) {
      console.log(err);
    }
  };

  unregisterWebhook = async (session, webhook) => {
    try {
      const res = await ShopifyWebhookService.webhookSubscriptionDelete(session.shop, webhook.id);
      console.log(`Webhook for shop "${session.shop}", Topic: ${webhook.topic} --- Unregistered.`);
    } catch (err) {
      console.error(err.message, webhook.id, webhook.topic);
    }
    await this.deleteWebhookByConditions({
      wh_subs_id: webhook.id,
    });
  };

  removeMandatoryWebhooks = async ({ session }) => {
    try {
      const webhooksToRemove = WEBHOOKS.filter((wh) => wh.priority === PRIORITY.MANDATORY);

      let removeCount = 0;

      for (let wh of webhooksToRemove) {
        try {
          const webhooks = await ShopifyWebhookService.webhookSubscriptions(session.shop, {
            first: 50,
            topics: [wh.topic],
          });

          if (webhooks.length === 0) {
            console.info(wh.topic, "-", "Not found");
            continue;
          }

          for (const webhook of webhooks) {
            await ShopifyWebhookService.webhookSubscriptionDelete(session.shop, webhook.id);
            await this.deleteWebhookByConditions({ wh_subs_id: webhook.id });
            console.info(wh.topic, "-", "Removed");
            removeCount++;
          }
        } catch (error) {
          console.warn(wh.topic, error.response.code, error.response.statusText);
        }
      }
      return removeCount;
    } catch (error) {
      console.error("Error removing mandatory webhooks =", error.message);
      return 0;
    }
  };
}

module.exports = new WebhookService();
