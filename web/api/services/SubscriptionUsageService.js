const AppSubscriptionStatus = require("storeseo-enums/appSubscriptionStatus");
const { SubscriptionUsage, Op } = require("../../sequelize");
const { preparePlanDescription } = require("../utils/helper");

class SubscriptionUsageService {
  serializeUsageFromShop = (shop, { meta = undefined, occurrance = 1 } = {}) => {
    if (shop.appSubscriptionData?.test) {
      throw new Error("Shop is in test mode, skipping usage charge creation");
    }

    if (shop.appSubscriptionData?.status !== AppSubscriptionStatus.ACTIVE) {
      throw new Error("Subscription is not active, Current status: " + shop.appSubscriptionData?.status);
    }

    const subsLiId = shop.appSubscriptionData?.lineItems?.find(
      (li) => li.plan.pricingDetails?.terms || li.plan.pricingDetails?.__typename === "AppUsagePricing"
    )?.id;

    if (!subsLiId) {
      throw new Error("Subscription line item id not found");
    }

    return {
      shop_id: shop.id,
      plan_id: shop.plan_id,
      subscription_id: shop.appSubscriptionId,
      subscription_li_id: subsLiId,
      description: preparePlanDescription(
        shop.plan_info?.interval,
        shop.plan_info?.subtotal,
        shop.plan_info?.duration || 18
      ),
      price: shop.plan_info?.subtotal,
      meta,
      occurrance,
    };
  };

  createUsage = async (data) => {
    const usage = await SubscriptionUsage.create(data, { returning: true });
    return usage?.toJSON();
  };

  updateUsage = async (id, data) => {
    const [rows, usages] = await SubscriptionUsage.update(data, { returning: true, where: { id } });

    if (rows === 1) return usages[0].toJSON();

    return usages.map((u) => u.toJSON());
  };

  updateOrCreateUsage = async (shop) => {
    const prevUsage = await this.getUsageByConditions({
      shop_id: shop.id,
      plan_id: shop.plan_id,
      // subscription_id: shop.appSubscriptionId,
      occurrance: 1,
      meta: { [Op.not]: null },
    });

    const data = this.serializeUsageFromShop(shop);

    if (prevUsage) {
      return await this.updateUsage(prevUsage.id, data);
    }

    return await this.createUsage(data);
  };

  getUsageByConditions = async (conditions, fields = undefined) => {
    const usage = await SubscriptionUsage.findOne({
      attributes: fields,
      where: conditions,
      rejectOnEmpty: false,
    });
    return usage?.toJSON();
  };

  deleteUsageByConditions = async (conditions) => {
    return await SubscriptionUsage.destroy({ where: { ...conditions } });
  };
}

module.exports = new SubscriptionUsageService();
