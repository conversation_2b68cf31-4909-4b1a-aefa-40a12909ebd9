const {
  sequelize,
  ProductImage,
  ProductMeta,
  CollectionImage,
  DocImage,
  CollectionMeta,
  Op,
} = require("../../sequelize");
const { PRODUCT, PAGE, ARTICLE, COLLECTION, DOC } = require("storeseo-enums/analysisEntityTypes");
const ProductService = require("./ProductService");
const DocService = require("./docs/DocService");
const { getPercentValue } = require("../utils/helper");

class ReportService {
  getProductsReportData = async (shopId) => {
    const [[result]] = await sequelize.query(
      `
      select
        COUNT(products.id) AS total_count,
        COUNT(CASE WHEN is_optimized THEN 1 END) AS optimized_count,
        FORMAT('%s', ROUND(AVG(score))) AS overall_score,
        SUM(issues) AS seo_issues,
        count(CASE WHEN score >= 75 THEN 1 ELSE NULL END) AS good_count,
        count(CASE WHEN score >= 50 and score < 75 THEN 1 ELSE NULL END) AS fair_count,
        count(CASE WHEN score < 50 THEN 1 ELSE NULL END) AS poor_count
      from products
      where shop_id= ?
      `,
      { replacements: [shopId] }
    );

    const imgAltTextAdded = await ProductImage.count({
      where: { shop_id: shopId, alt_text: { [Op.not]: "" } },
    });

    const metaTagsAdded = await ProductMeta.count({ where: { shop_id: shopId, value: { [Op.not]: "" } } });

    return {
      optimizedCount: result.optimized_count,
      unoptimizedCount: result.total_count - result.optimized_count,
      totalCount: result.total_count,
      seoIssues: result.seo_issues ? result.seo_issues : 0,
      goodCount: result.good_count,
      fairCount: result.fair_count,
      poorCount: result.poor_count,
      imgAltTextAdded: imgAltTextAdded || 0,
      metaTagsAdded: metaTagsAdded || 0,
      overallScore: result.overall_score ? `${result.overall_score}%` : 0,
      type: PRODUCT,
    };
  };

  getCollectionsReportData = async (shopId) => {
    const [[result]] = await sequelize.query(
      `
      select
        COUNT(collections.id) AS total_count,
        COUNT(CASE WHEN is_optimized THEN 1 END) AS optimized_count,
        FORMAT('%s', ROUND(AVG(score))) AS overall_score,
        SUM(issues) AS seo_issues,
        count(CASE WHEN score >= 75 THEN 1 ELSE NULL END) AS good_count,
        count(CASE WHEN score >= 50 and score < 75 THEN 1 ELSE NULL END) AS fair_count,
        count(CASE WHEN score < 50 THEN 1 ELSE NULL END) AS poor_count
      from collections
      where shop_id= ?
      `,
      { replacements: [shopId] }
    );

    const imgAltTextAdded = await CollectionImage.count({
      where: { shop_id: shopId, alt_text: { [Op.not]: "" } },
    });

    const metaTagsAdded = await CollectionMeta.count({ where: { shop_id: shopId, value: { [Op.not]: "" } } });

    return {
      optimizedCount: result.optimized_count,
      unoptimizedCount: result.total_count - result.optimized_count,
      totalCount: result.total_count,
      seoIssues: result.seo_issues,
      goodCount: result.good_count,
      fairCount: result.fair_count,
      poorCount: result.poor_count,
      imgAltTextAdded: imgAltTextAdded || 0,
      metaTagsAdded: metaTagsAdded || 0,
      overallScore: result.overall_score ? `${result.overall_score}%` : 0,
      type: COLLECTION,
    };
  };

  getDocsReportData = async (shopId) => {
    const [[result]] = await sequelize.query(
      `
      select
        COUNT(docs.id) AS total_count,
        COUNT(CASE WHEN is_optimized THEN 1 END) AS optimized_count,
        FORMAT('%s', ROUND(AVG(score))) AS overall_score,
        SUM(issues) AS seo_issues,
        count(CASE WHEN score >= 75 THEN 1 ELSE NULL END) AS good_count,
        count(CASE WHEN score >= 50 and score < 75 THEN 1 ELSE NULL END) AS fair_count,
        count(CASE WHEN score < 50 THEN 1 ELSE NULL END) AS poor_count
      from docs
      where shop_id= ?
      `,
      { replacements: [shopId] }
    );

    const imgAltTextAdded = await DocImage.count({
      where: { shop_id: shopId, alt_text: { [Op.not]: "" } },
    });

    const totalMetaTags = await DocService.countMetaFields();

    return {
      optimizedCount: result.optimized_count,
      unoptimizedCount: result.total_count - result.optimized_count,
      totalCount: result.total_count,
      seoIssues: result.seo_issues,
      goodCount: result.good_count,
      fairCount: result.fair_count,
      poorCount: result.poor_count,
      imgAltTextAdded: imgAltTextAdded || 0,
      metaTagsAdded: totalMetaTags || 0,
      overallScore: result.overall_score ? `${result.overall_score}%` : 0,
      type: DOC,
    };
  };

  getPagesReportData = async (shopId) => {
    const [[result]] = await sequelize.query(
      `
    SELECT
      COUNT(pages.id) AS total_count,
      COUNT(CASE WHEN is_optimized THEN 1 END) AS optimized_count,
      FORMAT('%s', ROUND(AVG(score))) AS overall_score,
      SUM(issues) AS seo_issues,
      SUM(json_array_length(metafields)) AS meta_tags_added,
      count(CASE WHEN score >= 75 THEN 1 ELSE NULL END) AS good_count,
      count(CASE WHEN score >= 50 and score < 75 THEN 1 ELSE NULL END) AS fair_count,
      count(CASE WHEN score < 50 THEN 1 ELSE NULL END) AS poor_count
    FROM pages
    WHERE shop_id = ?
    `,
      {
        replacements: [shopId],
      }
    );

    return {
      optimizedCount: result.optimized_count,
      unoptimizedCount: result.total_count - result.optimized_count,
      totalCount: result.total_count,
      seoIssues: result.seo_issues,
      goodCount: result.good_count,
      fairCount: result.fair_count,
      poorCount: result.poor_count,
      // imgAltTextAdded: result.img_alt_text_added,
      metaTagsAdded: result.meta_tags_added,
      overallScore: result.overall_score ? `${result.overall_score}%` : 0,
      type: PAGE,
    };
  };

  getBlogArticlesReportData = async (shopId) => {
    const [[result]] = await sequelize.query(
      `
    SELECT
      COUNT(articles.id) AS total_count,
      COUNT(CASE WHEN is_optimized THEN 1 END) AS optimized_count,
      FORMAT('%s', ROUND(AVG(score))) AS overall_score,
      SUM(issues) AS seo_issues,
      SUM(json_array_length(metafields)) AS meta_tags_added,
      COUNT(CASE WHEN length(image ->> 'alt') > 0 THEN 1 END) AS img_alt_text_added,
      count(CASE WHEN score >= 75 THEN 1 ELSE NULL END) AS good_count,
      count(CASE WHEN score >= 50 and score < 75 THEN 1 ELSE NULL END) AS fair_count,
      count(CASE WHEN score < 50 THEN 1 ELSE NULL END) AS poor_count
    FROM articles
    WHERE shop_id = ? and is_deleted = ?
    `,
      {
        replacements: [shopId, false],
      }
    );

    return {
      optimizedCount: result.optimized_count,
      unoptimizedCount: result.total_count - result.optimized_count,
      totalCount: result.total_count,
      seoIssues: result.seo_issues,
      goodCount: result.good_count,
      fairCount: result.fair_count,
      poorCount: result.poor_count,
      imgAltTextAdded: result.img_alt_text_added,
      metaTagsAdded: result.meta_tags_added,
      overallScore: result.overall_score ? `${result.overall_score}%` : 0,
      type: ARTICLE,
    };
  };

  serializeReportStats = (optimizeProducts, imgAltTextAdded, metaTagsAdded, shop) => {
    return [
      {
        key: "optimized_products",
        value: optimizeProducts,
      },
      {
        key: "alt_text_added",
        value: imgAltTextAdded,
      },
      {
        key: "meta_tag_added",
        value: metaTagsAdded,
      },
      {
        key: "total_products",
        value: shop.total_products,
      },
      {
        key: "overall_score",
        value: `${shop.overall_score}%`,
      },
      {
        key: "total_issues",
        value: shop.seo_issues,
      },
    ];
  };

  getDashboardStats = async (shopId) => {
    const [[productStats]] = await sequelize.query(
      `
      select
        COUNT(products.id) AS total_count,
        FORMAT('%s', ROUND(AVG(score))) AS overall_score,
        count(CASE WHEN score >= 75 THEN 1 ELSE NULL END) AS optimized_count,
        count(CASE WHEN score < 75 THEN 1 ELSE NULL END) AS not_optimized_count
      FROM products
      where shop_id= ?
      `,
      { replacements: [shopId] }
    );

    const [[collectionStats]] = await sequelize.query(
      `
      select
        COUNT(collections.id) AS total_count,
        FORMAT('%s', ROUND(AVG(score))) AS overall_score,
        count(CASE WHEN score >= 75 THEN 1 ELSE NULL END) AS optimized_count,
        count(CASE WHEN score < 75 THEN 1 ELSE NULL END) AS not_optimized_count
      FROM collections
      where shop_id= ?
      `,
      { replacements: [shopId] }
    );

    const [[pageStats]] = await sequelize.query(
      `
    SELECT
      COUNT(pages.id) AS total_count,
      FORMAT('%s', ROUND(AVG(score))) AS overall_score,
      count(CASE WHEN score >= 75 THEN 1 ELSE NULL END) AS optimized_count,
      count(CASE WHEN score < 75 THEN 1 ELSE NULL END) AS not_optimized_count
    FROM pages
    WHERE shop_id = ?
    `,
      {
        replacements: [shopId],
      }
    );

    const [[articleStats]] = await sequelize.query(
      `
    SELECT
      COUNT(articles.id) AS total_count,
      FORMAT('%s', ROUND(AVG(score))) AS overall_score,
      count(CASE WHEN score >= 75 THEN 1 ELSE NULL END) AS optimized_count,
      count(CASE WHEN score < 75 THEN 1 ELSE NULL END) AS not_optimized_count
    FROM articles
    WHERE shop_id = ? and is_deleted = ?
    `,
      {
        replacements: [shopId, false],
      }
    );

    return {
      productStats: {
        total: productStats.total_count,
        overallScore: productStats.overall_score ? productStats.overall_score : 0,
        optimized: getPercentValue(parseInt(productStats.optimized_count), parseInt(productStats.total_count)),
        notOptimized: getPercentValue(parseInt(productStats.not_optimized_count), parseInt(productStats.total_count)),
      },
      collectionStats: {
        total: collectionStats.total_count,
        overallScore: collectionStats.overall_score ? collectionStats.overall_score : 0,
        optimized: getPercentValue(parseInt(collectionStats.optimized_count), parseInt(collectionStats.total_count)),
        notOptimized: getPercentValue(
          parseInt(collectionStats.not_optimized_count),
          parseInt(collectionStats.total_count)
        ),
      },
      pageStats: {
        total: pageStats.total_count,
        overallScore: pageStats.overall_score ? pageStats.overall_score : 0,
        optimized: getPercentValue(parseInt(pageStats.optimized_count), parseInt(pageStats.total_count)),
        notOptimized: getPercentValue(parseInt(pageStats.not_optimized_count), parseInt(pageStats.total_count)),
      },
      articleStats: {
        total: articleStats.total_count,
        overallScore: articleStats.overall_score ? articleStats.overall_score : 0,
        optimized: getPercentValue(parseInt(articleStats.optimized_count), parseInt(articleStats.total_count)),
        notOptimized: getPercentValue(parseInt(articleStats.not_optimized_count), parseInt(articleStats.total_count)),
      },
    };
  };

  getProductStats = async (shopId) => {
    const result = (
      await sequelize.query(
        `select SUM(CASE WHEN is_analysed THEN 1 ELSE 0 END) analysed_products, 
        COUNT(id) total_products,
        AVG(score)::INT overall_score,
        SUM(CASE WHEN is_optimized THEN 0 ELSE 1 END) seo_issues,
        SUM(CASE WHEN is_optimized THEN 1 ELSE 0 END) total_optimized
        from products where shop_id=:shopId group by shop_id`,
        {
          replacements: {
            shopId,
          },
          type: sequelize.QueryTypes.SELECT,
        }
      )
    )[0];
    return {
      optimizedCount: result?.total_optimized || 0,
      totalCount: result?.total_products || 0,
      overallScore: (result?.overall_score || 0) + "%",
      seoIssues: result?.seo_issues || 0,
      analysedCount: result?.analysed_products || 0,
    };
  };

  /**
   *
   * @param {Object} shop
   */
  getWeeklyEmailNotificationProductReport = async (shop) => {
    const fields = ["id", "title", "score"];
    const productStats = await this.getProductsReportData(shop.id);
    const topScoredProducts = await ProductService.getTopScoredProducts(shop.id, { fields });
    const leastScoredProducts = await ProductService.getLeastScoredProducts(shop.id, { fields });

    return {
      store: {
        name: shop.name,
        domain: shop.domain,
      },
      performanceOverview: {
        overAllPercentage: productStats.overallScore,
        goodPercentage:
          (Math.round((Number(productStats?.goodCount) * 100) / productStats?.totalCount) || 0).toString() + "%",
        fairPercentage:
          (Math.round((Number(productStats?.fairCount) * 100) / productStats?.totalCount) || 0).toString() + "%",
        poorPercentage:
          (Math.round((Number(productStats?.poorCount) * 100) / productStats?.totalCount) || 0).toString() + "%",
      },
      performanceBreakdown: {
        optimizedCount: productStats.optimizedCount,
        unoptimizedCount: productStats.unoptimizedCount,
        imgAltTextAdded: productStats.imgAltTextAdded,
        metaTagsAdded: productStats.metaTagsAdded,
        totalCount: productStats.totalCount,
        overallScore: productStats.overallScore,
        seoIssues: productStats.seoIssues,
      },
      topScoredItems: topScoredProducts,
      leastScoredItems: leastScoredProducts,
    };
  };
}

module.exports = new ReportService();
