const { google } = require("@google-analytics/data/build/protos/protos");
const GoogleAuthService = require("./GoogleAuthService");
const { BetaAnalyticsDataClient } = require("@google-analytics/data");
const dimensions = require("storeseo-enums/analytics/dimensions");
const aggregations = require("storeseo-enums/analytics/aggregations");
const metrics = require("storeseo-enums/analytics/metrics");
const logger = require("storeseo-logger");
const dimensionOrderTypes = require("storeseo-enums/analytics/dimensionOrderTypes");
const { isSubscriptionTestMode } = require("../config/app");

/**
 * @typedef {object} authenticatedUser
 * @property {string} email
 * @property {string} refresh_token
 * @property {string[]} scopes
 * @property {object} [serviceJson]
 */

/**
 * @typedef {object} runReportByDate.Options
 * @property {dateRange} dateRange,
 * @property {string[]} metrics
 */

/**
 * @typedef {object} runTopProductsReport.Options
 * @property {dateRange} dateRange,
 * @property {number} [limit] default **5**
 * @property {number} [offset] default **0**
 * @property {string[]} [selectedPaths]
 */

class GoogleAnalyticsService {
  /**
   *
   * @param {authenticatedUser} user
   */
  analyticsClient = (user) => {
    let authClient;

    if (user.serviceJson) {
      authClient = GoogleAuthService.getServiceJsonAuthClient(user.serviceJson);
    } else authClient = GoogleAuthService.getOAuthClient(user.refresh_token);
    // const oauthClient = GoogleAuthService.getOAuthClient(user.refresh_token);

    const client = new BetaAnalyticsDataClient({ authClient });

    return client;
  };

  /**
   *
   * @param {authenticatedUser} user
   * @param {string} propertyId
   */
  verifyPropertyId = async (user, propertyId) => {
    try {
      const res = await this.runReport(user, propertyId, {
        metrics: [{ name: metrics.TOTAL_USERS }],
        dimensions: [{ name: dimensions.DATE }],
        dateRanges: [
          {
            startDate: "1daysAgo",
            endDate: "today",
          },
        ],
      });

      return true;
    } catch (err) {
      console.log(`Error verifying property id for user ${user?.email}: ${err}`);
      return false;
    }
  };

  /**
   *
   * @param {authenticatedUser} user
   * @param {string} propertyId
   * @param {google.analytics.data.v1beta.IRunReportRequest[]} reportsOption
   */
  batchRunReports = async (user, propertyId, reportsOption) => {
    const client = this.analyticsClient(user);
    const [res] = await client.batchRunReports({
      property: `properties/${propertyId}`,
      requests: reportsOption,
    });

    return res;
  };

  /**
   *
   * @param {authenticatedUser} user
   * @param {string} propertyId
   * @param {google.analytics.data.v1beta.IRunReportRequest} options
   */
  runReport = async (user, propertyId, options) => {
    const client = this.analyticsClient(user);
    const [res] = await client.runReport({
      property: `properties/${propertyId}`,
      ...options,
    });

    return res;
  };

  /**
   *
   * @param {authenticatedUser} user
   * @param {string} propertyId
   * @param {runReportByDate.Options} options
   */
  runReportByDate = async (user, propertyId, options) => {
    const { metrics, dateRange } = options;

    /**
     * @type {google.analytics.data.v1beta.IRunReportRequest}
     */
    const reportOptions = {
      metrics: metrics.map((m) => ({ name: m })),
      dimensions: [{ name: dimensions.DATE }],
      metricAggregations: [aggregations.TOTAL],
      dateRanges: [dateRange],
      keepEmptyRows: true,
    };

    const res = await this.runReport(user, propertyId, reportOptions);

    return res;
  };

  /**
   *
   * @param {authenticatedUser} user
   * @param {string} propertyId
   * @param {runReportByDate.Options[]} optionsArr
   */
  batchRunReportsByDate = async (user, propertyId, optionsArr) => {
    /**
     * @type {google.analytics.data.v1beta.IRunReportRequest[]}
     */
    const reportsOptionArr = optionsArr.map((option) => {
      const { metrics, dateRange } = option;

      return {
        metrics: metrics.map((m) => ({ name: m })),
        dimensions: [{ name: dimensions.DATE }],
        metricAggregations: [aggregations.TOTAL],
        dateRanges: [dateRange],
        keepEmptyRows: true,
        orderBys: [
          {
            dimension: {
              dimensionName: dimensions.DATE,
              orderType: dimensionOrderTypes.ALPHANUMERIC,
            },
          },
        ],
      };
    });

    const res = await this.batchRunReports(user, propertyId, reportsOptionArr);

    return res;
  };

  /**
   *
   * @param {*} user
   * @param {*} propertyId
   * @param {runTopProductsReport.Options} options
   */
  runProductsReport = async (user, propertyId, options) => {
    const productMetrics = [metrics.VIEWS, metrics.TOTAL_USERS, metrics.AVERAGE_SESSION_DURATION];
    const productDimentions = [dimensions.PAGE_PATH, dimensions.UNIFIED_SCREEN_NAME];

    const { limit = 5, offset = 0, dateRange, selectedPaths } = options;
    const productUrlRegExp = isSubscriptionTestMode ? "/" : "/products/.";

    /**
     * @type {google.analytics.data.v1beta.IFilter}
     */
    const filterOption = selectedPaths?.length
      ? {
          inListFilter: {
            values: selectedPaths,
          },
        }
      : {
          stringFilter: {
            matchType: "PARTIAL_REGEXP",
            value: productUrlRegExp,
          },
        };

    /**
     * @type {google.analytics.data.v1beta.IRunReportRequest}
     */
    const reportOptions = {
      dimensions: productDimentions.map((d) => ({ name: d })),
      metrics: productMetrics.map((m) => ({ name: m })),
      dateRanges: [dateRange],
      limit,
      offset,
      dimensionFilter: {
        filter: {
          fieldName: dimensions.PAGE_PATH,
          ...filterOption,
        },
      },
      keepEmptyRows: true,
    };

    return this.runReport(user, propertyId, reportOptions);
  };
}

module.exports = new GoogleAnalyticsService();
