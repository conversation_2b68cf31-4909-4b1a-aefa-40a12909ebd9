const { default: axios } = require("axios");

class BetterDocsService {
  defaultOptions = {
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${process.env.BETTERDOCS_TOKEN}`,
    },
  };

  getDocsByPageAndLimit = async (domain, page, limit) => {
    const url = `${process.env.BETTERDOCS_API_URL}/doc?page=${page}&limit=${limit}`;
    const docs = await axios.get(url, {
      ...this.defaultOptions,
      headers: {
        ...this.defaultOptions.headers,
        shop: domain,
      },
    });
    return docs?.data?.data;
  };

  getSingleDoc = async (docId, domain) => {
    const url = `${process.env.BETTERDOCS_API_URL}/doc/${docId}`;

    const res = await axios.get(url, {
      ...this.defaultOptions,
      headers: {
        ...this.defaultOptions.headers,
        shop: domain,
      },
    });
    return res?.data;
  };

  updateDoc = async (docId, domain, payload) => {
    const url = `${process.env.BETTERDOCS_API_URL}/doc/${docId}`;

    const doc = await axios.put(url, payload, {
      headers: {
        ...this.defaultOptions.headers,
        shop: domain,
      },
    });
    return doc.data;
  };

  docsCount = async (domain) => {
    const url = `${process.env.BETTERDOCS_API_URL}/doc`;

    const docsCount = await axios.get(url, {
      ...this.defaultOptions,
      headers: {
        ...this.defaultOptions.headers,
        shop: domain,
      },
    });

    return docsCount?.data?.data?.pagination?.total;
  };

  updatedNoFollowNoIndex = async (docId, domain, nofollow, noindex) => {
    const url = `${process.env.BETTERDOCS_API_URL}/doc/${docId}/meta-robots`;

    const data = await axios.put(
      url,
      { nofollow, noindex },
      {
        ...this.defaultOptions,
        headers: {
          ...this.defaultOptions.headers,
          shop: domain,
        },
      }
    );

    const metaRobots = data?.data?.data?.seo_meta_robots;
    return metaRobots;
  };

  checkAppInstall = async (shop) => {
    const url = `${process.env.BETTERDOCS_API_URL}/check-app-install?shop=${shop}`;
    const data = await axios.get(url, {
      ...this.defaultOptions,
      headers: {
        ...this.defaultOptions.headers,
        shop,
      },
    });
    const appInstallStatus = data?.data?.data?.is_installed;
    return appInstallStatus;
  };

  getBetterDocsHomepage = async (domain) => {
    const url = `${process.env.BETTERDOCS_API_URL}/homepage`;
    const data = await axios.get(url, {
      ...this.defaultOptions,
      headers: {
        ...this.defaultOptions.headers,
        shop: domain,
      },
    });

    const { data: homepageData } = data.data;
    return homepageData.meta;
  };

  updateBetterDocsHomepageMeta = async (domain, payload) => {
    const url = `${process.env.BETTERDOCS_API_URL}/homepage`;
    const data = await axios.put(url, payload, {
      headers: {
        ...this.defaultOptions.headers,
        shop: domain,
      },
    });

    return data.data.data;
  };
}

module.exports = new BetterDocsService();
