require("../../../types");
const { isNull, isEmpty } = require("lodash");
const moment = require("moment");
const ShopifyService = require("./ShopifyService");
const CouponService = require("./CouponService");
const SubscriptionPlanService = require("./SubscriptionPlanService");
const SubscriptionTransactionService = require("./SubscriptionTransactionService");
const SubscriptionUsageService = require("../services/SubscriptionUsageService");
const appPurchaseStatus = require("storeseo-enums/appPurchaseStatus");

const {
  serializeShopPlanData,
  serializeSinglePlan,
  serilizeAddonsData,
  serializeCreditAddonsPlanRules,
  serializeCreditAddonsPlanInfo,
} = require("../serializers/SubscriptionSerializer");
const {
  calculateCouponDiscount,
  calculateAddonPrice,
  calculateIsFreeSubcription,
  isOnlyFreeAddonSubscriptionWithVisionary,
} = require("../utils/subscriptionCalculations");
const { dispatchQueue } = require("../queue/queueDispatcher");
const { QUEUE_NAMES } = require("../queue/config");
const appSubscriptionStatus = require("storeseo-enums/appSubscriptionStatus");
const { USAGE } = require("storeseo-enums/planInterval");
const { FREE } = require("storeseo-enums/planType");
const cache = require("../cache");
const SubscriptionAddonService = require("./SubscriptionAddonService");
const { extractAddonRules } = require("../utils/helper");
const logger = require("storeseo-logger");
const OnetimePurchaseService = require("./OnetimePurchaseService");
const AddonUsageService = require("./AddonUsageService");
const { cli } = require("winston/lib/winston/config");

class SubscriptionService {
  /**
   * Create subscription to  shopify and returns subscription details and confirmation url
   * @param {Session} session
   * @param {number} shopId
   * @param {string} slug
   * @param {string | undefined} couponCode
   * @param {object} addons
   * @returns {Promise<{appSubscription: *, confirmationUrl: *}>}
   */
  subscriptionCreate = async (session, { shopId, slug, couponCode, addons }) => {
    const ShopService = require("./ShopService");
    const shopData = await ShopService.getShop(session.shop);
    const addonsData = await SubscriptionAddonService.getSelectedAddons(Object.values(addons));
    let subscriptionPlan = await SubscriptionPlanService.getSubscriptionPlanBySlug(slug);
    let coupon;
    let appSubscription, confirmationUrl;

    // handling only addon subscription for visionary plan
    if (isOnlyFreeAddonSubscriptionWithVisionary(shopData, subscriptionPlan, addonsData)) {
      await this.handleOnlyFreeAddonSubscriptionWithVisionary(shopData, addonsData);

      return {
        appSubscription,
        confirmationUrl,
        isFreeSubscription: true,
      };
    }

    if (!isEmpty(couponCode)) {
      ({ coupon, plan: subscriptionPlan } = await this.validateCouponCode({
        activePlanId: shopData.plan_id,
        planSlug: slug,
        couponCode,
      }));
    } else {
      subscriptionPlan = serializeSinglePlan(subscriptionPlan);
    }

    const isFreeSubscription = calculateIsFreeSubcription(subscriptionPlan, addonsData);
    if (isFreeSubscription) {
      await this.handleFreeSubscription(session, {
        shopId,
        shopData,
        subscriptionPlan,
        addons: addonsData,
      });
      await cache.planId(session.shop, subscriptionPlan.id);
    } else {
      ({ appSubscription, confirmationUrl } = await ShopifyService.appSubscriptionCreate(session.shop, {
        shopDetails: shopData,
        plan: subscriptionPlan,
        addons: addonsData,
        coupon,
      }));

      await cache.tempSubsctiptionSet(shopData.domain, {
        id: appSubscription.id,
        plan: slug,
        addons: Object.values(addons),
        couponCode,
      });
    }

    return {
      appSubscription,
      confirmationUrl,
      isFreeSubscription,
    };
  };

  handleFreeSubscription = async (session, { shopId, subscriptionPlan, addons = [], shopData }) => {
    const currentSubscriptionId = shopData.appSubscriptionId || shopData.appSubscriptionData?.id;
    if (currentSubscriptionId) await ShopifyService.appSubscriptionCancel(session.shop, currentSubscriptionId);

    const updateData = serializeShopPlanData(
      shopData,
      subscriptionPlan,
      {
        status: appSubscriptionStatus.ACTIVE,
      },
      addons
    );

    const updatedShopData = await this.updateShopSubscriptionPlan({ id: shopId }, updateData);
    await AddonUsageService.updateRecurringUsagePermissions({
      shopId,
      shopDomain: updatedShopData.domain,
      purchaseDate: updateData.subscribed_at,
      addons,
    });

    await SubscriptionTransactionService.createTransaction(updatedShopData);
    // WebhookService.registerAllWebhooks(session, shopId, updatedShopData.plan_id);

    await cache.webhooks.addShopToPendingWebhookRegistrationList(session.shop);

    dispatchQueue({
      queueName: QUEUE_NAMES.SUBSCRIPTION_DOWNGRADE,
      message: {
        shopId,
        plan: subscriptionPlan,
      },
    });
  };

  handleOnlyFreeAddonSubscriptionWithVisionary = async (shopData, addons = []) => {
    const ShopService = require("./ShopService");
    const seriaizedAddons = serilizeAddonsData(shopData.plan_info, addons);

    const updateData = {
      plan_rules: {
        ...shopData.plan_rules,
        ...extractAddonRules(seriaizedAddons),
      },
      plan_info: {
        ...shopData.plan_info,
        addons: seriaizedAddons,
      },
    };

    const shop = await ShopService.updateShop(shopData.id, updateData);
    await cache.imageOptimizerUsageLimit(shop.domain, updateData.plan_rules?.image_optimizer);
  };

  #calculateTransactionPlanData = async (shop, newPlanSlug, addons = [], couponCode = null) => {
    const plan = await SubscriptionPlanService.getSubscriptionPlanByConditions({ slug: newPlanSlug });

    if (shop.plan_info?.slug === newPlanSlug && shop.plan_rules) {
      plan.rules = {
        ...plan.rules,
        products: shop.plan_rules?.products,
      };
    }

    const activePlan = await SubscriptionPlanService.getSubscriptionPlan(shop.plan_id);

    if (couponCode) {
      plan.coupon = await CouponService.getAnyCouponDetails(couponCode);
    }

    let calculatedPrice = calculateCouponDiscount(plan, activePlan, false);

    if (addons.length > 0) {
      calculatedPrice = calculateAddonPrice(calculatedPrice, addons);
    }

    return {
      ...plan,
      price: parseFloat(calculatedPrice.price.toFixed(2)),
      discount: parseFloat(calculatedPrice.discount.toFixed(2)),
      subtotal: parseFloat(calculatedPrice.subtotal.toFixed(2)),
    };
  };

  /**
   * Subscription update from webhook
   * @param session {Session}
   * @param {object} app_subscription
   * @param {boolean} isManual
   * @returns {Promise<void>}
   */
  subscriptionUpdate = async (session, app_subscription, isManual = false) => {
    const ShopService = require("./ShopService");
    let { name, status, admin_graphql_api_id, admin_graphql_api_shop_id: shopGqlId } = app_subscription;

    console.log(session.shop, `Updating subscription to: ${name} --- ${status}`);

    let shop = await ShopService.getShopDataForWebhook(shopGqlId);

    // console.log("shop.appSubscriptionId =>", shop.appSubscriptionId);
    // console.log("admin_graphql_api_id =>", admin_graphql_api_id);
    // console.log("status =>", status);

    const prevProductCount = shop.plan_rules?.products || 0;
    const { id, plan, addons, couponCode } = (await cache.tempSubsctiptionGet(shop.domain)) || {};

    if (status === appSubscriptionStatus.DECLINED) {
      console.error(session.shop, "Subscription not upgraded.", { status, admin_graphql_api_id, id, plan, addons });
      return;
    }

    if (status === appSubscriptionStatus.ACTIVE) {
      const addonData = await SubscriptionAddonService.getSelectedAddons(addons);
      const subscriptionPlan = await this.#calculateTransactionPlanData(shop, plan, addonData, couponCode);
      const prevPlan = shop.plan_info;
      const activeSubscription = await ShopifyService.getActiveSubscription(session.shop);

      const updateData = serializeShopPlanData(shop, subscriptionPlan, activeSubscription, addonData);
      shop = await this.updateShopSubscriptionPlan({ shop_id: shopGqlId }, updateData);

      await AddonUsageService.updateRecurringUsagePermissions({
        shopId: shop.id,
        shopDomain: shop.domain,
        purchaseDate: updateData.subscribed_at,
        addons: addonData,
      });

      if (subscriptionPlan.interval === USAGE && !isManual) {
        const usage = await SubscriptionUsageService.updateOrCreateUsage(shop);

        if (usage.meta === null) {
          const res = await ShopifyService.createUsageCharge(session.shop, usage);
          await SubscriptionUsageService.updateUsage(usage.id, { meta: { ...res } });
        }
      }

      dispatchQueue({
        queueName: QUEUE_NAMES.FLUENT_CRM_ADD_CONTACT,
        message: { domain: shop.domain },
      });

      dispatchQueue({
        queueName: QUEUE_NAMES.MAILCHIMP_ADD_MEMBER,
        message: {
          domain: shop.domain,
          tags: [subscriptionPlan.name],
          tagsToRemove: !isEmpty(prevPlan) ? [prevPlan.name] : undefined,
        },
      });

      if (prevProductCount > subscriptionPlan.rules.products) {
        dispatchQueue({
          queueName: QUEUE_NAMES.SUBSCRIPTION_DOWNGRADE,
          message: { shopId: shop.id, plan: subscriptionPlan },
        });
      }

      // creating transaction
      await SubscriptionTransactionService.createTransaction(shop, app_subscription, couponCode);
      console.log(session.shop, `Subscription upgraded to: ${name} --- ${status}`);
      return;
    }

    if (status !== appSubscriptionStatus.ACTIVE && shop.appSubscriptionId === admin_graphql_api_id) {
      await ShopService.updateShopByCondition(
        { shop_id: shopGqlId },
        {
          appSubscriptionId: admin_graphql_api_id,
          appSubscriptionData: app_subscription,
          plan_status: status,
        }
      );

      logger.info(`Subscription plan "${status}" of the shop: ${shop.domain}`, {
        user: {
          domain: shop.domain,
          email: shop.email,
        },
        ...app_subscription,
      });
    }
  };

  /**
   * Onetime purchase update from webhook
   * @param {Session} session
   * @param {AppPurchaseOnetime} app_purchase_one_time
   * @returns {Promise<void>}
   */
  onetimePurchaseUpdate = async (session, app_purchase_one_time) => {
    const ShopService = require("./ShopService");
    let {
      name,
      status,
      admin_graphql_api_id,
      admin_graphql_api_shop_id: shopGqlId,
      created_at,
    } = app_purchase_one_time;

    console.log(session.shop, `Saving onetime purchase to: ${name} --- ${status} -- ${created_at}`);

    let shop = await ShopService.getShopDataForWebhook(shopGqlId);
    const { addons } = await cache.tempOnetimeAddon(shop.domain);

    if (status !== appPurchaseStatus.ACTIVE) {
      logger.info(`Onetime purchase ${status}.`, {
        user: {
          domain: session.shop,
          email: shop.email,
        },
        addons,
        app_purchase_one_time,
        status,
      });
      return;
    }

    if (status === appPurchaseStatus.ACTIVE) {
      const addonData = await SubscriptionAddonService.getSelectedCreditAddons(addons);

      const planRules = serializeCreditAddonsPlanRules(shop.plan_rules, addonData);
      const planInfo = serializeCreditAddonsPlanInfo(shop.plan_info, addonData);

      shop = await ShopService.updateShop(shop.id, { plan_rules: planRules, plan_info: planInfo });
      await AddonUsageService.updateCreditUsagePermissions({
        shopId: shop.id,
        shopDomain: shop.domain,
        purchaseDate: moment(created_at),
        addons: addonData,
      });

      await OnetimePurchaseService.createOnetimePurchase(shop.id, app_purchase_one_time, addonData);

      console.log(session.shop, `Saved onetime purchase to: ${name} --- ${status} -- ${created_at}`);
      return;
    }
  };

  /**
   *
   * @param {object} conditions
   * @param {object} updateData
   * @returns {Promise<*>}
   */
  updateShopSubscriptionPlan = async (conditions, updateData) => {
    const ShopService = require("./ShopService");

    const shop = await ShopService.updateShopByCondition(conditions, updateData);

    await cache.planId(shop.domain, updateData.plan_id);
    await cache.tempSubsctiptionReset(shop.domain);

    return shop;
  };

  /**
   * Subscribe to free plan
   * @param {number} shopId
   * @returns {Promise<*>}
   */
  subscribeToFreePlan = async ({ shopId }) => {
    const ShopService = require("./ShopService");
    const subscriptionPlan = await SubscriptionPlanService.getFreeSubscriptionPlan();
    const updateData = serializeShopPlanData(subscriptionPlan);
    const shop = await ShopService.updateShop(shopId, updateData);
    await SubscriptionTransactionService.createTransaction(shop);

    dispatchQueue({
      queueName: QUEUE_NAMES.FLUENT_CRM_ADD_CONTACT,
      message: { domain: shop.domain },
    });

    dispatchQueue({
      queueName: QUEUE_NAMES.MAILCHIMP_ADD_MEMBER,
      message: {
        domain: shop.domain,
        tags: ["FREE"],
      },
    });

    return shop;
  };

  /**
   * Downgrade subscription plan
   * @param {number} shopId
   * @param {string} planSlug
   * @returns {Promise<*>}
   */
  downgradeSubscriptionPlanToFree = async (shopId, planSlug) => {
    const plan = await SubscriptionPlanService.getSubscriptionPlanBySlug(planSlug);

    if (plan.type !== FREE) {
      throw Error("Invalid Request.");
    }

    const updateData = serializeShopPlanData(plan);

    const updatedShop = await this.updateShopSubscriptionPlan({ id: shopId }, updateData);

    dispatchQueue({
      queueName: QUEUE_NAMES.SUBSCRIPTION_DOWNGRADE,
      message: {
        shopId,
        plan,
      },
    });

    return updatedShop;
  };

  /**
   * Validates the provided coupon codes and returns coupon applied plan data
   * @param {number} activePlanId
   * @param {string} planSlug
   * @param {string} couponCode
   * @returns {Promise<{isHundredPercentCoupon: *, coupon: PartialObject<object> | *, plan: {price: *, subtotal: number, name, discount, id}}>}
   */
  validateCouponCode = async ({ activePlanId, planSlug, couponCode }) => {
    let planData = await SubscriptionPlanService.getSubscriptionPlanByConditions({ slug: planSlug });
    let activePlan = await SubscriptionPlanService.getSubscriptionPlanByConditions({ id: activePlanId });

    const couponDetail = await CouponService.getCouponDetail(couponCode);

    if (isEmpty(planData) || isEmpty(couponDetail)) {
      throw new Error("Invalid coupon code.");
    }

    if (!isEmpty(couponDetail.plans) && !couponDetail.plans.includes(planData.id)) {
      throw new Error("Invalid coupon code.");
    }

    if (!isNull(couponDetail.max_limit) && couponDetail.max_limit <= couponDetail.redeem_count) {
      throw new Error("Coupon limit reached.");
    }

    const startDate = +new Date(couponDetail.start_date).setHours(0, 0, 0);
    const endDate = +new Date(couponDetail.end_date).setHours(23, 59, 59);
    const now = +new Date();

    if (!isNull(couponDetail.start_date) && now < startDate) {
      throw new Error("Invalid coupon code.");
    }

    if (!isNull(couponDetail.end_date) && now > endDate) {
      throw new Error("Invalid coupon code.");
    }

    planData.coupon = couponDetail;

    const { coupon, isHundredPercentCoupon, isDiscountApplicable, ...serializedData } = serializeSinglePlan(
      planData,
      activePlan
    );

    if (!isDiscountApplicable) throw new Error("Discount not applicable!");

    return {
      plan: serializedData,
      coupon,
      isHundredPercentCoupon,
    };
  };

  hasJSONLDFeaturePermission = (shopObj) => shopObj?.plan_rules?.json_ld;

  hasBrokenLinksFeaturePermission = (shopObj) => shopObj?.plan_rules?.broken_links;

  getSerializeCheckoutPageData = async (shop, plan, couponDetails) => {
    let activePlan = await SubscriptionPlanService.getSubscriptionPlanByConditions({ id: shop.plan_id });
    if (couponDetails) {
      plan.coupon = couponDetails;
    }
    const { coupon, isHundredPercentCoupon, ...planData } = serializeSinglePlan(plan, activePlan);
    const { narration } = await SubscriptionTransactionService.getTransTypeNarration(shop, plan);
    return {
      name: planData.name,
      slug: planData.slug,
      type: planData.type,
      interval: planData.interval,
      narration: narration,
      price: planData.price || 0,
      duration: planData.duration,
      subtotal: planData.totalPrice,
      discount: planData.discount || 0,
      total: planData.subtotal || 0,
      couponCode: coupon?.code,
      couponDesc: coupon?.name,
      hundredPercentCouponApplied: isHundredPercentCoupon,
      isUpgradable: planData.isUpgradable,
      isMonthly: planData.isMonthly,
      isFree: planData.isFree,
      intervalText: planData.intervalText,
      status: planData.status,
    };
  };

  creditPurchaseCreate = async (session, addons) => {
    const ShopService = require("./ShopService");
    const shop = await ShopService.getShop(session.shop);
    const addonsData = await SubscriptionAddonService.getSelectedCreditAddons(Object.values(addons));

    await cache.tempOnetimeAddon(session.shop, {
      addons: Object.values(addons),
    });

    return await ShopifyService.appPurchaseOneTimeCreate(session.shop, { shopDetails: shop, addons: addonsData });
  };
}

module.exports = new SubscriptionService();
