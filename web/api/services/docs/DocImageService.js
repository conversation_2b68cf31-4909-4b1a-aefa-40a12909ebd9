const { DocImage } = require("../../../sequelize");

class DocImageService {
  /**
   * Save or update BetterDocs image to database
   * @param shopId - DB shop id
   * @param docId - DB doc id
   * @returns {Promise<any>}
   */
  getAll = async (shopId, docId) => {
    return await DocImage.findAll({
      where: { shop_id: shopId, doc_id: docId },
    });
  };

  /**
   * Save or update BetterDocs image to database
   * @param shopId - DB shop id
   * @param docId - DB doc id
   * @param {object} image - image data
   * @returns {Promise<any>}
   */
  upsert = async (shopId, docId, imageData) => {
    const [docImage] = await DocImage.upsert(imageData, {
      // conflictFields: ["shop_id", "doc_id"],
    });
    console.log("Shop =>", shopId, "docs =>", docId, "Doc Image =>", docImage.id);
    return docImage;
  };

  /**
   * Deletes doc images
   * @param {number} shopId - shop id
   * @param {number} docId - doc id from DB
   * @param transaction
   * @returns {Promise<number>}
   */
  delete = async (shopId, docId, transaction = undefined) => {
    const where = { shop_id: shopId, doc_id: docId };
    return await DocImage.destroy({ where, transaction });
  };

  /**
   * Deletes doc images by image id
   * @param {number} id - image DB id
   * @param {number} shopId - shop id
   * @param {number} docId - doc id from DB
   * @param transaction
   * @returns {Promise<number>}
   */
  deleteById = async (id, shopId, docId, transaction = undefined) => {
    const where = { id, shop_id: shopId, doc_id: docId };
    return await DocImage.destroy({ where, transaction });
  };

  deleteByShopId = async (shopId, transaction = undefined) => {
    const where = { shop_id: shopId };
    return await DocImage.destroy({ where, transaction });
  };
}

module.exports = new DocImageService();
