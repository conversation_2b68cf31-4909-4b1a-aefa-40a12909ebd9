const SCORES = require("../../config/docSeoScores");
const {
  calculateAnalysisScores,
  calculateUniqueFocusKeyword,
  calculateMetaDescLength,
  calculateFocusKeywordInMetaDesc,
  calculateFocusKeywordInURL,
  totalScore,
  calculateContentLength,
  calculateFocusKeywordDensity,
  calculateInternalLink,
  calculateExternalLink,
  calculateFocusKeywordInSubheading,
  calculateFocusKeywordInImgAltText,
  calculateFocusKeywordInIntroduction,
  calculateImgAltText,
} = require("../../utils/docScoreCalculation");

const { DocAnalysis } = require("../../../sequelize");
const { optimizePercent } = require("../../config/app");

class DocAnalysisService {
  /**
   * Counts doc analysis
   * @param {number} shopId - shop id
   * @param {number} docId - doc id from DB
   * @returns {Promise<*>}
   */
  count = async (shopId, docId) => {
    return DocAnalysis.count({ where: { shop_id: shopId, doc_id: docId } });
  };
  /**
   * Analysis of a single doc with various checking and score calculation.
   * @param shopId - shop id
   * @param doc - doc object from DB
   * @param oldDoc - old doc object from DB
   * @param uniqueCheckEnable - enable unique check
   */
  analysis = async ({ shopId, doc, oldDoc = null, uniqueCheckEnable = false }) => {
    const DocService = require("./DocService");
    const { analysis, ...rest } = await this.calculateScore({
      shopId,
      doc,
      oldDoc,
      uniqueCheckEnable,
    });
    const _docAnalysis = await this.upsert(shopId, doc.id, analysis);
    return await DocService.update(shopId, doc.id, rest);
  };

  /**
   * Insert / Update doc analysis data
   * @param {number} shopId - shop id
   * @param {number} docId - doc id from DB
   * @param {any} data
   * @returns {Promise<*>}
   */
  upsert = async (shopId, docId, data) => {
    const hasData = (await this.count(shopId, docId)) > 0;
    if (hasData) {
      return this.update(shopId, docId, data);
    }

    return this.create(shopId, docId, data);
  };

  /**
   * Updates doc analysis data
   * @param {number} shopId
   * @param {number} docId
   * @param {any} data
   * @returns {Promise<*>}
   */
  update = async (shopId, docId, data) => {
    const [_count, docAnalysis] = await DocAnalysis.update(data, {
      where: { shop_id: shopId, doc_id: docId },
      returning: true,
    });

    return docAnalysis[0]?.toJSON();
  };

  /**
   * Create doc analysis data
   * @param {number} shopId
   * @param {number} docId
   * @param {any} data
   * @returns {Promise<any>}
   */
  create = async (shopId, docId, data) => {
    const docAnalysis = await DocAnalysis.create({
      shop_id: shopId,
      doc_id: docId,
      ...data,
    });

    return docAnalysis.toJSON();
  };

  calculateScore = async ({ shopId, doc, oldDoc = null, uniqueCheckEnable = false }) => {
    let checkUniqueFK = uniqueCheckEnable || !!(oldDoc && doc.focus_keyword !== oldDoc?.focus_keyword);

    const analysis = {
      // Basic SEO
      unique_focus_keyword: checkUniqueFK
        ? await calculateUniqueFocusKeyword(shopId, doc.focus_keyword, doc.id)
        : doc?.analysis?.unique_focus_keyword || SCORES.UNIQUE_FOCUS_KEYWORD,
      // focus_keyword_in_meta_title: calculateFocusKeywordInMetaTitle(doc),
      // content_more_than_800_words: calculateContentLength(doc?.description, 800),
      content_more_than_200_words: calculateContentLength(doc?.description, 200),
      focus_keyword_density: calculateFocusKeywordDensity(doc?.description, doc?.focus_keyword),
      focus_keyword_in_introduction: calculateFocusKeywordInIntroduction(doc?.description, doc?.focus_keyword),
      // meta_title_within_char_limit: calculateMetaTitleLength(doc, 70),

      // Detailed SEO
      // focus_keyword_in_title: calculateFocusKeywordInTitle(doc?.title, doc?.focus_keyword),
      focus_keyword_in_meta_desc: calculateFocusKeywordInMetaDesc(doc, doc?.focus_keyword),
      focus_keyword_in_img_alt_text: calculateFocusKeywordInImgAltText(
        doc?.image,
        doc?.description,
        doc?.focus_keyword
      ),
      focus_keyword_in_url: calculateFocusKeywordInURL(doc?.handle, doc?.focus_keyword),
      meta_desc_within_160_limit: calculateMetaDescLength(doc, 80, 160),
      internal_link_in_content: calculateInternalLink(doc?.description),
      external_link_in_content: calculateExternalLink(doc?.description),
      focus_keyword_in_subheading: calculateFocusKeywordInSubheading(doc?.description, doc?.focus_keyword),
      alt_text_in_all_img: calculateImgAltText(doc?.image, doc?.description),
    };

    const analysisScore = calculateAnalysisScores(analysis);

    const score = Math.round((analysisScore.point * 100) / totalScore());
    return {
      analysis,
      score: score < 100 ? score : 100,
      issues: analysisScore.issues,
      passed: analysisScore.passed,
      is_analysed: true,
      is_optimized: score >= optimizePercent,
    };
  };

  /**
   * Delete single doc analysis of a shop
   * @param {number} shopId - shop id
   * @param {number} docId - doc id from DB
   * @param transaction
   * @returns {Promise<number>}
   */
  delete = async (shopId, docId, transaction = undefined) => {
    const where = { shop_id: shopId, doc_id: docId };
    return DocAnalysis.destroy({ where, transaction });
  };

  deleteByShopId = async (shopId, transaction = undefined) => {
    const where = { shop_id: shopId };
    return DocAnalysis.destroy({ where, transaction });
  };
}

module.exports = new DocAnalysisService();
