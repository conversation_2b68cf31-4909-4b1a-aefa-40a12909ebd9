const {
  serializeDoc,
  serializeDocImage,
  serializeSingleDoc,
  serializeDocUpdatedMetadata,
} = require("../../serializers/DocSerializer");
const { Doc, Op, sequelize, Page } = require("../../../sequelize");
const { QueryTypes } = require("sequelize");
const {
  extractFocusKeyword,
  formatFromDateForQuery,
  formatToDateForQuery,
  preparePagination,
  replaceSpecialChars,
} = require("../../utils/helper");
const DocImageService = require("./DocImageService");
const BetterDocsService = require("../betterdocs/BetterDocsService");
const DocAnalysisService = require("./DocAnalysisService");
const SitemapService = require("../SitemapService");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const pageType = require("storeseo-enums/pageType");
const { NAMESPACE, METAFIELD_KEYS, METAFIELD_TYPES } = require("storeseo-enums/metafields");
const PageMetaService = require("../PageMetaService");
const PageService = require("../PageService");
const AnalysisService = require("../AnalysisService");
const helper = require("../../utils/helper");

class DocService {
  /**
   * Get DB docs count by shop id and conditions
   * @param shopId - shop id
   * @param {import("sequelize").WhereOptions} [conditions]
   * @returns {Promise<number>}
   */
  count = async (shopId, conditions = {}) => {
    return Doc.count({ where: { shop_id: shopId, ...conditions } });
  };

  /**
   * Update doc by shop id, collection id
   * @param {number} shopId - shop Id
   * @param {number} docId - doc id from DB
   * @param {any} data
   * @param transaction
   * @returns {Promise<{}|*>}
   */
  update = async (shopId, docId, data, transaction = undefined) => {
    const [_updateCount, _docs] = await Doc.update(data, {
      where: { shop_id: shopId, id: docId },
      transaction,
    });
    return await this.getDetails(shopId, docId);
  };

  /**
   * Get docs with pagination
   * @param shopId
   * @param page
   * @param limit
   * @param from
   * @param to
   * @param status
   * @param search
   * @param sortBy
   * @param sortOrder
   * @param {Array} fields
   * @returns {Promise<{pagination: {pageCount: number, pageSize: *, page: *, rowCount: *}, collections: (*&{twitterPreviewImage: *, facebookPreviewImage: *, featuredImage: null})[]}>}
   */
  getDocs = async (
    shopId,
    {
      page = 1,
      limit = 20,
      from = "",
      to = "",
      optimize_status = "",
      search = "",
      sortBy = "created_at",
      sortOrder = "DESC",
    },
    fields = []
  ) => {
    const where = {
      shop_id: shopId,
    };

    if (from || to) {
      let fromDate = formatFromDateForQuery(from);

      let toDate = formatToDateForQuery(to);

      where.created_at = {
        [Op.between]: [fromDate, toDate],
      };
    }

    if (search) {
      where.title = { [Op.iLike]: `%${search}%` };
    }

    const optimizeStatusCombinationToScoreCondition = {
      "NEED_IMPROVEMENT,NOT_OPTIMIZED,OPTIMIZED": { [Op.gte]: 0 },
      "NEED_IMPROVEMENT,OPTIMIZED": { [Op.gte]: 50 },
      "NOT_OPTIMIZED,OPTIMIZED": { [Op.or]: [{ [Op.gte]: 75 }, { [Op.lt]: 50 }] },
      "NEED_IMPROVEMENT,NOT_OPTIMIZED": { [Op.gte]: 0, [Op.lt]: 75 },
      OPTIMIZED: { [Op.gte]: 75 },
      NEED_IMPROVEMENT: { [Op.gte]: 50, [Op.lt]: 75 },
      NOT_OPTIMIZED: { [Op.lt]: 50 },
    };
    optimize_status = optimize_status.split(",").sort().join(",");
    if (optimizeStatusCombinationToScoreCondition.hasOwnProperty(optimize_status)) {
      where.score = optimizeStatusCombinationToScoreCondition[optimize_status];
    }

    let offset = (page - 1) * limit;
    sortBy = sortBy === "optimize_status" ? "score" : sortBy;
    let order = [[sortBy, sortOrder]];

    let { count, rows: docs } = await Doc.findAndCountAll({
      attributes: ["doc_id", ...fields],
      where,
      limit,
      offset,
      order,
      include: ["img"],
    });

    return {
      // docs: docs.map((collection) => serializeCollectionToListItem(collection.toJSON())),
      docs: docs?.map((doc) => doc.toJSON()),
      pagination: preparePagination(count, page, limit),
    };
  };

  /**
   * Get doc details by shop id & doc id from DB
   * @param shopId - shop id
   * @param docId - doc id
   * @returns {Promise<{}|*|{}>}
   */
  getDetails = async (shopId, docId) => {
    const conditions = { id: docId };
    return await this.getByCondition(shopId, conditions);
  };

  getByCondition = async (shopId, conditions, reject = false, include = ["img", "analysisData"]) => {
    const doc = await Doc.findOne({
      where: { shop_id: shopId, ...conditions },
      include,
      rejectOnEmpty: reject,
    });
    return serializeSingleDoc(doc.toJSON());
    // return doc.toJSON();
  };

  getPagination = async (shopId, docId) => {
    const paginationQuery = `
      WITH virtual_table AS (
        SELECT
          doc_id,
          lag(doc_id) OVER w AS prev,
          lead(doc_id) OVER w AS next
        FROM docs
        WHERE shop_id = :shopId
        WINDOW w AS (ORDER BY created_at DESC)
      )
      SELECT prev, next FROM virtual_table WHERE doc_id = :docId;
    `;

    const [result] = await sequelize.query(paginationQuery, {
      replacements: { shopId, docId },
      type: QueryTypes.SELECT,
    });

    return result;
  };

  serializeForAnalysis = async (shopId, docId, data) => {
    const { metaTitle, metaDescription, focusKeyword, image, handle } = data;

    const doc = await this.getByCondition(shopId, { doc_id: docId });
    const metadata = serializeDocUpdatedMetadata(doc, { metaTitle, metaDescription });

    const updateData = {
      metafields: metadata.length > 0 ? metadata : doc.metafields,
      focus_keyword: focusKeyword ? replaceSpecialChars(focusKeyword) : doc.focus_keyword,
      img: image || doc.image,
      handle: handle || doc.handle,
    };

    return { ...doc, ...updateData };
  };

  updateDoc = async (shopId, shopDomain, docId, body, session) => {
    const doc = await this.getByCondition(shopId, { doc_id: docId });
    const { focusKeyword, handle, metaTitle, metaDescription, tags } = body;

    // TODO: Update URL redirect logic if needed

    // if (createRedirectUrl && handle) {
    //   let urlRedirect = await ShopifyService.createRedirectURL(session.shop, {
    //     oldPath: `/docs/${doc.handle}`,
    //     newPath: `/docs/${handle}`,
    //   });
    // }
    const payloadData = {
      slug: handle,
      seo_meta_title: metaTitle,
      seo_meta_description: metaDescription,
      focus_keyword: replaceSpecialChars(focusKeyword),
      tags: tags,
      featured_image_alt_text: body?.image?.altText ? body.image.altText : null,
    };

    const updatedResponse = await BetterDocsService.updateDoc(docId, shopDomain, payloadData);

    const { data } = updatedResponse;

    const docUpdatePayload = {
      metafields: [
        { key: "title_tag", value: data.seo_meta_title },
        { key: "description_tag", value: data.seo_meta_description },
      ],
      focus_keyword: replaceSpecialChars(focusKeyword),
      handle: data.slug,
      tags: data.tags,
    };

    const updatedDoc = await this.update(shopId, doc.id, docUpdatePayload);

    if (body?.image) {
      const { doc_id, media_id, src, altText } = body.image;
      const imageData = {
        shop_id: shopId,
        doc_id: doc_id,
        alt_text: altText,
        media_id,
        src,
      };

      await this.upsertImages(shopId, doc_id, imageData);
    }
    return updatedDoc;
  };

  syncDoc = async (docId, domain) => {
    return await BetterDocsService.getSingleDoc(docId, domain);
  };

  /**
   * Inserts new, updates old and deletes removed images from DB
   * @param shopId - shop id
   * @param docId - doc id
   * @param docImage - docs image data
   * @returns {Promise<void>}
   */
  upsertImages = async (shopId, docId, docImage) => {
    let newDocImage;
    if (docImage) {
      newDocImage = await DocImageService.upsert(shopId, docId, docImage);
    }

    // delete duplicate images
    const images = await DocImageService.getAll(shopId, docId);
    images &&
      images.forEach((image) => {
        if (image.id !== newDocImage?.id) {
          DocImageService.deleteById(image.id, shopId, docId);
        }
      });
  };

  upsertRelatedData = async (shopId, docData) => {
    try {
      const serializedDoc = serializeDoc(shopId, docData);
      let doc = await this.upsert(shopId, serializedDoc);

      const docId = doc.get("id");

      const serializedDocImage = serializeDocImage(shopId, docId, docData);
      await this.upsertImages(shopId, docId, serializedDocImage);

      return await this.getDetails(shopId, docId);
    } catch (err) {
      console.error("upsert= ", err);
      return false;
    }
  };

  /**
   * Inserts new or updates docs to local DB
   * @param shopId - shop id
   * @param collectionData - DB collection object data
   * @param transaction - sequelize transaction
   * @returns {Promise<Collection>}
   */
  upsert = async (shopId, docData, transaction = undefined) => {
    let [doc] = await Doc.upsert(docData, { transaction });

    return doc;
  };

  deleteByDocId = async (shopId, docId) => {
    try {
      const doc = await Doc.findOne({
        where: { shop_id: shopId, doc_id: docId },
        rejectOnEmpty: false,
      });
      if (doc) {
        const id = doc.get("id");
        await this.delete(shopId, id);
        return doc;
      }
    } catch (e) {
      return false;
    }
  };

  /**
   * Deletes doc and related table data by shop id, doc id
   * @param {number} shopId - shop Id
   * @param {number} docId - doc id from DB
   * @returns {Promise<boolean|*>}
   */
  delete = async (shopId, docId) => {
    try {
      await Doc.destroy({ where: { shop_id: shopId, id: docId } });
      await DocAnalysisService.delete(shopId, docId);
      await DocImageService.delete(shopId, docId);
      await SitemapService.deleteSitemaps(shopId, docId, analysisEntityTypes.DOC);
    } catch (error) {
      return false;
    }
  };

  deleteByShopId = async (shopId) => {
    try {
      await Doc.destroy({ where: { shop_id: shopId } });
      await DocAnalysisService.deleteByShopId(shopId);
      await DocImageService.deleteByShopId(shopId);
      await SitemapService.deleteAllSitemaps(shopId, analysisEntityTypes.DOC);
    } catch (error) {
      return false;
    }
  };

  deletePageByPageId = async () => {
    try {
      const betterdocsPage = await Page.findOne({ where: { page_type: pageType.BETTERDOCS_HOMEPAGE } });
      const page = betterdocsPage.toJSON();
      const pageId = page.id;
      await Page.destroy({ where: { id: pageId } });
      await PageMetaService.deleteMeta(pageId);
      await AnalysisService.deletePageAnalysis(pageId);
    } catch (error) {
      console.error(error);
      return false;
    }
  };

  /**
   * Get top scored docs by shop id.
   * @param {number} shopId
   * @param {{ limit: number, fields?: string[]}} param1
   */
  getTopScored = async (shopId, { limit = 5, fields = undefined }) => {
    const sortBy = "score";
    const sortOrder = "DESC";

    const { docs } = await this.getDocs(
      shopId,
      {
        limit,
        sortBy,
        sortOrder,
      },
      [...fields, "doc_id"]
    );

    return docs;
  };

  /**
   * Get least scored docs by shop id.
   * @param {number} shopId
   * @param {{ limit: number, fields?: string[]}} param1
   */
  getLeastScored = async (shopId, { limit = 5, fields = undefined }) => {
    const sortBy = "score";
    const sortOrder = "ASC";

    const { docs } = await this.getDocs(
      shopId,
      {
        limit,
        sortBy,
        sortOrder,
      },
      [...fields, "doc_id"]
    );

    return docs;
  };

  countMetaFields = async () => {
    const [[result]] = await sequelize.query(
      `SELECT 
        SUM(json_array_length(metaFields)) AS total_metaFields
        FROM docs`
    );
    const count = result.total_metafields;
    return count;
  };

  /**
   * Extract metadata from homepage body html content
   * @param {string} title
   * @param {string} description
   * @returns {[Metafield]} Array of title & description metafields
   */
  getBetterDocsHomepageMetafields = (title, description) => {
    const namespace = NAMESPACE.GLOBAL;
    const type = METAFIELD_TYPES.SINGLE_LINE_TEXT;

    return [
      {
        namespace,
        key: METAFIELD_KEYS.TITLE_TAG,
        type,
        value: title || "",
      },
      {
        namespace,
        key: METAFIELD_KEYS.DESCRIPTION_TAG,
        type,
        value: description || "",
      },
    ];
  };

  upsertDocHomepage = async (domain, shopId) => {
    try {
      const docsHomepageData = await BetterDocsService.getBetterDocsHomepage(domain);
      const docsMetafields = this.getBetterDocsHomepageMetafields(docsHomepageData.title, docsHomepageData.description);

      const docsData = {
        shop_id: shopId,
        page_id: 2,
        title: helper.minifyTitle(docsHomepageData.title),
        handle: "",
        author: "",
        body_html: "",
        published_at: new Date().toISOString(),
        page_type: pageType.BETTERDOCS_HOMEPAGE,
        is_synced: true,
      };

      const [docsHomePageSaved] = await Page.upsert(docsData, { returning: true });
      let docsHomepage = docsHomePageSaved.toJSON();

      docsHomepage.meta = await PageMetaService.upsertDocsMetas(shopId, docsHomepage.id, docsMetafields, true);
      return docsHomepage;
    } catch (error) {
      console.error("Error updating BetterDocs homepage", error);
      return null;
    }
  };

  updateDocHomepageMeta = async (shopId, pageId, domain, body, url, session) => {
    let page = await PageService.getPage(shopId, pageId);
    await BetterDocsService.updateBetterDocsHomepageMeta(domain, {
      meta: {
        title: body.metaTitle,
        description: body.metaDescription,
      },
    });
    const socialMediaImages = PageService.getSocialMediaPreviewImages(page);
    page = await PageService.updatePageData(shopId, pageId, body, session);
    await PageMetaService.upsertDocsMetas(shopId, page.id, page.meta, true);
    const analysedPage = await AnalysisService.analyseEachPage({ shopId, page, url });
    return { page, socialMediaImages, analysedPage };
  };
}

module.exports = new DocService();
