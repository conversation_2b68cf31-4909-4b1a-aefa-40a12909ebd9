const pusher = require("../../pusher/serverClient");
const socketEvents = require("storeseo-enums/socketEvents");
const NotificationService = require("../NotificationService");
const ShopService = require("../ShopService");

const handleDocSyncComplete = async (eventDetails) => {
  const { shop, total } = eventDetails;

  const shopDetails = await ShopService.getShop(shop);

  const notification = await NotificationService.createNotification({
    shop_id: shopDetails.id,
    title: "Docs sync complete!",
    message: `Total ${total} docs have been synced. You can now see & fix SEO issues for all of them inside StoreSEO.`,
  });

  const unreadNotifications = await NotificationService.countUnread(shopDetails.id);

  // send pusher event
  console.log("\n---\ntriggered doc sync complete event for: ", shop, "\n---\n");
  pusher.trigger(shop, socketEvents.DOC_SYNC_COMPLETE, eventDetails);
  pusher.trigger(shop, socketEvents.NEW_NOTIFICATION, { shop, notification, unreadNotifications });
};

const handleDocSyncUpdate = async (eventDetails) => {
  console.log("Update event", eventDetails);
  //   pusher.trigger(eventDetails.shop, socketEvents.DOC_SYNC_UPDATE, eventDetails);
};

module.exports = {
  handleDocSyncComplete,
  handleDocSyncUpdate,
};
