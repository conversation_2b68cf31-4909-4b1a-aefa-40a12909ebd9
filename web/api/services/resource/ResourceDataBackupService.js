//@ts-check

// @ts-ignore
const { ResourceDataBackup } = require("../../../sequelize");

/**
 * @typedef {{id: number, shop_id: number, resource_id: number, resource_type: string, resource_op_type: string, data: object, create_at: Date, updated_at: Date  }}  ResourceDataBackupType
 * @typedef {{shop_id: number, resource_id: number, resource_type: string, resource_op_type: string, data: object }}  UpsertUpdateResourceDataBackupType
 */

class ResourceDataBackupService {
  /**
   * Inserts new or updates existing resource backup data
   * @param {UpsertUpdateResourceDataBackupType} resourceOptimizationData - DB object data
   * @returns {Promise<ResourceDataBackupType>}
   */
  upsert = async (resourceOptimizationData) => {
    let [data] = await ResourceDataBackup.upsert(resourceOptimizationData);
    return data;
  };

  /**
   * Update resource backup data by shop id, resource id & resource type
   * @param {UpsertUpdateResourceDataBackupType} data
   * @param transaction
   * @returns {Promise<ResourceDataBackupType>}
   */
  update = async (data, transaction = undefined) => {
    const { shop_id, resource_id, resource_type, resource_op_type, data: updateDate } = data;

    await ResourceDataBackup.update(updateDate, {
      where: { shop_id, resource_id, resource_type, resource_op_type },
      transaction,
    });

    return await this.getByCondition({ shop_id, resource_id, resource_type, resource_op_type });
  };

  /**
   * Find a single resource optimization data with the given condition
   * @param {{shop_id: number, resource_id: number, resource_type: string, resource_op_type: string}} where - where where
   * @param {boolean} rejectOnEmpty - reject on empty
   * @returns {Promise<ResourceDataBackupType>}
   */
  getByCondition = async (where, rejectOnEmpty = false) => {
    const data = await ResourceDataBackup.findOne({
      where,
      rejectOnEmpty,
    });
    return data?.toJSON() ?? null;
  };
}

module.exports = new ResourceDataBackupService();
