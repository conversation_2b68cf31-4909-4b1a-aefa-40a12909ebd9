//@ts-check

// @ts-ignore
const { ResourceOptimization } = require("../../../sequelize");

/**
 * @typedef {{meta:boolean,tags:boolean,imageAltText:"allImages" | "featuredImage"}} AiOptimizationSettings
 * @typedef {{optimization_count?: number, restore_count?: number, last_queued_date?: Date | null, last_queue_process_completed_date?: Date | null}} ResourceOptimizationStateType
 * @typedef {{id: number, shop_id: number, resource_id: number, resource_type: string, resource_op_type: string, optimization_meta?: {}, optimization_setting?: AiOptimizationSettings | undefined, optimization_stats?:ResourceOptimizationStateType, approximate_credit_usage?: number, actual_credit_usage?: number, create_at: Date, updated_at: Date  }}  ResourceOptimizationType
 * @typedef {{shop_id: number, resource_id: number, resource_type: string, resource_op_type: string, optimization_meta?: {}, optimization_setting?: AiOptimizationSettings | undefined, optimization_stats?:ResourceOptimizationStateType, approximate_credit_usage?: number, actual_credit_usage?: number }}  UpsertUpdateResourceOptimizationType
 */

class ResourceOptimizationService {
  /**
   * Inserts new or updates existing resource optimization data
   * @param {UpsertUpdateResourceOptimizationType} resourceOptimizationData - DB object data
   * @returns {Promise<ResourceOptimizationType>}
   */
  upsert = async (resourceOptimizationData) => {
    let [data] = await ResourceOptimization.upsert(resourceOptimizationData);
    return data?.toJSON() ?? null;
  };

  /**
   * Update resource optimization data by shop id, resource id & resource type & resource OP type
   * @param {UpsertUpdateResourceOptimizationType} data
   * @param transaction
   * @returns {Promise<ResourceOptimizationType>}
   */
  update = async (data, transaction = undefined) => {
    const { shop_id, resource_id, resource_type, resource_op_type, ...restData } = data;
    await ResourceOptimization.update(restData, {
      where: { shop_id, resource_id, resource_type, resource_op_type },
      transaction,
    });

    return await this.getByCondition({ shop_id, resource_id, resource_type, resource_op_type });
  };

  /**
   * Find a single resource optimization data with the given condition
   * @param {{shop_id: number, resource_id: number, resource_type: string, resource_op_type: string}} where - where where
   * @param {boolean} rejectOnEmpty - reject on empty
   * @returns {Promise<ResourceOptimizationType>}
   */
  getByCondition = async (where, rejectOnEmpty = false) => {
    const data = await ResourceOptimization.findOne({
      where,
      rejectOnEmpty,
    });
    return data?.toJSON() ?? null;
  };
}

module.exports = new ResourceOptimizationService();
