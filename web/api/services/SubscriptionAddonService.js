const { SubscriptionAddon, Op } = require("../../sequelize");
const { ACTIVE, INACTIVE, HIDDEN } = require("storeseo-enums/subscriptionAddonStatus");
const { MONTHLY, CREDIT } = require("storeseo-enums/subscriptionAddonInterval");
const { serializeAddon, serializeAddons } = require("../serializers/AppSubscriptionSerializer");
const { COMBINED } = require("storeseo-enums/subscriptionAddonType");

class SubscriptionAddonService {
  getAddons = async () => {
    const addons = await SubscriptionAddon.findAll({
      where: {
        status: ACTIVE,
      },
      order: ["order"],
    });

    return addons.map((a) => serializeAddon(a.toJSON()));
  };

  getRecurringAddons = async () => {
    const addons = await SubscriptionAddon.findAll({
      where: {
        status: {
          [Op.not]: INACTIVE,
        },
        interval: MONTHLY,
      },
      order: ["order"],
    });

    return addons.map((a) => serializeAddon(a.toJSON()));
  };

  getGrandfatheredAddons = async () => {
    const addons = await SubscriptionAddon.findAll({
      where: {
        type: { [Op.not]: COMBINED },
        status: HIDDEN,
        interval: MONTHLY,
      },
      order: ["order"],
    });

    return addons.map((a) => serializeAddon(a.toJSON()));
  };

  getSelectedAddons = async (ids = []) => {
    const addons = await SubscriptionAddon.findAll({
      where: {
        id: { [Op.in]: ids },
        status: { [Op.not]: INACTIVE },
      },
      order: ["order"],
    });

    return addons.map((a) => a.toJSON()) || [];
  };

  getAddonById = async (id) => {
    const addon = await SubscriptionAddon.findOne({
      where: {
        id,
      },
    });

    return addon.toJSON();
  };

  getCreditAddons = async () => {
    const addons = await SubscriptionAddon.findAll({
      where: {
        status: ACTIVE,
        interval: CREDIT,
      },
      order: ["order"],
    });

    return serializeAddons(addons);
  };

  getSelectedCreditAddons = async (ids = []) => {
    const addons = await SubscriptionAddon.findAll({
      where: {
        id: { [Op.in]: ids },
        status: ACTIVE,
        interval: CREDIT,
      },
      order: ["order"],
    });

    return addons.map((a) => a.toJSON()) || [];
  };
}

module.exports = new SubscriptionAddonService();
