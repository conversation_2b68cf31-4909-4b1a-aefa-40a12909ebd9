const cacheKeys = require("storeseo-enums/cacheKeys");
const { BulkOperationTracker } = require("../../sequelize");
const redisClient = require("../cache/client");
const resourceType = require("storeseo-enums/resourceType");
const resourceOPType = require("storeseo-enums/resourceOPType");
const bulkOperationStatus = require("storeseo-enums/bulkOperationStatus");
const { extractShopifyIdFromGqlId } = require("../utils/helper");

class BulkOperationTrackerService {
  #cacheKey = cacheKeys.BULK_OPERATION_TRACKER;

  /**
   *
   * @param {string} shop - Shop domain
   * @param {string} resourceType - Resource type
   * @param {string} opType - Operation type
   * @param {string | Array<string>} cacheKey - Cache key to get the value
   * @example getCachedBulkOperationTracker("shop1.myshopify.com", "PRODUCTS", "AI_OP", "op_id") // returns "123"
   * @example getCachedBulkOperationTracker("shop1.myshopify.com", "PRODUCTS", "AI_OP", ["op_id", "batch_size"]) // returns Map { "op_id": "123", "batch_size": "10" }
   */
  async getCachedBulkOperationTracker(shop, resourceType, opType, cacheKey) {
    const baseKey = `${shop}:${this.#cacheKey}:${opType}:${resourceType}`;

    if (typeof cacheKey === "string") {
      // Get single key
      const key = `${baseKey}:${cacheKey}`;
      const result = await redisClient.get(key);
      return result;
    }

    const keys = cacheKey.map((key) => `${baseKey}:${key}`);

    const data = await redisClient.mGet(keys);
    const result = new Map();
    for (let i = 0; i < keys.length; i++) {
      const cacheKey = keys[i].split(":").pop();
      const value = data[i];
      result.set(cacheKey, value);
    }
    return result;
  }

  /**
   *
   * @param {string} shop - Shop domain
   * @param {keyof typeof resourceType} resourceType
   * @param {keyof typeof resourceOPType} opType
   * @param {{op_id?: number, op_status?: string, batch_size: number, success_count?: number, failed_count?: number, op_output: Record<string, string>}} data
   */
  async setCachedBulkOperationTracker(shop, resourceType, opType, data) {
    const baseKey = `${shop}:${this.#cacheKey}:${opType}:${resourceType}`;
    for (let key of Object.keys(data)) {
      const cacheKey = `${baseKey}:${key}`;
      const value = data[key];
      if (value === undefined) {
        continue;
      }
      await redisClient.set(cacheKey, value);
    }
  }

  /**
   *
   * @param {string} shop - Shop domain
   * @param {keyof typeof resourceType} resourceType
   * @param {keyof typeof resourceOPType} opType
   * @param {string | Array<string} cacheKey - Cache key to delete
   * @example clearCachedBulkOperationTracker("shop1.myshopify.com", "PRODUCTS", "AI_OP", "op_id") // deletes the key "op_id"
   * @example clearCachedBulkOperationTracker("shop1.myshopify.com", "PRODUCTS", "AI_OP", ["op_id", "batch_size"]) // deletes the keys "op_id" and "batch_size"
   * @example clearCachedBulkOperationTracker("shop1.myshopify.com", "PRODUCTS", "AI_OP") // deletes all keys
   */
  async clearCachedBulkOperationTracker(shop, resourceType, opType, cacheKey) {
    const baseKey = `${shop}:${this.#cacheKey}:${opType}:${resourceType}`;

    let keys;
    if (Array.isArray(cacheKey)) {
      keys = cacheKey.map((key) => `${baseKey}:${key}`);
    } else if (typeof cacheKey === "string") {
      keys = `${baseKey}:${cacheKey}`;
    } else {
      keys = await redisClient.keys(`${baseKey}:*`);
    }

    return await redisClient.del(keys);
  }

  /**
   * @param {{id: number, domain: string}} shop - Shop details
   * @param {keyof typeof resourceType} resourceType
   * @param {keyof typeof resourceOPType} opType
   * @param {{batch_size: number, resource: Array<{gId}>}} options
   */
  async initializeBulkOperationTracker(shop, resourceType, opType, options) {
    const { batch_size, resource } = options;

    const cacheKeys = ["op_status", "batch_size", "op_id"];
    const pendingOpTracker = await this.getCachedBulkOperationTracker(shop.domain, resourceType, opType, cacheKeys);
    const isPendingOpTrackerExist =
      pendingOpTracker.size > 0 && pendingOpTracker.get("op_status") === bulkOperationStatus.PENDING;

    if (isPendingOpTrackerExist) {
      const pendingBatchSize = pendingOpTracker.get("batch_size");
      const pendingOpTrackerId = pendingOpTracker.get("op_id");
      await this.setCachedBulkOperationTracker(shop.domain, resourceType, opType, {
        batch_size: Number(pendingBatchSize) + batch_size,
      });
      await this.#setResourceTrackerIds(shop.domain, resourceType, opType, resource, pendingOpTrackerId);
    } else {
      const op_tracker = await this.createBulkOperationTracker({
        shop_id: shop.id,
        resource_type: resourceType,
        op_type: opType,
        batch_size,
        status: bulkOperationStatus.PENDING,
        start_date: new Date(),
      });

      await this.setCachedBulkOperationTracker(shop.domain, resourceType, opType, {
        op_id: op_tracker.id,
        op_status: op_tracker.status,
        batch_size: op_tracker.batch_size,
        success_count: op_tracker.success_count,
        failed_count: op_tracker.failed_count,
      });

      await this.#setResourceTrackerIds(shop.domain, resourceType, opType, resource, op_tracker.id);
    }
  }

  /**
   * @private
   * @param {string} domain - Shop domain
   * @param {keyof typeof resourceType} resourceType
   * @param {keyof typeof resourceOPType} opType
   * @param {Array<{gId}>} resource
   * @param {number} trackerId
   */
  async #setResourceTrackerIds(domain, resourceType, opType, resource, trackerId) {
    for (let item of resource) {
      const id = extractShopifyIdFromGqlId(item.gId);
      const itemKey = `${id}`;
      await this.setCachedBulkOperationTracker(domain, resourceType, opType, {
        [itemKey]: trackerId,
      });
    }
  }

  /**
   * @param {number} shopId - Shop id
   * @param {import("sequelize").WhereAttributeHash} conditions
   */
  async getBulkOperationTrackerDetails(shopId, conditions) {
    const res = await BulkOperationTracker.findOne({ where: { shop_id: shopId, ...conditions } });
    return res?.toJSON() || null;
  }

  /**
   *
   * @param {*} data
   */
  async createBulkOperationTracker(data) {
    const res = await BulkOperationTracker.create(data);
    return res.toJSON();
  }

  /**
   *
   * @param {import("sequelize").WhereAttributeHash} conditions
   * @param {*} data
   */
  async updateBulkOperationTrackerByCondition(conditions, data) {
    const [_, bulkOperations] = await BulkOperationTracker.update(data, {
      where: conditions,
      returning: true,
    });

    return bulkOperations[0]?.toJSON();
  }
}

const bulkOperationTrackerService = Object.freeze(new BulkOperationTrackerService());
module.exports = bulkOperationTrackerService;
