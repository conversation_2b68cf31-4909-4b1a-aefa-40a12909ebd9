// // @ts-check
const pusher = require("../../pusher/serverClient");
const socketEvents = require("storeseo-enums/socketEvents");
const NotificationService = require("../NotificationService");
const ShopService = require("../ShopService");
const CollectionService = require("./CollectionService");
const CollectionProductsService = require("./CollectionProductsService");

/**
 * Handle collection sync complete event by updating/resetting flags & sending push notifications
 * @param {import("../../jsDocTypes").CollectionSyncCompleteEventDetails} eventDetails
 */
const handleCollectionSyncComplete = async (eventDetails) => {
  const { shop, deleteSyncCursor, total } = eventDetails;

  // cleanup necessary flags
  await CollectionService.setSyncOngoingStatus(shop, false);
  if (deleteSyncCursor) await CollectionService.deleteSyncCursor(shop);

  // create a notification entry for the event
  const shopDetails = await ShopService.getShop(shop);
  const notification = await NotificationService.createNotification({
    shop_id: shopDetails.id,
    title: "Collection sync complete!",
    message: `Total ${total} collections have been synced. You can now see & fix SEO issues for all of them inside StoreSEO.`,
  });
  const unreadNotifications = await NotificationService.countUnread(shopDetails.id);

  // send pusher event
  console.log("\n---\ntriggered collection sync complete event for: ", shop, "\n---\n");
  pusher.trigger(shop, socketEvents.COLLECTION_SYNC_COMPLETE, eventDetails);
  pusher.trigger(shop, socketEvents.NEW_NOTIFICATION, { shop, notification, unreadNotifications });
};
/**
 * Handle collection sync update event
 * @param {import("../../jsDocTypes").CollectionSyncUpdateEventDetails} eventDetails
 */
const handleCollectionSyncUpdate = async (eventDetails) => {
  pusher.trigger(eventDetails.shop, socketEvents.COLLECTION_SYNC_UPDATE, eventDetails);
};

/**
 * Handle collection sync complete event by updating/resetting flags & sending push notifications
 */
const handleCollectionProductsSyncComplete = async (eventDetails) => {
  const { shop, deleteSyncCursor, total, collection } = eventDetails;

  const { id: collectionId, title: collectionTitle } = collection;

  // cleanup necessary flags
  // await CollectionProductsService.setSyncOngoingStatus(shop, false);
  if (deleteSyncCursor) await CollectionProductsService.deleteSyncCursor(shop, collectionId);

  // create a notification entry for the event
  const shopDetails = await ShopService.getShop(shop);
  const notification = await NotificationService.createNotification({
    shop_id: shopDetails.id,
    title: "Collection products sync complete!",
    message: `Total ${total} collection products have been synced. You can now see & fix SEO issues for all of them inside StoreSEO.`,
  });
  const unreadNotifications = await NotificationService.countUnread(shopDetails.id);

  // send pusher event
  console.log("\n---\ntriggered collection products sync complete event for: ", collectionTitle, "\n---\n");
  pusher.trigger(shop, socketEvents.COLLECTION_PRODUCTS_SYNC_COMPLETE, eventDetails);
  pusher.trigger(shop, socketEvents.NEW_NOTIFICATION, { shop, notification, unreadNotifications });
};

module.exports = {
  handleCollectionSyncComplete,
  handleCollectionSyncUpdate,
  handleCollectionProductsSyncComplete,
};
