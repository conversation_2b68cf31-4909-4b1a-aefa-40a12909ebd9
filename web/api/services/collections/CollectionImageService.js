// // @ts-check
const { CollectionImage } = require("../../../sequelize");
const { formatCollectionImages, formatCollectionImage } = require("../../serializers/CollectionSerializer");
const { preparePagination } = require("../../utils/helper");
const { Op } = require("sequelize");

class CollectionImageService {
  /**
   * Get all collection images
   * @param shopId - DB shop id
   * @param collectionId - DB collection id
   * @returns {Promise<any>}
   */
  getAll = async (shopId, collectionId) => {
    return await CollectionImage.findAll({
      where: { shop_id: shopId, collection_id: collectionId },
    });
  };

  /**
   * Get collection image by gql id
   * @param {number} shopId
   * @param {string} media_id
   * @param {Array<string>} include
   * @returns
   */
  getById = async (shopId, media_id, include = ["collection"]) => {
    return await CollectionImage.findOne({
      where: { shop_id: shopId, gql_id: media_id },
      include,
    });
  };

  /**
   * Get all images by shop
   * @param shopId - DB shop id
   * @param {object} options - pagination and filters
   * @returns {Promise<any>}
   */
  getImagesByShop = async (
    shopId,
    {
      page = 1,
      limit = 20,
      search = "",
      status = "",
      // alt_status = "",
      fileSize,
      has_alt_text,
      sortBy = "created_at",
      sortOrder = "DESC",
    }
  ) => {
    const fileSizeInBytes = fileSize
      ? fileSize.split(",").map((size) => {
          const byte = size ? (Number(size) * 1024 * 1024).toString() : "";
          return parseInt(byte);
        })
      : [];

    const where = {
      shop_id: shopId,
    };

    // Convert the following logic to Sequelize where clause
    if (fileSizeInBytes.length > 0) {
      if (fileSizeInBytes[0] > 0 && fileSizeInBytes[1] > 0) {
        where.file_size = {
          [Op.gte]: fileSizeInBytes[0],
          [Op.lte]: fileSizeInBytes[1],
        };
      } else if (fileSizeInBytes[0] > 0) {
        where.file_size = { [Op.gte]: fileSizeInBytes[0] };
      } else if (fileSizeInBytes[1] > 0) {
        where.file_size = { [Op.lte]: fileSizeInBytes[1] };
      }
    }

    if (search) {
      where.src = { [Op.like]: `%${search}%` };
    }

    if (status) {
      where.optimization_status = {
        [Op.in]: status.split(","),
      };
    }

    if (has_alt_text !== undefined) {
      where.has_alt_text = has_alt_text;
    }

    const imagesPromise = CollectionImage.findAndCountAll({
      where,
      limit,
      offset: (page - 1) * limit,
      order: [[sortBy, sortOrder]],
      include: ["collection", "resourceOptimizationMeta"],
    });
    const resourceCountPromise = CollectionImage.count({
      where: {
        shop_id: shopId,
      },
    });

    const [images, resourceCount] = await Promise.all([imagesPromise, resourceCountPromise]);

    const { count, rows } = images;

    const formattedImages = rows.map((item) => item.toJSON());

    return {
      images: formatCollectionImages(formattedImages),
      pagination: preparePagination(count, page, limit),
      totalCount: resourceCount,
    };
  };

  /**
   * Save or update shopify collection image to database
   * @param {object} imageData - image data
   * @returns {Promise<any>}
   */
  upsert = async (imageData) => {
    const [updateCollectionImage] = await CollectionImage.upsert(imageData, {
      conflictFields: ["gql_id", "shop_id", "collection_id"],
    });

    const collectionImage = await this.getById(imageData.shop_id, imageData.gql_id);

    return formatCollectionImage(collectionImage?.toJSON() || null);
  };

  /**
   * Deletes collection images
   * @param {number} shopId - shop id
   * @param {number} collectionId - collection id from DB
   * @param transaction
   * @returns {Promise<number>}
   */
  delete = async (shopId, collectionId, transaction = undefined) => {
    const where = { shop_id: shopId, collection_id: collectionId };
    return await CollectionImage.destroy({ where, transaction });
  };

  /**
   * Deletes collection images by image id
   * @param {number} id - image DB id
   * @param {number} shopId - shop id
   * @param {number} collectionId - collection id from DB
   * @param transaction
   * @returns {Promise<number>}
   */
  deleteById = async (id, shopId, collectionId, transaction = undefined) => {
    const where = { id, shop_id: shopId, collection_id: collectionId };
    return await CollectionImage.destroy({ where, transaction });
  };
}
module.exports = new CollectionImageService();
