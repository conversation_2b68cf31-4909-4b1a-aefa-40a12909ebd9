// // @ts-check
const { sequelize, Collection, Op, Sitemap, CollectionImage } = require("../../../sequelize");
const { QueryTypes } = require("sequelize");
const cache = require("../../cache");
const logger = require("storeseo-logger");
const {
  serializeShopifyCollection,
  serializeSingleCollection,
  serializeShopifyCollectionMetas,
  serializeCollectionToListItem,
  serializeCollectionUpdatedMetadata,
  serializeShopifyCollectionImage,
  serializeCollectionSitemap,
} = require("../../serializers/CollectionSerializer");
const {
  formatFromDateForQuery,
  formatToDateForQuery,
  preparePagination,
  replaceSpecialChars,
} = require("../../utils/helper");
const CollectionMetaService = require("./CollectionMetaService");
const CollectionImageService = require("./CollectionImageService");
const CollectionAnalysisService = require("./CollectionAnalysisService");
const ShopifyService = require("../ShopifyService");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const { findSitemapFilter } = require("../../utils/sitemapUtils");
const SitemapService = require("../SitemapService");
const { METAFIELD_KEYS } = require("storeseo-enums/metafields");
const AiOptimizationStatus = require("storeseo-enums/aiOptimization");

class CollectionService {
  /**
   * Get DB collection count by shop id and conditions
   * @param shopId - shop id
   * @param {import("sequelize").WhereOptions} [conditions]
   * @returns {Promise<number>}
   */
  count = async (shopId, conditions = {}) => {
    return Collection.count({ where: { shop_id: shopId, ...conditions } });
  };
  /**
   * Get last sync cursor for collection by shop domain. It's used for pagination in sync process.
   * @param {*} shop - shop domain
   * @returns {Promise<string|null>}
   */
  getSyncCursor = async (shop) => cache.getCollectionSyncCursor(shop);
  /**
   * Set last sync cursor for collection by shop domain. It's used for pagination in sync process.
   * @param {*} shop - shop domain
   * @param {*} cursor - cursor value
   * @returns {Promise<string>}
   */
  setSyncCursor = async (shop, cursor) => cache.setCollectionSyncCursor(shop, cursor);

  /**
   * Delete last sync cursor for collection by shop domain. It's used for pagination in sync process.
   * @param {*} shop - shop domain
   * @returns {Promise<number>}
   * */
  deleteSyncCursor = async (shop) => cache.removeCollectionSyncCursor(shop);
  /**
   * Set sync ongoing status for collection by shop domain.
   * @param {*} shop - shop domain
   * @param {*} status - status value (true/false)
   * @returns {Promise<boolean | null>} - status value
   */
  setSyncOngoingStatus = async (shop, status) => cache.collectionSyncOngoingStatus(shop, status);

  /**
   * Save or update collection and related data to DB
   * @param shopId - shop id
   * @param shopifyCollection - shopify collection object
   * @returns {Promise<boolean|{}|*>}
   */
  upsertRelatedData = async (shopId, shopifyCollection) => {
    // const transaction = await sequelize.transaction();
    try {
      const serializedShopifyCollection = serializeShopifyCollection(shopId, shopifyCollection);
      let collection = await this.upsert(serializedShopifyCollection);

      const collectionId = collection.get("id");

      const serializedShopifyMetas = serializeShopifyCollectionMetas(shopifyCollection);
      await this.upsertMetadata(shopId, collectionId, serializedShopifyMetas);

      const serializedShopifyImage = serializeShopifyCollectionImage(shopId, collectionId, shopifyCollection);
      await this.upsertImages(shopId, collectionId, serializedShopifyImage);
      // await transaction.commit();
      return await this.getDetails(shopId, collectionId);
    } catch (e) {
      console.error("upsert= ", e);
      // await transaction.rollback();
      return false;
    }
  };

  /**
   * Inserts new or updates collections to local DB
   * @param collectionData - DB collection object data
   * @param transaction - sequelize transaction
   * @returns {Promise<Collection>}
   */
  upsert = async (collectionData, transaction = undefined) => {
    let [collection] = await Collection.upsert(collectionData, { transaction });

    return collection;
  };

  /**
   * Inserts new, updates old and deletes removed metafields from DB
   * @param shopId - shop id
   * @param collectionId - collection id
   * @param collectionMetas - collection metafields
   * @returns {Promise<void>}
   */
  upsertMetadata = async (shopId, collectionId, collectionMetas) => {
    await CollectionMetaService.destroyDeletedMeta(shopId, collectionId, collectionMetas);
    return await CollectionMetaService.upsert(shopId, collectionId, collectionMetas);
  };

  /**
   * Inserts new, updates old and deletes removed images from DB
   * @param shopId - shop id
   * @param collectionId - collection id
   * @param collectionImage - collection image data
   * @returns {Promise<void>}
   */
  upsertImages = async (shopId, collectionId, collectionImage) => {
    let newCollectionImage;
    if (collectionImage) {
      newCollectionImage = await CollectionImageService.upsert(collectionImage);
      console.log("Shop =>", shopId, "Collection =>", collectionId, "Collection Image =>", newCollectionImage.id);
    }

    // delete duplicate images
    const images = await CollectionImageService.getAll(shopId, collectionId);
    images &&
      images.forEach((image) => {
        if (image.id !== newCollectionImage?.id) {
          CollectionImageService.deleteById(image.id, shopId, collectionId);
        }
      });
  };

  /**
   *
   * @param {number} shopId
   * @param {import("sequelize").WhereOptions} conditions
   */
  iterateOverCollections = async function* (shopId, conditions = {}) {
    let limit = 30;
    let offset = 0;
    let collections = [1];

    while (collections.length > 0) {
      collections = await Collection.findAll({
        where: { shop_id: shopId, ...conditions },
        limit,
        offset,
        include: ["meta", "img"],
      });

      offset += limit;

      yield collections.map((p) => p.toJSON());
    }
  };

  /**
   * Get collection details by shop id & collection id from DB
   * @param shopId - shop id
   * @param collectionId - collection id
   * @returns {Promise<{}|*|{}>}
   */
  getDetails = async (shopId, collectionId) => {
    const conditions = { id: collectionId };
    return await this.getByCondition(shopId, conditions);
  };

  /**
   * Find a single collection with shop id & conditions from DB
   * @param shopId - shop id
   * @param conditions - where conditions
   * @param {boolean} reject - reject on empty
   * @param {string[] | object[] | object} include - include fields
   * @returns {Promise<(*&{twitterPreviewImage: *, images: *, facebookPreviewImage: *, featuredImage: {altText: *, src: *, id: *}, meta: {id: *}, analysis: *})|{}>}
   */
  getByCondition = async (shopId, conditions, reject = false, include = ["meta", "analysisData", "img"]) => {
    const collection = await Collection.findOne({
      where: { shop_id: shopId, ...conditions },
      include,
      rejectOnEmpty: reject,
    });
    return serializeSingleCollection(collection.toJSON());
  };

  /**
   * Find Collections by shop id & conditions with all related data
   * @param {number} shopId
   * @param {import("sequelize").WhereOptions} conditions
   * @param {import("sequelize").FindOptions} [options]
   * @returns {Promise<any[]>}
   */
  getFullCollectionsByCondition = async (shopId, conditions, options = {}) => {
    const collections = await Collection.findAll({
      ...options,
      where: { shop_id: shopId, ...conditions },
      include: ["meta", "analysisData", "img"],
    });
    if (!collections) {
      return [];
    }
    return collections.map((collection) => serializeSingleCollection(collection.toJSON()));
  };

  /**
   * Update collection by shop id, collection id
   * @param {number} shopId - shop Id
   * @param {number} collectionId - collection id from DB
   * @param {any} data
   * @param transaction
   * @returns {Promise<{}|*>}
   */
  update = async (shopId, collectionId, data, transaction = undefined) => {
    const [_updateCount, _collections] = await Collection.update(data, {
      where: { shop_id: shopId, id: collectionId },
      transaction,
    });
    return await this.getDetails(shopId, collectionId);
  };

  /**
   * Deletes collection and related table data from webhook call
   * @param {number} shopId - shop Id
   * @param {string} collectionGID - collection GraphQL ID from shopify
   * @returns {Promise<any>}
   */
  deleteByGID = async (shopId, collectionGID) => {
    try {
      const collection = await Collection.findOne({
        where: { shop_id: shopId, collection_id: collectionGID },
        rejectOnEmpty: false,
      });
      if (collection) {
        const collectionId = collection.get("id");
        await this.delete(shopId, collectionId);
        return collection;
      }
    } catch (e) {
      return false;
    }
  };

  /**
   * Deletes collection and related table data by shop id, collection id
   * @param {number} shopId - shop Id
   * @param {number} collectionId - collection id from DB
   * @returns {Promise<boolean|*>}
   */
  delete = async (shopId, collectionId) => {
    try {
      await Collection.destroy({ where: { shop_id: shopId, id: collectionId } });
      await CollectionAnalysisService.delete(shopId, collectionId);
      await CollectionMetaService.delete(shopId, collectionId);
      await CollectionImageService.delete(shopId, collectionId);
      await SitemapService.deleteSitemaps(shopId, collectionId, analysisEntityTypes.COLLECTION);
    } catch (e) {
      return false;
    }
  };

  /**
   * Get collections with pagination
   * @param shopId
   * @param page
   * @param limit
   * @param from
   * @param to
   * @param status
   * @param search
   * @param sortBy
   * @param sortOrder
   * @param {Array} fields
   * @returns {Promise<{pagination: {pageCount: number, pageSize: *, page: *, rowCount: *}, collections: (*&{twitterPreviewImage: *, facebookPreviewImage: *, featuredImage: null})[]}>}
   */
  getCollections = async (
    shopId,
    {
      page = 1,
      limit = 20,
      from = "",
      to = "",
      optimize_status = "",
      ai_optimize_status = "",
      search = "",
      sortBy = "created_at",
      sortOrder = "DESC",
    },
    fields = []
  ) => {
    const where = {
      shop_id: shopId,
    };

    if (from || to) {
      let fromDate = formatFromDateForQuery(from);

      let toDate = formatToDateForQuery(to);

      where.created_at = {
        [Op.between]: [fromDate, toDate],
      };
    }

    if (search) {
      where.title = { [Op.iLike]: `%${search}%` };
    }

    const optimizeStatusCombinationToScoreCondition = {
      "NEED_IMPROVEMENT,NOT_OPTIMIZED,OPTIMIZED": { [Op.gte]: 0 },
      "NEED_IMPROVEMENT,OPTIMIZED": { [Op.gte]: 50 },
      "NOT_OPTIMIZED,OPTIMIZED": { [Op.or]: [{ [Op.gte]: 75 }, { [Op.lt]: 50 }] },
      "NEED_IMPROVEMENT,NOT_OPTIMIZED": { [Op.gte]: 0, [Op.lt]: 75 },
      OPTIMIZED: { [Op.gte]: 75 },
      NEED_IMPROVEMENT: { [Op.gte]: 50, [Op.lt]: 75 },
      NOT_OPTIMIZED: { [Op.lt]: 50 },
    };
    optimize_status = optimize_status.split(",").sort().join(",");
    if (optimizeStatusCombinationToScoreCondition.hasOwnProperty(optimize_status)) {
      where.score = optimizeStatusCombinationToScoreCondition[optimize_status];
    }

    if (ai_optimize_status) {
      where.ai_optimization_status = {
        [Op.in]: ai_optimize_status.split(","),
      };
    }

    let offset = (page - 1) * limit;
    sortBy = sortBy === "optimize_status" ? "score" : sortBy;
    let order = [[sortBy, sortOrder]];

    let { count, rows: collections } = await Collection.findAndCountAll({
      attributes: ["collection_id", ...fields],
      where,
      limit,
      offset,
      order,
      include: ["img"],
    });

    return {
      collections: collections.map((collection) => serializeCollectionToListItem(collection.toJSON())),
      pagination: preparePagination(count, page, limit),
    };
  };

  /**
   * Get pagination of collection
   * @param {*} shopId
   * @param {*} collectionId - shopify collection id
   * @returns
   */
  getPagination = async (shopId, collectionId) => {
    const prefix = `gid://shopify/Collection/`;
    const paginationQuery = `
    WITH virtual_table AS (
      SELECT
          REPLACE(collection_id, '${prefix}', '') as collection_id,
          REPLACE(lag(collection_id) OVER w, '${prefix}', '') AS prev,
          REPLACE(lead(collection_id) OVER w, '${prefix}', '') AS next
      FROM collections
      WHERE shop_id = :shopId
      WINDOW w AS (ORDER BY created_at DESC)
    )

    SELECT prev, next FROM virtual_table WHERE collection_id = :collectionId;
  `;

    const [result] = await sequelize.query(paginationQuery, {
      replacements: { shopId, collectionId },
      type: QueryTypes.SELECT,
    });

    return result;
  };

  /**
   * Serialize collection to analyse when updating
   * @param shopId
   * @param collectionId
   * @param data
   * @returns {Promise<*&{twitterPreviewImage: *, facebookPreviewImage: *, featuredImage: {altText: *, src: *, id: *}}>}
   */
  serializeForAnalysis = async (shopId, collectionId, data) => {
    const { metaTitle, metaDescription, focusKeyword, image, handle } = data;
    const collectionGID = `gid://shopify/Collection/${collectionId}`;

    const collection = await this.getByCondition(shopId, { collection_id: collectionGID });
    const metadata = serializeCollectionUpdatedMetadata(collection, { metaTitle, metaDescription });

    const updateData = {
      meta: metadata.length > 0 ? metadata : collection.meta,
      focus_keyword: focusKeyword ? replaceSpecialChars(focusKeyword) : collection.focus_keyword,
      img: image || collection.image,
      handle: handle || collection.handle,
    };

    return { ...collection, ...updateData };
  };

  /**
   * Update collection and related data to DB and sync (Update) with shopify
   * @param {*} shopId
   * @param {*} collectionId - shopify collection id
   * @param {*} body
   * @param {*} session
   */
  updateAndSyncWithShopify = async (shopId, collectionId, body, session) => {
    const collection = await this.getByCondition(shopId, { collection_id: collectionId });

    const { metaTitle, metaDescription, focusKeyword, handle = undefined, createRedirectUrl = false } = body;

    if (createRedirectUrl && handle) {
      await ShopifyService.createRedirectURL(session.shop, {
        oldPath: `/collections/${collection.handle}`,
        newPath: `/collections/${handle}`,
      });
    }

    const {
      collectionUpdate: { collection: shopifyCollection, userErrors },
    } = await ShopifyService.updateCollection(session.shop, {
      id: collection.collection_id,
      seo: {
        title: metaTitle,
        description: metaDescription,
      },
      handle,
    });

    if (userErrors.length > 0) {
      logger.error("Collection Metafields update to shopify error.", { userErrors });
      console.log("Collection Metafields update to shopify error.", { userErrors });
      return false;
    }

    const serializedShopifyMetas = serializeShopifyCollectionMetas(shopifyCollection);
    await this.upsertMetadata(shopId, collection.id, serializedShopifyMetas);

    const updateData = {
      focus_keyword: replaceSpecialChars(focusKeyword),
      handle: shopifyCollection.handle,
    };

    return await this.update(shopId, collection.id, updateData);
  };

  /**
   *
   * @param shopId
   * @param page
   * @param limit
   * @param search
   * @param filterOn
   * @param filterValue
   * @param sortBy
   * @param sortOrder
   * @returns {Promise<{pagination: Promise<{pageCount: number, pageSize, page, rowCount: Promise<number>}>, sitemaps: ((*&{featuredImage: {altText: *, src: *, id: *}|null, noFollow: boolean, isChecked: boolean, noIndex: boolean, status: boolean})[]|*[])}>}
   */
  getSitemaps = async (
    shopId,
    {
      page = 1,
      limit = 20,
      search = "",
      filterOn = "sitemap",
      filterValue = "-1",
      sortBy = "created_at",
      sortOrder = "DESC",
    }
  ) => {
    const offset = limit * (page - 1);

    const where = { shop_id: shopId };

    let order = [[sortBy, sortOrder]];

    if (search) {
      where.title = { [Op.iLike]: `%${search}%` };
    }

    const sitemapWhere = findSitemapFilter(filterOn, filterValue);

    const { count, rows } = await Collection.findAndCountAll({
      attributes: ["id", "shop_id", "collection_id", "title", "handle"],
      where,
      include: [
        {
          model: Sitemap,
          as: "sitemap",
          where: [sitemapWhere, { resource_type: analysisEntityTypes.COLLECTION }],
          required: true,
          attributes: ["id", "sitemap_disabled", "no_index", "no_follow"],
        },
        "img",
      ],
      limit,
      offset,
      order,
    });

    return {
      sitemaps: rows.map((p) => serializeCollectionSitemap(p)) || [],
      pagination: preparePagination(count, page, limit),
    };
  };

  /**
   * Updates sitemap data to shopify and database
   * @param shopId
   * @param session
   * @param sitemap
   * @returns {Promise<void>}
   */
  updateSitemap = async ({ shopId, session, sitemap }) => {
    const collectionGID = `gid://shopify/Collection/${sitemap.id}`;
    const collection = await this.getByCondition(shopId, {
      collection_id: collectionGID,
    });

    let newStatus = sitemap.status ? "0" : "1";

    const {
      metafieldsSet: { metafields },
    } = await ShopifyService.setMetafield(session.shop, {
      ownerId: collectionGID,
      key: METAFIELD_KEYS.HIDDEN,
      value: newStatus,
    });

    await CollectionMetaService.upsert(shopId, collection.id, metafields);

    await SitemapService.updateSitemapData({
      shopId,
      resourceId: collection.id,
      resourceType: analysisEntityTypes.COLLECTION,
      metaKey: METAFIELD_KEYS.SITEMAP_DISABLED,
      metaStatus: newStatus,
    });
  };

  /**
   * Get top scored collections by shop id.
   * @param {number} shopId
   * @param {{ limit: number, fields?: string[]}} param1
   */
  getTopScored = async (shopId, { limit = 5, fields = undefined }) => {
    const sortBy = "score";
    const sortOrder = "DESC";
    const order = [[sortBy, sortOrder]];

    const collections = await Collection.findAll({
      attributes: [...fields, "collection_id"],
      where: { shop_id: shopId },
      limit,
      order,
      include: [
        {
          model: CollectionImage,
          as: "img",
          required: false,
          attributes: ["src", "alt_text"],
        },
      ],
    });

    return collections.map((collection) => serializeCollectionToListItem(collection.toJSON()));
  };

  /**
   * Get least scored collections by shop id.
   * @param {number} shopId
   * @param {{ limit: number, fields?: string[]}} param1
   */
  getLeastScored = async (shopId, { limit = 5, fields = undefined }) => {
    const sortBy = "score";
    const sortOrder = "ASC";
    const order = [[sortBy, sortOrder]];

    const collections = await Collection.findAll({
      attributes: [...fields, "collection_id"],
      where: { shop_id: shopId },
      limit,
      order,
      include: [
        {
          model: CollectionImage,
          as: "img",
          required: false,
          attributes: ["src", "alt_text"],
        },
      ],
    });

    return collections.map((collection) => serializeCollectionToListItem(collection.toJSON()));
  };

  /**
   * Get all collections by shop id.
   * @param {number} shopId
   * @param {{ limit?: number, fields?: string[]}} param1
   */
  getCollectionsByShopId = async (shopId, fields = [], limit = null) => {
    const collections = await Collection.findAll({ where: { shop_id: shopId }, attributes: ["id", ...fields], limit });
    return collections.map((p) => p.toJSON());
  };

  getSitemapEnabledCollectionsByShopId = async (shopId, fields = [], limit = null) => {
    const collections = await Collection.findAll({
      where: { shop_id: shopId },
      attributes: ["id", ...fields],
      limit,
      include: [
        {
          model: Sitemap,
          as: "sitemap",
          attributes: ["resource_id", "resource_type", "sitemap_disabled"],
          where: {
            resource_type: analysisEntityTypes.COLLECTION,
            sitemap_disabled: 0,
          },
        },
      ],
    });
    return collections.map((p) => p.toJSON());
  };

  /**
   * Restores collection content from backup data
   * @param {number} shopId
   * @param {any} collectionDetails
   * @param {Object} restoreData
   * @param {Object} session
   * @returns {Promise<any>}
   */
  restoreCollectionContent = async (shopId, collectionDetails, restoreData, session) => {
    await this.update(shopId, collectionDetails.id, {
      ai_optimization_status: AiOptimizationStatus.NOT_OPTIMIZED,
    });

    // Update the collection data to Shopify
    await this.updateAndSyncWithShopify(
      shopId,
      collectionDetails.collection_id,
      {
        ...restoreData,
        focusKeyword: collectionDetails?.focus_keyword,
      },
      session
    );

    const afterRestoreCollection = await this.getByCondition(shopId, {
      id: collectionDetails.id,
    });

    // Reanalyze the collection after restore
    return await CollectionAnalysisService.analysis({
      shopId,
      collection: afterRestoreCollection,
      oldCollection: collectionDetails,
      uniqueCheckEnable: true,
    });
  };
}

module.exports = new CollectionService();
