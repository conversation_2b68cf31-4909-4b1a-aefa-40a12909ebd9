// // @ts-check
const { isEmpty } = require("lodash");
const { CollectionMeta, Op } = require("../../../sequelize");

class CollectionMetaService {
  /**
   * Get collection metas by shop id, collection id
   * @param {number} shopId - shop id
   * @param {number} collectionId - collection id from DB
   * @returns {Promise<any[]>}
   */
  get = async (shopId, collectionId) => {
    const where = { shop_id: shopId, collection_id: collectionId };
    const pms = await CollectionMeta.findAll({ where });
    return pms.map((pm) => pm.toJSON());
  };

  /**
   * Save or update shopify collection meta to database
   * @param {number} shopId - shop id
   * @param {number} collectionId - collection id from DB
   * @param {any[]} metas
   * @returns {Promise<any>}
   */
  upsert = async (shopId, collectionId, metas) => {
    const dbMetas = [];
    for (let meta of metas) {
      const metaData = {
        shop_id: shopId,
        collection_id: collectionId,
        gql_id: meta.id,
        key: meta.key,
        type: meta.type,
        namespace: meta.namespace,
        value: meta.value,
        description: meta.description,
      };
      const [m] = await CollectionMeta.upsert(metaData, { conflictFields: ["gql_id", "shop_id", "collection_id"] });
      dbMetas.push(m);
    }

    console.log(
      "Shop =>",
      shopId,
      "Collection =>",
      collectionId,
      "Shopify Meta =>",
      metas.length,
      "Inserted/Updated Meta =>",
      dbMetas.length
    );

    return dbMetas;
  };

  /**
   * Deletes the deleted meta field data comparing shopify and database
   * @param {number} shopId - shop id
   * @param {number} collectionId - collection id from DB
   * @param shopifyMetas
   * @returns {Promise<any>}
   */
  destroyDeletedMeta = async (shopId, collectionId, shopifyMetas = []) => {
    const dbMetas = await this.get(shopId, collectionId);
    if (dbMetas.length > 0) {
      const deletedIds = this.findDeletedMetaByGqlIds(dbMetas, shopifyMetas);
      return await this.deleteByGId(shopId, collectionId, deletedIds);
    }
  };

  /**
   * Find deleted meta ids comparing database & shopify meta
   * @param {Array} dbMetas
   * @param {Array} shopifyMetas
   * @returns {*}
   */
  findDeletedMetaByGqlIds = (dbMetas = [], shopifyMetas = []) => {
    return dbMetas.filter((dm) => !shopifyMetas.find((m) => m.id === dm.gql_id))?.map((m) => m.gql_id || null);
  };

  /**
   * Deletes collection meta by graphql ids array
   * @param shopId
   * @param collectionId
   * @param deletedIds
   * @returns {Promise<any>}
   */
  deleteByGId = async (shopId, collectionId, deletedIds = []) => {
    if (deletedIds.length > 0) {
      const where = { shop_id: shopId, collection_id: collectionId };
      where.gql_id = { [Op.in]: deletedIds };
      return await CollectionMeta.destroy({ where });
    }
  };

  /**
   * Delete collections meta
   * @param {number} shopId - shop id
   * @param {number} collectionId - product id from DB
   * @param transaction
   * @returns {Promise<number|boolean>}
   */
  delete = async (shopId, collectionId, transaction = undefined) => {
    const where = { shop_id: shopId, collection_id: collectionId };
    return await CollectionMeta.destroy({ where, transaction });
  };
}

module.exports = new CollectionMetaService();
