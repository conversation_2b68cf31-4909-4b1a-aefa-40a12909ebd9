// // @ts-check
const SCORES = require("../../config/collectionSeoScores");
const {
  calculateUniqueFocusKeyword,
  calculateMetaDescLength,
  calculateFocusKeywordInMetaTitle,
  calculateMetaTitleLength,
  calculateFocusKeywordInTitle,
  calculateFocusKeywordInMetaDesc,
  calculateFocusKeywordInURL,
  calculateAnalysisScores,
  totalScore,
} = require("../../utils/collectionScoreCalculations");
const { CollectionAnalysis } = require("../../../sequelize");
const { optimizePercent } = require("../../config/app");

class CollectionAnalysisService {
  /**
   * Analysis of a single collection with various checking and score calculation.
   * @param shopId - shop id
   * @param collection - collection object from DB
   * @param oldCollection - old collection object from DB
   * @param uniqueCheckEnable - enable unique check
   */
  analysis = async ({ shopId, collection, oldCollection = null, uniqueCheckEnable = false }) => {
    const CollectionService = require("./CollectionService");
    const { analysis, ...rest } = await this.calculateScore({
      shopId,
      collection,
      oldCollection,
      uniqueCheckEnable,
    });
    const _collectionAnalysis = await this.upsert(shopId, collection.id, analysis);

    console.log("collection for analysis: ", collection.id, collection.title);
    return await CollectionService.update(shopId, collection.id, rest);
  };

  /**
   * Insert / Update collection analysis data
   * @param {number} shopId - shop id
   * @param {number} collectionId - collection id from DB
   * @param {any} data
   * @returns {Promise<*>}
   */
  upsert = async (shopId, collectionId, data) => {
    const hasData = (await this.count(shopId, collectionId)) > 0;
    if (hasData) {
      return this.update(shopId, collectionId, data);
    }

    return this.create(shopId, collectionId, data);
  };

  /**
   * Counts collection analysis
   * @param {number} shopId - shop id
   * @param {number} collectionId - collection id from DB
   * @returns {Promise<*>}
   */
  count = async (shopId, collectionId) => {
    return CollectionAnalysis.count({ where: { shop_id: shopId, collection_id: collectionId } });
  };

  /**
   * Updates collection analysis data
   * @param {number} shopId
   * @param {number} collectionId
   * @param {any} data
   * @returns {Promise<*>}
   */
  update = async (shopId, collectionId, data) => {
    const [_count, collectionAnalysis] = await CollectionAnalysis.update(data, {
      where: { shop_id: shopId, collection_id: collectionId },
      returning: true,
    });

    return collectionAnalysis[0]?.toJSON();
  };

  /**
   * Create collection analysis data
   * @param {number} shopId
   * @param {number} collectionId
   * @param {any} data
   * @returns {Promise<any>}
   */
  create = async (shopId, collectionId, data) => {
    const collectionAnalysis = await CollectionAnalysis.create({
      shop_id: shopId,
      collection_id: collectionId,
      ...data,
    });

    return collectionAnalysis.toJSON();
  };

  /**
   * Calculates collection analysis scores
   * @param shopId - shop id
   * @param collection - collection object from DB
   * @param oldCollection - old collection object from DB
   * @param uniqueCheckEnable - enable unique check
   * @returns {Promise<{score: (number|number), is_analysed: boolean, is_optimized: boolean, passed, analysis: {alt_text_in_all_img: (number|number), meta_title_within_char_limit: (number|number|number), focus_keyword_in_meta_title: (number|number), focus_keyword_in_title: (number|number), focus_keyword_in_img_alt_text: (number|number), focus_keyword_in_meta_desc: (number|number), unique_focus_keyword: (number|{defaultValue: number, type: SmallIntegerDataTypeConstructor}|*), meta_desc_within_char_limit: (number|number|number), focus_keyword_in_url: (number|{defaultValue: number, type: SmallIntegerDataTypeConstructor}|*), focus_keyword_at_the_beginning_of_meta_title: (number|number), optimized_image : (number)}, issues}>}
   */
  calculateScore = async ({ shopId, collection, oldCollection = null, uniqueCheckEnable = false }) => {
    let checkUniqueFK =
      uniqueCheckEnable || !!(oldCollection && collection.focus_keyword !== oldCollection?.focus_keyword);

    const analysis = {
      // Basic SEO
      unique_focus_keyword: checkUniqueFK
        ? await calculateUniqueFocusKeyword(shopId, collection.focus_keyword, collection?.id)
        : collection?.analysis?.unique_focus_keyword || SCORES.UNIQUE_FOCUS_KEYWORD,
      focus_keyword_in_meta_title: calculateFocusKeywordInMetaTitle(collection),
      meta_desc_within_char_limit: calculateMetaDescLength(collection, 80, 165),
      meta_title_within_char_limit: calculateMetaTitleLength(collection, 70),

      // Detailed SEO
      focus_keyword_in_title: calculateFocusKeywordInTitle(collection?.title, collection?.focus_keyword),
      focus_keyword_in_meta_desc: calculateFocusKeywordInMetaDesc(collection, collection?.focus_keyword),
      focus_keyword_in_url: calculateFocusKeywordInURL(collection?.handle, collection?.focus_keyword),
    };

    const analysisScore = calculateAnalysisScores(analysis);

    const score = Math.round((analysisScore.point * 100) / totalScore());
    return {
      analysis,
      score: score < 100 ? score : 100,
      issues: analysisScore.issues,
      passed: analysisScore.passed,
      is_analysed: true,
      is_optimized: score >= optimizePercent,
    };
  };

  /**
   * Delete single collection analysis of a shop
   * @param {number} shopId - shop id
   * @param {number} collectionId - collection id from DB
   * @param transaction
   * @returns {Promise<number>}
   */
  delete = async (shopId, collectionId, transaction = undefined) => {
    const where = { shop_id: shopId, collection_id: collectionId };
    return CollectionAnalysis.destroy({ where, transaction });
  };
}
module.exports = new CollectionAnalysisService();
