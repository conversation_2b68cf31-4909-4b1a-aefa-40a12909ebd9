// // @ts-check
const cache = require("../../cache");
const { CollectionProduct } = require("../../../sequelize");

class CollectionProductsService {
  /**
   * Get DB collection products count by shop id and conditions
   * @param {number} shopId - shop id
   * @param {number} collectionId - collection db id
   * @param {import("sequelize").WhereOptions} [conditions]
   * @returns {Promise<number>}
   */
  count = async (shopId, collectionId, conditions = {}) => {
    return CollectionProduct.count({ where: { shop_id: shopId, collection_id: collectionId, ...conditions } });
  };

  /**
   * Get last sync cursor for collection products by shop domain. It's used for pagination in sync process.
   * @param {*} shop - shop domain
   * @param {string?} keySuffix
   * @returns {Promise<string|null>}
   */
  getSyncCursor = async (shop, keySuffix = null) => cache.getCollectionProductsSyncCursor(shop, keySuffix);
  /**
   * Set last sync cursor for collection products by shop domain. It's used for pagination in sync process.
   * @param {*} shop - shop domain
   * @param {string?} keySuffix
   * @param {*} cursor - cursor value
   * @returns {Promise<string>}
   */
  setSyncCursor = async (shop, keySuffix = null, cursor) =>
    cache.setCollectionProductsSyncCursor(shop, keySuffix, cursor);

  /**
   * Delete last sync cursor for collection products by shop domain. It's used for pagination in sync process.
   * @param {*} shop - shop domain
   * @param {string?} keySuffix
   * @returns {Promise<number>}
   * */
  deleteSyncCursor = async (shop, keySuffix = null) => cache.removeCollectionProductsSyncCursor(shop, keySuffix);
  /**
   * Set sync ongoing status for collection products by shop domain.
   * @param {*} shop - shop domain
   * @param {*} status - status value (true/false)
   * @returns {Promise<boolean | null>} - status value
   */
  setSyncOngoingStatus = async (shop, status) => cache.setCollectionProductsSyncOngoingStatus(shop, status);
  /**
   * Get collection products by shop id and collection id
   * @param {{shopId : number, collectionId : number, conditions? : import("sequelize").WhereOptions, include: string[] | object[] | object}} param0
   * @returns {Promise<any[]>}
   */
  getByCollectionId = async ({ shopId, collectionId, conditions = {}, include = [] }) => {
    const result = await CollectionProduct.findAll({
      where: { shop_id: shopId, collection_id: collectionId, ...conditions },
      include,
    });
    return result.map((item) => item.toJSON());
  };
  /**
   * Inserts new or updates collection products to local DB
   * @param shopId - shop id
   * @param {number} productId - shopify product id from DB
   * @param collectionId - shopify collection id from DB
   * @param transaction - sequelize transaction
   * @returns {Promise<any>}
   */
  upsert = async (shopId, productId, collectionId, transaction = undefined) => {
    const collectionProducts = {
      shop_id: shopId,
      product_id: productId,
      collection_id: collectionId,
    };
    let [collectionproduct] = await CollectionProduct.upsert(collectionProducts, { transaction });
    return collectionproduct ? collectionproduct.toJSON() : null;
  };
  /**
   * Delete collection products from local DB by shop id, collection id and product id
   * @param {number} shopId
   * @param {number} collectionId
   * @param {import("sequelize").WhereOptions} [conditions]
   * @returns {Promise<number | boolean>}
   */
  delete = async (shopId, collectionId, conditions = {}) => {
    return await CollectionProduct.destroy({
      where: {
        shop_id: shopId,
        collection_id: collectionId,
        ...conditions,
      },
      force: true,
    });
  };

  /**
   * Delete collection products from local DB by shop id and collection id. It's used for deleting products that are not in the collection anymore.
   * @param {number} shopId
   * @param {number} collectionId - collection db id
   * @returns {Promise<number | boolean>}
   */

  destroyDeletedProducts = async (shopId, collectionId) => {
    // // get collection products from local db
    // const collectionLocalDBProducts = await this.getByCollectionId(shopId, collectionId, null, ["product"]);
    // // get shopify product graphql ids
    // const shopifyProductIds = shopifyProducts.map((p) => p.id);
    // // get local db product graphql ids
    // const localDBCollectionProductIds = collectionLocalDBProducts.map((p) => p?.product?.product_id);
    // // get products graphql ids to delete
    // const productsToDelete = localDBCollectionProductIds.filter((id) => !shopifyProductIds.includes(id));
    // // get products db ids to delete
    // const productsDbIdsToDelete = collectionLocalDBProducts
    //   .filter((p) => productsToDelete.includes(p?.product?.product_id))
    //   .map((p) => p.id);
    // console.info("productsDbIdsToDelete =", productsDbIdsToDelete);
    // delete products from local db
    // TODO: Find a way to delete products that are not in the collection anymore. Currently we are deleting all products of a collection and reinserting them.
    return await this.delete(shopId, collectionId);
  };
}

module.exports = new CollectionProductsService();
