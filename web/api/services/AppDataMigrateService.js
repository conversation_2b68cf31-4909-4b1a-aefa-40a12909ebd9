const { default: axios } = require("axios");
const { omit, truncate } = require("lodash");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const seoApps = require("storeseo-enums/seoApps");
const logger = require("storeseo-logger");
const { crawler } = require("../utils/helper");

const { SeoDataMigrationLog: logModel } = require("../../sequelize");
const { NAMESPACE, METAFIELD_KEYS, METAFIELD_TYPES } = require("storeseo-enums/metafields");

/**
 * @type {typeof import('sequelize').Model}
 */
const SeoDataMigrationLog = logModel;

class AppDataMigrateService {
  collectAndMergeOtherAppData = async (appName, shopifyProduct, shop, user) => {
    try {
      const {
        data: { jsonld, metafields },
      } = await crawler().get(`/seo/apps/${seoApps.metadata[appName].handle}`, {
        params: {
          url: encodeURIComponent(shopifyProduct.onlineStorePreviewUrl),
        },
      });

      await this.logCollectedAppData({
        appName,
        shop,
        metafields: metafields.allMetafields,
        jsonldScripts: jsonld.allJsonldScripts,
        pageUrl: shopifyProduct.onlineStorePreviewUrl,
        shopifyGraphQlId: shopifyProduct.id,
      });

      const shopifyMetaFields = shopifyProduct.metafields || {
        edges: shopifyProduct.meta.map((m) => ({ node: { ...omit(m, ["__parentId"]) } })),
      };
      const mergedMetafields = await this.mergeMetaFields({
        shopifyMetaFields,
        crawledMetafields: metafields,
        user,
        productGQLId: shopifyProduct.id,
      });

      let meta = [];
      if (shopifyProduct.meta) {
        meta = this.joinNewMetasWithExistingMetas(
          shopifyProduct.meta,
          mergedMetafields.edges.map((m) => ({ ...m.node }))
        );
      }

      return { ...shopifyProduct, metafields: mergedMetafields, meta };
    } catch (err) {
      logger.error(err, { domain: shop, message: "Error collecting and merging data from other app..." });
      return shopifyProduct;
    }
  };

  logCollectedAppData = async ({
    appName,
    shop,
    metafields,
    jsonldScripts,
    pageUrl,
    pageType = analysisEntityTypes.PRODUCT,
    shopifyGraphQlId = null,
  }) => {
    try {
      const logData = {
        shop,
        page_type: pageType,
        page_url: pageUrl,
        app_name: appName,
        shopify_graphql_id: shopifyGraphQlId,
        metafields: metafields,
        jsonld_scripts: jsonldScripts,
      };

      await SeoDataMigrationLog.create(logData);
      return true;
    } catch (err) {
      logger.error(err, { domain: shop, message: "Failed to create log in seo_data_migration_logs table...", appName });
    }
  };

  mergeMetaFields = async ({ shopifyMetaFields, crawledMetafields, user, productGQLId }) => {
    let foundMetaTitle = false;
    let foundMetaDesc = false;
    let metaFieldsToInsert = [];

    const edges = shopifyMetaFields.edges?.map((m) => {
      const metaData = { ...m.node };

      if (metaData.key == METAFIELD_KEYS.TITLE_TAG) {
        metaData.value = truncate(crawledMetafields?.title, { length: 70, omission: "" }) || metaData.value;
        console.log("\n---");
        console.log("crawled meta title: ", crawledMetafields.title);
        console.log("metaData.value (turncated): ", metaData.value);
        console.log("---\n");
        foundMetaTitle = true;
        metaFieldsToInsert.push(metaData);
      } else if (metaData.key == METAFIELD_KEYS.DESCRIPTION_TAG) {
        metaData.value =
          truncate(crawledMetafields?.description, { length: 165, omission: "" }) || metaData.description;
        console.log("\n---");
        console.log("crawled meta desc: ", crawledMetafields.description);
        console.log("metaData.value (turncated): ", metaData.value);
        console.log("---\n");
        metaFieldsToInsert.push(metaData);
        foundMetaDesc = true;
      }

      return { node: metaData };
    });

    if (!foundMetaTitle) {
      metaFieldsToInsert.push({
        namespace: NAMESPACE.GLOBAL,
        key: METAFIELD_KEYS.TITLE_TAG,
        value: crawledMetafields?.title || "",
        type: METAFIELD_TYPES.STRING,
      });
    }

    if (!foundMetaDesc) {
      metaFieldsToInsert.push({
        namespace: NAMESPACE.GLOBAL,
        key: METAFIELD_KEYS.DESCRIPTION_TAG,
        value: crawledMetafields?.description || "",
        type: METAFIELD_TYPES.STRING,
      });
    }

    if (metaFieldsToInsert.length > 0) {
      const ShopifyService = require("./ShopifyService");
      await ShopifyService.updateProductMetaFields(user.shop, {
        productId: productGQLId,
        metaFieldDefinitions: metaFieldsToInsert,
      });
      const updatedProduct = await ShopifyService.getProductFromShopify(user.shop, productGQLId);
      return updatedProduct.metafields;
    }

    return { edges };
  };

  collectAndMergeHomepageData = async (appName, shop, homepageUrl) => {
    try {
      const {
        data: { jsonld, metafields },
      } = await crawler().get(`/seo/apps/${seoApps[appName].handle}`, {
        params: {
          url: encodeURIComponent(homepageUrl),
        },
      });

      await this.logCollectedAppData({
        appName,
        shop,
        metafields: metafields.allMetafields,
        jsonldScripts: jsonld.allJsonldScripts,
        pageType: analysisEntityTypes.HOMEPAGE,
        pageUrl: homepageUrl,
      });

      let localSeoJson = jsonld?.apps?.[appName]?.localSeo;
      if (localSeoJson) {
        const ShopService = require("./ShopService");
        const { id } = await ShopService.getShop(shop, ["id"]);
        await ShopService.upsertJsonldSetting(id, localSeoJson);
      }
    } catch (err) {
      logger.error(err, { domain: shop, message: "Error while migrating homepage seo data from app...", appName });
    }
  };

  joinNewMetasWithExistingMetas = (existingMetas, newMetas) => {
    const allMetas = [...existingMetas, ...newMetas];

    const metaMap = {};
    for (let meta of allMetas) {
      const key = `${meta.key}-${meta.namespace}`;
      metaMap[key] = metaMap[key] ? { ...metaMap[key], ...meta } : meta;
    }

    return Object.values(metaMap);
  };
}

module.exports = new AppDataMigrateService();
