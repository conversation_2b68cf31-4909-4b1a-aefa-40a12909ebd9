// @ts-check
const { ArticleImage, Op } = require("../../sequelize");
const { formatArticleImages, formatArticleImage } = require("../serializers/ArticleSerializer");
const { preparePagination } = require("../utils/helper");

class ArticleImageService {
  /**
   * Get all images by shop
   * @param shopId - DB shop id
   * @param {object} options - pagination and filters
   * @returns {Promise<any>}
   */
  getImagesByShop = async (
    shopId,
    {
      page = 1,
      limit = 20,
      search = "",
      status = "",
      // alt_status = "",
      fileSize,
      has_alt_text,
      sortBy = "created_at",
      sortOrder = "DESC",
    }
  ) => {
    const fileSizeInBytes = fileSize
      ? fileSize.split(",").map((size) => {
          const byte = size ? (Number(size) * 1024 * 1024).toString() : "";
          return parseInt(byte);
        })
      : [];

    const where = {
      shop_id: shopId,
    };

    // Convert the following logic to Sequelize where clause
    if (fileSizeInBytes.length > 0) {
      if (fileSizeInBytes[0] > 0 && fileSizeInBytes[1] > 0) {
        where.file_size = {
          [Op.gte]: fileSizeInBytes[0],
          [Op.lte]: fileSizeInBytes[1],
        };
      } else if (fileSizeInBytes[0] > 0) {
        where.file_size = { [Op.gte]: fileSizeInBytes[0] };
      } else if (fileSizeInBytes[1] > 0) {
        where.file_size = { [Op.lte]: fileSizeInBytes[1] };
      }
    }

    if (search) {
      where[Op.or] = [{ src: { [Op.like]: `%${search}%` } }, { "$article.title$": { [Op.like]: `%${search}%` } }];
    }

    if (status) {
      where.optimization_status = {
        [Op.in]: status.split(","),
      };
    }

    if (has_alt_text !== undefined) {
      where.has_alt_text = has_alt_text;
    }

    const imagesPromise = ArticleImage.findAndCountAll({
      where,
      limit,
      offset: (page - 1) * limit,
      order: [[sortBy, sortOrder]],
      include: ["article", "resourceOptimizationMeta"],
    });

    const resourceCountPromise = ArticleImage.count({
      where: {
        shop_id: shopId,
      },
    });

    const [images, resourceCount] = await Promise.all([imagesPromise, resourceCountPromise]);

    const { count, rows } = images;

    const formattedImages = rows.map((item) => item.toJSON());

    return {
      images: formatArticleImages(formattedImages),
      pagination: preparePagination(count, page, limit),
      totalCount: resourceCount,
    };
  };

  /**
   * Get collection image by gql id
   * @param {number} id
   * @param {Array<string>} include
   * @returns
   */
  getById = async (id, include = ["article"]) => {
    return await ArticleImage.findOne({
      where: { id },
      include,
    });
  };

  /**
   * Save or update shopify article image to database
   * @param {object} imageData - image data
   * @returns {Promise<any>}
   */
  upsert = async (imageData) => {
    const [updateArticleImage] = await ArticleImage.upsert(imageData, {
      conflictFields: ["shop_id", "article_id"],
    });

    const articleImage = await this.getById(imageData.id);

    return formatArticleImage(articleImage?.toJSON() || null);
  };

  /**
   * Deletes collection images by image id
   * @param {number} id - image DB id
   * @param {number} shopId - shop id
   * @param {number} articleId - article id from DB
   * @param transaction
   * @returns {Promise<number>}
   */
  deleteById = async (id, shopId, articleId, transaction = undefined) => {
    const where = { id, shop_id: shopId, article_id: articleId };
    return await ArticleImage.destroy({ where, transaction });
  };

  /**
   * Deletes article images by article id
   * @param {number} articleId - article id from DB
   * @param {number} shopId - shop id
   * @param transaction
   * @returns {Promise<number>}
   */
  deleteByArticleId = async (articleId, shopId, transaction = undefined) => {
    const where = { article_id: articleId, shop_id: shopId };
    return await ArticleImage.destroy({ where, transaction });
  };
}

module.exports = new ArticleImageService();
