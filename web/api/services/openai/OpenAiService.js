require("dotenv").config();
const { OpenAI } = require("openai");
const OpenAiModels = require("storeseo-enums/opanai/Models");

class OpenAiService {
  #openai;
  #defaultModel = OpenAiModels.GPT_4_1_NANO;

  constructor() {
    this.#openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
  }

  extractKeywords = async ({ title, description, resourceType = "PRODUCT", model = this.#defaultModel }) => {
    const response = await this.#openai.chat.completions.create({
      model,
      response_format: { type: "json_object" },
      messages: [
        {
          role: "system",
          content: [
            `You are tool to extract keywords from ${resourceType.toLocaleLowerCase()} title, description.`,
            `Keep the brand name, product name, and other important information in the keywords.`,
            `Extract the keywords from the following ${resourceType.toLocaleLowerCase()} title and description.`,
            `Output the best seven matching keywords with more than two words which are suitable for search engines in json format.`,
            `Only give keywords that are available as sequence of consecutive words in the title or description.`,
            `Give more priority to the words available in the title.`,
            `The keywords should have less competition in major search engines.`,
            `Keep the special characters and punctuations in the keywords.`,
            `Sort them in the order of their appearance in the title.`,
            `Keep them under 3-4 words, max 5 words.`,
          ].join(" "),
        },
        {
          role: "user",
          content: `Title: ${title}\nDescription: ${description}`,
        },
      ],
    });

    return {
      keywords: JSON.parse(response.choices[0].message.content)?.keywords || [],
      usage: response.usage,
    };
  };

  defineIndustry = async ({ url, model = this.#defaultModel }) => {
    const response = await this.#openai.chat.completions.create({
      model,
      response_format: { type: "json_object" },
      temperature: 1,
      max_completion_tokens: 100,
      top_p: 1,
      frequency_penalty: 0,
      presence_penalty: 0,
      messages: [
        {
          role: "system",
          content: [
            {
              type: "text",
              text: 'Define the industry of an e-commerce website by analyzing its URL. You need to websearch the URL and produce the top 3 industries. Do not include e-commerce or any generic industry.\n\n# Output Format\nThe output should be a JSON with the best 3 industry results sorted in descending relevance order.\n\n# Examples\n**Input:**\nURL: \'https://www.techgadgetshop.com\'\n\n**Output:**\n{\n"industries": [\n  "Electronics",\n  "Consumer Goods",\n  "Retail"\n]\n}\n\n            **Input:**\n            URL: \'https://www.bedlinenandmore.com\'\n\n            **Output:**\n{\n"industries": [\n"Homegoods",\n              "Textiles",\n              "Retail"\n]\n}',
            },
          ],
        },
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `URL: ${url}`,
            },
          ],
        },
      ],
    });

    return {
      industries: JSON.parse(response.choices[0].message.content)?.industries || [],
      usage: response.usage,
    };
  };

  /**
   * Generate SEO friendly 'Meta Title' and 'Meta Description' for Shopify Collection
   * @param {{title: string, description: string, focusKeyword: string, language?: string}} data
   * @example
   * const { output, usage } = await generateCollectionContent({
   *   title: "Summer Collection",
   *   description: "A collection of summer clothes and accessories.",
   *   focusKeyword: "summer clothes"
   * })
   */
  generateCollectionContent = async (data) => {
    const { title, description, focusKeyword, language } = data;

    const response = await this.#openai.chat.completions.create({
      model: this.#defaultModel,
      response_format: { type: "json_object" },
      messages: [
        {
          role: "system",
          content: [
            {
              type: "text",
              text: `You are a tool that generates SEO friendly 'Meta Title' (meta_title) and 'Meta Description' (meta_description) ${language && `generate all of them are in completely ${language} (language)`} for a Shopify e-commerce website. They MUST follow these SEO Guidelines: 
              For 'meta_title', follow these rules: 
              1. Focus Keyword MUST be in the title. 
              2. Length MUST be between 60 to 70 characters. 
              For 'meta_description', follow these rules: 
              1. Length MUST be between 150 to 160 characters. 
              2. Focus Keyword MUST be in the description. 
              Output MUST be a JSON like this: 
              {
                "meta_title": "Generated meta title", 
                "meta_description": "Generated meta description"
              }`,
            },
          ],
        },
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `Generate the JSON for the following Shopify Collection based on the provided details and rules MUST be followed: 
              Collection Title: ${title} 
              Collection Description: ${description} 
              Focus Keyword: ${focusKeyword}`,
            },
          ],
        },
      ],
    });

    return {
      output: JSON.parse(response.choices[0].message.content),
      usage: {
        completion_tokens: response.usage.completion_tokens,
        prompt_tokens: response.usage.prompt_tokens,
        total_tokens: response.usage.total_tokens,
      },
    };
  };
}

module.exports = new OpenAiService();
