// const Mailchimp = require("mailchimp-api-v3");
const mailchimp = require("@mailchimp/mailchimp_marketing");
const { INACTIVE, ACTIVE } = require("storeseo-enums/mailchimp/contactStatus");
const { isEmpty, pick } = require("lodash");
const ShopService = require("../services/ShopService");
const SubscriptionPlanService = require("../services/SubscriptionPlanService");
const {
  mailchimp: { apiKey, serverPrefix, listId },
} = require("../config/app");

class MailchimpService {
  #listId;

  constructor() {
    this.#listId = listId;
    mailchimp.setConfig({
      apiKey: apiKey,
      server: serverPrefix,
    });
  }

  getList = async () => {
    try {
      return await mailchimp.lists.getList(this.#listId);
    } catch (err) {
      // console.log(err.toString());
    }
  };

  getMember = async (email) => {
    try {
      return await mailchimp.lists.getListMember(this.#listId, email);
    } catch (err) {
      // console.log(err.toString());
    }
  };

  /**
   * @param {string} domain
   * @returns {Promise<*>}
   */
  addMember = async (domain) => {
    const { name, email, url } = await ShopService.getShop(domain, ["id", "name", "email", "url", "domain"]);
    const data = {
      email_address: email,
      status: "subscribed",
      status_if_new: "subscribed",
      merge_fields: {
        SHOP_NAME: name,
        SHOP_URL: url,
        DOMAIN: domain,
      },
    };

    return await mailchimp.lists.setListMember(this.#listId, email, data);
  };

  /**
   * @param {string} email
   * @param {Array} tags
   * @param {Array} tagsToRemove
   */
  updateMemberTags = async (email, tags, tagsToRemove = []) => {
    let tagsToUpdate = tagsToRemove?.map((t) => ({ name: t.toUpperCase(), status: INACTIVE })) || [];

    tags = tags?.map((t) => ({ name: t.toUpperCase(), status: ACTIVE })) || [];

    tagsToUpdate = tagsToUpdate.concat(tags);

    if (tagsToUpdate.length === 0) {
      return;
    }

    return mailchimp.lists.updateListMemberTags(this.#listId, email, {
      tags: tagsToUpdate,
    });
  };

  getMemberTags = async (email) => {
    try {
      const response = await mailchimp.lists.getListMemberTags(this.#listId, email);
      return response.tags;
    } catch (err) {
      // console.log(err.response.body);
    }
  };

  addMergeFields = async () => {
    try {
      const fields = ["Shop Name", "Shop URL", "Domain"];
      const { merge_fields } = await this.getMergeFields();

      return await Promise.all(
        fields.map(async (field) => {
          const tag = field.replace(" ", "_").toUpperCase();
          const isExist = merge_fields.some((mf) => mf.tag === tag);
          if (!isExist) {
            const fieldData = await mailchimp.lists.addListMergeField(this.#listId, {
              name: field,
              tag,
              type: "text",
            });

            console.log(fieldData.name, fieldData.tag, "Created");
          } else {
            console.log(tag, "Already exist.");
          }
          return field;
        })
      );
    } catch (err) {
      // console.log(err.message);
    }
  };

  deleteMergeFields = async () => {
    try {
      const ids = [5, 6];
      return await Promise.all(
        ids.map(async (id) => {
          return await mailchimp.lists.deleteListMergeField(this.#listId, id);
        })
      );
    } catch (err) {
      // console.log(err.message);
    }
  };

  getMergeFields = async () => {
    try {
      return await mailchimp.lists.getListMergeFields(this.#listId);
    } catch (err) {
      // console.log(err.message);
    }
  };

  addMemberToMailchimpAudience = async () => {
    try {
      const shops = await ShopService.getAllShop();
      if (shops.length > 0) {
        const members = shops.map((shop) => {
          return {
            email_address: shop.email,
            status: "subscribed",
            status_if_new: "subscribed",
            merge_fields: {
              SHOP_NAME: shop.name,
              SHOP_URL: shop.url,
              SHOP_DOMAIN: shop.domain,
            },
          };
        });

        const addData = await mailchimp.lists.batchListMembers(this.#listId, {
          members,
          update_existing: true,
        });

        console.log(`Total ${addData.total_created} members created, ${addData.total_updated} updated`);
      }
      return null;
    } catch (err) {
      // console.log(err.response.body);
    }
  };

  addTagToMembers = async () => {
    try {
      const { segments } = await mailchimp.lists.listSegments(this.#listId);
      const plans = await SubscriptionPlanService.getSubscriptionPlans();
      return await Promise.all(
        plans.map(async (plan) => {
          const shops = await ShopService.getAllShop(plan.id);
          if (shops.length > 0) {
            const members = shops.map((shop) => {
              return shop.email;
            });
            const segment = segments.find((seg) => seg.name === plan.name.toUpperCase());

            console.log(`Adding Tag: ${segment.name} to ${members.length} Members.`);
            if (members.length > 0) {
              try {
                const addData = await mailchimp.lists.batchSegmentMembers(
                  {
                    members_to_add: members,
                  },
                  this.#listId,
                  segment.id
                );
                console.log(`Tag: ${segment.name} added to ${addData.total_added} Members.`);
              } catch (err) {
                console.log(segment.name, err.response.body);
              }
            }
          }
        })
      );

      // return null;
    } catch (err) {
      // console.log(err.response.body);
    }
  };

  // deleteAllMemberPermanent = async () => {
  //   const { members } = await mailchimp.lists.getListMembersInfo(this.#listId);
  //
  //   if (members.length > 0) {
  //     return await Promise.all(members.map(async (member) => {
  //       return await mailchimp.lists.deleteListMemberPermanent(this.#listId, member.email_address);
  //     }));
  //   }
  //
  //   return null;
  // };

  addPlansAsSegments = async () => {
    try {
      const plans = await SubscriptionPlanService.getSubscriptionPlans();

      return await Promise.all(
        plans.map(async (plan) => {
          const segment = await mailchimp.lists.createSegment(this.#listId, {
            name: plan.name.toUpperCase(),
            static_segment: [],
          });

          console.log(`${segment.name} created.`);
          return segment;
        })
      );
    } catch (e) {
      // console.log(e.response.body);
    }
  };

  viewMemberWithTags = async () => {
    const { members } = await mailchimp.lists.getListMembersInfo(this.#listId, {
      count: 300,
      sort_field: "timestamp_opt",
      sort_dir: "DESC",
    });

    if (members.length > 0) {
      members.forEach((member) => {
        console.log(
          member.timestamp_opt,
          `Email: ${member.email_address} -- Shop: ${member.merge_fields.SHOP_URL} -- Tags: ${member.tags
            ?.map((t) => t.name)
            ?.join(",")}`
        );
      });
    }
  };

  updateShopDataToDb = async (domain, email) => {
    // get the member's updated details
    let member = await this.getMember(email);

    member = pick(member, ["id", "email_address", "status", "ip_opt", "tags"]);

    return await ShopService.updateShopByCondition(
      {
        domain,
        email,
      },
      {
        mailchimp: member,
      }
    );
  };
}

module.exports = new MailchimpService();
