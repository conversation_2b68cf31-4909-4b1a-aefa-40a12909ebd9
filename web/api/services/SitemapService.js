const { Sitemap } = require("../../sequelize");
const logger = require("storeseo-logger");
const { Op } = require("sequelize");
const { NAMESPACE, METAFIELD_KEYS } = require("storeseo-enums/metafields");

class SitemapService {
  /**
   * Get product sitemap details
   * @param shopId
   * @param productId
   * @returns {Promise<null|any>}
   */
  // getProductSitemapDetails = async (shopId, productId) => {
  //   try {
  //     const result = await Sitemap.findOne({ where: { shop_id: shopId, product_id: productId }, rejectOnEmpty: true });
  //     return result.toJSON();
  //   } catch (err) {
  //     return null;
  //   }
  // };

  /**
   * Store sitemap data for all resources (products, collections, pages, blogs)
   * @param resource
   * @param resource_type
   * @param transaction
   * @returns {Promise<null|any>}
   */
  storeSitemapData = async (resource, resource_type, transaction = undefined) => {
    const { shop_id, id: resource_id, meta } = resource;
    const metaSettings = this.#prepareSeoMetaSettingsData(meta);

    if (!shop_id || !resource_id) {
      return null;
    }

    const data = {
      shop_id,
      resource_id,
      resource_type,
      ...metaSettings,
    };

    const [result] = await Sitemap.upsert(data, { transaction });
    return result.toJSON();
  };

  /**
   * Update sitemap data for a resource (product, collection, page, blog)
   * @param shopId
   * @param resourceId
   * @param resourceType
   * @param metaKey
   * @param metaStatus
   * @returns {Promise<null|*>}
   */
  updateSitemapData = async (
    { shopId, resourceId, resourceType, metaKey = "sitemap_disabled", metaStatus = 0 },
    transaction = undefined
  ) => {
    try {
      const [count, sitemaps] = await Sitemap.update(
        { [metaKey]: Number(metaStatus) },
        {
          where: {
            shop_id: shopId,
            resource_id: resourceId,
            resource_type: resourceType,
          },
          returning: true,
        },
        transaction
      );
      return sitemaps[0].toJSON();
    } catch (err) {
      logger.error(
        `Error updating SEO meta setting in DB for resourceId ${resourceId} resourceType ${resourceType} shopId ${shopId} metaKey ${metaKey}. Error: ${err}`
      );
      return null;
    }
  };
  /**
   * Delete sitemap data for a resource (product, collection, page, blog)
   * @param shopId
   * @param resourceId
   * @param resourceType
   * @param transaction
   * @returns
   */
  deleteSitemaps = async (shopId, resourceId, resourceType, transaction = undefined) => {
    try {
      return await Sitemap.destroy({
        where: { shop_id: shopId, resource_id: resourceId, resource_type: resourceType },
        transaction,
      });
    } catch (err) {
      return null;
    }
  };
  /**
   * Delete all sitemaps for a resource type (product, collection, page, blog)
   * @param shopId
   * @param resourceType
   * @param transaction
   * @returns
   */
  deleteAllSitemaps = async (shopId, resourceType, transaction = undefined) => {
    try {
      return await Sitemap.destroy({
        where: { shop_id: shopId, resource_type: resourceType },
        transaction,
      });
    } catch (err) {
      return null;
    }
  };
  /**
   * Delete sitemaps greater than the resource id
   * @param shopId
   * @param resourceId
   * @param resourceType
   * @param transaction
   * @returns {Promise<null|number>}
   */
  deleteSitemapsGreaterThenResourceId = async (shopId, resourceId, resourceType, transaction = undefined) => {
    try {
      return await Sitemap.destroy({
        where: { shop_id: shopId, resource_id: { [Op.gte]: resourceId, resource_type: resourceType } },
        transaction,
      });
    } catch (err) {
      return null;
    }
  };

  #prepareSeoMetaSettingsData = (meta) => {
    const metaSettings = {
      sitemap_disabled: 0,
      no_index: 0,
      no_follow: 0,
    };

    meta
      ?.filter((m) => m.namespace === NAMESPACE.SEO || m.namespace === NAMESPACE.STORE_SEO)
      .filter(
        (m) =>
          ![
            METAFIELD_KEYS.FACEBOOK_PREVIEW_IMAGE_URL,
            METAFIELD_KEYS.TWITTER_PREVIEW_IMAGE_URL,
            METAFIELD_KEYS.CANNONICAL_URL,
          ].includes(m.key)
      )
      .forEach((m) => {
        if (m.key === METAFIELD_KEYS.HIDDEN) metaSettings.sitemap_disabled = Number(m.value);
        else metaSettings[m.key] = Number(m.value);
      });

    return metaSettings;
  };
}

module.exports = new SitemapService();
