const { Location: LocationModel } = require("../../sequelize");
const logger = require("storeseo-logger");
const ShopifyService = require("./ShopifyService");
const googleApiService = require("./GoogleApiService");
const { serializeShopifyLocationData } = require("../serializers/LocationSerializer");

/**
 * @typedef {import("../../jsDocTypes").LocationDetails} LocationDetails
 * @typedef {import("../../jsDocTypes").User} User
 */

/**
 * @type {typeof import('sequelize').Model}
 */
const Location = LocationModel;

class LocationService {
  /**
   * Save new location in DB
   * @param {LocationDetails} data
   * @returns {Promise<LocationDetails | null>} created location details from the DB | null
   */
  saveLocation = async (data) => {
    try {
      const savedLocation = await Location.create(data);
      return savedLocation.toJSON();
    } catch (err) {
      console.error(`Error saving location into database. Data: ${JSON.stringify(data, null, 2)}. Error: ${err}`);
      console.log(err);
      return null;
    }
  };

  /**
   * Update location in DB using the DB id
   * @param {(integer | string)} id
   * @param {LocationDetails} data
   * @returns {Promise<LocationDetails | null>} updated location details from the DB | null
   */
  updateLocation = async (id, data) => {
    try {
      const [affectedRows, locations] = await Location.update(data, { where: { id }, returning: true });
      if (affectedRows === 1) return locations[0]?.toJSON();

      return locations.map((l) => l.toJSON());
    } catch (err) {
      console.error(
        `Error updating location with id: ${id} in database. Data: ${JSON.stringify(data, null, 2)}. Error: ${err}`
      );
      return null;
    }
  };

  /**
   * Update location in DB using the shopify location id
   * @param {string} id
   * @param {LocationDetails} data
   * @returns {Promise<LocationDetails | null>} updated location details from the DB | null
   */
  updateLocationByShopifyId = async (id, data) => {
    try {
      const [affectedRows, locations] = await Location.update(data, { where: { location_id: id }, returning: true });
      if (affectedRows === 1) return locations[0]?.toJSON();

      return locations.map((l) => l.toJSON());
    } catch (err) {
      console.error(
        `Error updating location with shopify location id: ${id} in database. Data: ${JSON.stringify(
          data,
          null,
          2
        )}. Error: ${err}`
      );
      return null;
    }
  };

  /**
   * Delete location from DB using shopify location id
   * @param {string} id
   * @returns {Promise<boolean>}
   */
  deleteLocationByShopifyId = async (id) => {
    try {
      await Location.destroy({ where: { location_id: String(id) } });
      return true;
    } catch (err) {
      console.error(`Error deleting location from DB using shopify id: ${id}. Error: ${err}`);
      return false;
    }
  };

  /**
   * Get list of all locations of a shop
   * @param {(integer | string)} shopId
   * @returns {Promise<[LocationDetails]>} List of locations from DB
   */
  getAllLocations = async (shopId) => {
    try {
      const locations = await Location.findAll({ where: { shop_id: shopId } });
      return locations.map((l) => l.toJSON());
    } catch (err) {
      return [];
    }
  };

  /**
   * Get list of active locations of a shop
   * @param {(integer | string)} shopId
   * @returns {Promise<[LocationDetails]>} List of locations from DB
   */
  getAllActiveLocations = async (shopId) => {
    try {
      const locations = await Location.findAll({ where: { shop_id: shopId, active: true } });
      return locations.map((l) => l.toJSON());
    } catch (err) {
      return [];
    }
  };

  /**
   * Get location from DB using the shop id & shopify location id
   * @param {(integer | string)} shopId
   * @param {string} locationId
   * @returns {Promise<LocationDetails | null>} Location details from the DB | null
   */
  getLocationByLocationId = async (shopId, locationId) => {
    try {
      const location = await Location.findOne({
        where: {
          shop_id: shopId,
          location_id: locationId,
        },
      });
      return location.toJSON();
    } catch (err) {
      return null;
    }
  };

  /**
   * Update existing location or create a new one in the DB
   * @param {LocationDetails} data
   * @returns {Promise.<LocationDetails>} the created/updated locaiton in the DB
   */
  upsertLocation = async (data) => {
    const [location] = await Location.upsert(data, { returning: true });
    return location.toJSON();
  };

  /**
   * Fetch all locations for the relevant shop from Shopify
   * and save/update them in the DB
   * @param {User} user User object to perform auth for Shopify API
   */
  saveOrUpdateAllLocationsFromShopify = async (user) => {
    const locations = await ShopifyService.getShopifyStoreLocations(user);

    for (let location of locations) {
      const data = await serializeShopifyLocationData(location, user.shopId);
      await this.upsertLocation(data);
    }
  };

  /**
   * Fetch all locations for the relevant shop from Shopify
   * and save/update them in the DB
   * @param {User} user User object to perform auth for Shopify API
   */
  saveAllNewLocationsFromShopify = async (user) => {
    try {
      const locations = await ShopifyService.getShopifyStoreLocations(user);

      for (let location of locations) {
        const data = await serializeShopifyLocationData(location, user.shopId);
        await this.upsertLocation(data);
      }
    } catch (err) {
      console.error(
        `Error reading & storing new locations from shopify for user: ${JSON.stringify(user, null, 2)}. Error: ${err}`
      );
    }
  };

  /**
   *
   * @param {(number | string)} shopId relevant shop id of the location in DB
   * @returns {Promise.<boolean>} true indicates delete successful
   */
  deleteLocationsOfShop = async (shopId) => {
    try {
      await Location.destroy({ where: { shop_id: shopId } });
      return true;
    } catch (e) {
      console.log(e);
    }
  };
}

module.exports = new LocationService();
