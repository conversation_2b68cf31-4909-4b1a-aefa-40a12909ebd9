const { Coupon, CouponApplied, Op } = require("../../sequelize");
const { isEmpty } = require("lodash");
const { pick } = require("lodash/object");
const couponStatus = require("storeseo-enums/couponStatus");

class CouponService {
  /**
   * Get coupon details by unique coupon code
   * @param code
   * @param reject
   * @returns {Promise<*>}
   */
  getCouponDetail = async (code, reject = false) => {
    try {
      const coupon = await Coupon.findOne({
        where: { code: { [Op.iLike]: code }, status: couponStatus.ACTIVE },
        rejectOnEmpty: reject,
      });
      return coupon?.toJSON();
    } catch (err) {
      return null;
    }
  };

  getAnyCouponDetails = async (code) => {
    const coupon = await Coupon.findOne({
      where: { code: { [Op.iLike]: code } },
      rejectOnEmpty: false,
    });
    return coupon?.toJSON() || null;
  };

  /**
   * Get applied coupon by shop id, subscription id & coupon code
   * @param shopId
   * @param appSubscriptionId
   * @param couponCode
   * @param reject
   * @returns {Promise<null|*>}
   */
  getCouponApplied = async ({ shopId, appSubscriptionId, couponCode, reject = false }) => {
    try {
      const where = {
        shop_id: shopId,
        appSubscriptionId: !isEmpty(appSubscriptionId) ? appSubscriptionId : null,
        code: couponCode,
      };
      const query = await CouponApplied.findOne({ where, order: [["created_at", "desc"]], rejectOnEmpty: reject });
      return query?.toJSON();
    } catch (err) {
      return null;
    }
  };

  /**
   * Get last applied coupon code by shop id & subscription id
   * @param shopId
   * @param appSubscriptionId
   * @param reject
   * @returns {Promise<null|*>}
   */
  getLastAppliedCoupon = async ({ shopId, appSubscriptionId, reject = false }) => {
    try {
      const where = {
        shop_id: shopId,
        // appSubscriptionId: !isEmpty(appSubscriptionId) ? appSubscriptionId : null,
      };
      const query = await CouponApplied.findOne({ where, order: [["created_at", "desc"]], rejectOnEmpty: reject });
      return query?.toJSON();
    } catch {
      return null;
    }
  };

  /**
   *
   * @param shopId
   * @param appSubscriptionId
   * @param couponCode
   * @returns {Promise<*|null>}
   */
  saveCouponApplied = async ({ shopId, appSubscriptionId, couponCode }) => {
    const couponApplied = await this.getCouponApplied({ shopId, appSubscriptionId, couponCode });
    if (isEmpty(couponApplied)) {
      const coupon = await this.getCouponDetail(couponCode);
      const data = {
        shop_id: shopId,
        appSubscriptionId,
        coupon_id: coupon.id,
        ...pick(coupon, ["name", "code", "discount_type", "amount"]),
      };

      const res = await CouponApplied.create(data);
      return res.toJSON();
    } else {
      return couponApplied;
    }
  };
}

module.exports = new CouponService();
