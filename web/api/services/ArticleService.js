const logger = require("storeseo-logger");
const ShopifyService = require("./ShopifyService");
const { dispatchQueue } = require("../queue/queueDispatcher");
const { QUEUE_NAMES } = require("../queue/config");
const {
  updateOrInsertMetaInfoInArray,
  extractFocusKeyword,
  calculateIsOptimized,
  getPercentValue,
  replaceSpecialChars,
  preparePagination,
} = require("../utils/helper");
const SCORES = require("../config/articleSeoScores");
const { isEmpty, pick } = require("lodash");
const {
  Article: ArticleModel,
  ArticleImage,
  ArticleMeta,
  ArticleAnalysis,
  Blog,
  Op,
  sequelize,
  Sitemap,
} = require("../../sequelize");
const { serializArticleMetas } = require("../serializers/ArticleSerializer");
const ArticleMetaService = require("./ArticleMetaService");
const { QueryTypes } = require("sequelize");
const { NAMESPACE, METAFIELD_KEYS, METAFIELD_TYPES, metafieldKeysFilterArray } = require("storeseo-enums/metafields");
const SitemapService = require("./SitemapService");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const { findSitemapFilter } = require("../utils/sitemapUtils");
const { serializeArticleSitemap } = require("../serializers/ArticleSerializer");
const AnalysisService = require("./AnalysisService");
const resourceType = require("storeseo-enums/resourceType");
const ArticleSerializer = require("../serializers/ArticleSerializer");
const ArticleImageService = require("./ArticleImageService");

/**
 * @typedef {import('../../jsDocTypes').ArticleDetails} ArticleDetails
 * @typedef {import("../../jsDocTypes").Pagination} Pagination
 * @typedef {import("../../jsDocTypes").Metafield} Metafield
 * @typedef {import("../../jsDocTypes").User} User
 */

/**
 * @type {typeof import('sequelize').Model}
 */
const Article = ArticleModel;

class ArticleService {
  /**
   * Insert new article in DB
   * @param {ArticleDetails} data article details object
   * @returns {Promise<ArticleDetails>} saved article from DB
   */
  saveArticle = async (data) => {
    try {
      const savedArticle = await Article.create({ ...data, focus_keyword: extractFocusKeyword(data.title) });
      return savedArticle.toJSON();
    } catch (err) {
      console.error(`Error saving article into database. Data: ${JSON.stringify(data, null, 2)}. Error: ${err}`);
      return null;
    }
  };

  /**
   * Update an existing article in DB
   * @param {number} id database id of the article
   * @param {ArticleDetails} data updated article data object
   * @returns {Promise<ArticleDetails>} updated article from DB
   */
  updateArticle = async (id, data) => {
    try {
      const [affectedRows, articles] = await Article.update(data, { returning: true, where: { id } });
      if (affectedRows === 1) return articles[0]?.toJSON();

      return articles.map((a) => a.toJSON());
    } catch (err) {
      console.error(
        `Error updating article with id: ${id} in database. Data: ${JSON.stringify(data, null, 2)}. Error: ${err}`
      );
      return null;
    }
  };

  /**
   *
   * @param {number} shopId
   * @param {number} blogId
   * @param {number} articleId
   * @param {*} image
   */
  upsertImage = async (shopId, blogId, articleId, image) => {
    const data = {
      shop_id: shopId,
      blog_id: blogId,
      article_id: articleId,
      ...image,
    };

    const [img] = await ArticleImage.upsert(data, { returning: true });
    return img.toJSON();
  };

  /**
   * Update article(s) data by condition
   * @param conditions
   * @param data
   * @returns {Promise<*>}
   */
  updateArticleByCondition = async (conditions, data) => {
    const [affectedRows, articles] = await Article.update(data, {
      where: conditions,
      returning: true,
    });

    if (affectedRows === 1) {
      return articles[0].toJSON();
    }

    return articles.map((p) => p.toJSON());
  };

  /**
   * Count number of blog articles saved in DB by shop id
   * @param {number | string} id DB id of the shop
   * @returns {Promise<number>}
   */
  count = async (id) => {
    return Article.count({
      where: { shop_id: id, is_deleted: false },
    });
  };

  /**
   * Set 'is_deleted' flag 'true' to mark the articles as temporarily deleted
   * @param {number} shopId database id of the shop
   * @returns
   */
  temproarilyMarkAllArticlesAsDeleted = async (shopId) => {
    try {
      await Article.update({ is_deleted: true }, { where: { shop_id: shopId } });
    } catch (err) {
      console.log(err);
      return null;
    }
  };

  serializeArticleImg = (image) => {
    if (!image) return null;

    return {
      alt: image.alt_text,
      src: image.src,
    };
  };

  /**
   * Retrieve article from DB by id & shop id
   * @param {number} shopId
   * @param {number} id database id of the article
   * @returns {Promise<ArticleDetails>} article from DB
   */
  getArticle = async (shopId, id) => {
    try {
      const a = await Article.findOne({ where: { id, shop_id: shopId }, include: ["analysisData", "meta", "img"] });
      const article = a.toJSON();

      return { ...article, meta: serializArticleMetas(article.meta), image: this.serializeArticleImg(article.img) };
    } catch (err) {
      // console.error(`Error retreiving article by id. Article id: ${id}, shop id: ${shopId}. Error: ${err}`);
      return null;
    }
  };

  /**
   * Retrieve article from DB by id & shopify id
   * @param {number} shopId
   * @param {number} id database id of the article
   * @returns {Promise<ArticleDetails>} article from DB
   */
  getArticleByShopifyId = async (shopId, shopifyId) => {
    try {
      const a = await Article.findOne({
        where: { article_id: shopifyId, shop_id: shopId },
        include: [
          "meta",
          "img",
          {
            model: ArticleAnalysis,
            as: "analysisData",
            required: false,
          },
        ],
        rejectOnEmpty: true,
      });
      const article = a.toJSON();

      return { ...article, meta: serializArticleMetas(article.meta), image: this.serializeArticleImg(article.img) };
    } catch (err) {
      // console.error(`Error retreiving article by id. Article id: ${id}, shop id: ${shopId}. Error: ${err}`);
      return null;
    }
  };

  /**
   *
   * @param {number} shopId
   * @param {number} articleId
   */
  idsOfThePrevAndNextArticle = async (shopId, articleId) => {
    const [result] = await sequelize.query(
      `
    SELECT
      id,
      blog_id as blogId,
      title,
      (SELECT prev WHERE prev = id) as prevId,
      (SELECT next WHERE next = id) as nextId
    FROM articles a1
    INNER JOIN (
      Select 
        MAX(CASE WHEN id < :articleId THEN id END) as prev,
        MIN(CASE WHEN id > :articleId THEN id END) as next
      FROM articles where shop_id=:shopId and (id != :articleId) Group by shop_id
      ) as a2 ON a1.id = a2.prev or a1.id = a2.next;
    `,
      {
        replacements: { shopId, articleId },
      }
    );

    const prev = {
      previd: null,
      prevBlogId: null,
    };

    const next = {
      nextid: null,
      nextBlogId: null,
    };

    for (let row of result) {
      const { previd, nextid, blogid } = row;
      if (previd) {
        prev.previd = previd;
        prev.prevBlogId = blogid;
      } else {
        next.nextid = nextid;
        next.nextBlogId = blogid;
      }
    }

    return {
      ...prev,
      ...next,
    };
  };

  getPaginationOfArticle = async (shopId, articleId) => {
    const paginationQuery = `
    WITH virtual_table AS (
      SELECT
        article_id,
        lag(article_id) OVER w AS prev,
        lead(article_id) OVER w AS next
      FROM articles
      WHERE shop_id = :shopId
      WINDOW w AS (ORDER BY created_at DESC)
    )

    SELECT prev, next FROM virtual_table WHERE article_id = :articleId;
  `;

    const [result] = await sequelize.query(paginationQuery, {
      replacements: { shopId, articleId },
      type: QueryTypes.SELECT,
    });

    console.log(result);

    return result;
  };

  /**
   * Retireve all articles from DB for a shop
   * @param {number} shopId
   * @returns {Promise<[ArticleDetails]>} list of articles
   */
  getAllArticles = async (shopId) => {
    try {
      const result = await Article.findAll({ shop_id: shopId });
      return result.toJSON();
    } catch (err) {
      return [];
    }
  };

  /**
   * Get paginated list of articles from DB by applying query params
   * @param {number} shopId
   * @param fields
   * @param {queryParam} queryParam
   * @returns {Promise<{pagination: Pagination, articles: [ArticleDetails & { blog: {blog_title: string, blog_handle: string }}]}>}
   */
  getAllArticlesWithPagination = async (
    shopId,
    {
      page = 1,
      limit = 20,
      search = "",
      sortBy = "created_at",
      sortOrder = "DESC",
      fields = undefined,
      optimize_status = "",
    }
  ) => {
    try {
      const where = {
        shop_id: shopId,
        is_deleted: false,
      };

      if (search) {
        where.title = { [Op.iLike]: `%${search}%` };
      }

      const optimizeStatusCombinationToScoreCondition = {
        "NEED_IMPROVEMENT,NOT_OPTIMIZED,OPTIMIZED": { [Op.gte]: 0 },
        "NEED_IMPROVEMENT,OPTIMIZED": { [Op.gte]: 50 },
        "NOT_OPTIMIZED,OPTIMIZED": { [Op.or]: [{ [Op.gte]: 75 }, { [Op.lt]: 50 }] },
        "NEED_IMPROVEMENT,NOT_OPTIMIZED": { [Op.gte]: 0, [Op.lt]: 75 },
        OPTIMIZED: { [Op.gte]: 75 },
        NEED_IMPROVEMENT: { [Op.gte]: 50, [Op.lt]: 75 },
        NOT_OPTIMIZED: { [Op.lt]: 50 },
      };
      optimize_status = optimize_status.split(",").sort().join(",");
      if (optimizeStatusCombinationToScoreCondition.hasOwnProperty(optimize_status)) {
        where.score = optimizeStatusCombinationToScoreCondition[optimize_status];
      }

      let offset = (page - 1) * limit;

      sortBy = sortBy === "status" ? "published_at" : sortBy === "optimize_status" ? "score" : sortBy;
      let order = [[sortBy, sortOrder]];

      const { rows: articles, count } = await Article.findAndCountAll({
        attributes: fields,
        where,
        offset,
        limit,
        include: [
          {
            as: "blog",
            model: Blog,
            attributes: [
              ["handle", "blog_handle"],
              ["title", "blog_title"],
            ],
          },
        ],
        order,
      });

      return {
        articles: articles.map((a) => a.toJSON()),
        pagination: preparePagination(count, page, limit),
      };
    } catch (err) {
      console.error(`Error retrieving articles with pagination. Error: ${err}`);
      return null;
    }
  };

  /**
   *
   * @param {number} shopId
   * @param {{ limit: number, fields?: string[]}} param1
   */
  getLeastScoredArticles = async (shopId, { limit = 5, fields = undefined }) => {
    const sortBy = "score";
    const sortOrder = "ASC";
    const order = [[sortBy, sortOrder]];

    const articles = await Article.findAll({
      attributes: ["blog_id", "article_id", ...fields],
      where: { shop_id: shopId },
      limit,
      order,
      include: [
        {
          model: Blog,
          as: "blog",
          attributes: [
            ["handle", "blog_handle"],
            ["title", "blog_title"],
          ],
        },
      ],
    });

    return articles.map((a) => a.toJSON());
  };

  /**
   *
   * @param {number} shopId
   * @param {{ limit: number, fields?: string[]}} param1
   */
  getTopScoredArticles = async (shopId, { limit = 5, fields = undefined }) => {
    const sortBy = "score";
    const sortOrder = "DESC";
    const order = [[sortBy, sortOrder]];

    const articles = await Article.findAll({
      attributes: ["blog_id", "article_id", ...fields],
      where: { shop_id: shopId },
      limit,
      order,
      include: [
        {
          model: Blog,
          as: "blog",
          attributes: [
            ["handle", "blog_handle"],
            ["title", "blog_title"],
          ],
        },
      ],
    });

    return articles.map((a) => a.toJSON());
  };

  /**
   *
   * @param {number} shopId
   * @param {import("sequelize").WhereOptions} conditions
   */
  iterateOverArticles = async function* (shopId, conditions = {}) {
    let limit = 30;
    let offset = 0;
    let articles = [1];

    while (articles.length > 0) {
      articles = await Article.findAll({
        where: { shop_id: shopId, is_deleted: false, ...conditions },
        limit,
        offset,
        include: ["meta", "img", "blog"],
      });

      offset += limit;

      yield articles.map((p) => p.toJSON());
    }
  };

  /**
   * Get article by shop id, blog id & shopify article id
   * @param {number} shopId database id of shop
   * @param {number} blogId database id of blog
   * @param {number} articleId shopify id of article
   * @returns {Promise<ArticleDetails>} article from DB
   */
  getArticleByArticleId = async (shopId, blogId, articleId) => {
    try {
      const article = await Article.findOne({ where: { shop_id: shopId, blog_id: blogId, article_id: articleId } });
      return article.toJSON();
    } catch (err) {
      // console.error(
      //   `Error retrieving article by shopify article id. Article id: ${articleId}, blogId: ${blogId}, shopId: ${shopId}. Error: ${err}`
      // );
      return null;
    }
  };

  /**
   * Serialize article analysis data for front end
   * @param {object} analysis article analysis object data
   * @returns {object} serialized optimization data
   */
  serializeArticleOptimizationDetails = (analysis) => {
    return [
      {
        title: "Basic SEO Analysis",
        key: "BASIC_SEO",
        values: [
          {
            key: "UNIQUE_FOCUS_KEYWORD",
            title: "Focus keyword is unique",
            value: analysis?.unique_focus_keyword,
            percent: getPercentValue(analysis?.unique_focus_keyword, SCORES?.UNIQUE_FOCUS_KEYWORD),
            isOptimized: calculateIsOptimized(analysis?.unique_focus_keyword, SCORES?.UNIQUE_FOCUS_KEYWORD),
          },
          {
            key: "FOCUS_KEYWORD_IN_INTRODUCTION",
            title: "Focus keyword is used in the introduction",
            value: analysis?.focus_keyword_in_introduction,
            percent: getPercentValue(analysis?.focus_keyword_in_introduction, SCORES?.FOCUS_KEYWORD_IN_INTRODUCTION),
            isOptimized: calculateIsOptimized(
              analysis?.focus_keyword_in_introduction,
              SCORES?.FOCUS_KEYWORD_IN_INTRODUCTION
            ),
          },
          {
            key: "CONTENT_MORE_THEN_800_WORDS",
            title: "Content should be more than 800 words",
            value: analysis?.content_more_then_800_words,
            percent: getPercentValue(analysis?.content_more_then_800_words, SCORES?.CONTENT_MORE_THEN_800_WORDS),
            isOptimized: calculateIsOptimized(
              analysis?.content_more_then_800_words,
              SCORES?.CONTENT_MORE_THEN_800_WORDS
            ),
          },
          {
            key: "FOCUS_KEYWORD_DENSITY",
            title: "Focus keyword is used 1-2% times of the blog content",
            value: analysis?.focus_keyword_density,
            percent: getPercentValue(analysis?.focus_keyword_density, SCORES?.FOCUS_KEYWORD_DENSITY),
            isOptimized: calculateIsOptimized(analysis?.focus_keyword_density, SCORES?.FOCUS_KEYWORD_DENSITY),
          },
        ],
      },
      {
        title: "Detailed SEO Analysis",
        key: "DETAILED_SEO",
        values: [
          {
            key: "FOCUS_KEYWORD_IN_IMG_ALT_TEXT",
            title: "Focus keyword is used in image alt text",
            value: analysis?.focus_keyword_in_img_alt_text,
            percent: getPercentValue(analysis?.focus_keyword_in_img_alt_text, SCORES?.FOCUS_KEYWORD_IN_IMG_ALT_TEXT),
            isOptimized: calculateIsOptimized(
              analysis?.focus_keyword_in_img_alt_text,
              SCORES?.FOCUS_KEYWORD_IN_IMG_ALT_TEXT
            ),
          },
          {
            key: "FOCUS_KEYWORD_IN_SUBHEADING",
            title: "Focus keyword found in subheadings",
            value: analysis?.focus_keyword_in_subheading,
            percent: getPercentValue(analysis?.focus_keyword_in_subheading, SCORES?.FOCUS_KEYWORD_IN_SUBHEADING),
            isOptimized: calculateIsOptimized(
              analysis?.focus_keyword_in_subheading,
              SCORES?.FOCUS_KEYWORD_IN_SUBHEADING
            ),
          },
          {
            key: "FOCUS_KEYWORD_IN_META_DESC",
            title: "Focus keyword found in meta description",
            value: analysis?.focus_keyword_in_meta_desc,
            percent: getPercentValue(analysis?.focus_keyword_in_meta_desc, SCORES?.FOCUS_KEYWORD_IN_META_DESC),
            isOptimized: calculateIsOptimized(analysis?.focus_keyword_in_meta_desc, SCORES?.FOCUS_KEYWORD_IN_META_DESC),
          },
          {
            key: "FOCUS_KEYWORD_IN_URL",
            title: "Focus Keyword used in the URL",
            value: analysis?.focus_keyword_in_url,
            percent: getPercentValue(analysis?.focus_keyword_in_url, SCORES?.FOCUS_KEYWORD_IN_URL),
            isOptimized: calculateIsOptimized(analysis?.focus_keyword_in_url, SCORES?.FOCUS_KEYWORD_IN_URL),
          },
          {
            key: "META_DESC_WITHIN_160_CHAR",
            title: "Meta description must be within 160 characters",
            value: analysis?.meta_desc_within_160_char,
            percent: getPercentValue(analysis?.meta_desc_within_160_char, SCORES?.META_DESC_WITHIN_160_CHAR),
            isOptimized: calculateIsOptimized(analysis?.meta_desc_within_160_char, SCORES?.META_DESC_WITHIN_160_CHAR),
            hint: "To get a higher score, write meta description using 160 characters",
          },
          {
            key: "FOCUS_KEYWORD_IN_META_TITLE",
            title: "Focus keyword is used in the meta title",
            value: analysis?.focus_keyword_in_meta_title,
            percent: getPercentValue(analysis?.focus_keyword_in_meta_title, SCORES?.FOCUS_KEYWORD_IN_META_TITLE),
            isOptimized: calculateIsOptimized(
              analysis?.focus_keyword_in_meta_title,
              SCORES?.FOCUS_KEYWORD_IN_META_TITLE
            ),
          },
          {
            key: "INTERNAL_LINK_IN_CONTENT",
            title: "1 internal link found in blog content",
            value: analysis?.internal_link_in_content,
            percent: getPercentValue(analysis?.internal_link_in_content, SCORES?.INTERNAL_LINK_IN_CONTENT),
            isOptimized: calculateIsOptimized(analysis?.internal_link_in_content, SCORES?.INTERNAL_LINK_IN_CONTENT),
          },
          {
            key: "EXTERNAL_LINK_IN_CONTENT",
            title: "1 external link found in blog content with do-follow",
            value: analysis?.external_link_in_content,
            percent: getPercentValue(analysis?.external_link_in_content, SCORES?.EXTERNAL_LINK_IN_CONTENT),
            isOptimized: calculateIsOptimized(analysis?.external_link_in_content, SCORES?.EXTERNAL_LINK_IN_CONTENT),
          },
          {
            key: "ALT_TEXT_IN_ALL_IMG",
            title: "Add alt text to all images",
            value: analysis?.alt_text_in_all_img,
            percent: getPercentValue(analysis?.alt_text_in_all_img, SCORES?.ALT_TEXT_IN_ALL_IMG),
            isOptimized: calculateIsOptimized(analysis?.alt_text_in_all_img, SCORES?.ALT_TEXT_IN_ALL_IMG),
          },
          // {
          //   key: "UNIQUE_BLOG_CONTENT",
          //   title: "Blog content is unique",
          //   value: analysis?.DETAILED_SEO?.UNIQUE_BLOG_CONTENT,
          //   percent: getPercentValue(
          //     analysis?.DETAILED_SEO?.UNIQUE_BLOG_CONTENT,
          //     SCORES?.DETAILED_SEO?.UNIQUE_BLOG_CONTENT
          //   ),
          //   isOptimized: calculateIsOptimized(
          //     analysis?.DETAILED_SEO?.UNIQUE_BLOG_CONTENT,
          //     SCORES?.DETAILED_SEO?.UNIQUE_BLOG_CONTENT
          //   ),
          // },
        ],
      },
    ];
  };

  /**
   * Serialize article for live score analysis update
   * @param {number} shopId
   * @param {number} articleId
   * @param {{ metaTitle: string, metaDescription: string, focusKeyword: string, image: ArticleDetails["image"]}} data
   * @returns {Promise<ArticleDetails>} serialized article
   */
  serializeArticleForAnalysis = async (shopId, articleId, data) => {
    const { metaTitle, metaDescription, focusKeyword, image, handle } = data;
    const article = await this.getArticleByShopifyId(shopId, articleId);
    const metadata = this.serializeArticleUpdatedMetadata(article, { metaTitle, metaDescription });

    const updateData = {
      meta: metadata.length > 0 ? metadata : article.meta,
      focus_keyword: focusKeyword ? replaceSpecialChars(focusKeyword) : article.focus_keyword,
      image: image || article.image,
      handle,
    };

    return { ...article, ...updateData };
  };

  /**
   * Map user inputted metaTitle & metaDescription to shopify metafield structure
   * @param {ArticleDetails} article article details object
   * @param {{ metaTitle: string, metaDescription: string }} userInput user inputted metaTitle & metaDescription
   * @returns {Metafield[]} mapped metafields for metaTitle & metaDescription
   */
  serializeArticleUpdatedMetadata = (article, { metaTitle, metaDescription }) => {
    const metadata =
      article.meta?.filter((m) => m.key !== METAFIELD_KEYS.TITLE_TAG && m.key !== METAFIELD_KEYS.DESCRIPTION_TAG) || [];

    const articleMetaTitle = !isEmpty(article.meta)
      ? article.meta.find((md) => md.key === METAFIELD_KEYS.TITLE_TAG)
      : null;

    if (!isEmpty(articleMetaTitle) && metaTitle == undefined) {
      metadata.push(articleMetaTitle);
    } else if (!isEmpty(articleMetaTitle) && metaTitle !== undefined) {
      metadata.push({ ...pick(articleMetaTitle, ["id", "key", "namespace", "type"]), value: metaTitle });
    } else if (metaTitle !== undefined) {
      metadata.push({
        namespace: NAMESPACE.GLOBAL,
        key: METAFIELD_KEYS.TITLE_TAG,
        value: metaTitle.substring(0, 70),
        type: METAFIELD_TYPES.STRING,
      });
    }

    const articleMetaDescription = !isEmpty(article.meta)
      ? article.meta.find((md) => md.key === METAFIELD_KEYS.DESCRIPTION_TAG)
      : null;

    if (!isEmpty(articleMetaDescription) && metaDescription == undefined) {
      metadata.push(articleMetaDescription);
    } else if (!isEmpty(articleMetaDescription) && metaDescription !== undefined) {
      metadata.push({ ...pick(articleMetaDescription, ["id", "key", "namespace", "type"]), value: metaDescription });
    } else if (metaDescription !== undefined) {
      metadata.push({
        namespace: NAMESPACE.GLOBAL,
        key: METAFIELD_KEYS.DESCRIPTION_TAG,
        value: metaDescription,
        type: METAFIELD_TYPES.STRING,
      });
    }

    return metadata;
  };

  /**
   * Extract social media preview image urls from article
   * @param {ArticleDetails} article article details
   * @returns {{ facebookPreviewImage: string, twitterPreviewImage: string }} social media preview images objects
   */
  getSocialMediaPreviewImages = (article) => {
    let facebookPreviewImage = null;
    let twitterPreviewImage = null;

    try {
      article.meta.forEach((m) => {
        if (m.namespace === NAMESPACE.STORE_SEO && m.key === METAFIELD_KEYS.FACEBOOK_PREVIEW_IMAGE_URL) {
          facebookPreviewImage = m.value;
        } else if (m.namespace === NAMESPACE.STORE_SEO && m.key === METAFIELD_KEYS.TWITTER_PREVIEW_IMAGE_URL) {
          twitterPreviewImage = m.value;
        }
      });
    } catch (err) {}

    return { facebookPreviewImage, twitterPreviewImage };
  };

  /**
   * Update existing article in Shopify & DB
   * @param {number} shopId database id of the shop
   * @param {number} articleId database id of the article
   * @param {{ metaTitle: string, metaDescription: string, focusKeyword: string, tags: [string], image: ArticleDetails["image"]}} body user inputted data
   * @param {User} user User to access shopify api
   * @returns {Promise<ArticleDetails>} updated article data
   */
  updateArticleData = async (shopId, articleId, body, user) => {
    const BlogService = require("./BlogService");

    const article = await this.getArticleByShopifyId(shopId, articleId);
    const blog = await BlogService.getBlog(shopId, article?.blog_id);

    const {
      metaTitle,
      metaDescription,
      focusKeyword,
      tags = article.tags || [],
      image,
      handle = undefined,
      createRedirectUrl = false,
    } = body;

    const metadata = this.serializeArticleUpdatedMetadata(article, { metaTitle, metaDescription });

    const data = { metafields: metadata, tags };
    if (image) {
      data.image = {
        alt: image.alt,
      };
    }

    const { article: shopifyArticle } = await ShopifyService.updateShopifyArticle({
      articleId: article?.article_id,
      blogId: blog?.blog_id,
      user: user,
      data,
      handle,
    });

    const { metafields: updatedGlobalMetafields } = await ShopifyService.getShopifyArticleMetafields({
      user,
      blogId: blog?.blog_id,
      articleId: article?.article_id,
      namespace: NAMESPACE.GLOBAL,
    });

    const { metafields: updatedStoreSeoMetafields } = await ShopifyService.getShopifyArticleMetafields({
      user,
      blogId: blog?.blog_id,
      articleId: article?.article_id,
      namespace: NAMESPACE.STORE_SEO,
    });

    if (createRedirectUrl) {
      let urlRedirect = await ShopifyService.createRedirectURL(user.shop, {
        oldPath: `/blogs/${blog.handle}/${article.handle}`,
        newPath: `/blogs/${blog.handle}/${handle}`,
      });
    }

    const updateData = {
      focus_keyword: replaceSpecialChars(focusKeyword) || article?.focus_keyword,
      tags: tags?.length > 0 ? tags : article?.tags,
      handle: shopifyArticle.handle,
    };

    const updatedArticle = await this.updateArticle(article.id, updateData);
    const updatedMetas = await ArticleMetaService.upsertMetas(shopId, blog.id, article.id, [
      ...updatedGlobalMetafields,
      ...updatedStoreSeoMetafields,
    ]);
    const updatedImg = data.image
      ? this.serializeArticleImg(
          await this.upsertImage(shopId, blog.id, article.id, { ...article.image, ...data.image })
        )
      : null;

    dispatchQueue({
      queueName: QUEUE_NAMES.ARTICLE_METAFIELD_SYNC,
      message: { user: user, shopId, articleId: article.id },
      ttl: 1000,
    });
    return { ...updatedArticle, meta: updatedMetas, image: updatedImg || article.image };
  };

  /**
   * Fetch updated list of metafields from shopify & update the related article in DB
   * @param {User} user user to access shopify api
   * @param {number} shopId database id of the shop
   * @param {number} articleId database id of the article
   * @param {string} [namespace] shopify metafield namespace | Default 'global'
   * @returns {{ limitRemaining: number, article: ArticleDetails }}
   */
  updateMetafieldsFromShopify = async (user, shopId, articleId) => {
    const BlogService = require("./BlogService");

    const article = await this.getArticle(shopId, articleId);
    const blog = await BlogService.getBlog(shopId, article?.blog_id);
    const { metafields, limitRemaining } = await ShopifyService.getShopifyArticleMetafields({
      user,
      blogId: blog?.blog_id,
      articleId: article?.article_id,
    });

    let metaArr = metafields || [];

    await ArticleMetaService.deleteMeta(articleId);
    await ArticleMetaService.upsertMetas(shopId, blog.id, articleId, metaArr);
    const updatedArticle = await this.getArticle(shopId, articleId);

    return { limitRemaining, article: updatedArticle };
  };

  /**
   * Serialize shopify article data for DB insert operation
   * @param {BlogDetails} blog relevant blog details
   * @param {object} articleData shopify article data
   * @returns {ArticleDetails} serialized article
   */
  serializeShopifyArticleData = (blog, articleData) => {
    const { id: blogId, shop_id } = blog;
    const {
      id: articleId,
      title,
      body_html,
      author,
      published_at,
      template_suffix,
      handle,
      tags,
      admin_graphql_api_id,
      // image,
    } = articleData;

    return {
      shop_id,
      blog_id: blogId,
      article_id: articleId,
      title,
      handle,
      author,
      body_html,
      // image: image || null,
      template_suffix,
      tags: tags?.split(", ").filter((t) => t),
      admin_graphql_api_id,
      published_at,
      is_deleted: false,
    };
  };

  /**
   * Insert / update existing article in DB
   * @param {*} shopifyArticle shopify article details
   * @returns {Promise<ArticleDetails>} saved / updated article from DB
   */
  saveOrUpdateArticle = async (shopifyArticle) => {
    const { meta, image, ...data } = shopifyArticle;
    let [article] = await Article.upsert(data, { returning: true });

    article = article.toJSON();

    // Delete existing article image before upsert new image
    await ArticleImage.destroy({
      where: { shop_id: article?.shop_id, blog_id: article?.blog_id, article_id: article?.id },
    });
    if (image) {
      await this.upsertImage(article.shop_id, article.blog_id, article.id, image);
    }

    await ArticleMetaService.deleteMeta(article.id);
    await ArticleMetaService.upsertMetas(article.shop_id, article.blog_id, article.id, meta);

    return this.getArticleByShopifyId(article.shop_id, article.article_id);
  };

  /**
   * Fetch article from shopify & insert/update in DB
   * @param {User} user user to access shopify api
   * @param {BlogDetails} blog relevant blog details
   * @param {string} nextPageToken shopify pagination token
   * @returns {Promise<{ limitRemaining: number, nextPageQuery: any }>}
   */
  saveOrUpdateArticlesFromShopify = async ({ user, blog, limit = 1, nextPageQuery = {} }) => {
    const { articles = [], ...rest } = await ShopifyService.getBlogArticlesFromShopify({
      session: user,
      blogId: blog.blog_id,
      limit,
      nextPageQuery,
    });

    for (let article of articles) {
      const savedArticle = await this.saveOrUpdateArticle(blog, article);
      const { id, shop_id: shopId } = savedArticle;

      if (!savedArticle.focus_keyword) {
        dispatchQueue({
          queueName: QUEUE_NAMES.FOCUS_KEYWORD_GENERATOR,
          message: {
            shopId,
            dbResourceId: id,
            resourceType: resourceType.ARTICLE,
          },
        });
      }

      const { article: updatedArticle } = await this.updateMetafieldsFromShopify(user, shopId, id);

      // Store sitemap data
      await SitemapService.storeSitemapData(updatedArticle, analysisEntityTypes.ARTICLE);

      const ShopService = require("./ShopService");
      const AnalysisService = require("./AnalysisService");
      const shop = await ShopService.getShopById(shopId);
      await AnalysisService.analyseEachArticle({ shopId, article: updatedArticle, shopURL: shop.url });
    }

    return rest;
  };

  /**
   * Delete articles of shop
   * @param {number} shopId
   */
  deleteArticleOfShop = async (shopId) => {
    try {
      await Article.destroy({ where: { shop_id: shopId } });
    } catch (e) {
      console.log(e);
    }
  };

  deleteArticlesMarkedForDeletion = async (shopId) => {
    try {
      const articles = await Article.findAll({ where: { is_deleted: true, shop_id: shopId }, attributes: ["id"] });
      const ids = articles.map((a) => a.toJSON().id);

      await Article.destroy({
        where: {
          id: { [Op.in]: ids },
        },
      });

      await ArticleMeta.destroy({ where: { article_id: { [Op.in]: ids } } });
      await ArticleImage.destroy({ where: { article_id: { [Op.in]: ids } } });
    } catch (e) {
      console.log(e);
    }
  };

  deleteArticleById = async (shopId, articleId) => {
    const BlogService = require("./BlogService");

    const article = await this.getArticleByShopifyId(shopId, articleId);
    const blog = await BlogService.getBlog(shopId, article?.blog_id);

    if (!article || !blog) return;

    await Article.destroy({ where: { shop_id: shopId, blog_id: blog?.id, article_id: articleId } });
    await ArticleMetaService.deleteMeta(article?.id);
    await ArticleImage.destroy({ where: { shop_id: shopId, blog_id: blog?.id, article_id: article?.id } });
    await AnalysisService.deleteArticleAnalysis(blog?.id, article?.id);
  };

  getArticlesByShopId = async (shopId, fields = [], limit = null) => {
    const articles = await Article.findAll({
      where: {
        shop_id: shopId,
        published_at: {
          [Op.not]: null,
        },
      },
      attributes: ["id", ...fields],
      limit,
      include: [
        {
          as: "blog",
          model: Blog,
          attributes: [
            ["handle", "blog_handle"],
            ["title", "blog_title"],
          ],
        },
      ],
    });

    return articles.map((p) => p.toJSON());
  };

  getSitemapEnabledArticlesByShopId = async (shopId, fields = [], limit = null) => {
    const articles = await Article.findAll({
      where: {
        shop_id: shopId,
        published_at: {
          [Op.not]: null,
        },
      },
      attributes: ["id", ...fields],
      limit,
      include: [
        {
          as: "blog",
          model: Blog,
          attributes: [
            ["handle", "blog_handle"],
            ["title", "blog_title"],
          ],
        },

        {
          model: Sitemap,
          as: "sitemap",
          attributes: ["resource_id", "resource_type", "sitemap_disabled"],
          where: {
            resource_type: analysisEntityTypes.ARTICLE,
            sitemap_disabled: 0,
          },
        },
      ],
    });

    return articles.map((p) => p.toJSON());
  };

  /**
   *
   * @param shopId
   * @param page
   * @param limit
   * @param search
   * @param filterOn
   * @param filterValue
   * @param sortBy
   * @param sortOrder
   * @returns {Promise<{pagination: Promise<{pageCount: number, pageSize, page, rowCount: Promise<number>}>, sitemaps: ((*&{featuredImage: {altText: *, src: *, id: *}|null, noFollow: boolean, isChecked: boolean, noIndex: boolean, status: boolean})[]|*[])}>}
   */
  getSitemaps = async (
    shopId,
    {
      page = 1,
      limit = 20,
      search = "",
      filterOn = "sitemap",
      filterValue = "-1",
      sortBy = "created_at",
      sortOrder = "DESC",
    }
  ) => {
    const offset = limit * (page - 1);

    const where = { shop_id: shopId };

    let order = [[sortBy, sortOrder]];

    if (search) {
      where.title = { [Op.iLike]: `%${search}%` };
    }

    const sitemapWhere = findSitemapFilter(filterOn, filterValue);

    const { count, rows } = await ArticleModel.findAndCountAll({
      attributes: ["id", "shop_id", "article_id", "title", "handle"],
      where,
      include: [
        {
          model: Sitemap,
          as: "sitemap",
          where: [sitemapWhere, { resource_type: analysisEntityTypes.ARTICLE }],
          required: true,
          attributes: ["id", "sitemap_disabled", "no_index", "no_follow"],
        },
        "img",
      ],
      limit,
      offset,
      order,
    });

    return {
      sitemaps: rows.map((p) => serializeArticleSitemap(p)) || [],
      pagination: preparePagination(count, page, limit),
    };
  };

  /**
   * Updates sitemap data to shopify and database
   * @param shopId
   * @param session
   * @param sitemap
   * @returns {Promise<void>}
   */
  updateSitemap = async ({ shopId, session, sitemap }) => {
    const article = await this.getArticleByShopifyId(shopId, sitemap.id);

    let newStatus = sitemap.status ? "0" : "1";
    const metaInput = [
      {
        key: METAFIELD_KEYS.HIDDEN,
        value: newStatus,
        ownerId: article.shopify_gql_id,
      },
    ];
    await ShopifyService.setMetafields(session.shop, metaInput);

    const shopifyArticle = await ShopifyService.onlineStore.getArticle(session.shop, {
      id: article.shopify_gql_id,
      metafieldKeys: metafieldKeysFilterArray,
    });
    const serializedShopifyArticle = ArticleSerializer.serializeShopifyArticleData(
      shopId,
      article?.blog_id,
      shopifyArticle
    );
    await this.saveOrUpdateArticle(serializedShopifyArticle);

    // Update sitemap data in database
    await SitemapService.updateSitemapData({
      shopId,
      resourceId: article.id,
      resourceType: analysisEntityTypes.ARTICLE,
      metaKey: METAFIELD_KEYS.SITEMAP_DISABLED,
      metaStatus: newStatus,
    });
  };

  markArticlesAsNotSynced = async (shopId) => {
    await Article.update(
      {
        is_synced: false,
      },
      {
        where: { shop_id: shopId },
      }
    );
  };

  deleteNotSyncedArticles = async (shopId) => {
    let articlesToDelete = await Article.findAll({ where: { shop_id: shopId, is_synced: false } });

    const AnalysisService = require("./AnalysisService");
    for (let article of articlesToDelete) {
      const articleId = article.toJSON().id;
      const blogId = article.toJSON().blog_id;
      await article.destroy();
      await ArticleMetaService.deleteMeta(articleId);
      await AnalysisService.deleteArticleAnalysis(blogId, articleId);
      await ArticleImageService.deleteByArticleId(articleId, shopId);
    }
  };
}

module.exports = new ArticleService();
