const { Integration } = require("../../sequelize");

class IntegrationService {
  getConfig = async (shopId, type) => {
    try {
      const where = {
        shop_id: shopId,
        type,
      };
      const config = await Integration.where(where).fetch({ require: false });
      return config.toJSON();
    } catch (e) {}
    return false;
  };

  insertOrUpdateConfig = async (shopId, type, configs) => {
    try {
      let data;
      const where = {
        shop_id: shopId,
        type,
      };
      const oldConfigs = await this.getConfig(shopId, type);
      if (oldConfigs) {
        data = {
          ...oldConfigs,
          configs,
          updated_at: new Date().toISOString(),
        };
        const updatedData = await this.updateIntegrationByCondition(where, data);
        return {
          ...updatedData,
          configs: updatedData.configs,
        };
      } else {
        data = {
          shop_id: shopId,
          type,
          configs,
        };
        const insertedData = await this.insertIntegrationByCondition(data);
        return { ...insertedData, configs: insertedData.configs };
      }
    } catch (e) {
      // console.log(e);
      return null;
    }
  };

  updateIntegrationByCondition = async (conditions, data) => {
    const updatedData = await Integration.where(conditions).save(data, {
      patch: true,
    });
    return updatedData.toJSON();
  };

  insertIntegrationByCondition = async (data) => {
    const insertedData = await new Integration(data).save();
    return insertedData.toJSON();
  };

  updateAccessTokens = async (user, tokens) => {
    const { shopId } = user;
    return await this.updateIntegrationByCondition(
      {
        shop_id: shopId,
        type: "GOOGLE",
      },
      {
        tokens,
      }
    );
  };
}

module.exports = new IntegrationService();
