const { ShopUser } = require("../../sequelize");
const ShopService = require("../services/ShopService");
const UserService = require("../services/UserService");

class ShopUserService {
  getShopUser = async (shopId) => {
    const shopUser = await ShopUser.findOne({ where: { shop_id: shopId } });
    return shopUser.toJSON();
  };

  getShopUserByUrl = async (shopUrl) => {
    const shop = await ShopService.getShopByUrl(shopUrl);
    const shopUser = await this.getShopUser(shop.id);
    const user = await UserService.findUser(shopUser.user_id);
    return {
      shop,
      user,
    };
  };

  getShopUserByDomain = async (domain) => {
    const shop = await ShopService.getShop(domain);
    const shopUser = await this.getShopUser(shop.id);
    const user = await UserService.findUser(shopUser.user_id);
    return {
      shop,
      user,
    };
  };

  storeShopUsers = async (shopId, userId) => {
    const data = { user_id: userId, shop_id: shopId };
    const count = await ShopUser.count({
      where: data,
    });
    if (count === 0) {
      await ShopUser.create(data);
    }
  };

  getUserByShopId = async (shopId) => {
    const shopUser = await ShopUser.where({ shop_id: shopId }).fetch({
      require: false,
    });
    return await UserService.findUser(shopUser.attributes.user_id);
  };
}

module.exports = new ShopUserService();
