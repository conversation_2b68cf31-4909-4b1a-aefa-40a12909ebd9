const fs = require("fs/promises");
const { createCanvas } = require("canvas");
const Fs = require("fs");
const axios = require("axios");
const logger = require("storeseo-logger");
const googleBucketService = require("./GoogleBucketService");
const JSZip = require("jszip");
const { stripTags, escapeRegExp } = require("../utils/helper");
const { isEmpty } = require("lodash");

class FileService {
  buildImageURL = (imageFilePath, baseURL = process.env.HOST) => `${baseURL}/${imageFilePath}`;

  generatePlaceholderImageForShop = async (shopObj) => {
    try {
      // const logoText = shopObj.name.substring(0, 2).toUpperCase();
      const backgroundColor = "#f6f7fb";
      const textBackgroundColor = "#00cc76";
      const textColor = "#fff";

      const path = `uploads/${shopObj.domain}`;
      await fs.mkdir(path, { recursive: true });
      const destinationPath = `${path}/default-logo.png`;

      const width = 1200;
      const height = 630;

      const canvas = createCanvas(width, height);
      const context = canvas.getContext("2d");

      context.fillStyle = backgroundColor;
      context.fillRect(0, 0, width, height);

      context.font = "bold 35pt Menlo";
      context.textAlign = "center";
      context.textBaseline = "top";
      context.fillStyle = textBackgroundColor;

      const text = shopObj.name;

      const textWidth = context.measureText(text).width;
      context.fillRect(600 - textWidth / 2 - 10, 300 - 5, textWidth + 20, 70);
      context.fillStyle = textColor;
      context.fillText(text, 600, 300);

      const buffer = canvas.toBuffer();
      fs.writeFile(destinationPath, buffer);

      const { fileName, publicUrl } = await googleBucketService.uploadMedia({
        folder: `shops/${shopObj.domain}`,
        originalFilePath: destinationPath,
        makePublic: true,
      });

      this.deleteFile(destinationPath);

      return {
        fileName,
        url: publicUrl,
        filePath: fileName,
      };
    } catch (err) {
      console.error(`Error generating default placeholder logo for shop ${shopObj.domain}: ${JSON.stringify(err)}`);
    }
  };

  saveFile = (file, uploadPath = "uploads") => {
    return new Promise(async (resolve, reject) => {
      try {
        await fs.mkdir(uploadPath, { recursive: true });
        const newFileName = `${Date.now()}-${file?.name}`;
        const destinationPath = `${uploadPath}/${newFileName}`;

        file.mv(destinationPath, (err) => {
          if (err) {
            throw err;
          } else {
            const filePath = destinationPath.replace("uploads/", "");
            const url = this.buildImageURL(filePath);

            resolve({
              fullPath: destinationPath,
              filePath,
              url,
            });
          }
        });

        // await fs.rename(file?.path, destinationPath);

        // const filePath = destinationPath.replace("uploads/", "");
        // const url = this.buildImageURL(filePath);

        // resolve({ filePath, url });
      } catch (err) {
        console.error(`Error saving file to path '${uploadPath}'. File: ${file}. Error: ${error}`);
        resolve(null);
      }
    });
  };

  saveUploadedFileForShop = async (shopDomain, file, saveLocally = false) => {
    if (saveLocally) {
      return await this.saveFile(file, `uploads/${shopDomain}`);
    } else {
      const { fullPath } = await this.saveFile(file);

      const { fileName, publicUrl: url } = await googleBucketService.uploadMedia({
        folder: `shops/${shopDomain}`,
        originalFilePath: fullPath,
        makePublic: true,
      });

      this.deleteFile(fullPath);

      return {
        fileName,
        url,
        filePath: fileName,
      };
    }
  };

  deleteFile = async (filePath, fromCloudStorage = false) => {
    try {
      if (fromCloudStorage) {
        return await googleBucketService.removeMedia(filePath);
      }

      await fs.unlink(filePath);
      return true;
    } catch (err) {
      // console.error(`Error deleting file ${filePath}.`, err);
      return false;
    }
  };

  /**
   *
   * @param {string[]} inputFiles Array of file paths
   * @param {string} saveTo save location for zip file
   * @return {Promise<string>} saved file location same as 'saveTo' param
   */
  createZip = async (inputFiles = [], saveTo) =>
    new Promise((resolve, reject) => {
      const zip = new JSZip();

      for (let filePath of inputFiles) {
        const fileName = filePath.split("/").pop();
        zip.file(fileName, Fs.createReadStream(filePath), {
          compression: "DEFLATE",
          compressionOptions: {
            level: 6,
          },
        });
      }

      zip
        .generateNodeStream({
          streamFiles: true,
        })
        .pipe(Fs.createWriteStream(saveTo))
        .on("finish", () => {
          console.log("Zip file written.");
          resolve(saveTo);
        });
    });

  /**
   *
   * @param {string} filePath
   * @param {string} extractPath
   */
  extractZip = async (filePath, extractPath) => {
    let zip = new JSZip();
    zip = await zip.loadAsync(fs.readFile(filePath));

    const filesPromises = [];

    for (let file in zip.files) {
      const p = new Promise((resolve, reject) => {
        zip.files[file]
          .nodeStream()
          .pipe(Fs.createWriteStream(`${extractPath}/${file}`))
          .on("finish", () => resolve(true))
          .on("error", (err) => reject(err));
      });

      filesPromises.push(p);
    }

    return Promise.allSettled(filesPromises);
  };

  readCsvFile = async (filePath) => {
    try {
      // Read CSV file
      const data = await fs.readFile(filePath, "utf-8");

      const lines = data.split("\n");
      // const headers = lines[0].split(",");

      // const csvData = [];
      // for (let i = 1; i < lines.length; i++) {
      //   const row = lines[i].split(",");
      //   const rowObject = {};
      //   for (let j = 0; j < headers.length; j++) {
      //     rowObject[headers[j].trim()] = row[j].trim();
      //   }
      //   csvData.push(rowObject);
      // }

      return lines.filter((l) => !isEmpty(l));
    } catch (error) {
      // console.error("error =", error);
      return [];
    }
  };

  writeCsvFile = async (filePath, csvContent) => {
    await fs.writeFile(filePath, csvContent, (err) => {
      if (err) {
        console.error("Error writing CSV File =", err);
        return;
      }
    });
    console.log("CSV file written successfully!");
  };

  /**
   *
   * @param {string} domain
   * @param {object} row
   * @param {string} fileName
   */
  writeAiContentUsage = async (domain, row, fileName) => {
    try {
      const filePath = `uploads/${domain}/${fileName}`;
      const data = await this.readCsvFile(filePath);

      const csvContent = [];

      if (data.length) {
        // const prevData = data.map((row) => Object.values(row).join(","));
        // console.info("prevData =", prevData);
        csvContent.push(...data);
      } else {
        const headers = Object.keys(row).join(",");
        csvContent.push(headers);
      }

      const rowData = Object.values(row)
        .map((item) => escapeRegExp(stripTags(item).replace(/\r?\n/g, "")))
        .join(",");

      csvContent.push(rowData);

      await this.writeCsvFile(filePath, csvContent.join(`\n`));
    } catch (error) {
      console.error("error =", error);
    }
  };

  countAiOptimizedItems = async (domain) => {
    const csvContent = [];
    try {
      const filePath = `uploads/${domain}/ai-content.csv`;
      const data = await this.readCsvFile(filePath);

      if (data.length) {
        csvContent.push(...data);
      }

      return csvContent.length;
    } catch (error) {
      return csvContent.length;
    }
  };
}

module.exports = new FileService();
