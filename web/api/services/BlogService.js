const logger = require("storeseo-logger");
const ShopifyService = require("./ShopifyService");
const { Blog, Op } = require("../../sequelize");
const { serializeShopifyBlogData } = require("../serializers/BlogSerializer");
const cache = require("../cache");
const { dispatchQueue } = require("../queue/queueDispatcher");
const { QUEUE_NAMES } = require("../queue/config");
const EventService = require("./EventService");
const SitemapService = require("./SitemapService");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");

/**
 * @typedef {import('../../jsDocTypes').BlogDetails} BlogDetails
 * @typedef {import("../../jsDocTypes").Pagination} Pagination
 * @typedef {import("../../jsDocTypes").Metafield} Metafield
 */

class BlogService {
  /**
   * Create a new blog in DB
   * @param {BlogDetails} data blog details object
   * @returns {Promise<BlogDetails>} saved blog details from DB
   */
  saveBlog = async (data) => {
    try {
      const savedBlog = await Blog.create(data);
      return savedBlog.toJSON();
    } catch (err) {
      console.error(`Error saving blog into database. Data: ${JSON.stringify(data)}. Error: ${err}}`);
      return null;
    }
  };

  /**
   * Update existing blog data in DB
   * @param {number} id database id of the blog
   * @param {BlogDetails} data blog attributes to update
   * @returns {Promise<BlogDetails>} updated blog from DB
   */
  updateBlog = async (id, data) => {
    try {
      const [affectedRows, blogs] = await Blog.update(data, { returning: true, where: { id } });

      if (affectedRows === 1) return blogs[0].toJSON();

      return blogs.map((b) => b.toJSON());
    } catch (err) {
      console.error(`Error updating blog ${id}. Data: ${JSON.stringify(data, null, 2)}. Error: ${err}`);
      return null;
    }
  };

  /**
   * Get blog by DB id & shop id
   * @param {number} shopId database id of the shop
   * @param {number} id database id of the blog
   * @returns {Promise<BlogDetails>} blog details retrieved from DB
   */
  getBlog = async (shopId, id) => {
    try {
      const blog = await Blog.findOne({ where: { id, shop_id: shopId } });
      return blog.toJSON();
    } catch (err) {
      // console.error(`Error retrieving blog by id. Id: ${id}, shop id: ${shopId}. Error: ${err}`);
      return null;
    }
  };

  /**
   * Get blog by shop id & shopify blog id
   * @param {number} shopId database id of the shop
   * @param {number} blogId shopify blog id
   * @returns {Promise<BlogDetails>} blog details retrieved from DB
   */
  getBlogByBlogId = async (shopId, blogId) => {
    try {
      const blog = await Blog.findOne({ where: { shop_id: shopId, blog_id: blogId } });
      return blog?.toJSON();
    } catch (err) {
      // console.error(`Error retrieving blog by shopify blog id. Blog id: ${blogId}, shopId: ${shopId}. Error: ${err}`);
      return null;
    }
  };

  /**
   * Insert or update existing blog in DB
   * @param {BlogDetails} data blog details object
   * @returns {Promise<BlogDetails>} saved/updated blog from DB
   */
  saveOrUpdateBlog = async (data) => {
    const [blog] = await Blog.upsert(data, { returning: true });
    return blog.toJSON();
  };

  /**
   * Fetch list of all blogs from Shopify and insert/update them in DB
   * @param {User} user
   * @param {number} shopId
   * @returns {Promise<BlogDetails[]>}
   */
  saveOrUpdateBlogsFromShopify = async (user, shopId) => {
    try {
      const ArticleService = require("./ArticleService");
      await ArticleService.temproarilyMarkAllArticlesAsDeleted(shopId);
      const blogs = await ShopifyService.getShopifyBlogs(user);
      const results = await Promise.allSettled(
        blogs?.map((b) => {
          const data = serializeShopifyBlogData(shopId, b);
          return this.saveOrUpdateBlog(data);
        })
      );

      const savedBlogs = results.filter((r) => r.status === "fulfilled").map((r) => r.value);

      if (savedBlogs.length === 0) {
        await EventService.handleBlogArticleSyncUpdate({
          shop: user.shop,
          id: null,
          articles: 0,
        });
        return;
      }

      // Delete all articles related sitemaps
      await SitemapService.deleteAllSitemaps(shopId, analysisEntityTypes.ARTICLE);

      for (const b of savedBlogs) {
        await cache.blogsPendingInQueue(user.shop, b.id);
        dispatchQueue({
          queueName: QUEUE_NAMES.BLOG_ARTICLES_SYNC,
          message: { user, blog: b },
          ttl: 1000,
        });
      }

      return savedBlogs;
    } catch (err) {
      return null;
    }
  };

  /**
   * Delete all blogs for a shop from DB
   * @param {number} shopId
   */
  deleteBlogOfShop = async (shopId) => {
    try {
      await Blog.destroy({ where: { shop_id: shopId } });
    } catch (e) {
      console.log(e);
    }
  };

  deleteNotSyncedBlogs = async (shopId) => {
    let blogsToDelete = await Blog.findAll({ where: { shop_id: shopId, is_synced: false } });

    for (let blog of blogsToDelete) {
      await blog.destroy();
    }
  };

  markBlogsAsNotSynced = async (shopId) => {
    await Blog.update(
      {
        is_synced: false,
      },
      {
        where: { shop_id: shopId },
      }
    );
  };
}

module.exports = new BlogService();
