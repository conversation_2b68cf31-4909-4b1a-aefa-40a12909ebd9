const { isEmpty, pick } = require("lodash");
const ShopService = require("./ShopService");
const ShopifyService = require("./ShopifyService");
const ProductService = require("./ProductService");
const ProductAnalysisService = require("./ProductAnalysisService");
const SCORES = require("../config/seoScores");
const { calculateScorePercent, stripTags, sleep } = require("../utils/helper");
const { OptimizationRuns } = require("../../sequelize");
const cache = require("../cache");
const fs = require("fs");
const path = require("path");
const { dispatchQueue } = require("../queue/queueDispatcher");
const { QUEUE_NAMES } = require("../queue/config");
const { NAMESPACE, METAFIELD_KEYS, METAFIELD_TYPES } = require("storeseo-enums/metafields");
const bpoOptions = require("storeseo-enums/bpoOptions");
const { bulkOptimizationBatchSize, ruleOptimizePercent } = require("../config/app");

class OptimizationService {
  getOptimizationData = async (shopId) => {
    const productsCount = await ProductService.countProducts(shopId);

    if (productsCount > 0) {
      const analysisSumData = await ProductAnalysisService.getSumOfScoresByShop(shopId);

      const countHasMetaTitle = await ProductAnalysisService.productsCountHavingMetaTitle(shopId);
      const countHasDesc = await ProductService.hasDescCount(shopId);
      const countHasImgAltTag = await ProductAnalysisService.productsCountHavingImgAltText(shopId);
      const countHasMetaDesc = await ProductAnalysisService.productsCountHavingMetaDesc(shopId);
      const countUniqueTitle = await ProductAnalysisService.productsCountHavingUniqueTitle(shopId);

      const optimizationScores = {
        META_TITLE_WITHIN_CHAR_LIMIT: calculateScorePercent(
          analysisSumData.meta_title_within_char_limit,
          productsCount,
          SCORES.META_TITLE_WITHIN_CHAR_LIMIT
        ),
        DESC_MIN_WORD_COUNT_300: calculateScorePercent(
          analysisSumData.desc_min_word_count_300,
          productsCount,
          SCORES.DESC_MIN_WORD_COUNT_300
        ),
        ALT_TEXT_IN_ALL_IMG: calculateScorePercent(
          analysisSumData.alt_text_in_all_img,
          productsCount,
          SCORES.ALT_TEXT_IN_ALL_IMG
        ),
        UNIQUE_TITLE: calculateScorePercent(analysisSumData.unique_title, productsCount, SCORES.UNIQUE_TITLE),
        META_DESC_WITHIN_CHAR_LIMIT: calculateScorePercent(
          analysisSumData.meta_desc_within_char_limit,
          productsCount,
          SCORES.META_DESC_WITHIN_CHAR_LIMIT
        ),
      };

      return [
        {
          key: "META_TITLE_WITHIN_CHAR_LIMIT",
          title: "Need meta title within 70 characters",
          subTitle: `Meta title found on {{productCount}} products.`,
          value: countHasMetaTitle,
          score: analysisSumData.meta_title_within_char_limit,
          percent: optimizationScores.META_TITLE_WITHIN_CHAR_LIMIT,
          isOptimized: optimizationScores.META_TITLE_WITHIN_CHAR_LIMIT > ruleOptimizePercent,
        },
        {
          key: "DESC_MIN_WORD_COUNT_300",
          title: "Need product description within 50 to 300 words",
          subTitle: `Product description found on {{productCount}} products.`,
          value: countHasDesc,
          score: analysisSumData.desc_min_word_count_300,
          percent: optimizationScores.DESC_MIN_WORD_COUNT_300,
          isOptimized: optimizationScores.DESC_MIN_WORD_COUNT_300 > ruleOptimizePercent,
        },
        {
          key: "ALT_TEXT_IN_ALL_IMG",
          title: "Need alt text on all images",
          subTitle: `Image alt-text found on {{productCount}} products.`,
          value: countHasImgAltTag,
          score: analysisSumData.alt_text_in_all_img,
          percent: optimizationScores.ALT_TEXT_IN_ALL_IMG,
          isOptimized: optimizationScores.ALT_TEXT_IN_ALL_IMG > ruleOptimizePercent,
        },
        {
          key: "UNIQUE_TITLE",
          title: "Need a unique product title",
          subTitle: `Unique title found on {{productCount}} products.`,
          value: countUniqueTitle,
          score: analysisSumData.unique_title,
          percent: optimizationScores.UNIQUE_TITLE,
          isOptimized: optimizationScores.UNIQUE_TITLE > ruleOptimizePercent,
        },
        {
          key: "META_DESC_WITHIN_CHAR_LIMIT",
          title: "Need Meta Description length within 165 characters",
          subTitle: `Meta description found on {{productCount}} products.`,
          value: countHasMetaDesc,
          score: analysisSumData.meta_desc_within_char_limit,
          percent: optimizationScores.META_DESC_WITHIN_CHAR_LIMIT,
          isOptimized: optimizationScores.META_DESC_WITHIN_CHAR_LIMIT > ruleOptimizePercent,
        },
      ];
    }
    return [];
  };

  /**
   * Store information about the auto-optimization task that is going to run
   * @param {number} shopId
   * @param {number} products
   * @returns
   */
  logOptimizationTaskStartInfo = async (shopId, products) => {
    await OptimizationRuns.create({
      shop_id: shopId,
      products_to_optimize: products,
    });
  };

  /**
   * Store information about the finished auto-optimization task by updating the previous related log
   * @param {number} shopId
   * @param {number} optimizedProducts
   * @returns {Promise<any>} updated log data
   */
  logOptimizationTaskFinishInfo = async (shopId, optimizedProducts) => {
    const [count, rows] = await OptimizationRuns.update(
      {
        optimized_products: optimizedProducts,
        optimization_finished_at: Date.now(),
      },
      {
        where: {
          shop_id: shopId,
          optimization_finished_at: null,
        },
        limit: 1,
      }
    );

    return rows[0].toJSON();
  };

  /**
   * Starts the process of auto-optimization for products that are not optimized
   * @param {{ shopId: number, shop: string, metaTemplates: any }} param0
   * @returns
   */
  startProductOptimizationTask = async ({ shopId, shop, metaTemplates }) => {
    const products = await ProductService.getProductsFocusKeywordWithId(shopId, { is_optimized: false });

    console.log(`[${shopId}] Total ${products.length} products found for optimization.`);

    await this.logOptimizationTaskStartInfo(shopId, products.length);
    await cache.productsPendingInAutoOptimizationTask(shop, products);

    // optimize all product
    for (let product of products) {
      await ProductService.addFocusKeywordToProduct(shopId, product);
      dispatchQueue({
        queueName: QUEUE_NAMES.PRODUCT_OPTIMIZE,
        message: {
          shopId,
          shop,
          productId: product.id,
          metaTemplates,
        },
        ttl: 3000,
      });

      await sleep(550);
    }
    return true;
  };

  /**
   * Starts the process of auto-optimization for products
   * @param {{ shopId: number, shop: string, bpoOption: string }} param0
   * @returns
   */
  startProductOptimizationViaBulkOperation = async ({
    shopId,
    shop,
    bpoOption = bpoOptions.NON_OPTIMIZED_PRODUCTS,
  }) => {
    const countCondition = bpoOption === bpoOptions.NON_OPTIMIZED_PRODUCTS ? { is_optimized: false } : undefined;
    const products = await ProductService.countProducts(shopId, countCondition);
    const totalBatches = Math.ceil(products / bulkOptimizationBatchSize);

    console.log(
      `Shop id: [${shopId}], total ${products} products found for optimization divided into ${totalBatches} batches.`
    );

    if (totalBatches > 0) {
      await cache.storeOptimizationTaskStartInfo(shop, totalBatches);
      dispatchQueue({
        queueName: QUEUE_NAMES.PRODUCT_OPTIMIZE_BULK_OPERATION,
        message: { shop, bpoOption },
      });
    }
  };

  optimizeEachProduct = async ({ shopId, productId, metaTemplates }) => {
    try {
      const shop = await ShopService.getShopById(shopId);

      const product = await ProductService.getProductDetails(shopId, productId);
      const session = {
        shop: shop.domain,
        accessToken: shop.access_session,
      };
      const { rdPageTitle, rdMetaDescription, rdAltText } = this.getReplacedMetaData({ shop, product, metaTemplates });

      await this.optimizeProductImage({ product, altText: rdAltText, session });

      await this.optimizeProductMeta({
        product,
        title: rdPageTitle,
        description: rdMetaDescription,
        session,
      });

      return await ProductAnalysisService.analyseEachProduct({ shopId, product });
    } catch (e) {
      throw e;
    }
  };

  serializeProductMeta = (metas, { title, description }) => {
    const metadata = [];

    const metaTitle = metas ? metas.find((pm) => pm.key === METAFIELD_KEYS.TITLE_TAG) : null;
    const titleValue = title?.value.substring(0, 70).trim();
    if (isEmpty(metaTitle) && titleValue) {
      metadata.push({
        namespace: NAMESPACE.GLOBAL,
        key: METAFIELD_KEYS.TITLE_TAG,
        value: titleValue,
        type: METAFIELD_TYPES.STRING,
      });
    } else if (metaTitle) {
      metadata.push({
        ...pick(metaTitle, ["id", "key"]),
        value: titleValue || metaTitle.value,
      });
    }

    const metaDescription = metas ? metas.find((pm) => pm.key === METAFIELD_KEYS.DESCRIPTION_TAG) : null;
    const descValue = description?.value?.substring(0, 165).trim();
    if (isEmpty(metaDescription) && descValue) {
      metadata.push({
        namespace: NAMESPACE.GLOBAL,
        key: METAFIELD_KEYS.DESCRIPTION_TAG,
        value: descValue,
        type: METAFIELD_TYPES.STRING,
      });
    } else if (metaDescription) {
      metadata.push({
        ...pick(metaDescription, ["id", "key"]),
        value: descValue || metaDescription?.value,
      });
    }

    return metadata;
  };

  optimizeProductImage = async ({ product, altText, session }) => {
    if (isEmpty(product.images) || product.images.length === 0) {
      return false;
    }

    const productId = product.id;
    const shopId = product.shop_id;

    const {
      productUpdate: { product: shopifyProduct, userErrors },
    } = await ShopifyService.updateProductImages(session.shop, {
      productId: product.product_id,
      images: product.images
        .sort((i1, i2) => i1.position - i2.position)
        .map((img) => ({
          id: img.gql_id,
          altText: altText.value,
        })),
    });

    if (userErrors.length > 0) {
      console.error("Product Image Optimize Error", userErrors);
      return false;
    }

    await ProductService.upsertProductImages(shopId, productId, shopifyProduct);
  };

  optimizeProductMeta = async ({ product, title, description, session }) => {
    const productId = product.id;
    const shopId = product.shop_id;

    const productMeta = {
      productId: product.product_id,
      metadata: this.serializeProductMeta(product.meta, { title, description }),
      tags: product.tags,
    };

    if (productMeta.metadata.length > 0) {
      const {
        productUpdate: { product: updatedProduct, userErrors },
      } = await ShopifyService.saveShopifyProductMeta(session.shop, productMeta);

      if (userErrors.length > 0) {
        console.error("Product Meta Optimize Error", userErrors, productMeta);
        return false;
      }

      await ProductService.upsertProductMetadata(shopId, productId, updatedProduct);
    }
  };

  getReplacedValue = (template, data) =>
    data.reduce((string, item) => string.replace(item.regExpression, item.value), template);

  getReplacedMetaData = ({ shop, product, metaTemplates }) => {
    const data = [
      { key: "[product_title]", value: product.title?.substring(0, 70), regExpression: /(\[product_title\])/gim },
      {
        key: "[product_description]",
        value: stripTags(product.description).substring(0, 165),
        regExpression: /(\[product_description\])/gim,
      },
      { key: "[product_type]", value: product.product_type, regExpression: /(\[product_type\])/gim },
      { key: "[product_vendor]", value: product.vendor, regExpression: /(\[product_vendor\])/gim },
      { key: "[shop_name]", value: shop.name, regExpression: /(\[shop_name\])/gim },
      { key: "[focus_keyword]", value: product.focus_keyword, regExpression: /(\[focus_keyword\])/gim },
    ];

    const replacedData = Object.keys(metaTemplates).map((key) => ({
      key,
      value: this.getReplacedValue(metaTemplates[key], data),
    }));

    const rdPageTitle = replacedData.find((rd) => rd.key === "pageTitle");
    const rdMetaDescription = replacedData.find((rd) => rd.key === "metaDescription");
    const rdAltText = replacedData.find((rd) => rd.key === "imgAltText");

    return {
      rdPageTitle,
      rdMetaDescription,
      rdAltText,
    };
  };

  createJsonlFileWriteStream = (filePath) => {
    fs.mkdirSync(path.dirname(filePath), {
      recursive: true,
    });

    return fs.createWriteStream(filePath, {
      encoding: "utf-8",
    });
  };

  optimizeProductMetaAndImgAltText = (shop, product, metaTemplates) => {
    const { rdPageTitle, rdMetaDescription, rdAltText } = this.getReplacedMetaData({
      shop,
      product,
      metaTemplates,
    });

    // const media = product.images.map((img) => ({ id: img.media_id, alt: rdAltText.value || img.altText }));
    const metafields = this.serializeProductMeta(product.meta, {
      title: rdPageTitle,
      description: rdMetaDescription,
    });

    return {
      // media,
      metafields,
      rdAltText,
    };
  };

  createInputFileForBulkOperation = async ({
    shop,
    metaTemplates,
    limit,
    skip,
    bpoOption = bpoOptions.NON_OPTIMIZED_PRODUCTS,
  }) => {
    const fileName = "product-update-bulk-operation";
    const mediaFileName = "product-media-update-bulk-operation";
    const fileExt = ".jsonl";
    const filePath = `uploads/${shop.domain}/${fileName}${fileExt}`;
    const mediaFilePath = `uploads/${shop.domain}/${mediaFileName}${fileExt}`;

    const wStream = this.createJsonlFileWriteStream(filePath);
    const wMediaStream = this.createJsonlFileWriteStream(mediaFilePath);

    console.log("Preparing input file for shopify bulk update operation...");

    const conditions = bpoOption === bpoOptions.NON_OPTIMIZED_PRODUCTS ? { is_optimized: false } : undefined;

    const products = await ProductService.getFullProductsByCondition(shop.id, conditions, {
      offset: skip,
      limit,
      order: ["id"],
    });

    const session = { shop: shop.domain, accessToken: shop.access_session };
    for (let product of products) {
      const { metafields, rdAltText } = this.optimizeProductMetaAndImgAltText(shop, product, metaTemplates);

      const images = await ProductService.updateProductMediaData(session, {
        productGqlId: product.product_id,
        images: product.images,
      });

      const productUpdateInput = {
        product: {
          id: product.product_id,
          metafields: metafields.length ? metafields : undefined,
        },
      };

      const mediaUpdateInput = {
        id: product.product_id,
        media: images.length ? images.map((image) => ({ id: image.media_id, alt: rdAltText.value })) : undefined,
      };
      console.log(`[${shop.domain}] - writing product ${product.product_id} into file...`);
      wStream.write(JSON.stringify(productUpdateInput) + "\n");
      if (mediaUpdateInput.media) wMediaStream.write(JSON.stringify(mediaUpdateInput) + "\n");
    }

    wStream.end();
    wMediaStream.end();
    return {
      filePath,
      fileName,
      mediaFilePath,
      mediaFileName,
    };
  };
}

module.exports = new OptimizationService();
