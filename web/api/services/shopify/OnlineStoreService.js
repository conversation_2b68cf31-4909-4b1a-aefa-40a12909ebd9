const { graphQLClient } = require("../../utils/shopify.clients");

const themesQuery = require("../../queries/onlineStore/query.themes.gql");
const metafieldFragment = require("../../queries/fragment.metafield.gql");
const pageInfoFragment = require("../../queries/fragment.page-info.gql");

const pagesQuery = require("../../queries/onlineStore/query.pages.gql");
const pageQuery = require("../../queries/onlineStore/query.page.gql");
const pageFragment = require("../../queries/onlineStore/fragment.page.gql");
const pageUpdateMutation = require("../../queries/onlineStore/mutation.pageUpdate.gql");

const articlesQuery = require("../../queries/onlineStore/query.articles.gql");
const articleQuery = require("../../queries/onlineStore/query.article.gql");
const articleUpdateMutation = require("../../queries/onlineStore/mutation.articleUpdate.gql");
const articleFragment = require("../../queries/onlineStore/fragment.article.gql");
class OnlineStoreService {
  /**
   *
   * @param {string} shop - The shop myshopify domain
   * @param {string[]} [fileNames]
   *
   * @returns {Promise<Theme>}
   */
  async getCurrentTheme(shop, fileNames = []) {
    const { data } = await graphQLClient(shop, {
      query: themesQuery,
      variables: {
        first: 1,
        after: null,
        roles: ["MAIN"],
        fileNames,
      },
    });

    return data.themes?.edges?.[0]?.node;
  }

  /**
   *
   * @param {string} shop - The shop myshopify domain
   * @param {{ first: number, after: string, metafieldKeys: string[]}} param1
   *
   * @returns {Promise<{ pageInfo: PageInfo, pages: any[] }>}
   */
  async getPages(shop, { first = 5, after = null, metafieldKeys = [] } = {}) {
    const { data } = await graphQLClient(shop, {
      query: pagesQuery,
      fragments: [pageFragment, metafieldFragment, pageInfoFragment],
      variables: {
        first,
        after,
        metafieldKeys,
      },
    });

    return { pages: data.pages.edges?.map((e) => e.node), pageInfo: data.pages.pageInfo };
  }

  /**
   *
   * @param {string} shop - The shop myshopify domain
   * @param {Object} param1 - Parameters for the page retrieval
   * @returns {Promise<Page>}
   */
  async getPage(shop, { id, metafieldKeys = [] } = {}) {
    const { data } = await graphQLClient(shop, {
      query: pageQuery,
      fragments: [pageFragment, metafieldFragment],
      variables: {
        id,
        metafieldKeys,
      },
    });

    return data.page;
  }

  /**
   * Updates a specific page in the online store.
   *
   * @param {string} shop - The shop myshopify domain
   * @param {Object} param1 - Parameters for the page update
   * @returns {Promise<any>}
   */
  async updatePage(shop, { id, page, metafieldKeys = [] } = {}) {
    const { data } = await graphQLClient(shop, {
      query: pageUpdateMutation,
      fragments: [pageFragment, metafieldFragment],
      variables: {
        id,
        page,
        metafieldKeys,
      },
    });

    return data.pageUpdate.page;
  }

  /**
   *
   * @param {string} shop - The shop myshopify domain
   * @param {{ first: number, after: string, metafieldKeys: string[]}} param1
   *
   * @returns {Promise<{ pageInfo: PageInfo, articles: any[] }>}
   */
  async getArticles(shop, { first = 5, after = null, metafieldKeys = [] } = {}) {
    const { data } = await graphQLClient(shop, {
      query: articlesQuery,
      fragments: [articleFragment, metafieldFragment, pageInfoFragment],
      variables: {
        first,
        after,
        metafieldKeys,
      },
    });

    return { articles: data.articles.edges?.map((e) => e.node), pageInfo: data.articles.pageInfo };
  }

  /**
   * Retrieves a specific article from the online store.
   *
   * @param {string} shop - The shop myshopify domain
   * @param {Object} param1 - Parameters for the article retrieval
   * @returns {Promise<any>}
   */
  async getArticle(shop, { id, metafieldKeys = [] } = {}) {
    const { data } = await graphQLClient(shop, {
      query: articleQuery,
      fragments: [articleFragment, metafieldFragment],
      variables: {
        id,
        metafieldKeys,
      },
    });

    return data.article;
  }

  /**
   * Updates a specific article in the online store.
   *
   * @param {string} shop - The shop myshopify domain
   * @param {Object} param1 - Parameters for the article update
   * @returns {Promise<any>} - The updated article
   */
  async updateArticle(shop, { id, article, metafieldKeys = [] } = {}) {
    const { data } = await graphQLClient(shop, {
      query: articleUpdateMutation,
      fragments: [articleFragment, metafieldFragment],
      variables: {
        id,
        article,
        metafieldKeys,
      },
    });

    return data.articleUpdate.article;
  }
}

module.exports = OnlineStoreService;

/**
 * @typedef {object} Theme
 * @property {string} id
 * @property {string} name
 * @property {string} role
 */

/**
 * @typedef {object} PageInfo
 * @property {string} endCursor
 * @property {boolean} hasNextPage
 * @property {boolean} hasPreviousPage
 * @property {string} startCursor
 */