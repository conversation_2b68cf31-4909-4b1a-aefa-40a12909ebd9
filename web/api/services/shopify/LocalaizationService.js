const { graphQLClient } = require("../../utils/shopify.clients");

const shopLocales = require("../../queries/localaizations/shopLocales.gql");
const getTranslations = require("../../queries/localaizations/translatableResourcesByIds.gql");
const registerTranslations = require("../../queries/localaizations/registerTranslations.gql");

class LocalaizationService {
  /**
   *
   * @param {string} shop - The shop domain
  
   * @returns {Promise<ShopLocale[]>}
   */
  getShopLocales = async (shop) => {
    const { data } = await graphQLClient(shop, { query: shopLocales });

    return data.shopLocales;
  };

  /**
   *
   * @param {string} shop - The shop domain
   * @param {{ locale: string, resourceIds: string[] }} param0
   * @returns {Promise<{edges: { node: TranslatableResource}[]}>}
   */
  getTranslationsByResourceIds = async (shop, { locale, resourceIds }) => {
    const { data } = await graphQLClient(shop, {
      query: getTranslations,
      variables: {
        locale,
        resourceIds,
      },
    });

    return data.translatableResourcesByIds;
  };

  /**
   *
   * @param {string} shop - The shop domain
   * @param {{ resourceId: string, translations: { key: string, locale: string, translatableContentDigest: string, value :string }[] }} param0
   * @returns {Promise<{ key: string, value: string }[]>}
   */
  registerTranslationsByResourceId = async (shop, { resourceId, translations }) => {
    const { data } = await graphQLClient(shop, {
      query: registerTranslations,
      variables: {
        resourceId,
        translations,
      },
    });

    return data.translationsRegister.translations;
  };
}

module.exports = LocalaizationService;

/**
 * @typedef {object} ShopLocale
 *
 * @property {string} locale
 * @property {string} name
 * @property {boolean} primary
 * @property {boolean} published
 */

/**
 * @typedef {object} TranslatableResource
 *
 * @property {string} resourceId,
 * @property {{ key: string, value: string, locale: string, digest: string}[]} translatableContent
 * @property {{ key: string, value: string, locale: string}[]} translations
 */
