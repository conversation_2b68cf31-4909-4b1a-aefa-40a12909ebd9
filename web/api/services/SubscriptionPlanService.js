const { SubscriptionPlan, Op, SubscriptionAddon } = require("../../sequelize");
const planType = require("storeseo-enums/planType");
const planStatus = require("storeseo-enums/planStatus");
const { subscriptionPlanSerializer } = require("../serializers/SubscriptionSerializer");
const CouponService = require("./CouponService");
const { calculateCouponDiscount } = require("../utils/subscriptionCalculations");
const SubscriptionAddonType = require("storeseo-enums/subscriptionAddonType");
const SubscriptionAddonStatus = require("storeseo-enums/subscriptionAddonStatus");

class SubscriptionPlanService {
  /**
   * Get subscription plan by conditions
   * @param {Object} conditions
   * @param {Array} includes
   * @returns {Promise<null|*>}
   */
  getSubscriptionPlanByConditions = async (conditions, includes = undefined) => {
    const plan = await SubscriptionPlan.findOne({
      where: conditions,
      rejectOnEmpty: false,
      include: includes,
    });
    return plan?.toJSON() || null;
  };
  /**
   * Get subscription plan by slug
   * @param slug
   * @returns {Promise<null|*>}
   */
  getSubscriptionPlanBySlug = async (slug) => {
    return this.getSubscriptionPlanByConditions({ slug });
  };

  /**
   * Get subscription plan by id
   * @param id
   * @param {Array} attributes
   * @returns {Promise<any>}
   */
  getSubscriptionPlan = async (id, attributes = undefined) => {
    const plan = await SubscriptionPlan.findByPk(id, { attributes, rejectOnEmpty: false });
    return plan?.toJSON() || null;
  };

  /**
   * Get subscription plan name & type
   * @param id
   * @returns {Promise<*>}
   */
  getSubscriptionPlanNameAndType = async (id) => {
    const attributes = [
      ["name", "planName"],
      ["slug", "planSlug"],
      ["type", "planType"],
      ["coupon_code", "planCoupon"],
      ["interval", "planInterval"],
    ];
    return await this.getSubscriptionPlan(id, attributes);
  };

  /**
   * Get free subscription plan
   * @returns {Promise<*|null>}
   */
  getFreeSubscriptionPlan = async () => {
    return this.getSubscriptionPlanByConditions({ type: planType.FREE });
  };

  /**
   * Get all active subscription plans
   * @param {number} status
   * @returns {Promise<U[]>}
   */
  getSubscriptionPlans = async (status = planStatus.ACTIVE) => {
    const plans = await SubscriptionPlan.findAll({
      where: { status },
    });

    return plans.map((plan) => plan.toJSON());
  };

  /**
   * Get all active subscription plans
   * @param {Object} conditions
   * @param {Array} order
   * @param {Array} includes
   * @returns {Promise<U[]>}
   */
  getSubscriptionPlansByCondition = async (conditions, order = undefined, includes = undefined) => {
    const plans = await SubscriptionPlan.findAll({
      where: conditions,
      order: [order],
      include: includes,
    });

    return plans.map((plan) => plan.toJSON());
  };

  getConvertedPlan = async (conditions, isMonthly = true) => {
    const plan = await this.getSubscriptionPlanByConditions(conditions);
    if (plan) {
      return plan;
    }
    return null;
  };

  /**
   * Get subscription Plans for dashboard
   * @param activePlanId
   * @returns {Promise<*>}
   */
  getActiveSubscriptionPlans = async (activePlanId, type = null) => {
    const status = this.getStatusByType(type);
    const plans = await this.getSubscriptionPlansByCondition({ status }, ["order", "asc"], ["coupon"]);
    const combinedAddons = await this.getCombinedAddons();
    const activePlan = await this.getSubscriptionPlan(activePlanId);
    return subscriptionPlanSerializer(plans, activePlan, combinedAddons);
  };

  getStatusByType = (type) => {
    switch (type) {
      case "special":
        return planStatus.SPECIAL;
      case "grandfathered":
        return planStatus.HIDDEN;
      default:
        return planStatus.ACTIVE;
    }
  };

  getCombinedAddons = async (ids = [], returenAll = true) => {
    if (ids?.length === 0 && !returenAll) {
      return [];
    }

    const addons = await SubscriptionAddon.findAll({
      where: {
        type: SubscriptionAddonType.COMBINED,
        status: SubscriptionAddonStatus.ACTIVE,
        ...(ids.length > 0 && { id: { [Op.in]: ids } }),
      },
    });
    return addons.map((addon) => addon.toJSON());
  };

  getConvertedSubscriptionPlan = async (shop) => {
    let plan = shop.plan_info;
    // if (shop.plan_info.slug !== name) {
    plan = await this.getSubscriptionPlanByConditions({ slug: name });
    plan.coupon = await CouponService.getLastAppliedCoupon({
      shopId: shop.id,
      appSubscriptionId: shop.appSubscriptionId,
    });

    const activePlan = await this.getSubscriptionPlan(shop.plan_id);

    let calculatedPrice = calculateCouponDiscount(plan, activePlan);
  };

  updatePlan = async (conditions, data) => {
    const [updateCount, plans] = await SubscriptionPlan.update(data, { where: conditions, returning: true });

    return updateCount > 1 ? plans.map((p) => p.toJSON()) : plans[0].toJSON();
  };
}

module.exports = new SubscriptionPlanService();
