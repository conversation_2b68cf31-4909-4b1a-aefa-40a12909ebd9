const { Storage } = require("@google-cloud/storage");
const logger = require("storeseo-logger");
const googleConfig = require("../config/google");

class GoogleBucketService {
  #storage = new Storage({
    projectId: googleConfig.bucket?.project_id,
    credentials: {
      client_id: googleConfig.bucket?.client_id,
      client_email: googleConfig.bucket?.client_email,
      private_key: googleConfig.bucket?.private_key,
    },
  });

  #getBucketClient = async (bucketName = googleConfig.bucketName) => {
    const bucket = this.#storage.bucket(bucketName);
    await bucket.setMetadata({
      iamConfiguration: {
        uniformBucketLevelAccess: {
          enabled: false,
        },
      },
    });

    return bucket;
  };

  uploadMedia = async ({ folder = "", newFileName = "", originalFilePath, makePublic = false }) => {
    try {
      const originalFileName = originalFilePath.split("/").pop();
      let destination = `${folder && folder + "/"}${newFileName || originalFileName}`;

      const bucket = await this.#getBucketClient();
      const [uploadedFile] = await bucket.upload(originalFilePath, {
        destination,
        public: makePublic,
        metadata: {
          customTime: new Date().toISOString(),
        },
      });

      return {
        fileName: uploadedFile?.name,
        publicUrl: makePublic && uploadedFile?.publicUrl(),
      };
    } catch (err) {
      console.log("err: ", err);
      return null;
    }
  };

  removeMedia = async (fileName) => {
    try {
      await (await this.#getBucketClient()).file(fileName).delete();
      console.log(`${fileName} deleted successfully from Google bucket ${googleConfig.bucketName}`);
      return true;
    } catch (err) {
      console.error(`Error removing ${fileName} from Google bucket ${googleConfig.bucketName}.`, err);
      return false;
    }
  };

  downloadMedia = async (fileName, downloadPath) =>
    (await this.#getBucketClient()).file(fileName).download({
      destination: downloadPath,
    });

  /**
   *
   * @param {string} filePath
   */
  isFileExists = async (filePath) => {
    const bucket = await this.#getBucketClient();
    const file = bucket.file(filePath);
    const [exists] = await file.exists();
    return exists;
  };

  deleteFolder = async (folderName) => {
    const bucket = await this.#getBucketClient();

    // Get files in the folder
    const [files] = await bucket.getFiles({
      prefix: folderName,
    });

    if (files.length === 0) {
      console.log("No files found in the folder.");
      return;
    }

    // Delete each file
    const deletePromises = files.map((file) => file.delete());
    await Promise.all(deletePromises);

    console.log(`All files in folder '${folderName}' have been deleted.`);
  };

  /**
   * Uploads a media file to Google Cloud Storage CDN
   * @param {Object} params - The upload parameters
   * @param {string} [params.folder=""] - The destination folder in the bucket
   * @param {string} [params.newFileName=""] - The new name for the uploaded file
   * @param {string} params.originalFilePath - The local path of the file to upload
   * @param {boolean} [params.isPublic=false] - Whether to make the uploaded file publicly accessible
   * @returns {Promise<{fileName: string, publicUrl: string}|null>} Object containing the uploaded file name and public URL (if made public), or null if upload fails
   * @throws {Error} When bucket upload fails
   */
  uploadMediaToCDN = async ({ folder = "", newFileName = "", originalFilePath, isPublic = false }) => {
    try {
      const originalFileName = originalFilePath.split("/").pop();
      let destination = `${folder && folder + "/"}${newFileName || originalFileName}`;

      const bucket = await this.#getBucketClient("cdn.storeseo.com");
      const [uploadedFile] = await bucket.upload(originalFilePath, {
        destination,
        public: isPublic,
        metadata: {
          customTime: new Date().toISOString(),
          cacheControl: "public, max-age=31536000",
        },
      });

      return {
        fileName: uploadedFile?.name,
        publicUrl: isPublic && uploadedFile?.publicUrl().replace("storage.googleapis.com/", ""),
      };
    } catch (err) {
      console.log("err: ", err);
      return null;
    }
  };
}

module.exports = new GoogleBucketService();
