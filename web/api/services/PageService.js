const ShopifyService = require("../services/ShopifyService");
const { isNull, uniq, isEmpty, pick } = require("lodash");
const helper = require("../utils/helper");
const { getPercentValue, calculateIsOptimized, replaceSpecialChars, preparePagination } = require("../utils/helper");
const SCORES = require("../config/pageSeoScores");
const pageType = require("storeseo-enums/pageType");
const logger = require("storeseo-logger");
const HOMEPAGE_SCORES = require("../config/homepageSeoScores");
const { difference } = require("lodash/array");
const webcrawlerService = require("./webcrawlerService");
const { Page, Op, sequelize, Sitemap, PageAnalysis } = require("../../sequelize");
const { serializeShopifyPageData, serializePageSitemap, serializPageMetas } = require("../serializers/PageSerializer");
const PageMetaService = require("./PageMetaService");
const { QueryTypes } = require("sequelize");
const { NAMESPACE, METAFIELD_KEYS, METAFIELD_TYPES, metafieldKeysFilterArray } = require("storeseo-enums/metafields");
const { findSitemapFilter } = require("../utils/sitemapUtils");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const SitemapService = require("./SitemapService");
const { HttpResponseError } = require("@shopify/shopify-api");
const { PageNotFoundFromShopifyError } = require("../../errors");
const { isSubscriptionTestMode } = require("../config/app");

/**
 * @typedef {import('../../jsDocTypes').ArticleDetails} ArticleDetails
 * @typedef {import("../../jsDocTypes").Pagination} Pagination
 * @typedef {import("../../jsDocTypes").Metafield} Metafield
 * @typedef {import("../../jsDocTypes").User} User
 */

class PageService {
  /**
   * Get pages from DB by applying the provided query params
   * @param {number | string} shopId database id of the shop
   * @param {queryParam} queryParams
   * @param fields
   * @returns {Promise<{pagination: Pagination, pages: [PageDetails]}>} Pages list & pagination data
   */
  getAllPagesWithPagination = async (
    shopId,
    {
      page = 1,
      limit = 20,
      search = "",
      sortBy = "page_id",
      sortOrder = "ASC",
      fields = undefined,
      optimize_status = "",
    }
  ) => {
    try {
      const where = {
        shop_id: shopId,
      };

      if (search) {
        where.title = { [Op.iLike]: `%${search}%` };
      }

      sortBy = sortBy === "status" ? "published_at" : sortBy;

      const optimizeStatusCombinationToScoreCondition = {
        "NEED_IMPROVEMENT,NOT_OPTIMIZED,OPTIMIZED": { [Op.gte]: 0 },
        "NEED_IMPROVEMENT,OPTIMIZED": { [Op.gte]: 50 },
        "NOT_OPTIMIZED,OPTIMIZED": { [Op.or]: [{ [Op.gte]: 75 }, { [Op.lt]: 50 }] },
        "NEED_IMPROVEMENT,NOT_OPTIMIZED": { [Op.gte]: 0, [Op.lt]: 75 },
        OPTIMIZED: { [Op.gte]: 75 },
        NEED_IMPROVEMENT: { [Op.gte]: 50, [Op.lt]: 75 },
        NOT_OPTIMIZED: { [Op.lt]: 50 },
      };
      optimize_status = optimize_status.split(",").sort().join(",");
      if (optimizeStatusCombinationToScoreCondition.hasOwnProperty(optimize_status)) {
        where.score = optimizeStatusCombinationToScoreCondition[optimize_status];
      }

      let offset = (page - 1) * limit;
      sortBy = sortBy === "optimize_status" ? "score" : sortBy;
      let order = [[sortBy, sortOrder]];

      let { count, rows: pages } = await Page.findAndCountAll({
        attributes: fields,
        where,
        limit,
        offset,
        order,
      });

      return {
        pages: pages.map((p) => p.toJSON()) || [],
        pagination: preparePagination(count, page, limit),
      };
    } catch (error) {
      console.log(error);
      return null;
    }
  };

  /**
   *
   * @param {number} shopId
   * @param {{ limit: number, fields?: string[]}} param1
   */
  getTopScoredPages = async (shopId, { limit = 5, fields = undefined }) => {
    const sortBy = "score";
    const sortOrder = "DESC";
    const order = [[sortBy, sortOrder]];

    const pages = await Page.findAll({
      attributes: [...fields, "page_id"],
      where: { shop_id: shopId },
      limit,
      order,
    });

    return pages.map((p) => p.toJSON());
  };

  /**
   *
   * @param {number} shopId
   * @param {{ limit: number, fields?: string[]}} param1
   */
  getLeastScoredPages = async (shopId, { limit = 5, fields = undefined }) => {
    const sortBy = "score";
    const sortOrder = "ASC";
    const order = [[sortBy, sortOrder]];

    const pages = await Page.findAll({
      attributes: [...fields, "page_id"],
      where: { shop_id: shopId },
      limit,
      order,
    });

    return pages.map((p) => p.toJSON());
  };

  /**
   *
   * @param {number | string} shopId database id of the shop
   * @returns {Promise<[PageDetails]>} list of all pages for the shop
   */
  getAllPages = async (shopId) => {
    try {
      const pages = await Page.findAll({ where: { shop_id: shopId }, include: ["meta"] });
      return pages.map((p) => p.toJSON());
    } catch (error) {
      return null;
    }
  };

  /**
   *
   * @param {number | string} shopId database id of the shop
   * @returns {Promise<[{ id: PageDetails['id'], page_id: PageDetails['page_id'] }]>} List of page objects with id field only
   */
  getAllPageIds = async (shopId) => {
    try {
      const pages = await Page.findAll({
        where: { shop_id: shopId, page_type: pageType.PAGE },
        attributes: ["id", "page_id"],
      });

      return pages.map((p) => p.toJSON());
    } catch (error) {
      return null;
    }
  };

  /**
   *
   * @param {number} shopId
   * @param {import("sequelize").WhereOptions} conditions
   */
  iterateOverPages = async function* (shopId, conditions = {}) {
    let limit = 30;
    let offset = 0;
    let pages = [1];

    while (pages.length > 0) {
      pages = await Page.findAll({
        where: { shop_id: shopId, ...conditions },
        limit,
        offset,
        include: ["meta"],
      });

      offset += limit;

      yield pages.map((p) => p.toJSON());
    }
  };

  /**
   * Get a shopify page details from DB using shop id & page id (DB Ids only)
   * @param {(number | string)} shopId DB id of the relevant shop
   * @param {number | string} pageId DB id of the page
   * @returns {Promise<PageDetails>} Complete page details retrieved from DB
   */
  getPage = async (shopId, pageId) => {
    try {
      const p = await Page.findOne({
        where: { id: pageId, shop_id: shopId },
        include: [
          "meta",
          {
            model: PageAnalysis,
            as: "analysisData",
            required: false,
          },
        ],
      });
      const page = p.toJSON();
      return { ...page, meta: serializPageMetas(page.meta) };
    } catch (error) {
      return null;
    }
  };
  /**
   *
   * @param {*} shopId
   * @param {*} pageId - Shopify page id
   * @returns
   */
  getPageByPageId = async (shopId, pageId) => {
    try {
      const p = await Page.findOne({
        where: { page_id: pageId, shop_id: shopId },
        include: [
          "meta",
          {
            model: PageAnalysis,
            as: "analysisData",
            required: false,
          },
        ],
      });
      const page = p.toJSON();
      return { ...page, meta: serializPageMetas(page.meta) };
    } catch (error) {
      return null;
    }
  };

  /**
   *
   * @param {number} shopId
   * @param {number} pageId
   * @returns {Promise<{ prevId: string | null, nextId: string | null}>}
   */
  idsOfThePrevAndNextPage = async (shopId, pageId) => {
    const [[result]] = await sequelize.query(
      `
    SELECT 
      MAX(CASE WHEN id < :pageId THEN id END) as prevId,
      MIN(CASE WHEN id > :pageId THEN id END) as nextId
    FROM pages 
    WHERE shop_id=:shopId and id != :pageId 
    Group by shop_id
    `,
      {
        replacements: { shopId, pageId },
      }
    );
    return result;
  };

  getPaginationOfPage = async (shopId, pageId) => {
    const paginationQuery = `
    WITH virtual_table AS (
      SELECT
        page_id,
        lag(page_id) OVER w AS prev,
        lead(page_id) OVER w AS next
      FROM pages
      WHERE shop_id = :shopId
      WINDOW w AS (ORDER BY page_id)
    )

    SELECT prev, next FROM virtual_table WHERE page_id = :pageId;
  `;

    const [result] = await sequelize.query(paginationQuery, {
      replacements: { shopId, pageId },
      type: QueryTypes.SELECT,
    });

    return result;
  };

  /**
   * Extract the social media images from page metafields array
   * @param {PageDetails} page
   * @returns {{ facebookPreviewImage: string, twitterPreviewImage: string }} Preview image urls for facebook & twitter
   */
  getSocialMediaPreviewImages = (page) => {
    let facebookPreviewImage = null;
    let twitterPreviewImage = null;

    try {
      page.meta.forEach((m) => {
        if (m.namespace === NAMESPACE.STORE_SEO && m.key === METAFIELD_KEYS.FACEBOOK_PREVIEW_IMAGE_URL) {
          facebookPreviewImage = m.value;
        } else if (m.namespace === NAMESPACE.STORE_SEO && m.key === METAFIELD_KEYS.TWITTER_PREVIEW_IMAGE_URL) {
          twitterPreviewImage = m.value;
        }
      });
    } catch (err) {}

    return { facebookPreviewImage, twitterPreviewImage };
  };

  markPagesAsNotSynced = async (shopId) => {
    await Page.update(
      {
        is_synced: false,
      },
      {
        where: { shop_id: shopId },
      }
    );
  };

  deleteNotSyncedPages = async (shopId) => {
    let pagesToDelete = await Page.findAll({ where: { shop_id: shopId, is_synced: false } });

    const AnalysisService = require("./AnalysisService");
    for (let page of pagesToDelete) {
      const pageId = page.toJSON().id;
      await page.destroy();
      await PageMetaService.deleteMeta(pageId);
      await AnalysisService.deletePageAnalysis(pageId);
    }
  };

  /**
   * Delete pages from DB that are not found in the shopify for a store
   * @param {User} user
   * @param {number | string} shopId
   * @returns
   */
  deleteRemovedPages = async (user, shopId) => {
    try {
      const shopifyPages = await ShopifyService.getShopifyPages(user);
      const pages = await Page.findAll({ where: { shop_id: shopId, page_type: pageType.PAGE } });

      const pageIds = pages.map((page) => Number(page.toJSON().page_id));
      const shopifyPageIds = shopifyPages.map((page) => page.id);

      const pagesToDelete = difference(pageIds, shopifyPageIds);

      pagesToDelete.forEach(async (pageId) => {
        const page = pages.find((p) => p.page_id == pageId);
        await page.destroy();
      });

      const dbIds = pages
        .map((p) => p.toJSON())
        .filter((p) => pagesToDelete.includes(p.page_id))
        .map((p) => p.id);
      await PageMetaService.deleteMeta(dbIds);
    } catch (error) {
      return null;
    }
  };

  /**
   *
   * @param {*} shopId
   * @param {*} pageId - Shopify page id
   */
  deletePageById = async (shopId, pageId) => {
    const page = await this.getPage(shopId, pageId);
    if (!page) return;
    await Page.destroy({ where: { shop_id: shopId, page_id: page.page_id } });
    await PageMetaService.deleteMeta(pageId);

    const AnalysisService = require("./AnalysisService");
    await AnalysisService.deletePageAnalysis(pageId);
  };

  getDefaultAnalysisScore = () => {
    const score = {};
    for (const key in SCORES) {
      score[key] = {};
      for (const subKey in SCORES[key]) {
        score[key][subKey] = 0;
      }
    }
    return score;
  };

  countIssues = () => {
    let count = 0;
    for (const key in SCORES) {
      count += Object.keys(SCORES[key]).length;
    }
    return count;
  };

  /**
   * Save a new shopify page in DB
   * @param {PageDetails} data
   * @returns {Promise<PageDetails>} the page created in DB
   */
  savePage = async (data) => {
    try {
      const page = await Page.create({ ...data });
      return page.toJSON();
    } catch (error) {
      console.log("Page save error", error);
      return null;
    }
  };

  /**
   * Update a page in DB with the provided input
   * @param {integer} pageId DB page id
   * @param {PageDetails} data Page details object
   * @returns {Promise<PageDetails>} Updated page from DB
   */
  updatePage = async (pageId, data) => {
    try {
      const [affectedRows, pages] = await Page.update(data, {
        returning: true,
        where: { id: pageId },
      });

      if (affectedRows === 1) return pages[0].toJSON();

      return pages.map((p) => p.toJSON());
    } catch (error) {
      return null;
    }
  };

  /**
   * Update page(s) data by condition
   * @param conditions
   * @param data
   * @returns {Promise<*>}
   */
  updatePageByCondition = async (conditions, data) => {
    const [affectedRows, pages] = await Page.update(data, {
      where: conditions,
      returning: true,
    });

    if (affectedRows === 1) {
      return pages[0].toJSON();
    }

    return pages.map((p) => p.toJSON());
  };

  /**
   * Extract metadata from homepage body html content
   * @param {string} bodyHtml
   * @returns {[Metafield]} Array of title & description metafields
   */
  getHomePageMetafieldsFromHtml = (bodyHtml) => {
    const { title, description } = webcrawlerService.getPageTitleAndDescription(bodyHtml);
    const namespace = NAMESPACE.GLOBAL;
    const type = METAFIELD_TYPES.SINGLE_LINE_TEXT;

    return [
      {
        namespace,
        key: METAFIELD_KEYS.TITLE_TAG,
        type,
        value: title || "",
      },
      {
        namespace,
        key: METAFIELD_KEYS.DESCRIPTION_TAG,
        type,
        value: description || "",
      },
    ];
  };

  /**
   * Update existing homepage data or insert in DB
   * @param {User} user user to object to access shopify api
   * @param {number} shopId DB id of the relevant shop
   * @returns {Promise<PageDetails>} Updated/created homepage data from DB
   */
  saveOrUpdateHomepage = async (user, shopId) => {
    try {
      let url = user.url;
      if (isSubscriptionTestMode) {
        url = "https://wpdevshop.myshopify.com/";
      }

      const bodyHtml = await webcrawlerService.crawlPage(url);
      const metafields = this.getHomePageMetafieldsFromHtml(bodyHtml);

      const data = {
        shop_id: shopId,
        page_id: 1,
        title: metafields[0].value,
        handle: "",
        author: "",
        body_html: "",
        published_at: new Date().toISOString(),
        // metafields: metafields,
        page_type: pageType.HOMEPAGE,
        is_synced: true,
      };

      console.log("Page Title", data.title, url);

      const [saved] = await Page.upsert(data, { returning: true });
      let page = saved.toJSON();

      page.meta = await PageMetaService.upsertMetas(shopId, page.id, metafields, true);
      return page;
    } catch (err) {
      console.error(`Error save or update homepage for shopId: ${shopId}`, err);
      return null;
    }
  };

  /**
   *
   * @param {*} shopId
   * @param {*} shopifyPage shopify page serialized
   */
  saveOrUpdatePage = async (shopId, shopifyPage) => {
    const { meta, ...pageData } = shopifyPage;
    const [saved] = await Page.upsert(pageData, { returning: true });
    const page = saved.toJSON();

    await PageMetaService.upsertMetas(shopId, page.id, meta);
    return this.getPageByPageId(shopId, page.page_id);
  };

  /**
   * Fetch all pages from shopify api & update/insert into DB
   * @param {User} user
   * @param {number} shopId
   * @returns {Promise<import("../../jsDocTypes").PageDetails[]>} list of saved/updated page in DB
   */
  saveOrUpdatePagesFromShopify = async (user, shopId) => {
    try {
      const pages = await ShopifyService.getShopifyPages(user);
      // console.log("pages fetched from shopify: ", pages.length);

      return await Promise.all(
        pages.map(async (page) => {
          const pageData = serializeShopifyPageData(shopId, page);
          const pageInDb = await this.getPageByPageId(shopId, page.id);
          if (isNull(pageInDb)) {
            return await this.savePage(pageData);
          }
          return await this.updatePage(pageInDb.id, pageData);
        })
      );
    } catch (error) {
      console.log("saveOrUpdatePagesFromShopify error", error);
      return [];
    }
  };

  /**
   * Save or update a single page in DB from shopify
   * @param {User} user
   * @param {number} shopId
   * @param {number | string} pageId shopify page id
   * @returns {Promise<PageDetails>} created/updated page in DB
   */
  saveOrUpdatePageFromShopify = async (user, shopId, pageId) => {
    try {
      const page = await ShopifyService.getShopifyPage(user, pageId);
      const pageData = serializeShopifyPageData(shopId, page);
      const pageInDb = await this.getPageByPageId(shopId, page.id);
      if (isNull(pageInDb)) {
        return await this.savePage(pageData);
      }
      return await this.updatePage(pageInDb.id, pageData);
    } catch (error) {
      if (error instanceof HttpResponseError && error.response.code === 404) {
        throw new PageNotFoundFromShopifyError(pageId, {
          cause: error,
        });
      }
      return null;
    }
  };

  /**
   * Fetch metafields from shopify & update them in DB
   * @param {User} user
   * @param {number} shopId
   * @param {number | string} pageId shopify page id
   * @returns {Promise<{limitRemaining: number, updatedPage: PageDetails}>}
   */
  updatePageMetafieldsFromShopify = async (user, shopId, pageId) => {
    try {
      const page = await this.getPage(shopId, pageId);

      await PageMetaService.deleteMeta(pageId);

      const { metafields, limitRemaining } = await ShopifyService.getShopifyPageMetafields(user, page.page_id);
      const savedMetas = await PageMetaService.upsertMetas(page.shop_id, page.id, metafields);
      const updatedPage = { ...page, meta: savedMetas };
      return { limitRemaining, updatedPage };
    } catch (error) {
      console.log("updatePageMetafieldsFromShopify error", error);
      return { limitRemaining: null, updatedPage: null };
    }
  };

  /**
   * Generate focus keyword suggestions
   * @param {object} param
   * @param {string} param.title meta title string
   * @param {string} param.descripiton meta description string
   * @param {[Metafield]} param.meta list of metafields
   * @returns {[string]} focus keyword suggestions
   */
  getPageFocusKeywordSuggestions = ({ title, description, meta }) => {
    const suggestedKeywords = [];
    const metaTags = [METAFIELD_KEYS.TITLE_TAG, METAFIELD_KEYS.DESCRIPTION_TAG];
    const metaText = meta
      ?.filter((m) => metaTags.includes(m?.key) && m?.value)
      .map((m) => m?.value)
      .join(". ")
      .toLowerCase();
    suggestedKeywords.push(...helper.extractKeywords(metaText));

    suggestedKeywords.push(...helper.extractKeywords(title.toLowerCase()));
    suggestedKeywords.push(...helper.extractKeywords(description.toLowerCase()));

    return uniq(suggestedKeywords.filter((k) => k?.trim()));
  };

  serializeHomePageOptimizationDetails = (analysis) => {
    return [
      {
        title: "Basic SEO Analysis",
        key: "BASIC_SEO",
        values: [
          {
            key: "FOCUS_KEYWORD_IN_META_TITLE",
            title: "Focus keyword used in page title",
            value: analysis?.focus_keyword_in_meta_title,
            percent: getPercentValue(
              analysis?.focus_keyword_in_meta_title,
              HOMEPAGE_SCORES.FOCUS_KEYWORD_IN_META_TITLE
            ),
            isOptimized: calculateIsOptimized(
              analysis?.focus_keyword_in_meta_title,
              HOMEPAGE_SCORES.FOCUS_KEYWORD_IN_META_TITLE
            ),
          },
          {
            key: "META_TITLE_WITHIN_CHAR_LIMIT",
            title: "Page title within 70 characters",
            value: analysis?.meta_title_within_char_limit,
            percent: getPercentValue(
              analysis?.meta_title_within_char_limit,
              HOMEPAGE_SCORES.META_TITLE_WITHIN_CHAR_LIMIT
            ),
            isOptimized: calculateIsOptimized(
              analysis?.meta_title_within_char_limit,
              HOMEPAGE_SCORES.META_TITLE_WITHIN_CHAR_LIMIT
            ),
          },
          {
            key: "FOCUS_KEYWORD_IN_META_DESC",
            title: "Focus keyword used in meta description",
            value: analysis?.focus_keyword_in_meta_desc,
            percent: getPercentValue(analysis?.focus_keyword_in_meta_desc, HOMEPAGE_SCORES.FOCUS_KEYWORD_IN_META_DESC),
            isOptimized: calculateIsOptimized(
              analysis?.focus_keyword_in_meta_desc,
              HOMEPAGE_SCORES.FOCUS_KEYWORD_IN_META_DESC
            ),
          },
          {
            key: "META_DESC_WITHIN_CHAR_LIMIT",
            title: "Meta descripiton within 165 characters",
            value: analysis?.meta_desc_within_char_limit,
            percent: getPercentValue(
              analysis?.meta_desc_within_char_limit,
              HOMEPAGE_SCORES.META_DESC_WITHIN_CHAR_LIMIT
            ),
            isOptimized: calculateIsOptimized(
              analysis?.meta_desc_within_char_limit,
              HOMEPAGE_SCORES.META_DESC_WITHIN_CHAR_LIMIT
            ),
          },
        ],
      },
    ];
  };

  serializePageOptimizationDetails = (analysis) => {
    return [
      {
        title: "Basic SEO Analysis",
        key: "BASIC_SEO",
        values: [
          {
            key: "UNIQUE_FOCUS_KEYWORD",
            title: "Focus keyword is unique",
            value: analysis?.unique_focus_keyword,
            percent: getPercentValue(analysis?.unique_focus_keyword, SCORES?.UNIQUE_FOCUS_KEYWORD),
            isOptimized: calculateIsOptimized(analysis?.unique_focus_keyword, SCORES?.UNIQUE_FOCUS_KEYWORD),
          },
          {
            key: "FOCUS_KEYWORD_IN_INTRODUCTION",
            title: "Focus keyword is used in the introduction",
            value: analysis?.focus_keyword_in_introduction,
            percent: getPercentValue(analysis?.focus_keyword_in_introduction, SCORES?.FOCUS_KEYWORD_IN_INTRODUCTION),
            isOptimized: calculateIsOptimized(
              analysis?.focus_keyword_in_introduction,
              SCORES?.FOCUS_KEYWORD_IN_INTRODUCTION
            ),
          },
          {
            key: "CONTENT_MORE_THEN_300_WORDS",
            title: "Content should be more than 300 words",
            value: analysis?.content_more_then_300_words,
            percent: getPercentValue(analysis?.content_more_then_300_words, SCORES?.CONTENT_MORE_THEN_300_WORDS),
            isOptimized: calculateIsOptimized(
              analysis?.content_more_then_300_words,
              SCORES?.CONTENT_MORE_THEN_300_WORDS
            ),
          },
          {
            key: "FOCUS_KEYWORD_DENSITY",
            title: "Focus keyword is used 1-2% times of the page content",
            value: analysis?.focus_keyword_density,
            percent: getPercentValue(analysis?.focus_keyword_density, SCORES?.FOCUS_KEYWORD_DENSITY),
            isOptimized: calculateIsOptimized(analysis?.focus_keyword_density, SCORES?.FOCUS_KEYWORD_DENSITY),
          },
        ],
      },
      {
        title: "Detailed SEO Analysis",
        key: "DETAILED_SEO",
        values: [
          {
            key: "FOCUS_KEYWORD_IN_IMG_ALT_TEXT",
            title: "Focus keyword is used in image alt text",
            value: analysis?.focus_keyword_in_img_alt_text,
            percent: getPercentValue(analysis?.focus_keyword_in_img_alt_text, SCORES?.FOCUS_KEYWORD_IN_IMG_ALT_TEXT),
            isOptimized: calculateIsOptimized(
              analysis?.focus_keyword_in_img_alt_text,
              SCORES?.FOCUS_KEYWORD_IN_IMG_ALT_TEXT
            ),
          },
          {
            key: "FOCUS_KEYWORD_IN_SUBHEADING",
            title: "Focus keyword found in subheadings",
            value: analysis?.focus_keyword_in_subheading,
            percent: getPercentValue(analysis?.focus_keyword_in_subheading, SCORES?.FOCUS_KEYWORD_IN_SUBHEADING),
            isOptimized: calculateIsOptimized(
              analysis?.focus_keyword_in_subheading,
              SCORES?.FOCUS_KEYWORD_IN_SUBHEADING
            ),
          },
          {
            key: "FOCUS_KEYWORD_IN_META_DESC",
            title: "Focus keyword found in meta description",
            value: analysis?.focus_keyword_in_meta_desc,
            percent: getPercentValue(analysis?.focus_keyword_in_meta_desc, SCORES?.FOCUS_KEYWORD_IN_META_DESC),
            isOptimized: calculateIsOptimized(analysis?.focus_keyword_in_meta_desc, SCORES?.FOCUS_KEYWORD_IN_META_DESC),
          },
          {
            key: "FOCUS_KEYWORD_IN_URL",
            title: "Focus Keyword used in the URL",
            value: analysis?.focus_keyword_in_url,
            percent: getPercentValue(analysis?.focus_keyword_in_url, SCORES?.FOCUS_KEYWORD_IN_URL),
            isOptimized: calculateIsOptimized(analysis?.focus_keyword_in_url, SCORES?.FOCUS_KEYWORD_IN_URL),
          },
          {
            key: "META_DESC_WITHIN_160_CHAR",
            title: "Meta description must be within 160 characters",
            value: analysis?.meta_desc_within_160_char,
            percent: getPercentValue(analysis?.meta_desc_within_160_char, SCORES?.META_DESC_WITHIN_160_CHAR),
            isOptimized: calculateIsOptimized(analysis?.meta_desc_within_160_char, SCORES?.META_DESC_WITHIN_160_CHAR),
            hint: "To get higher SEO score, write a meta description within 160 characters",
          },
          {
            key: "FOCUS_KEYWORD_IN_META_TITLE",
            title: "Focus keyword is used in the meta title",
            value: analysis?.focus_keyword_in_meta_title,
            percent: getPercentValue(analysis?.focus_keyword_in_meta_title, SCORES?.FOCUS_KEYWORD_IN_META_TITLE),
            isOptimized: calculateIsOptimized(
              analysis?.focus_keyword_in_meta_title,
              SCORES?.FOCUS_KEYWORD_IN_META_TITLE
            ),
          },
          {
            key: "INTERNAL_LINK_IN_CONTENT",
            title: "1 internal link found in page content",
            value: analysis?.internal_link_in_content,
            percent: getPercentValue(analysis?.internal_link_in_content, SCORES?.INTERNAL_LINK_IN_CONTENT),
            isOptimized: calculateIsOptimized(analysis?.internal_link_in_content, SCORES?.INTERNAL_LINK_IN_CONTENT),
          },
          {
            key: "EXTERNAL_LINK_IN_CONTENT",
            title: "1 external link found in page content with do-follow",
            value: analysis?.external_link_in_content,
            percent: getPercentValue(analysis?.external_link_in_content, SCORES?.EXTERNAL_LINK_IN_CONTENT),
            isOptimized: calculateIsOptimized(analysis?.external_link_in_content, SCORES?.EXTERNAL_LINK_IN_CONTENT),
          },
          {
            key: "ALT_TEXT_IN_ALL_IMG",
            title: "Add alt text to all images",
            value: analysis?.alt_text_in_all_img,
            percent: getPercentValue(analysis?.alt_text_in_all_img, SCORES?.ALT_TEXT_IN_ALL_IMG),
            isOptimized: calculateIsOptimized(analysis?.alt_text_in_all_img, SCORES?.ALT_TEXT_IN_ALL_IMG),
          },
          // {
          //   key: "UNIQUE_PAGE_CONTENT",
          //   title: "Page content is unique",
          //   value: analysis?.DETAILED_SEO?.UNIQUE_PAGE_CONTENT,
          //   percent: getPercentValue(
          //     analysis?.DETAILED_SEO?.UNIQUE_PAGE_CONTENT,
          //     SCORES?.DETAILED_SEO?.UNIQUE_PAGE_CONTENT
          //   ),
          //   isOptimized: calculateIsOptimized(
          //     analysis?.DETAILED_SEO?.UNIQUE_PAGE_CONTENT,
          //     SCORES?.DETAILED_SEO?.UNIQUE_PAGE_CONTENT
          //   ),
          // },
        ],
      },
    ];
  };

  /**
   * Update page data in DB & shopify
   * @param {number | string} shopId DB id of relevant shop
   * @param {number | string} pageId DB id of the page
   * @param {*} body object containing metaTitle, metaDescription, focusKeyword, tags
   * @param {User} user
   * @returns {Promise<PageDetails>} update page from DB
   */
  updatePageData = async (shopId, pageId, body, session) => {
    const page = await this.getPage(shopId, pageId);

    const { metaTitle, metaDescription, focusKeyword, tags, handle = undefined, createRedirectUrl = false } = body;

    let updateData = {
      focus_keyword: replaceSpecialChars(focusKeyword),
      tags: tags.length > 0 ? tags : null,
    };

    const metadata = this.serializePageUpdatedMetadata(page, { metaTitle, metaDescription });
    let updatedMeta = null;

    if (page.page_type !== pageType.HOMEPAGE && page.page_type !== pageType.BETTERDOCS_HOMEPAGE) {
      const { page: updatedPage } = await ShopifyService.updateOrCreateShopifyPageMetafields(
        session,
        page.page_id,
        metadata,
        handle
      );
      updateData.handle = updatedPage.handle;

      const { metafields } = await ShopifyService.getShopifyPageMetafields(session, page.page_id);
      updatedMeta = await PageMetaService.upsertMetas(shopId, pageId, metafields);
    }

    if (createRedirectUrl) {
      let urlRedirect = await ShopifyService.createRedirectURL(session.shop, {
        oldPath: `/pages/${page.handle}`,
        newPath: `/pages/${handle}`,
      });
    }

    const [affectedRows, updatedPages] = await Page.update(updateData, {
      returning: true,
      where: { id: pageId, shop_id: shopId },
    });

    return { ...updatedPages[0].toJSON(), meta: updatedMeta || metadata };
  };

  serializePageUpdatedMetadata = (page, { metaTitle, metaDescription }) => {
    const metadata = [];

    const pageMetaTitle = !isEmpty(page.meta) ? page.meta.find((md) => md.key === METAFIELD_KEYS.TITLE_TAG) : null;

    if (metaTitle !== undefined) {
      if (!isEmpty(pageMetaTitle)) {
        metadata.push({ ...pick(pageMetaTitle, ["id", "key", "namespace", "type"]), value: metaTitle });
      } else {
        metadata.push({
          namespace: NAMESPACE.GLOBAL,
          key: METAFIELD_KEYS.TITLE_TAG,
          value: metaTitle,
          type: METAFIELD_TYPES.STRING,
        });
      }
    }

    if (metaDescription !== undefined) {
      const pageMetaDescription = !isEmpty(page.meta)
        ? page.meta.find((md) => md.key === METAFIELD_KEYS.DESCRIPTION_TAG)
        : null;
      if (!isEmpty(pageMetaDescription)) {
        metadata.push({
          ...pick(pageMetaDescription, ["id", "key", "namespace", "type"]),
          value: metaDescription,
        });
      } else {
        metadata.push({
          namespace: NAMESPACE.GLOBAL,
          key: METAFIELD_KEYS.DESCRIPTION_TAG,
          value: metaDescription,
          type: METAFIELD_TYPES.STRING,
        });
      }
    }

    return metadata;
  };

  serializePageForAnalysis = async (shopId, pageId, data) => {
    const { metaTitle, metaDescription, focusKeyword, handle } = data;
    const page = await this.getPageByPageId(shopId, pageId);
    const metadata = this.serializePageUpdatedMetadata(page, { metaTitle, metaDescription });

    const updateData = {
      meta: metadata.length > 0 ? metadata : page.meta,
      focus_keyword: focusKeyword ? replaceSpecialChars(focusKeyword) : page.focus_keyword,
      handle,
    };

    return { ...page, ...updateData };
  };

  /**
   * Delete all pages for a shop
   * @param {number | string} shopId
   */
  deletePageOfShop = async (shopId) => {
    try {
      await Page.destroy({ where: { shop_id: shopId } });
    } catch (e) {
      console.log(e);
    }
  };

  /**
   * Counts
   * @param shopId
   * @param {import("sequelize").WhereOptions} [conditions]
   * @returns {Promise<number>}
   */
  count = async (shopId, conditions = {}) => {
    return Page.count({ where: { shop_id: shopId, ...conditions } });
  };

  getPagesByShopId = async (shopId, fields = [], limit = null) => {
    const pages = await Page.findAll({
      where: { shop_id: shopId, published_at: { [Op.not]: null } },
      attributes: ["id", ...fields],
      limit,
    });

    return pages.map((p) => p.toJSON());
  };

  getSitemapEnabledPagesByShopId = async (shopId, fields = [], limit = null) => {
    const pages = await Page.findAll({
      where: {
        shop_id: shopId,
        published_at: { [Op.not]: null },
      },
      attributes: ["id", ...fields],
      limit,
      include: [
        {
          model: Sitemap,
          as: "sitemap",
          attributes: ["resource_id", "resource_type", "sitemap_disabled"],
          where: {
            resource_type: analysisEntityTypes.PAGE,
            sitemap_disabled: 0,
          },
        },
      ],
    });

    return pages.map((p) => p.toJSON());
  };

  /**
   *
   * @param shopId
   * @param page
   * @param limit
   * @param search
   * @param filterOn
   * @param filterValue
   * @param sortBy
   * @param sortOrder
   * @returns {Promise<{pagination: Promise<{pageCount: number, pageSize, page, rowCount: Promise<number>}>, sitemaps: ((*&{featuredImage: {altText: *, src: *, id: *}|null, noFollow: boolean, isChecked: boolean, noIndex: boolean, status: boolean})[]|*[])}>}
   */
  getSitemaps = async (
    shopId,
    {
      page = 1,
      limit = 20,
      search = "",
      filterOn = "sitemap",
      filterValue = "-1",
      sortBy = "created_at",
      sortOrder = "DESC",
    }
  ) => {
    const offset = limit * (page - 1);

    const where = { shop_id: shopId };

    let order = [[sortBy, sortOrder]];

    if (search) {
      where.title = { [Op.iLike]: `%${search}%` };
    }

    const sitemapWhere = findSitemapFilter(filterOn, filterValue);

    const { count, rows } = await Page.findAndCountAll({
      attributes: ["id", "shop_id", "page_id", "title", "handle"],
      where,
      include: [
        {
          model: Sitemap,
          as: "sitemap",
          where: [sitemapWhere, { resource_type: analysisEntityTypes.PAGE }],
          required: true,
          attributes: ["id", "sitemap_disabled", "no_index", "no_follow"],
        },
      ],
      limit,
      offset,
      order,
    });

    return {
      sitemaps: rows.map((p) => serializePageSitemap(p)) || [],
      pagination: preparePagination(count, page, limit),
    };
  };

  /**
   * Updates sitemap data to shopify and database
   * @param shopId
   * @param session
   * @param sitemap
   * @returns {Promise<void>}
   */
  updateSitemap = async ({ shopId, session, sitemap }) => {
    const page = await this.getPageByPageId(shopId, sitemap.id);

    let newStatus = sitemap.status ? "0" : "1";
    const metaInput = [
      {
        key: METAFIELD_KEYS.HIDDEN,
        value: newStatus,
        ownerId: page.shopify_gql_id,
      },
    ];

    await ShopifyService.setMetafields(session.shop, metaInput);

    const shopifyPage = await ShopifyService.onlineStore.getPage(session.shop, {
      id: page.shopify_gql_id,
      metafieldKeys: metafieldKeysFilterArray,
    });
    const serializedShopifyPage = serializeShopifyPageData(shopId, shopifyPage);
    await this.saveOrUpdatePage(shopId, serializedShopifyPage);

    await SitemapService.updateSitemapData({
      shopId,
      resourceId: page.id,
      resourceType: analysisEntityTypes.PAGE,
      metaKey: METAFIELD_KEYS.SITEMAP_DISABLED,
      metaStatus: newStatus,
    });
  };
}

module.exports = new PageService();
