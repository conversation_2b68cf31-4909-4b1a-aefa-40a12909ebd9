const ArticleService = require("./ArticleService");
const { PageAnalysis, ArticleAnalysis, sequelize } = require("../../sequelize");
const { stripTags, sleep, getMetaDescription, getMetaTitle, dataGet, replaceSpecialChars } = require("../utils/helper");
const pageScore = require("../utils/pageScoreCalculations");
const articleScore = require("../utils/articleScoreCalculations");
const commonScore = require("../utils/commonScoreCalculations");
const types = require("storeseo-enums/analysisEntityTypes");
const pageType = require("storeseo-enums/pageType");
const { isEmpty } = require("lodash");
const homepageScoreCalculations = require("../utils/homepageScoreCalculations");
const { optimizePercent } = require("../config/app");
const seoScores = require("../config/seoScores");

class AnalysisService {
  getAnalysisData = async (shopId) => {
    const ShopService = require("./ShopService");
    const shop = await ShopService.getShopById(shopId);
    const analysis = {
      total_products: shop.total_products,
      analysed_products: shop.analysed_products,
      seo_issues: shop.seo_issues,
      optimized_products: shop.optimized_products,
      overall_score: shop.overall_score,
    };

    analysis.productsHasIssues = analysis.total_products - analysis.optimized_products;

    return analysis;
  };

  analyseShopAllPages = async (shopId, url) => {
    const PageService = require("./PageService");
    const pages = await PageService.getAllPages(shopId);

    if (pages.length > 0) {
      return await Promise.all(pages.map(async (page) => await this.analyseEachPage({ page, shopId, url })));
    }

    return [];
  };

  analyseShopAllArticles = async (shopId) => {
    const ShopService = require("./ShopService");
    const shop = await ShopService.getShopById(shopId);
    const articles = await ArticleService.getAllArticles(shopId);

    await Promise.allSettled(
      articles.map((a) =>
        this.analyseEachArticle({
          shopId,
          article: a,
          shopURL: shop?.url,
        })
      )
    );
  };

  analyseEachPage = async ({ shopId, page, url }) => {
    const PageService = require("./PageService");
    try {
      const { analysis, ...rest } = await this.getSinglePageAnalysis({ shopId, page, url });
      await PageAnalysis.upsert({ shop_id: shopId, page_id: page.id, ...analysis });
      return await PageService.updatePage(page.id, rest);
    } catch (e) {
      console.log("err: ", e);
      return page;
    }
  };

  getSinglePageAnalysis = async ({ shopId, page, url, uniqueCheckEnable = false }) => {
    if (page.page_type === pageType.HOMEPAGE || page.page_type === pageType.BETTERDOCS_HOMEPAGE) {
      return await this.getHomepageAnalysis({ shopId, page, url, uniqueCheckEnable });
    } else {
      return await this.getRegularPageAnalysis({ shopId, page, url, uniqueCheckEnable });
    }
  };

  getHomepageAnalysis = async ({ shopId, page, url, uniqueCheckEnable = false }) => {
    const focusKeyword = page.focus_keyword?.toLowerCase()?.trim();
    const metafields = isEmpty(page.meta) ? [] : page.meta;
    const metaTitle = getMetaTitle(metafields);
    const metaDescription = getMetaDescription(metafields);

    const analysis = {
      // BASIC SEO
      focus_keyword_in_meta_title: homepageScoreCalculations.calculateFocusKeywordInMetaTitle(metaTitle, focusKeyword),
      meta_title_within_char_limit: homepageScoreCalculations.calculateMetaTitleLength(metaTitle),
      focus_keyword_in_meta_desc: homepageScoreCalculations.calculateFocusKeywordInMetaDesc(
        metaDescription,
        focusKeyword
      ),
      meta_desc_within_char_limit: homepageScoreCalculations.calculateMetaDescLength(metaDescription),
    };

    const analysisScore = homepageScoreCalculations.calculateAnalysisScores(analysis);
    const score = Math.round((analysisScore.point * 100) / pageScore.totalScore());

    return {
      analysis,
      score: score < 100 ? score : 100,
      issues: analysisScore.issues,
      passed: analysisScore.passed,
      is_analysed: true,
      is_optimized: score >= optimizePercent,
    };
  };

  getRegularPageAnalysis = async ({ shopId, page, url, uniqueCheckEnable = false }) => {
    const focusKeyword = replaceSpecialChars(page.focus_keyword?.toLowerCase()?.trim());
    const content = page.body_html;
    const contentRaw = stripTags(content).toLowerCase();
    const metaTitle = getMetaTitle(page.meta, page.title.trim());
    const metaDescription = getMetaDescription(page.meta, contentRaw.slice(0, 165).trim());

    const analysis = {
      // BASIC_SEO
      unique_focus_keyword: await pageScore.calculateUniqueFocusKeyword(shopId, focusKeyword, page?.id),
      focus_keyword_in_introduction: pageScore.calculateFocusKeywordInIntroduction(contentRaw, focusKeyword, page?.id),
      content_more_then_300_words: pageScore.calculateContentMinWord(contentRaw, 300),
      focus_keyword_density: pageScore.calculateFocusKeywordDensity(contentRaw, focusKeyword),

      // DETAILED SEO
      focus_keyword_in_img_alt_text: pageScore.calculateFocusKeywordInAltText(content, focusKeyword),
      focus_keyword_in_subheading: pageScore.calculateFocusKeywordInSubheading(content, focusKeyword),
      focus_keyword_in_meta_desc: commonScore.calculateFocusKeywordInMetaDesc(
        metaDescription,
        focusKeyword,
        types.PAGE
      ),
      focus_keyword_in_url: pageScore.calculateFocusKeywordInUrl(page.handle, focusKeyword),
      meta_desc_within_160_char: commonScore.calculateMetaDescLength(metaDescription, 165, types.PAGE),
      focus_keyword_in_meta_title: pageScore.calculateFocusKeywordInMetaTitle(page, focusKeyword),
      internal_link_in_content: commonScore.calculateInternalLink(content, url, types.PAGE),
      external_link_in_content: commonScore.calculateExternalLink(content, url, types.PAGE),
      alt_text_in_all_img: pageScore.calculateImgAltText(content),
    };

    const analysisScore = pageScore.calculateAnalysisScores(analysis);
    const score = Math.round((analysisScore.point * 100) / pageScore.totalScore());

    return {
      analysis,
      score: score < 100 ? score : 100,
      issues: analysisScore.issues,
      passed: analysisScore.passed,
      is_analysed: true,
      is_optimized: score >= optimizePercent,
    };
  };

  analyseEachArticle = async ({ shopId, article, shopURL = null }) => {
    const ShopService = require("../services/ShopService");
    const ArticleService = require("./ArticleService");
    try {
      if (!shopURL) {
        const shop = await ShopService.getShopById(shopId);
        shopURL = shop.url;
      }
      const { analysis, ...rest } = await this.getSingleArticleAnalysis({ shopId, article, shopURL });
      await ArticleAnalysis.upsert({ shop_id: shopId, blog_id: article.blog_id, article_id: article.id, ...analysis });
      return await ArticleService.updateArticle(article?.id, rest);
    } catch (err) {
      console.log("analysis error: ", err);
      return article;
    }
  };

  getSingleArticleAnalysis = async ({ shopId, article, shopURL, uniqueCheckEnable = false }) => {
    const ARTICLE_SCORES = require("../config/articleSeoScores");
    const focusKeyword = article?.focus_keyword?.toLowerCase().trim();
    const content = article.body_html;
    const rawContent = stripTags(content).toLowerCase();
    const articleTitle = stripTags(article.title).toLowerCase();
    const metafields = isEmpty(article?.meta) ? [] : article.meta;
    const featuredImageAltText = article?.image?.alt || "";
    const metaTitle = getMetaTitle(metafields, articleTitle.trim());
    const metaDescription = getMetaDescription(metafields, rawContent.slice(0, 165).trim());

    const analysis = {
      // BASIC_SEO
      unique_focus_keyword: await articleScore.calculateUniqueFocusKeyword(shopId, focusKeyword, article.id),
      focus_keyword_in_introduction: commonScore.calculateFocusKeywordInIntroduction(
        rawContent,
        focusKeyword,
        types.ARTICLE
      ),
      content_more_then_800_words: commonScore.calculateContentMinWord({
        content: rawContent,
        minWord: 800,
        entityType: types.ARTICLE,
      }),
      focus_keyword_density: commonScore.calculateFocusKeywordDensity(rawContent, focusKeyword, types.ARTICLE),

      // DETAILED_SEO
      focus_keyword_in_img_alt_text: article?.image
        ? commonScore.calculateFocusKeywordInAltText(featuredImageAltText, focusKeyword, types.ARTICLE)
        : ARTICLE_SCORES.FOCUS_KEYWORD_IN_IMG_ALT_TEXT,
      focus_keyword_in_subheading: commonScore.calculateFocusKeywordInSubheading(content, focusKeyword, types.ARTICLE),
      focus_keyword_in_meta_desc: commonScore.calculateFocusKeywordInMetaDesc(
        metaDescription,
        focusKeyword,
        types.ARTICLE
      ),
      focus_keyword_in_url: commonScore.calculateFocusKeywordInUrl(article?.handle, focusKeyword, types.ARTICLE),
      meta_desc_within_160_char: commonScore.calculateMetaDescLength(metaDescription, 165, types.ARTICLE),
      focus_keyword_in_meta_title: commonScore.calculateFocusKeywordInMetaTitle(metaTitle, focusKeyword, types.ARTICLE),
      internal_link_in_content: commonScore.calculateInternalLink(content, shopURL, types.ARTICLE),
      external_link_in_content: commonScore.calculateExternalLink(content, shopURL, types.ARTICLE),
      alt_text_in_all_img: commonScore.calculateImgAltText(content, types.ARTICLE),
    };

    const analysisScore = articleScore.calculateAnalysisScores(analysis);
    const score = Math.round((analysisScore.point * 100) / articleScore.totalScore());

    return {
      analysis,
      score: score < 100 ? score : 100,
      issues: analysisScore.issues,
      passed: analysisScore.passed,
      is_analysed: true,
      is_optimized: score >= optimizePercent,
    };
  };

  deleteArticleAnalysis = async (blogId, articleId) => {
    await ArticleAnalysis.destroy({ where: { blog_id: blogId, article_id: articleId } });
  };
  /**
   * Delete page analysis
   * @param {*} pageId - Database page id
   */
  deletePageAnalysis = async (pageId) => {
    await PageAnalysis.destroy({ where: { page_id: pageId } });
  };

  getOnboardingAnalysisData = async (shopId) => {
    const [products = {}] = await sequelize.query(
      `SELECT
        SUM(CASE WHEN p.is_analysed THEN 1 ELSE 0 END)::INT analysed_products, 
        COUNT(p.id)::INT total_products,
        AVG(p.score)::INT overall_score,
        SUM(CASE WHEN p.is_optimized=false  THEN 1 ELSE 0 END)::INT seo_issues,
        SUM(CASE WHEN p.is_optimized THEN 1 ELSE 0 END)::INT total_optimized,
        SUM(CASE WHEN pas.meta_title_within_char_limit >= :metaTitle THEN 1 ELSE 0 END)::INT meta_title,
        SUM(CASE WHEN pas.meta_desc_within_char_limit >= :metaDesc THEN 1 ELSE 0 END)::INT meta_desc,
        SUM(CASE WHEN pas.desc_min_word_count_300 >= :productDesc THEN 1 ELSE 0 END)::INT product_desc,
        SUM(CASE WHEN pas.focus_keyword_in_meta_desc >= :focusKeyword THEN 1 ELSE 0 END)::INT focus_keyword_density,
        SUM(CASE WHEN pas.alt_text_in_all_img >= :imageAltText THEN 1 ELSE 0 END)::INT image_alt_text,
        SUM(CASE WHEN pas.optimized_all_images >= :imageOptimized THEN 1 ELSE 0 END)::INT optimized_images
      FROM
        products AS p
        LEFT JOIN product_analysis_scores AS pas ON p.id = pas.product_id
        AND p.shop_id = pas.shop_id
      WHERE
        p.shop_id=:shopId
      GROUP BY
        p.shop_id;
      `,
      {
        replacements: {
          shopId: shopId,
          metaTitle: (seoScores.META_TITLE_WITHIN_CHAR_LIMIT * optimizePercent) / 100,
          metaDesc: (seoScores.META_DESC_WITHIN_CHAR_LIMIT * optimizePercent) / 100,
          productDesc: (seoScores.DESC_MIN_WORD_COUNT_300 * optimizePercent) / 100,
          focusKeyword: (seoScores.FOCUS_KEYWORD_IN_META_DESC * optimizePercent) / 100,
          imageAltText: (seoScores.FOCUS_KEYWORD_IN_IMG_ALT_TEXT * optimizePercent) / 100,
          imageOptimized: (seoScores.OPTIMIZED_ALL_IMAGES * optimizePercent) / 100,
        },
        type: sequelize.QueryTypes.SELECT,
      }
    );

    const [images = {}] = await sequelize.query(
      `SELECT 
        SUM (CASE WHEN alt_text = '' THEN 1 ELSE 0 END)::INT missing_alt_txt, 
        SUM (CASE WHEN optimization_status = 'NOT_OPTIMIZED' OR optimization_status = 'PENDING' THEN 1 ELSE 0 END)::INT not_optimized_images, 
        COUNT(id)::INT total_images 
      FROM product_images 
      WHERE shop_id=:shopId;
      `,
      { type: sequelize.QueryTypes.SELECT, replacements: { shopId } }
    );

    return {
      seoScore: products?.overall_score || 0,
      totalProducts: products?.total_products || 0,
      totalImages: images?.total_images || 0,
      issues: {
        meta_title: products?.total_products - products?.meta_title || 0,
        meta_desc: products?.total_products - products?.meta_desc || 0,
        product_desc: products?.total_products - products?.product_desc || 0,
        focus_keyword_density: products?.total_products - products?.focus_keyword_density || 0,
        missing_alt_txt: images?.missing_alt_txt || 0,
        not_optimized_images: images?.not_optimized_images || 0,
      },
      totals: {
        meta_title: products?.total_products || 0,
        meta_desc: products?.total_products || 0,
        product_desc: products?.total_products || 0,
        focus_keyword_density: products?.total_products || 0,
        missing_alt_txt: images?.total_images || 0,
        not_optimized_images: images?.total_images || 0,
      },
    };
  };
}

module.exports = new AnalysisService();
