const { ArticleMeta, Op } = require("../../sequelize");

class ArticleMetaService {
  /**
   *
   * @param {number} shopId DB shop id
   * @param {number} blogId DB blog id
   * @param {number} articleId DB article id
   * @param {*[]} metas
   * @returns {Promise<*[]>}
   */
  upsertMetas = async (shopId, blogId, articleId, metas, isHomepageMeta = false) => {
    const savedMetas = [];
    for (let meta of metas) {
      const { id, namespace, key, type, value } = meta;
      const data = {
        shop_id: shopId,
        blog_id: blogId,
        article_id: articleId,
        gql_id: id,
        namespace,
        key,
        type,
        value,
      };

      const [m] = await ArticleMeta.upsert(data, { returning: true });
      savedMetas.push(m.toJSON());
    }

    return savedMetas;
  };

  /**
   *
   * @param {number | number[]} articleId DB page id
   */
  deleteMeta = async (articleId) => {
    const where = {
      article_id: { [Op.in]: Array.isArray(articleId) ? articleId : [articleId] },
    };

    await ArticleMeta.destroy({ where });
  };
}

module.exports = new ArticleMetaService();
