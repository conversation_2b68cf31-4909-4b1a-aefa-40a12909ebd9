const { ProductImage, Op, Product, Sequelize, sequelize, ProductImageMapper } = require("../../sequelize");
const { preparePagination } = require("../utils/helper");
const { rejectOnEmpty } = require("../config/sequelize");
const imageOptimization = require("storeseo-enums/imageOptimization");

class ProductImageService {
  /**
   * Get DB Product images count by shop id and conditions
   * @param shopId - shop id
   * @param {import("sequelize").WhereOptions} [conditions]
   * @returns {Promise<number>}
   */
  count = async (shopId, conditions = {}) => {
    return ProductImage.count({ where: { shop_id: shopId, ...conditions } });
  };
  /**
   * Get product all images
   * @param shopId
   * @param productId
   * @returns {Promise<U[]>}
   */
  getProductImages = async (shopId, productId) => {
    const product = await Product.findOne({
      where: { id: productId, shop_id: shopId },
      attributes: ["id"],
      include: ["images"],
    });

    return product.toJSON().images;
  };

  getUnoptimizedImages = async (productId) => {
    const product = await Product.findOne({
      where: {
        id: productId,
      },
      include: [
        {
          association: "images",
          where: {
            "$images.optimization_status$": imageOptimization.NOT_OPTIMIZED,
          },
          required: false,
        },
      ],
    });

    return product.toJSON().images;
  };

  /**
   * Get product single image by gql_id
   * @param shopId
   * @param productId
   * @param gqlId
   * @param reject
   * @returns {Promise<*>}
   */
  getSingleProductImage = async (shopId, productId, gqlId, reject = rejectOnEmpty) => {
    const image = await ProductImage.findOne({
      where: { shop_id: shopId, product_id: productId, gql_id: gqlId },
      rejectOnEmpty: reject,
    });
    return image?.toJSON() || null;
  };

  /**
   * Save or update product images
   * @param shopId
   * @param productId
   * @param images
   * @param transaction
   * @returns {Promise<*[]>}
   */
  saveOrUpdateProductImages = async (shopId, productId, images) => {
    const dbImages = [];
    if (images.length > 0) {
      for (let image of images) {
        const imageData = {
          shop_id: shopId,
          media_id: image.id,
          src: image.url || image.src,
          alt_text: image.altText,
          position: image.position ?? 0,
          file_size: image.fileSize,
        };

        const [i] = await ProductImage.upsert(imageData, {
          fields: ["src", "alt_text", "position", "file_size"],
          conflictFields: ["media_id", "shop_id"],
        });
        dbImages.push(i);
      }
      console.log(
        "Shop =>",
        shopId,
        "Product =>",
        productId,
        "Shopify Images =>",
        images.length,
        "Inserted/Updated Images =>",
        dbImages.length
      );

      for (let dbImage of dbImages) {
        await ProductImageMapper.upsert(
          { shop_id: shopId, product_id: productId, image_id: dbImage.id },
          {
            conflictFields: ["shop_id", "product_id", "image_id"],
          }
        );
      }
    }
    return dbImages;
  };

  /**
   * Deletes deleted shopify product images relation from db
   * @param shopId
   * @param productId
   * @param savedImages
   * @returns {Promise<number>}
   */
  deleteDeletedProductImages = async (shopId, productId, savedImages) => {
    await ProductImageMapper.destroy({
      where: {
        shop_id: shopId,
        product_id: productId,
        image_id: {
          [Op.notIn]: savedImages.map((img) => img.id),
        },
      },
    });
  };

  /**
   * Deletes product images
   * @param shopId
   * @param productId
   * @param transaction
   * @returns {Promise<number>}
   */
  deleteProductImages = async (shopId, productId, transaction = undefined) => {
    const where = { shop_id: shopId, product_id: productId };
    return await ProductImage.destroy({ where, transaction });
  };

  /**
   *
   * @param {number | string} id Database id of the image
   * @returns
   */
  deleteImage = async (id) => {
    return ProductImage.destroy({ where: { id } });
  };

  /**
   * Deletes product images by graphql ids array
   * @param shopId
   * @param productId
   * @param deletedIds
   * @returns {Promise<number>}
   */
  deleteProductImagesByGqlId = async (shopId, productId, deletedIds = []) => {
    if (deletedIds.length > 0) {
      const where = { shop_id: shopId, product_id: productId };
      where.gql_id = { [Op.in]: deletedIds };
      return await ProductImage.destroy({ where });
    }
  };

  /**
   * Deletes product images greater than the provided id
   * @param shopId
   * @param productId
   * @param transaction
   * @returns {Promise<number|boolean>}
   */
  deleteShopImagesGreaterThenProductId = async (shopId, productId, transaction = undefined) => {
    const where = { shop_id: shopId };
    return await ProductImage.destroy({ where, transaction });
  };

  /**
   * Find deleted product image ids comparing database & shopify data
   * @param dbImages
   * @param shopifyImages
   * @returns {*}
   */
  #findDeletedImageIds = (dbImages, shopifyImages) => {
    return dbImages
      .filter((dimg) => !shopifyImages.find((img) => img.id === dimg.gql_id))
      ?.map((m) => m.gql_id || null);
  };

  /**
   *
   * @param {number} shopId DB id of the shop
   * @param {{ page: number?, limit: number?, search: string?, status: string?, has_alt_text?: boolean}} options
   */
  getImagesByShop = async (
    shopId,
    {
      page = 1,
      limit = 20,
      search = "",
      status = "",
      alt_status = "",
      fileSize,
      has_alt_text,
      sortBy = "created_at",
      sortOrder = "DESC",
    }
  ) => {
    if (status.includes(imageOptimization.NOT_OPTIMIZED)) {
      status = [...status.split(","), imageOptimization.RESTORED].join(",");
    }
    const offset = (page - 1) * limit;

    const fileSizeInBytes = fileSize
      ? fileSize.split(",").map((size) => (size ? (parseFloat(size) * 1024 * 1024).toString() : ""))
      : [];

    const whereQuery = `
    WHERE "pim"."shop_id" = :shopId
    ${search ? `AND (pi.src ILIKE :search OR p.title ILIKE :search)` : ""}
    ${
      status
        ? `AND "pi"."optimization_status" IN (${status
            .split(",")
            .map((s) => `\'${s}\'`)
            .join(",")})`
        : ""
    }
    ${
      alt_status
        ? `AND "pi"."alt_text_optimization_status" IN (${alt_status
            .split(",")
            .map((s) => `\'${s}\'`)
            .join(",")})`
        : ""
    }
    ${
      has_alt_text === "true"
        ? `AND "pi"."alt_text" IS NOT NULL AND length(trim("pi"."alt_text")) > 0`
        : has_alt_text === "false"
          ? `AND length(trim("pi"."alt_text")) = 0`
          : ""
    }
    ${
      fileSizeInBytes.length > 0
        ? fileSizeInBytes[0].length > 0 && fileSizeInBytes[1].length > 0
          ? `AND "pi"."file_size" >= ${fileSizeInBytes[0]} AND "pi"."file_size" <= ${fileSizeInBytes[1]}`
          : fileSizeInBytes[0].length > 0
            ? `AND "pi"."file_size" >= ${fileSizeInBytes[0]}`
            : fileSizeInBytes[1].length > 0
              ? `AND "pi"."file_size" <= ${fileSizeInBytes[1]}`
              : ""
        : ""
    }
    `;

    const getImageQuery = sequelize.query(
      `
      WITH ImageDetails AS (
        SELECT
          pi.*,
          REGEXP_REPLACE("pi"."src",
            '.*/',
            '') AS "fileName",
          COALESCE(json_agg(json_build_object('id',
                p.id,
                'product_id',
                p.product_id,
                'title',
                p.title,
                'description',
                p.description,
                'focus_keyword',
                p.focus_keyword,
                'created_at',
                p.created_at)) FILTER (WHERE p.id IS NOT NULL),
            '[]'::json) AS products FROM product_images pi
        LEFT JOIN product_image_mapper pim ON pi.id = pim.image_id
        LEFT JOIN products p ON pim.product_id = p.id` +
        whereQuery +
        `
      GROUP BY
        pi.id,
        pi.src,
        pi.optimization_status,
        pi.alt_text_optimization_status,
        pi.created_at,
        pi.updated_at
      ORDER BY
        pi.${sortBy} ${sortOrder}
      ),
      LimitedDetails AS (
      SELECT
        * FROM ImageDetails
      LIMIT :limit OFFSET :offset
      )
      SELECT
        (SELECT COUNT(*) FROM ImageDetails) AS count,
        json_agg(LimitedDetails) AS images
      FROM
        LimitedDetails
      `,
      {
        replacements: {
          shopId,
          limit,
          offset,
          search: `%${search}%`,
          status: status
            .split(",")
            .map((s) => `\"${s}\"`)
            .join(","),
        },
        nest: true,
      }
    );
    const [rows, resourceCount] = await Promise.all([getImageQuery, this.count(shopId)]);

    const { count, images } = rows[0];

    return {
      images,
      pagination: preparePagination(count, page, limit),
      totalCount: resourceCount,
    };
  };

  getImage = async (id) => {
    const image = await ProductImage.findOne({
      attributes: {
        include: ["*", [Sequelize.literal(`REGEXP_REPLACE(src, '.*\/', '')`), "fileName"]],
      },
      include: [
        {
          model: Product,
          as: "products",
          attributes: ["id", "title", "product_id", "created_at"],
        },
      ],
      where: { id },
    });

    return image.toJSON();
  };

  getImageByConditions = async (conditions, fields = undefined) => {
    const images = await ProductImage.findOne({
      where: { ...conditions },
      attributes: fields,
      include: [
        {
          model: Product,
          as: "products",
          attributes: ["id", "title", "product_id", "created_at"],
        },
      ],
    });

    return images?.toJSON();
  };

  getImagesByConditions = async (conditions, fields = undefined) => {
    const images = await ProductImage.findAll({
      where: { ...conditions },
      attributes: fields,
      include: [
        {
          model: Product,
          as: "products",
          attributes: ["id", "title", "product_id", "created_at"],
        },
      ],
    });

    return images.map((image) => image.toJSON());
  };

  /**
   *
   * @param {number} id DB id
   * @param {*} data
   */
  updateImage = async (id, data) => {
    const [count] = await ProductImage.update(data, { where: { id } });
    if (count) return this.getImage(id);
  };
}

module.exports = new ProductImageService();
