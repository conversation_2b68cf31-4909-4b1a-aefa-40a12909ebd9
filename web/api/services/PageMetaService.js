const { PageMeta, Op } = require("../../sequelize");
const pageType = require("storeseo-enums/pageType");

class PageMetaService {
  /**
   *
   * @param {number} shopId DB shop id
   * @param {number} pageId DB page id
   * @param {*[]} metas
   * @returns {Promise<*[]>}
   */
  upsertMetas = async (shopId, pageId, metas, isHomepageMeta = false) => {
    const savedMetas = [];
    for (let meta of metas) {
      const { id, namespace, key, type, value } = meta;
      const data = {
        shop_id: shopId,
        page_id: pageId,
        gql_id: isHomepageMeta ? `${pageType.HOMEPAGE}-${key}` : id,
        namespace,
        key,
        type,
        value,
      };

      const [m] = await PageMeta.upsert(data, { returning: true });
      savedMetas.push(m.toJSON());
    }

    return savedMetas;
  };

  upsertDocsMetas = async (shopId, pageId, metas, isHomepageMeta = false) => {
    const savedMetas = [];
    for (let meta of metas) {
      const { id, namespace, key, type, value } = meta;
      const data = {
        shop_id: shopId,
        page_id: pageId,
        gql_id: isHomepageMeta ? `${pageType.BETTERDOCS_HOMEPAGE}-${key}` : id,
        namespace,
        key,
        type,
        value,
      };
      const [m] = await PageMeta.upsert(data, { returning: true });
      savedMetas.push(m.toJSON());
    }
    return savedMetas;
  };

  /**
   *
   * @param {number | number[]} pageId DB page id
   */
  deleteMeta = async (pageId) => {
    const where = {
      page_id: { [Op.in]: Array.isArray(pageId) ? pageId : [pageId] },
    };

    await PageMeta.destroy({ where });
  };
}

module.exports = new PageMetaService();
