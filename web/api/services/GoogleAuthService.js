const { google } = require("googleapis");
const logger = require("storeseo-logger");
const {
  oauth: { web: keys },
} = require("../config/google");
const googleIntegrationSteps = require("storeseo-enums/googleIntegrationSteps");
const { generateJWTToken } = require("../utils/helper");

const { GoogleToken: GoogleTokenModel } = require("../../sequelize");
const { OAuth2Client } = require("google-auth-library");
const { url } = require("../config/app");

/**
 * @typedef {import("../../jsDocTypes").GoogleOAuthTokenDetails} GoogleOAuthTokenDetails
 */

/**
 * @type {typeof import('sequelize').Model}
 */
const GoogleToken = GoogleTokenModel;

class GoogleAuthService {
  /**
   * Generate OAuth2 client for Google
   * @param {string} [refreshToken] refresh token provided by google
   * @returns {import('google-auth-library').OAuth2Client} Google OAuth2 client
   */
  getOAuthClient = (refreshToken = "") => {
    const { client_id, client_secret, redirect_uris } = keys;
    const redirect_uri = redirect_uris.find((uri) => uri.includes(url)) || "https://app.storeseo.com/oauth2callback";

    const client = new google.auth.OAuth2(client_id, client_secret, redirect_uri);
    if (refreshToken) {
      client.setCredentials({ refresh_token: refreshToken });
    }
    return client;
  };

  getServiceJsonAuthClient = (serviceJson) => {
    const client = google.auth.fromJSON(serviceJson);
    return client;
  };

  /**
   * Generate OAuth2 authorize URL for Google
   * @param {object} param
   * @param {string} param.shopDomain myshopify domain of the shop
   * @param {[string]} param.scopesArr list of scopes to authorize
   * @param {string} param.redirectURL registered redirect url
   * @param {string} [param.authTargetAction] action to perform once the authorize is successful
   * @returns {string} OAuth2 authorization url
   */
  generateAuthURL = ({ shopDomain, scopesArr, redirectURL, authTargetAction = googleIntegrationSteps.AUTHENTICATE }) =>
    this.getOAuthClient().generateAuthUrl({
      access_type: "offline",
      scope: scopesArr.join(" "),
      state: generateJWTToken({ shopDomain, authTargetAction, redirectURL }),
      include_granted_scopes: true,
    });

  /**
   * Retrieve authenticated user & token details from DB
   * @param {string} email authenticated user's email
   * @returns {Promise<GoogleOAuthTokenDetails>}
   */
  getAuthenticatedUser = async (email) => {
    try {
      const user = await GoogleToken.findOne({ where: { email } });
      return user.toJSON();
    } catch (err) {
      // console.log("Error fetching authenticated user from db: ", err);
      return null;
    }
  };

  /**
   * Save new authenticated user & token details in DB
   * @param {object} param
   * @param {string} param.email authenticated user email
   * @param {string} param.refresh_token refresh token provided by Google
   * @param {string} param.scope space separated names of authenticated scopes
   * @returns {Promise<GoogleOAuthTokenDetails>} saved authenticated user & token details
   */
  saveNewAuthenticatedUser = async ({ email, refresh_token, scope }) => {
    try {
      const data = {
        email,
        refresh_token,
        scopes: scope.split(" "),
      };

      const savedUser = await GoogleToken.create(data);
      return savedUser.toJSON();
    } catch (err) {
      logger.error(err, { email });
      return false;
    }
  };

  /**
   * Update existing user & token details in DB
   * @param {object} param
   * @param {string} param.email authenticated user email
   * @param {string} param.refresh_token refresh token provided by Google
   * @param {string} param.scope space separated names of authenticated scopes
   * @returns {Promise<GoogleOAuthTokenDetails>} saved authenticated user & token details
   */
  updateAuthenticatedUser = async ({ email, refresh_token, scope }) => {
    try {
      const data = { scopes: scope.split(" ") };
      if (refresh_token) data.refresh_token = refresh_token;

      const [affectedRows, updatedUsers] = await GoogleToken.update(data, { where: { email }, returning: true });
      return updatedUsers[0].toJSON();
    } catch (err) {
      console.log("err", err);
      return false;
    }
  };

  /**
   * Extract user & token details from Google authorized code & save to DB
   * @param {string} code Google authorization code
   * @param {string} shopDomain relevant shop's myshopify domain
   * @returns {Promise<GoogleOAuthTokenDetails>}
   */
  storeAuthenticatedUserDetails = async (code, shopDomain) => {
    try {
      const oauthClient = this.getOAuthClient();
      const { tokens } = await oauthClient.getToken(code);
      const decodedIdToken = await oauthClient.verifyIdToken({
        idToken: tokens.id_token,
        audience: keys.client_id,
      });
      const userEmail = decodedIdToken.getPayload().email;

      const existingUser = await this.getAuthenticatedUser(userEmail);
      if (existingUser) {
        const updatedUser = await this.updateAuthenticatedUser({ email: userEmail, ...tokens });
        return updatedUser;
      } else {
        const user = await this.saveNewAuthenticatedUser({ email: userEmail, ...tokens });
        return user;
      }
    } catch (err) {
      logger.error(err, {
        message: `Error saving authenticated user details for code ${code} initiated from shop ${shopDomain}`,
        domain: shopDomain,
        code: code,
      });
      // return false;
      throw new Error("Failed to save Google authenticated user");
    }
  };

  getDefaultAuthenticatedUser = () => {
    return {
      email: "<EMAIL>",
      refresh_token:
        "1//0g44fVQlffC-ICgYIARAAGBASNwF-L9Ir2s3XttfamS9cd5SkrZQkk-kFsTp0sA9dHsxNQkoabVJBCuRa-eTL6s2d6g8EhWLZ2tM",
    };
  };

  /**
   * Revoke the oauth token granted by user
   * @param {string} email
   */
  revokeAuthToken = async (email) => {
    const user = await this.getAuthenticatedUser(email);
    const oauthClient = this.getOAuthClient(user.refresh_token);

    const res = await oauthClient.revokeToken(user.refresh_token);
    console.log("revokeAuthToken: ", res.data, "status: ", res.status);

    return res.status === 200;
  };
}

module.exports = new GoogleAuthService();
