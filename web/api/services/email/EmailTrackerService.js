const emailStatus = require("storeseo-enums/emailStatus");
const { EmailTracker, sequelize, Op } = require("../../../sequelize");

class EmailTrackerService {
  emailsReadyForDelivery = async (limit = 10) => {
    const { count, rows: emails } = await EmailTracker.findAndCountAll({
      where: {
        status: emailStatus.READY_FOR_DELIVERY,
        scheduled_delivery_date: { [Op.lte]: new Date() },
      },
      limit,
    });

    return {
      emails: emails.map((e) => e.toJSON()),
      count,
    };
  };

  insertEmail = async (data) => {
    const email = await EmailTracker.create(data);
    return email.toJSON();
  };

  /**
   *
   * @param {number} id
   * @param {*} data
   * @returns
   */
  updateEmailById = async (id, data) => {
    const [affectedRows, records] = await EmailTracker.update(data, {
      where: { id },
      returning: true,
    });

    return records?.[0]?.toJSON();
  };

  /**
   *
   * @param {number} shopId
   * @param {string} topic
   * @returns
   */
  getEmailByTopic = async (shopId, topic) => {
    const email = await EmailTracker.findOne({
      where: { shop_id: shopId, topic },
      order: [["created_at", "DESC"]],
    });
    return email?.toJSON();
  };

  getDeliveredEmailsCount = async () => {
    const threeMonthsAgoDate = sequelize.fn("DATE", sequelize.literal("NOW() - INTERVAL '3 months'"));
    return await EmailTracker.count({
      where: {
        status: emailStatus.DELIVERED,
        delivered_at: {
          [Op.lte]: threeMonthsAgoDate,
        },
      },
    });
  };

  deleteDeliveredEmails = async (limit = 500) => {
    const threeMonthsAgoDate = sequelize.fn("DATE", sequelize.literal("NOW() - INTERVAL '3 months'"));
    return await EmailTracker.destroy({
      where: {
        status: emailStatus.DELIVERED,
        delivered_at: {
          [Op.lte]: threeMonthsAgoDate,
        },
      },
      limit: limit,
    });
  };
}

module.exports = new EmailTrackerService();
