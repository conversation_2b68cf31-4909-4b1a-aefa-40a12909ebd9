const AuthService = require("../services/AuthService");
const ShopService = require("../services/ShopService");
const toastMessages = require("storeseo-enums/toastMessages");

class AuthController {
  register = async (req, res) => {
    try {
      const result = await AuthService.register(req.session);
      return res.success({
        res: result,
        message: "Shop registered.",
      });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getAccessToken = async (req, res) => {
    try {
      const result = await ShopService.getShop(req.query.shop);
      const shop = result.toJSON();
      return res.success({
        accessToken: shop.access_token,
        shopDomain: shop.domain,
      });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };
}

module.exports = new AuthController();
