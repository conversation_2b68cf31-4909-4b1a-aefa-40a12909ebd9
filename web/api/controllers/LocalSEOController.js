//@ts-check
const logger = require("storeseo-logger");
const toastMessages = require("storeseo-enums/toastMessages");
const ShopService = require("../services/ShopService");
const {
  LOCAL_SEO_BREADCRUMB_SCHEMA,
  LOCAL_SEO_COLLECTION_SCHEMA,
  LOCAL_SEO_BLOG_SCHEMA,
  LOCAL_SEO_PRODUCT_SCHEMA,
  LOCAL_SEO_PRODUCT_MERCHANT_SCHEMA,
  LOCAL_SEO_ARTICLE_SCHEMA,
  LOCAL_SEO_ORGANIZATION_SCHEMA,
  LOCAL_SEO_COMMON_SCHEMA,
  LOCAL_SEO_LOCAL_BUSINESS_SCHEMA,
} = require("storeseo-enums/localSEOSchemaTypes");
const ShopifyService = require("../services/ShopifyService");
const { METAFIELD_KEYS } = require("storeseo-enums/metafields");
const {
  organizationAPISchema,
  localBusinessAPISchema,
  productMerchantAPISchema,
} = require("storeseo-schema/local-seo");
const { validate } = require("../utils/dataValidator");

const UpdateSuccessMessage = "SEO Schema updated";
class LocalSEOController {
  async getBreadcrumbSchemaSettings(req, res) {
    const { shopId } = req.user;
    try {
      const result = await ShopService.getShopSetting(shopId, LOCAL_SEO_BREADCRUMB_SCHEMA);

      return res.success({ data: result?.value });
    } catch (err) {
      console.log(`Error on getting local SEO BreadCrumb schema data for shop id ${shopId}: ${JSON.stringify(err)}`);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  }

  async updateBreadcrumbSchemaSettings(req, res) {
    const { shopId } = req.user;

    try {
      const data = req.body;
      const settings = {
        key: LOCAL_SEO_BREADCRUMB_SCHEMA,
        value: JSON.stringify(data),
        value_type: "json",
      };

      const result = await ShopService.updateShopSetting(shopId, settings);
      const shop = await ShopService.getShopById(shopId);

      await ShopifyService.setMetafield(req.user.shop, {
        ownerId: shop.shop_id,
        key: METAFIELD_KEYS.LOCAL_SEO_BREADCRUMB_SCHEMA,
        value: JSON.stringify(data),
      });

      return res.success({ message: UpdateSuccessMessage, data: result?.value });
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: "Error updating local seo breadcrumb schema data" });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  }

  async getCollectionSchemaSettings(req, res) {
    const { shopId } = req.user;
    try {
      const result = await ShopService.getShopSetting(shopId, LOCAL_SEO_COLLECTION_SCHEMA);

      return res.success({ data: result?.value });
    } catch (err) {
      console.log(`Error on getting local SEO Collection schema data for shop id ${shopId}: ${JSON.stringify(err)}`);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  }

  async updateCollectionSchemaSettings(req, res) {
    const { shopId } = req.user;

    try {
      const data = req.body;
      const settings = {
        key: LOCAL_SEO_COLLECTION_SCHEMA,
        value: JSON.stringify(data),
        value_type: "json",
      };

      const result = await ShopService.updateShopSetting(shopId, settings);
      const shop = await ShopService.getShopById(shopId);

      await ShopifyService.setMetafield(req.user.shop, {
        ownerId: shop.shop_id,
        key: METAFIELD_KEYS.LOCAL_SEO_COLLECTION_SCHEMA,
        value: JSON.stringify(data),
      });

      return res.success({ message: UpdateSuccessMessage, data: result?.value });
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: "Error updating local seo Collection schema data" });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  }

  async getBlogSchemaSettings(req, res) {
    const { shopId } = req.user;
    try {
      const result = await ShopService.getShopSetting(shopId, LOCAL_SEO_BLOG_SCHEMA);

      return res.success({ data: result?.value });
    } catch (err) {
      console.log(`Error on getting local SEO Blog schema data for shop id ${shopId}: ${JSON.stringify(err)}`);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  }

  async updateBlogSchemaSettings(req, res) {
    const { shopId } = req.user;

    try {
      const data = req.body;
      const settings = {
        key: LOCAL_SEO_BLOG_SCHEMA,
        value: JSON.stringify(data),
        value_type: "json",
      };

      const result = await ShopService.updateShopSetting(shopId, settings);
      const shop = await ShopService.getShopById(shopId);

      await ShopifyService.setMetafield(req.user.shop, {
        ownerId: shop.shop_id,
        key: METAFIELD_KEYS.LOCAL_SEO_BLOG_SCHEMA,
        value: JSON.stringify(data),
      });

      return res.success({ message: UpdateSuccessMessage, data: result?.value });
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: "Error updating local seo Blog schema data" });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  }

  async getProductSchemaSettings(req, res) {
    const { shopId } = req.user;
    try {
      const result = await ShopService.getShopSetting(shopId, LOCAL_SEO_PRODUCT_SCHEMA);

      return res.success({ data: result?.value });
    } catch (err) {
      console.log(`Error on getting local SEO Product schema data for shop id ${shopId}: ${JSON.stringify(err)}`);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  }

  async updateProductSchemaSettings(req, res) {
    const { shopId } = req.user;

    try {
      const data = req.body;

      const settings = {
        key: LOCAL_SEO_PRODUCT_SCHEMA,
        value: JSON.stringify(data),
        value_type: "json",
      };

      const result = await ShopService.updateShopSetting(shopId, settings);
      const shop = await ShopService.getShopById(shopId);

      await ShopifyService.setMetafield(req.user.shop, {
        ownerId: shop.shop_id,
        key: METAFIELD_KEYS.LOCAL_SEO_PRODUCT_SCHEMA,
        value: JSON.stringify(data),
      });

      const { status } = data;
      // Update Product Merchant Schema status if Product Schema is disabled
      if (!status) {
        const productMerchantSchema = await ShopService.getShopSetting(shopId, LOCAL_SEO_PRODUCT_MERCHANT_SCHEMA);
        const newProductMerchantSchema = {
          ...(productMerchantSchema && productMerchantSchema?.value),
          status: false,
        };
        const settings = {
          key: LOCAL_SEO_PRODUCT_MERCHANT_SCHEMA,
          value: JSON.stringify(newProductMerchantSchema),
          value_type: "json",
        };

        await ShopService.updateShopSetting(shopId, settings);

        await ShopifyService.setMetafield(req.user.shop, {
          ownerId: shop.shop_id,
          key: METAFIELD_KEYS.LOCAL_SEO_PRODUCT_MERCHANT_SCHEMA,
          value: JSON.stringify(newProductMerchantSchema),
        });
      }

      return res.success({ message: UpdateSuccessMessage, data: result?.value });
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: "Error updating local seo Product schema data" });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  }

  async getProductMerchantSchemaSettings(req, res) {
    const { shopId } = req.user;
    try {
      const result = await ShopService.getShopSetting(shopId, LOCAL_SEO_PRODUCT_MERCHANT_SCHEMA);

      return res.success({ data: result?.value });
    } catch (err) {
      console.log(
        `Error on getting local SEO Product merchant schema data for shop id ${shopId}: ${JSON.stringify(err)}`
      );
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  }

  async updateProductMerchantSchemaSettings(req, res) {
    if (req.body?.settings) {
      const { error } = await validate(productMerchantAPISchema, req.body);
      if (error) {
        return res.failedValidation({ errors: error.details });
      }
    }
    const { shopId } = req.user;

    try {
      const data = req.body;
      const settings = {
        key: LOCAL_SEO_PRODUCT_MERCHANT_SCHEMA,
        value: JSON.stringify(data),
        value_type: "json",
      };

      const result = await ShopService.updateShopSetting(shopId, settings);
      const shop = await ShopService.getShopById(shopId);

      await ShopifyService.setMetafield(req.user.shop, {
        ownerId: shop.shop_id,
        key: METAFIELD_KEYS.LOCAL_SEO_PRODUCT_MERCHANT_SCHEMA,
        value: JSON.stringify(data),
      });

      return res.success({ message: UpdateSuccessMessage, data: result?.value });
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: "Error updating local seo Product merchant schema data" });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  }

  async getArticleSchemaSettings(req, res) {
    const { shopId } = req.user;
    try {
      const result = await ShopService.getShopSetting(shopId, LOCAL_SEO_ARTICLE_SCHEMA);

      return res.success({ data: result?.value });
    } catch (err) {
      console.log(`Error on getting local SEO Article schema data for shop id ${shopId}: ${JSON.stringify(err)}`);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  }

  async updateArticleSchemaSettings(req, res) {
    const { shopId } = req.user;

    try {
      const data = req.body;
      const settings = {
        key: LOCAL_SEO_ARTICLE_SCHEMA,
        value: JSON.stringify(data),
        value_type: "json",
      };

      const result = await ShopService.updateShopSetting(shopId, settings);
      const shop = await ShopService.getShopById(shopId);

      await ShopifyService.setMetafield(req.user.shop, {
        ownerId: shop.shop_id,
        key: METAFIELD_KEYS.LOCAL_SEO_ARTICLE_SCHEMA,
        value: JSON.stringify(data),
      });

      return res.success({ message: UpdateSuccessMessage, data: result?.value });
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: "Error updating local seo Article schema data" });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  }

  async getOrganizationSchemaSettings(req, res) {
    const { shopId } = req.user;
    try {
      const result = await ShopService.getShopSetting(shopId, LOCAL_SEO_ORGANIZATION_SCHEMA);
      const commonSettingsResult = await ShopService.getShopSetting(shopId, LOCAL_SEO_COMMON_SCHEMA);

      return res.success({
        data: {
          ...result?.value,
          settings: {
            ...result?.value?.settings,
            ...(commonSettingsResult?.value?.basicInformation && {
              basicInformation: commonSettingsResult?.value?.basicInformation,
            }),
          },
        },
      });
    } catch (err) {
      console.log(`Error on getting local SEO Organization schema data for shop id ${shopId}: ${JSON.stringify(err)}`);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  }

  async updateOrganizationSchemaSettings(req, res) {
    if (req.body?.settings && req.body?.settings?.address) {
      console.log("Validating organization schema", req.body);
      const { error } = await validate(organizationAPISchema, req.body);
      if (error) {
        return res.failedValidation({ errors: error.details });
      }
    }
    const { shopId } = req.user;

    try {
      const data = req.body;
      const shop = await ShopService.getShopById(shopId);

      const {
        settings: { basicInformation, ...restSettings },
      } = data;

      const existingCommonSettingsResult = await ShopService.getShopSetting(shopId, LOCAL_SEO_COMMON_SCHEMA);

      const commonSchemaSettingsData = {
        ...(existingCommonSettingsResult?.value || {}),
        ...(basicInformation && { basicInformation }),
      };

      const commonSettings = {
        key: LOCAL_SEO_COMMON_SCHEMA,
        value: JSON.stringify(commonSchemaSettingsData),
        value_type: "json",
      };

      // update common settings
      const commonSettingsResult = await ShopService.updateShopSetting(shopId, commonSettings);
      await ShopifyService.setMetafield(req.user.shop, {
        ownerId: shop.shop_id,
        key: METAFIELD_KEYS.LOCAL_SEO_COMMON_SCHEMA,
        value: JSON.stringify(commonSchemaSettingsData),
      });

      const organizationSchemaData = {
        ...data,
        settings: {
          ...restSettings,
        },
      };

      const settings = {
        key: LOCAL_SEO_ORGANIZATION_SCHEMA,
        value: JSON.stringify(organizationSchemaData),
        value_type: "json",
      };

      // update organization schema settings
      const result = await ShopService.updateShopSetting(shopId, settings);
      await ShopifyService.setMetafield(req.user.shop, {
        ownerId: shop.shop_id,
        key: METAFIELD_KEYS.LOCAL_SEO_ORGANIZATION_SCHEMA,
        value: JSON.stringify(organizationSchemaData),
      });

      return res.success({
        message: UpdateSuccessMessage,
        data: {
          ...result?.value,
          settings: {
            ...result?.value?.settings,
            ...(commonSettingsResult?.value?.basicInformation && {
              basicInformation: commonSettingsResult?.value?.basicInformation,
            }),
          },
        },
      });
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: "Error updating local seo Organization schema data" });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  }

  async getLocalBusinessSchemaSettings(req, res) {
    const { shopId } = req.user;
    try {
      const result = await ShopService.getShopSetting(shopId, LOCAL_SEO_LOCAL_BUSINESS_SCHEMA);
      const commonSettingsResult = await ShopService.getShopSetting(shopId, LOCAL_SEO_COMMON_SCHEMA);

      return res.success({
        data: {
          ...result?.value,
          settings: {
            ...result?.value?.settings,
            ...(commonSettingsResult?.value?.basicInformation && {
              basicInformation: commonSettingsResult?.value?.basicInformation,
            }),
          },
        },
      });
    } catch (err) {
      console.log(
        `Error on getting local SEO Local Business schema data for shop id ${shopId}: ${JSON.stringify(err)}`
      );
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  }

  async updateLocalBusinessSchemaSettings(req, res) {
    if (req.body?.settings && (req.body?.settings?.address || req.body?.settings?.workingDays)) {
      if (req.body?.settings) {
        const { error } = await validate(localBusinessAPISchema, req.body);
        if (error) {
          return res.failedValidation({ errors: error.details });
        }
      }
    }

    const { shopId } = req.user;

    try {
      const data = req.body;
      const shop = await ShopService.getShopById(shopId);

      const {
        settings: { basicInformation, ...restSettings },
      } = data;

      const existingCommonSettingsResult = await ShopService.getShopSetting(shopId, LOCAL_SEO_COMMON_SCHEMA);

      const commonSchemaSettingsData = {
        ...(existingCommonSettingsResult?.value || {}),
        ...(basicInformation && { basicInformation }),
      };

      const commonSettings = {
        key: LOCAL_SEO_COMMON_SCHEMA,
        value: JSON.stringify(commonSchemaSettingsData),
        value_type: "json",
      };

      // update common settings
      const commonSettingsResult = await ShopService.updateShopSetting(shopId, commonSettings);
      await ShopifyService.setMetafield(req.user.shop, {
        ownerId: shop.shop_id,
        key: METAFIELD_KEYS.LOCAL_SEO_COMMON_SCHEMA,
        value: JSON.stringify(commonSchemaSettingsData),
      });

      const organizationSchemaData = {
        ...data,
        settings: {
          ...restSettings,
        },
      };

      const settings = {
        key: LOCAL_SEO_LOCAL_BUSINESS_SCHEMA,
        value: JSON.stringify(organizationSchemaData),
        value_type: "json",
      };

      // update Local Business schema settings
      const result = await ShopService.updateShopSetting(shopId, settings);
      await ShopifyService.setMetafield(req.user.shop, {
        ownerId: shop.shop_id,
        key: METAFIELD_KEYS.LOCAL_SEO_LOCAL_BUSINESS_SCHEMA,
        value: JSON.stringify(organizationSchemaData),
      });

      return res.success({
        message: UpdateSuccessMessage,
        data: {
          ...result?.value,
          settings: {
            ...result.value.settings,
            ...(commonSettingsResult?.value?.basicInformation && {
              basicInformation: commonSettingsResult?.value?.basicInformation,
            }),
          },
        },
      });
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: "Error updating local seo Local Business schema data" });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  }
}

module.exports = new LocalSEOController();
