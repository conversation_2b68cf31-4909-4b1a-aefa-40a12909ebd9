const logger = require("storeseo-logger");
const NotificationService = require("../services/NotificationService");
const toastMessages = require("storeseo-enums/toastMessages");

class NotificationController {
  /**
   * Handle req to GET list of notifications by applying filter
   * @param {import("express").Request} req
   * @param {import("express").Response} res
   */
  getNotifications = async (req, res) => {
    const { shopId } = req.user;
    const is_read = req.query.status ? Boolean(parseInt(req.query.status)) : undefined;
    const { limit, page } = req.query;

    try {
      const result = await NotificationService.pullNotifications(shopId, { is_read, limit, page });
      return res.success(result);
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: `Error in NotificationController->getNotifications`,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  /**
   * Handle req to update(PUT) a single notification
   * @param {import("express").Request} req
   * @param {import("express").Response} res
   */
  updateNotification = async (req, res) => {
    const { id } = req.params;

    try {
      const notification = await NotificationService.updateNotification(id, req.body);
      return res.success({ notification });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: `Error in NotificationController->updateNotification`,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  /**
   * Handle req to read(PUT) all unread notifications of a shop
   * @param {import("express").Request} req
   * @param {import("express").Response} res
   */
  readAllNotifications = async (req, res) => {
    const { shopId } = req.user;

    try {
      const readCount = await NotificationService.markAllAsRead(shopId);
      return res.success({ read: readCount });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: `Error in NotificationController->readAllNotifications`,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };
}

module.exports = new NotificationController();
