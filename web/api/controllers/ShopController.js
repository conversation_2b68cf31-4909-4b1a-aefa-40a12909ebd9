const { isNull } = require("lodash");
const fs = require("fs/promises");

const ShopService = require("../services/ShopService");
const FileService = require("../services/FileService");
const ReportService = require("../services/ReportService");
const { getFileNameFromGoogleBucketPublicURL, normalizeLogoUrlPath } = require("../utils/helper");
const seoApps = require("storeseo-enums/seoApps");
const settingKeys = require("storeseo-enums/settingKeys");
const toastMessages = require("storeseo-enums/toastMessages");
const logger = require("storeseo-logger");
const cache = require("../cache");
const BulkOperationService = require("../services/BulkOperationService");
const bulkOperationTypes = require("storeseo-enums/bulkOperationTypes");
const { LOCAL_SEO_ARTICLE_SCHEMA, LOCAL_SEO_COMMON_SCHEMA } = require("storeseo-enums/localSEOSchemaTypes");
const activityLogService = require("../../admin/services/activityLog.service");
const LogOriginTypes = require("storeseo-enums/logOriginTypes");

class ShopController {
  getShop = async (req, res) => {
    try {
      const { shop: shopDomain } = req.user;
      const shop = await ShopService.getShopIncludingAssociations({ condition: { domain: shopDomain } });
      // shop.logoURL = shop.logo_path ? FileService.buildImageURL(shop.logo_path) : "";
      shop.logoURL = shop.logo_path;
      return res.success({ shop });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getDashboardStats = async (req, res) => {
    try {
      const { shopId } = req.user;
      const stats = await ReportService.getDashboardStats(shopId);
      return res.success(stats);
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getIsOnboarded = async (req, res) => {
    try {
      const { shop: shopDomain } = req.user;
      const shop = await ShopService.getShop(shopDomain);
      const isOnboarded = isNull(shop.onboard_step);
      return res.success({ step: shop.onboard_step, isOnboarded });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  updateShopLogo = async (req, res) => {
    try {
      const { shopId, shop: shopDomain } = req.user;
      const {
        files: { logoImage },
      } = req;

      if (logoImage) {
        const shop = await ShopService.getShop(shopDomain);
        const prevLogoURL = shop.logo_path;

        const { url } = await FileService.saveUploadedFileForShop(shopDomain, logoImage);

        let updatedShop = await ShopService.updateShop(shopId, { logo_path: url });
        const setting = await ShopService.getShopSetting(shopId, settingKeys.JSONLD_DATA);
        if (setting?.value) {
          const newJsonldData = { ...setting?.value, image: url };
          updatedShop = await ShopService.upsertJsonldSetting(shopId, newJsonldData);
        }

        const localSEOCommonSettings = await ShopService.getShopSetting(shopId, LOCAL_SEO_COMMON_SCHEMA);
        if (localSEOCommonSettings?.value) {
          const newLocalSEOArticleData = { ...localSEOCommonSettings?.value, store_logo: url };
          await ShopService.updateShopLocalSEOSchema(req.user, shopId, newLocalSEOArticleData, LOCAL_SEO_COMMON_SCHEMA);
        }

        const prevLogUrlPath = normalizeLogoUrlPath(prevLogoURL);
        const newLogUrlPath = normalizeLogoUrlPath(url);

        activityLogService.addActivityLog({
          req,
          prevData: { logo: prevLogUrlPath },
          updatedData: { logo: newLogUrlPath },
          subject: "Logo Updated",
          logOrigin: LogOriginTypes.APP,
          domain: shopDomain,
        });

        res.success({
          message: toastMessages.LOGO_UPDATED,
          uploadPath: url,
          logoURL: url,
        });

        const prevLogoFileName = getFileNameFromGoogleBucketPublicURL(prevLogoURL);
        FileService.deleteFile(prevLogoFileName, true);
      }
    } catch (err) {
      console.log(`Error updating logo for shop ${req.user.shop}`, err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getGoogleServiceJson = async (req, res) => {
    try {
      const { shopId } = req.user;

      const config = await ShopService.getShopSetting(shopId, settingKeys.GOOGLE_SERVICE_JSON);

      return res.success({ config, fileName: config?.value?.fileName });
    } catch (err) {
      console.log(`Error getting google service json for shop ${req.user.shop}`, err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  updateGoogleServiceJson = async (req, res) => {
    try {
      const { shopId, shop } = req.user;
      const {
        files: { serviceJsonFile },
      } = req;

      const fileContent = serviceJsonFile.data.toString();

      const jsonContent = JSON.parse(fileContent);
      jsonContent.fileName = serviceJsonFile.name;

      const prevSetting = await ShopService.getShopSetting(shopId, settingKeys.GOOGLE_SERVICE_JSON);

      const setting = await ShopService.updateShopSetting(shopId, {
        key: settingKeys.GOOGLE_SERVICE_JSON,
        value: JSON.stringify(jsonContent),
        value_type: "json",
      });

      activityLogService.addActivityLog({
        req,
        prevData: prevSetting?.value,
        updatedData: setting?.value,
        subject: "Google Service JSON Updated",
        logOrigin: LogOriginTypes.APP,
        domain: shop,
      });

      return res.success({
        message: toastMessages.UPLOAD_SUCCESSFUL,
      });
    } catch (err) {
      console.log(`Error uploading google service json for shop ${req.user.shop}`, err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  handleDataMigrateReq = async (req, res) => {
    try {
      const { selectedApp } = req.body;

      await cache.product.migrateDataFromApp(req.user.shop, selectedApp);
      await ShopService.updateShop(req.user.shopId, { data_migrated_from_app: selectedApp });

      const hasRunningDataMigrationBulkOperation = await BulkOperationService.hasRunningBulkOperation(
        req.user.shopId,
        bulkOperationTypes.DATA_MIGRATE
      );

      if (hasRunningDataMigrationBulkOperation) throw new Error("Data migration operation already ongoing.");

      await BulkOperationService.startBulkQueryOperation({
        shopId: req.user.shopId,
        session: req.user,
        operationType: bulkOperationTypes.DATA_MIGRATE,
      });

      return res.success({
        message: `Data migration from ${seoApps[selectedApp].title} is in progress.`.replace("_", ""),
        selectedAppTitle: seoApps[selectedApp].title,
        selectedApp,
      });
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };
}

module.exports = new ShopController();
