const fs = require("fs/promises");
const FileService = require("../services/FileService");

const logger = require("storeseo-logger");
const { isEmpty } = require("lodash");
const SEOService = require("../services/SEOService");
const LocationService = require("../services/LocationService");
// const ShopService = require("../services/ShopService");
const settingKeys = require("storeseo-enums/settingKeys");
const ShopifyService = require("../services/ShopifyService");
const { METAFIELD_KEYS } = require("storeseo-enums/metafields");
const toastMessages = require("storeseo-enums/toastMessages");

class SEOController {
  getJSONLDSettings = async (req, res) => {
    try {
      const requestOriginURL = req.headers.origin || "";

      const ShopService = require("../services/ShopService");
      const shop = await ShopService.getShopByUrl(requestOriginURL);
      const { value: jsonld_data } = await ShopService.getShopSetting(shop.id, settingKeys.JSONLD_DATA);

      if (!isEmpty(shop)) {
        const jsonldData = jsonld_data || (await ShopService.generateDefaultJsonldSettings(shop.id));
        const jsonSchema = shop.plan_rules?.json_ld ? SEOService.generateJSONLDSchemaForLocalSEO(jsonldData) : null;

        return res.success({ isEnabled: shop?.jsonld_enabled, jsonSchema });
      } else {
        return res.success({});
      }
    } catch (err) {
      console.log("Get JSON LD data error:", err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getLocalSEOData = async (req, res) => {
    try {
      const { shopId } = req.user;
      const ShopService = require("../services/ShopService");

      let jsonld_data = await ShopService.getJsonldSettings(shopId);
      // if (jsonld_data?.image) jsonld_data.logoURL = FileService.buildImageURL(jsonld_data.image);
      jsonld_data.logoURL = jsonld_data?.image;

      const locations = await LocationService.getAllActiveLocations(shopId);

      return res.success({ received: true, jsonld_data, locations });
    } catch (err) {
      logger.error(err, { domain: res.user.shop, message: "Error retreiving local SEO data for shop" });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  updateLocalSEOData = async (req, res) => {
    try {
      const { shopId } = req.user;
      const { body } = req;

      const locations = JSON.parse(body.locations);

      const data = JSON.parse(body.data);
      delete data.logoURL;

      for (let location of locations) {
        await LocationService.updateLocation(location.id, location);
      }

      const ShopService = require("../services/ShopService");
      const { value } = await ShopService.upsertJsonldSetting(shopId, data);

      const shop = await ShopService.getShopById(shopId);
      const jsonldSchema = SEOService.generateJSONLDSchemaForLocalSEO(data, locations);
      await ShopifyService.setMetafield(req.user.shop, {
        ownerId: shop.shop_id,
        key: METAFIELD_KEYS.LOCAL_SEO,
        value: JSON.stringify(jsonldSchema),
      });

      return res.success({ message: "Data updated", jsonld_data: value });
    } catch (err) {
      console.log("Error updating local seo data: ", err);
      logger.error("Error updating local seo data: " + JSON.stringify(err));
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };
}

module.exports = new SEOController();
