const { isEmpty } = require("lodash/lang");

const ShopService = require("../services/ShopService");
const SubscriptionService = require("../services/SubscriptionService");
const integrationService = require("../services/IntegrationService");
const googleApiService = require("../services/GoogleApiService");
const ShopifyService = require("../services/ShopifyService");
const SubscriptionPlanService = require("../services/SubscriptionPlanService");
const CouponService = require("../services/CouponService");
const GoogleAnalyticsService = require("../services/GoogleAnalyticsService");
const WebhookService = require("../services/WebhookService");

const { META_TEMPLATES } = require("storeseo-enums/settingKeys");
const integrationSteps = require("storeseo-enums/googleIntegrationSteps");
const integrationType = require("storeseo-enums/integrationType");

const fs = require("fs/promises");
const googleAuthScopes = require("storeseo-enums/googleAuthScopes");
const GoogleAuthService = require("../services/GoogleAuthService");
const logger = require("storeseo-logger");
const { validateSubscriptionCoupon } = require("../utils/dataValidator");
const googleIntegrationInfo = require("storeseo-enums/googleIntegrationInfo");

const cache = require("../cache");
const settingKeys = require("storeseo-enums/settingKeys");
const { METAFIELD_KEYS } = require("storeseo-enums/metafields");
const cacheKeys = require("storeseo-enums/cacheKeys");
const toastMessages = require("storeseo-enums/toastMessages");
const { HTML_SITEMAP, IMAGE_OPTIMIZER } = require("storeseo-enums/settingKeys");
const SubscriptionAddonService = require("../services/SubscriptionAddonService");
const { EMAIL_NOTIFICATION } = require("storeseo-enums/settings/email-notification");
const mailService = require("../services/MailService");
const { dispatch } = require("../queue/jobs/products/MultiLanguageProductSyncQueue");
const { dispatchQueue } = require("../queue/queueDispatcher");
const { QUEUE_NAMES } = require("../queue/config");
const { isSubscriptionTestMode } = require("../config/app");
const OptimizationService = require("../services/OptimizationService");
const { defaultEmailNotificationSettings } = require("../config/email-notification");
const {
  subscrptionPlansFeaturesSerializer,
  subscriptionPlansFeaturesSerializer,
} = require("../serializers/SubscriptionSerializer");
const activityLogService = require("../../admin/services/activityLog.service");
const LogOriginTypes = require("storeseo-enums/logOriginTypes");

class SettingController {
  getSeoSettingData = async (req, res) => {
    try {
      const { shopId } = req.user;
      // const shop = await ShopService.getShopById(shopId);
      // const tags = seoTags || [];
      const setting = await ShopService.getShopSetting(shopId, META_TEMPLATES);

      return res.success({
        // tags,
        // shop,
        setting,
      });
    } catch (err) {
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  storeSeoSetting = async (req, res) => {
    try {
      const { shopId, shop } = req.user;
      const { templatePageTitle, templateMetaDescription, templateImgAltText } = req.body;

      const data = {
        key: META_TEMPLATES,
        value: JSON.stringify({
          pageTitle: templatePageTitle,
          metaDescription: templateMetaDescription,
          imgAltText: templateImgAltText,
        }),
        value_type: "json",
      };

      const prevSetting = await ShopService.getShopSetting(shopId, META_TEMPLATES);
      const setting = await ShopService.updateShopSetting(shopId, data);

      activityLogService.addActivityLog({
        req,
        prevData: prevSetting?.value,
        updatedData: setting?.value,
        subject: "Bulk SEO Optimization Updated",
        logOrigin: LogOriginTypes.APP,
        domain: shop,
      });

      return res.success({
        setting,
        message: toastMessages.SEO_SETTINGS_UPDATED,
      });
    } catch (err) {
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getJSONLDStatus = async (req, res) => {
    try {
      const { shopId } = req.user;
      const shop = await ShopService.getShopById(shopId);

      return res.success({ isEnabled: shop?.jsonld_enabled });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  toggleJSONLDStatus = async (req, res) => {
    try {
      const { shopId } = req.user;
      const shop = await ShopService.getShopById(shopId);

      const newJSONLDStatus = !shop?.jsonld_enabled;
      await ShopifyService.setMetafield(req.user.shop, {
        ownerId: shop.shop_id,
        key: METAFIELD_KEYS.JSON_LD_ENABLED,
        value: String(newJSONLDStatus),
      });
      const updatedShop = await ShopService.updateShop(shopId, { jsonld_enabled: newJSONLDStatus });

      return res.success({ isEnabled: newJSONLDStatus, message: toastMessages.STATUS_UPDATED });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getGoogleIndexingStatus = async (req, res) => {
    try {
      const { shopId } = req.user;
      const shop = await ShopService.getShopById(shopId);
      const { value: google_integration_info } = await ShopService.getShopSetting(
        shopId,
        settingKeys.GOOGLE_INTEGRATION_INFO
      );
      const hasIndexingPermission = google_integration_info?.steps?.indexingPermission;
      return res.success({
        isEnabled: shop.google_indexing_enabled,
        hasIndexingPermission,
      });
    } catch (err) {
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  toggleGoogleIndexingStatus = async (req, res) => {
    try {
      const { shopId } = req.user;
      const { status } = req.body;
      const updatedShop = await ShopService.updateShop(shopId, { google_indexing_enabled: status });

      const ProductService = require("../services/ProductService");
      await ProductService.indexAllProducts(req.user, updatedShop.google_indexing_enabled);

      return res.success({
        isEnabled: updatedShop.google_indexing_enabled,
        message: toastMessages.STATUS_UPDATED,
      });
    } catch (err) {
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getSubscriptions = async (req, res) => {
    const SubscriptionPlanService = require("../services/SubscriptionPlanService");
    try {
      const { planId } = req.user;
      const { type } = req.query;
      const plans = await SubscriptionPlanService.getActiveSubscriptionPlans(planId, type);
      const { headings, features } = subscriptionPlansFeaturesSerializer(plans);
      return res.success({ plans, features, headings });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  subscribeToPlan = async (req, res) => {
    try {
      const { slug, coupon, addons } = req.body;
      const { shopId, isOnboarded } = req.user;
      const { session } = res.locals.shopify;

      const { appSubscription, confirmationUrl, isFreeSubscription } = await SubscriptionService.subscriptionCreate(
        session,
        {
          shopId,
          slug,
          couponCode: coupon,
          addons,
        }
      );
      return res.success({
        appSubscription,
        confirmationUrl,
        isFreeSubscription,
        isOnboarded,
        message: toastMessages.SUBSCRIPTION_SUCCESSFUL,
      });
    } catch (err) {
      console.error(err);
      return res.failed({ message: err.message });
    }
  };

  cancelSubscription = async (req, res) => {
    try {
      const { shopId } = req.user;
      const { session } = res.locals.shopify;

      const subscriptionPlan = await SubscriptionPlanService.getFreeSubscriptionPlan();
      const shopData = await ShopService.getShopById(shopId);
      const combinedAddons = await SubscriptionPlanService.getCombinedAddons(subscriptionPlan.addons);
      await SubscriptionService.handleFreeSubscription(session, {
        shopId,
        subscriptionPlan,
        shopData,
        addons: combinedAddons || [],
      });

      return res.success({
        message: toastMessages.SUBSCRIPTION_CANCELLED,
      });
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return res.failed({ message: err.message });
    }
  };

  getGoogleIntegration = async (req, res) => {
    try {
      const { shopId, permission } = req.user;

      if (permission.google_console === false) {
        return res.failed({ message: toastMessages.PERMISSION_DENIED });
      }

      const googleConfig = await integrationService.getConfig(shopId, integrationType.GOOGLE);
      return res.success({ googleConfig });
    } catch (err) {
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  storeGoogleIntegration = async (req, res) => {
    try {
      const { shopId } = req.user;
      const googleConfigs = {
        ...req.body,
      };
      const configs = await integrationService.insertOrUpdateConfig(
        shopId,
        integrationType.GOOGLE,
        JSON.stringify(googleConfigs)
      );
      return res.success({
        configs,
        message: toastMessages.GOOGLE_CONFIG_UPDATED,
      });
    } catch (err) {
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getGoogleServiceJSON = async (req, res) => {
    try {
      const { shopId, permission } = req.user;

      if (permission.google_console === false) {
        return res.failed({ message: toastMessages.PERMISSION_DENIED });
      }

      const googleConfig = await integrationService.getConfig(shopId, integrationType.GOOGLE_SERVICE_JSON);
      if (googleConfig) {
        const { configs } = googleConfig;
        return res.success({ configs });
      } else {
        return res.failed({ message: toastMessages.SERVICE_ACCOUNT_CONFIG_NOT_SET });
      }
    } catch (err) {
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  storeGoogleServiceJSON = async (req, res) => {
    const file = req.files.json_file;
    if (!file || file.size === 0 || file.type !== "application/json") {
      return res.failed({ message: toastMessages.INVALID_JSON_FILE });
    }

    try {
      const { shopId, shop } = req.user;
      const path = `./uploads/${shop}`;
      await fs.mkdir(path, { recursive: true });
      const destinationPath = `${path}/${file.name}`;
      const filePath = destinationPath.replace("./uploads/", "");
      await fs.rename(file.path, destinationPath);

      const { configs } = await integrationService.insertOrUpdateConfig(shopId, integrationType.GOOGLE_SERVICE_JSON, {
        file_path: filePath,
      });
      return res.success({
        configs,
        message: toastMessages.SERVICE_JSON_UPDATED,
      });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  connectToGoogle = async (req, res) => {
    try {
      const { shopId, permission } = req.user;

      if (permission.google_console === false) {
        return res.failed({ message: toastMessages.PERMISSION_DENIED });
      }

      const config = await integrationService.getConfig(shopId, integrationType.GOOGLE);

      if (isEmpty(config)) {
        return res.failed({ message: toastMessages.SET_A_CONFIG_FIRST });
      }
      const authUrl = await googleApiService.getOAuthUrl(req.user, config);

      return res.success({ authUrl });
    } catch (err) {
      return res.failed({ message: err.toString() });
    }
  };

  updateAccessTokens = async (req, res) => {
    try {
      const { permission } = req.user;

      if (permission.google_console === false) {
        return res.failed({ message: toastMessages.PERMISSION_DENIED });
      }

      const { code } = req.body;
      const tokens = await googleApiService.getAccessTokens(req.user, code);
      const config = await integrationService.updateAccessTokens(req.user, tokens);
      return res.success({
        config,
        message: toastMessages.GOOGLE_CONNECTED,
      });
    } catch (err) {
      return res.failed({ message: err.toString() });
    }
  };

  getSubscriptionTransactions = async (req, res) => {
    const SubscriptionTransactionService = require("../services/SubscriptionTransactionService");
    try {
      const { shopId } = req.user;
      const { transactions, pagination } = await SubscriptionTransactionService.getTransactionsByShopId(
        shopId,
        req.query
      );

      return res.success({ transactions, pagination });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  // todo: 100% coupon check & webhook checking...
  getCheckoutPageData = async (req, res) => {
    try {
      const { slug } = req.params;
      const { coupon: couponCode } = req.query;
      const { shopId } = req.user;
      const shop = await ShopService.getShopById(shopId);
      const plan = await SubscriptionPlanService.getSubscriptionPlanByConditions({ slug });
      const combinedAddons = await SubscriptionPlanService.getCombinedAddons(plan.addons || [], false);
      const couponDetails = await CouponService.getCouponDetail(couponCode);
      const planData = await SubscriptionService.getSerializeCheckoutPageData(shop, plan, couponDetails);
      const addons = await SubscriptionAddonService.getGrandfatheredAddons();
      return res.success({ planData, combinedAddons, addons });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  validateCoupon = async (req, res) => {
    try {
      const { error } = await validateSubscriptionCoupon(req.body);
      if (error) {
        return res.failedValidation({ errors: error.details });
      }

      const { slug, coupon: couponCode } = req.body;
      const { shopId } = req.user;
      const shop = await ShopService.getShopById(shopId);
      const { plan, coupon } = await SubscriptionService.validateCouponCode({
        activePlanId: shop.plan_id,
        planSlug: slug,
        couponCode,
      });

      const planData = await SubscriptionService.getSerializeCheckoutPageData(shop, plan, coupon);
      return res.success({
        planData,
        message: toastMessages.COUPON_APPLIED,
      });
    } catch (err) {
      console.log("validateCoupon", err);
      return res.failed({ message: err.message });
    }
  };

  getGoogleIntegrationInfo = async (req, res) => {
    try {
      const { shopId } = req.user;
      const shop = await ShopService.getShopById(shopId);
      const { value: googleIntegrationInfo } = await ShopService.getShopSetting(
        shopId,
        settingKeys.GOOGLE_INTEGRATION_INFO
      );
      const appHandle = await ShopifyService.getAppHandle(req.user.shop);
      const redirectURL = `${shop.url}/admin/apps/${appHandle}/settings/google-integration`;
      const scopes = [
        googleAuthScopes.EMAIL,
        googleAuthScopes.SITE_VERIFICATION_READ_WRITE,
        googleAuthScopes.SEARCH_CONSOLE_READ_WRITE,
        googleAuthScopes.INSTANT_INDEXING_READ_WRITE,
        googleAuthScopes.ANALYTICS_DATA_READ_WRITE,
      ];

      const {
        authenticated,
        siteVerified,
        searchConsoleAdded,
        sitemapSubmitted,
        indexingPermission,
        analyticsDataPermission,
      } = googleIntegrationInfo.steps;

      googleIntegrationInfo.steps = {
        [integrationSteps.AUTHENTICATE]: authenticated,
        [integrationSteps.SITE_VERIFICATION]: siteVerified,
        [integrationSteps.SEARCH_CONSOLE_ADD_SITE]: searchConsoleAdded,
        [integrationSteps.SITEMAP_SUBMISSION]: sitemapSubmitted,
        [integrationSteps.INSTANT_INDEXING]: indexingPermission,
        [integrationSteps.ANALYTICS_DATA]: analyticsDataPermission,
      };

      const authURLs = {
        [integrationSteps.AUTHENTICATE]: GoogleAuthService.generateAuthURL({
          shopDomain: shop?.domain,
          scopesArr: scopes.slice(0, 1),
          authTargetAction: integrationSteps.AUTHENTICATE,
          redirectURL: `${redirectURL}?${encodeURI(`message=${toastMessages.SIGN_IN_COMPLETED}`)}`,
        }),
        [integrationSteps.SITE_VERIFICATION]: GoogleAuthService.generateAuthURL({
          shopDomain: shop?.domain,
          scopesArr: scopes.slice(0, 2),
          authTargetAction: integrationSteps.SITE_VERIFICATION,
          redirectURL: `${redirectURL}?${encodeURI(`message=${toastMessages.VERIFICATION_DONE}`)}`,
        }),
        [integrationSteps.SEARCH_CONSOLE_ADD_SITE]: GoogleAuthService.generateAuthURL({
          shopDomain: shop?.domain,
          scopesArr: scopes.slice(0, 3),
          authTargetAction: integrationSteps.SEARCH_CONSOLE_ADD_SITE,
          redirectURL: `${redirectURL}?${encodeURI(`message=${toastMessages.SEARCH_CONSOLE_LINKED}`)}`,
        }),
        [integrationSteps.SITEMAP_SUBMISSION]: GoogleAuthService.generateAuthURL({
          shopDomain: shop?.domain,
          scopesArr: scopes.slice(0, 3),
          authTargetAction: integrationSteps.SITEMAP_SUBMISSION,
          redirectURL: `${redirectURL}?${encodeURI(`message=${toastMessages.SITEMAP_PERMITTED}`)}`,
        }),
        [integrationSteps.INSTANT_INDEXING]: GoogleAuthService.generateAuthURL({
          shopDomain: shop?.domain,
          scopesArr: scopes.slice(0, 4),
          authTargetAction: integrationSteps.INSTANT_INDEXING,
          redirectURL: `${redirectURL}?${encodeURI(`message=${toastMessages.INDEXING_ALLOWED}`)}&authTarget=${
            integrationSteps.INSTANT_INDEXING
          }`,
        }),
        [integrationSteps.ANALYTICS_DATA]: GoogleAuthService.generateAuthURL({
          shopDomain: shop?.domain,
          scopesArr: scopes.slice(0, 5),
          authTargetAction: integrationSteps.ANALYTICS_DATA,
          redirectURL: `${redirectURL}?${encodeURI(`message=${toastMessages.ANALYTICS_ENABLED}`)}`,
        }),
      };

      return res.success({ integrationInfo: googleIntegrationInfo, authURLs });
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  resetGoogleIntegrationInfo = async (req, res) => {
    try {
      const { shopId } = req.user;
      const { value: savedGoogleInfo } = await ShopService.getShopSetting(shopId, settingKeys.GOOGLE_INTEGRATION_INFO);

      try {
        const revokedToken = await GoogleAuthService.revokeAuthToken(savedGoogleInfo.googleUserEmail);
      } catch (err) {}

      await ShopService.upsertGoogleIntegrationSetting(shopId, googleIntegrationInfo);
      return res.success({ message: toastMessages.GOOGLE_DISCONNECTED });
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: `Error re-setting google integration info` });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  updateGoogleIntegrationInfo = async (req, res) => {
    try {
      const { shopId } = req.user;
      const updatedFields = req.body;

      const { value: googleIntegrationInfo } = await ShopService.getShopSetting(
        shopId,
        settingKeys.GOOGLE_INTEGRATION_INFO
      );

      const updatedInfo = { ...googleIntegrationInfo, ...updatedFields };
      await ShopService.upsertGoogleIntegrationSetting(shopId, updatedInfo);

      const {
        authenticated,
        siteVerified,
        searchConsoleAdded,
        sitemapSubmitted,
        indexingPermission,
        analyticsDataPermission,
      } = updatedInfo.steps;

      updatedInfo.steps = {
        [integrationSteps.AUTHENTICATE]: authenticated,
        [integrationSteps.SITE_VERIFICATION]: siteVerified,
        [integrationSteps.SEARCH_CONSOLE_ADD_SITE]: searchConsoleAdded,
        [integrationSteps.SITEMAP_SUBMISSION]: sitemapSubmitted,
        [integrationSteps.INSTANT_INDEXING]: indexingPermission,
        [integrationSteps.ANALYTICS_DATA]: analyticsDataPermission,
      };

      return res.success({ message: toastMessages.DATA_UPDATED, integrationInfo: updatedInfo });
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: `Error updating google integration info` });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  verifyGoogleAnalyticsPropertyId = async (req, res) => {
    try {
      const { shopId, shop } = req.user;
      const { analyticsPropertyId } = req.body;

      let authenticatedUser = await ShopService.getGoogleIntegrationInfoWithAuthUserDetails(shopId);
      if (isSubscriptionTestMode) {
        authenticatedUser = GoogleAuthService.getDefaultAuthenticatedUser();
      }

      const serviceJsonSetting = await ShopService.getShopSetting(shopId, settingKeys.GOOGLE_SERVICE_JSON);
      if (serviceJsonSetting) {
        authenticatedUser.serviceJson = serviceJsonSetting.value;
      }

      const isValid = await GoogleAnalyticsService.verifyPropertyId(authenticatedUser, analyticsPropertyId);

      activityLogService.addActivityLog({
        req,
        prevData: null,
        updatedData: analyticsPropertyId,
        subject: "Google Analytics Property Id Verified",
        logOrigin: LogOriginTypes.APP,
        domain: shop,
      });

      return res.json({
        isValid,
        analyticsPropertyId,
      });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: `Error verifying google analytics property id`,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getRedirectOutOfStockInfo = async (req, res) => {
    try {
      const { shopId } = req.user;
      const {
        out_of_stock_redirect_enabled: status,
        out_of_stock_redirect_url: redirectURL,
        url: hompageURL,
      } = await ShopService.getShopById(shopId);

      res.json({ status, redirectURL: redirectURL || hompageURL });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: `Error retrieving redirect out of stock feature info`,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  updateRedirectOutOfStockInfo = async (req, res) => {
    try {
      const { shopId } = req.user;
      const { status, redirectURL } = req.body;

      const shop = await ShopService.getShopById(shopId);

      const metas = [
        {
          ownerId: shop.shop_id,
          key: METAFIELD_KEYS.REDIRECT_OUT_OF_STOCK_ENABLED,
          value: String(status),
        },
        {
          ownerId: shop.shop_id,
          key: METAFIELD_KEYS.REDIRECT_OUT_OF_STOCK_URL,
          value: String(redirectURL || "/"),
        },
      ];
      await ShopifyService.setMetafields(req.user.shop, metas);

      const data = {
        out_of_stock_redirect_enabled: status,
        out_of_stock_redirect_url: redirectURL,
      };
      const updatedShop = await ShopService.updateShop(shopId, data);

      activityLogService.addActivityLog({
        req,
        prevData: {
          status: shop.out_of_stock_redirect_enabled,
          redirectURL: shop.out_of_stock_redirect_url,
        },
        updatedData: {
          status: updatedShop.out_of_stock_redirect_enabled,
          redirectURL: updatedShop.out_of_stock_redirect_url,
        },
        subject: "Redirect Out of Stock Feature Updated",
        logOrigin: LogOriginTypes.APP,
        domain: shop.domain,
      });

      res.success({
        newStatus: updatedShop.out_of_stock_redirect_enabled,
        redirectURL: updatedShop.out_of_stock_redirect_url,
        message: toastMessages.STATUS_UPDATED,
      });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: `Error updating redirect out of stock feature info`,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  hideAdForFreeUser = async (req, res) => {
    try {
      const hideAdvertise = await cache.setHideProPackageAd(req.user.shop);
      return res.success({ message: "ok", hideAdvertise });
    } catch (err) {
      console.log("err: ", err);
      logger.error(err, {
        domain: req.user.shop,
        message: `Error hiding ad for free user. Error ${JSON.stringify(err)}`,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  hideBetterDocsPromo = async (req, res) => {
    try {
      const hideBetterDocsPromo = await cache.hideBetterDocsPromo(req.user.shop);
      return res.success({ message: "ok", hideBetterDocsPromo });
    } catch (err) {
      console.log("err: ", err);
      logger.error(err, {
        domain: req.user.shop,
        message: `Error hiding ad for free user. Error ${JSON.stringify(err)}`,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  hideBanner = async (req, res) => {
    try {
      const {
        session: { shop },
      } = res.locals.shopify;
      const { key, value, expiry } = req.body;

      console.log("key", key);

      if (!cacheKeys.hasOwnProperty(key)) {
        throw Error("Invalid cache key");
      }

      await cache.setCache(shop, key, value, expiry);
      res.status(200).json({ success: true, key, value });
    } catch (e) {
      res.status(400).json({ message: e.message });
    }
  };

  getHtmlSitemapSetupInfo = async (req, res) => {
    try {
      const { shopId } = req.user;
      const setting = await ShopService.getShopSetting(shopId, HTML_SITEMAP);

      if (!setting) {
        return res.success({});
      }

      return res.success(setting.value);
    } catch (err) {
      return res.failed({ message: "Something went wrong." });
    }
  };

  updateHtmlSitemapSetupInfo = async (req, res) => {
    try {
      const { shopId, shop } = req.user;

      const existingSetting = await ShopService.getShopSetting(shopId, HTML_SITEMAP);
      const newSetting = {
        ...(existingSetting?.value || {}),
        ...req.body,
      };

      const data = {
        key: HTML_SITEMAP,
        value: JSON.stringify(newSetting),
        value_type: "json",
      };

      const result = await ShopService.updateShopSetting(shopId, data);

      activityLogService.addActivityLog({
        req,
        prevData: null,
        updatedData: {
          ...result.value.setting,
          branding: result.value.branding,
          setupStep: result?.value?.setupStep,
        },
        subject: "HTML Sitemap Generated",
        logOrigin: LogOriginTypes.APP,
        domain: shop,
      });

      return res.success({
        data: newSetting,
        message: toastMessages.HTML_SITEMAP_UPDATED,
      });
    } catch (err) {
      console.log("err: ", err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getHtmlSitemapSetting = async (req, res) => {
    try {
      const { shopId } = req.user;
      const setting = await ShopService.getShopSetting(shopId, HTML_SITEMAP);

      if (!setting) {
        return res.success({});
      }

      return res.success({
        sitemapSetting: setting.value.setting,
      });
    } catch (err) {
      return res.failed({ message: "Something went wrong." });
    }
  };

  storeHtmlSitemapSetting = async (req, res) => {
    try {
      const { shopId, shop } = req.user;

      const existingSetting = await ShopService.getShopSetting(shopId, HTML_SITEMAP);
      const newSetting = {
        ...(existingSetting?.value || {}),
        setting: req.body,
      };

      if (!("branding" in newSetting)) newSetting.branding = true;

      const data = {
        key: HTML_SITEMAP,
        value: JSON.stringify(newSetting),
        value_type: "json",
      };

      const setting = await ShopService.updateShopSetting(shopId, data);

      activityLogService.addActivityLog({
        req,
        prevData: { ...existingSetting.value.setting, branding: existingSetting.value.branding },
        updatedData: { ...setting.value.setting, branding: setting.value.branding },
        subject: "HTML Sitemap Settings Updated",
        logOrigin: LogOriginTypes.APP,
        domain: shop,
      });

      return res.success({
        setting,
        message: toastMessages.HTML_SITEMAP_UPDATED,
      });
    } catch (err) {
      console.log("err: ", err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  updateImageOptimizerSetting = async (req, res) => {
    try {
      const { shopId, shop } = req.user;
      const newSetting = req.body;

      const autoOptimization = newSetting.autoOptimization;
      delete newSetting.autoOptimization;
      const isNoneSelected = Object.values(newSetting).filter((val) => val !== "none").length === 0;

      const valueToUpdate = {
        ...newSetting,
        autoOptimization: isNoneSelected ? false : autoOptimization,
      };

      const data = {
        key: IMAGE_OPTIMIZER,
        value: JSON.stringify(valueToUpdate),
        value_type: "json",
      };

      const prevImageOptimizerSetting = await ShopService.getShopSetting(shopId, IMAGE_OPTIMIZER);
      const updatedSetting = await ShopService.updateShopSetting(shopId, data);

      await ShopService.updateShop(shopId, {
        onboard_step: 3,
      });

      activityLogService.addActivityLog({
        req,
        prevData: prevImageOptimizerSetting?.value,
        updatedData: updatedSetting?.value,
        subject: "Image Optimizer Setting Updated",
        logOrigin: LogOriginTypes.APP,
        domain: shop,
      });

      return res.success({
        data: newSetting,
        message: toastMessages.IMAGE_OPTIMIZER_UPDATED,
      });
    } catch (err) {
      console.error("updateImageOptimizerSetting: ", err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getImageOptimizerSetting = async (req, res) => {
    try {
      const { shopId } = req.user;
      const setting = await ShopService.getShopSetting(shopId, IMAGE_OPTIMIZER);
      return res.success({ data: setting?.value || null });
    } catch (err) {
      return res.failed({ message: "Something went wrong." });
    }
  };

  toggleAutoImageOptimization = async (req, res) => {
    try {
      const { shopId, shop } = req.user;
      const { autoOptimization } = req.body;

      const setting = await ShopService.getShopSetting(shopId, IMAGE_OPTIMIZER);

      const data = {
        key: IMAGE_OPTIMIZER,
        value: JSON.stringify({
          ...(setting?.value || {}),
          autoOptimization,
        }),
        value_type: "json",
      };

      const updatedSetting = await ShopService.updateShopSetting(shopId, data);

      activityLogService.addActivityLog({
        req,
        prevData: setting?.value,
        updatedData: updatedSetting?.value,
        subject: "Auto Image Optimizer Setting Updated",
        logOrigin: LogOriginTypes.APP,
        domain: shop,
      });

      return res.success({
        data: updatedSetting.value,
        message: autoOptimization ? toastMessages.AUTO_OPTIMIZER_ENABLED : toastMessages.AUTO_OPTIMIZER_DISABLED,
      });
    } catch (err) {
      return res.failed({ message: "Something went wrong" });
    }
  };

  getEmailNotificationSetting = async (req, res) => {
    try {
      const { shopId } = req.user;
      const setting = await ShopService.getShopSetting(shopId, EMAIL_NOTIFICATION);

      return res.success({ setting: setting?.value || JSON.parse(defaultEmailNotificationSettings.value) });
    } catch (err) {
      return res.failed({ message: "Something went wrong." });
    }
  };

  storeEmailNotificationSetting = async (req, res) => {
    try {
      const settings = req.body;
      const { shopId, email, shop } = req.user;

      const data = {
        key: EMAIL_NOTIFICATION,
        value: JSON.stringify(settings),
        value_type: "json",
      };

      try {
        if (!isEmpty(settings)) {
          await mailService.removeFromUnsubscribeList(email);
        }
      } catch (error) {
        if (error.status === 404) {
          console.log("User not found in unsubscribe list");
        }
      }

      const prevEmailSetting = await ShopService.getShopSetting(shopId, EMAIL_NOTIFICATION);

      const setting = await ShopService.updateShopSetting(shopId, data);

      activityLogService.addActivityLog({
        req,
        prevData: {
          ...prevEmailSetting?.value?.items?.WEEKLY_STORE_REPORT,
          EMAIL_NOTIFICATION_REPORT: prevEmailSetting.value.EMAIL_NOTIFICATION_REPORT,
        },
        updatedData: {
          ...setting?.value?.items?.WEEKLY_STORE_REPORT,
          EMAIL_NOTIFICATION_REPORT: setting.value.EMAIL_NOTIFICATION_REPORT,
        },
        subject: "Email Notification Setting Updated",
        logOrigin: LogOriginTypes.APP,
        domain: shop,
      });

      return res.success({
        setting,
        message: toastMessages.NOTIFICATION_SETTINGS_UPDATED,
      });
    } catch (error) {
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getMultiLanguageSetting = async (req, res) => {
    try {
      const { shopId, shop } = req.user;
      const settingData = await ShopService.getShopSetting(shopId, settingKeys.MULTI_LANGUAGE_SETTING);

      /** @type {{ enabled: boolean, shopLocales: (import("../services/shopify/LocalaizationService").ShopLocale & { syncOngoing: boolean})[]}} */
      let setting = settingData?.value;
      if (!setting) {
        setting = { enabled: false, shopLocales: [] };

        dispatchQueue({
          queueName: QUEUE_NAMES.SYNC_SHOP_LOCALES_LIST,
          message: {
            headers: {
              "x-shopify-shop-domain": shop,
            },
          },
        });
        // WebhookService.registerAllWebhooks(req.user, shopId, 1);
      }

      for (let locale of setting.shopLocales) {
        locale.syncOngoing = await cache.product.mulitLanguageSyncOngoing(shop, locale.locale);
      }

      return res.success({ setting });
    } catch (err) {
      return res.failed({ message: "Something went wrong." });
    }
  };

  toggleMultiLanguageSupport = async (req, res) => {
    try {
      const { shopId, shop } = req.user;
      const { enabled } = req.body;

      await cache.shop.enabledMultiLanguage(shop, enabled);

      const setting = await ShopService.getShopSetting(shopId, settingKeys.MULTI_LANGUAGE_SETTING);

      const data = {
        key: settingKeys.MULTI_LANGUAGE_SETTING,
        value: JSON.stringify({
          ...(setting?.value || {}),
          enabled,
        }),
        value_type: "json",
      };

      const updatedSetting = await ShopService.updateShopSetting(shopId, data);

      activityLogService.addActivityLog({
        req,
        prevData: { enabled: setting.value?.enabled, ...setting.value.shopLocales },
        updatedData: { enabled: updatedSetting.value?.enabled, ...updatedSetting.value.shopLocales },
        subject: "Multilingual SEO Support Updated",
        logOrigin: LogOriginTypes.APP,
        domain: shop,
      });

      return res.success({
        setting: updatedSetting.value,
        message: enabled ? toastMessages.MULTI_LANGUAGE_ENABLED : toastMessages.MULTI_LANGUAGE_DISABLED,
      });
    } catch (err) {
      console.log(err);
      return res.failed({ message: "Something went wrong" });
    }
  };

  syncMultiLanguageData = async (req, res) => {
    try {
      const { shop, shopId } = req.user;

      const { locale } = req.body;

      activityLogService.addActivityLog({
        req,
        prevData: null,
        updatedData: null,
        subject: `Multilingual Sync Started for language ${locale}`,
        logOrigin: LogOriginTypes.APP,
        domain: shop,
      });

      await cache.product.mulitLanguageSyncOngoing(shop, locale, true);
      await cache.product.multiLanugageSyncCursor(shop, locale, 0);

      dispatchQueue({
        queueName: QUEUE_NAMES.MULTI_LANGUAGE_PRODUCT_SYNC_QUEUE,
        message: {
          shopId,
          shopDomain: shop,
          languageCode: locale,
          session: req.user,
        },
      });

      return res.success({ status: true, message: toastMessages.LANGUAGE_SYNC_STARTED });
    } catch (err) {
      return res.failed({ message: "Something went wrong" });
    }
  };

  checkAppEmbedStatus = async (req, res) => {
    try {
      const { shop, accessToken } = req.user;
      const session = { shop, accessToken };

      const currentTheme = await ShopifyService.onlineStore.getCurrentTheme(session.shop, [
        "config/settings_data.json",
      ]);
      const configFileContent = currentTheme.files.edges?.[0]?.node?.body.content;
      const settings = JSON.parse(configFileContent.substr(configFileContent.indexOf("{")));

      const storeSeoMetaBlock = Object.values(settings.current.blocks).find((block) =>
        block.type.includes("store_seo_meta")
      );

      const status = storeSeoMetaBlock ? !storeSeoMetaBlock.disabled : false;

      await cache.appEmbedStatus(shop, status);

      return res.success({ status });
    } catch (err) {
      console.log("Error checking theme app embed status: ", err);
      return res.failed({});
    }
  };

  getBulkSEOUpdateData = async (req, res) => {
    try {
      const { shopId } = req.user;

      const optimizationData = await OptimizationService.getOptimizationData(shopId);

      return res.success({ optimizationData });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  submitBulkSEOUpdateData = async (req, res) => {
    try {
      const { shopId, shop } = req.user;

      const { bpoOption } = req.body;

      const setting = await ShopService.getShopSetting(shopId, META_TEMPLATES);
      const metaTemplates = setting?.value || null;

      if (!metaTemplates) {
        return res.failed({
          message: toastMessages.SET_TEMPLATE_FOR_OPTIMIZATION_FIRST,
        });
      }

      res.success({
        message: toastMessages.AUTO_FIX_STARTED,
      });

      // await optimizationService.startProductOptimizationTask(shopId, metaTemplates);
      await OptimizationService.startProductOptimizationViaBulkOperation({ shopId, shop, bpoOption });
    } catch (err) {
      console.log("optimization error: ", err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };
}

module.exports = new SettingController();
