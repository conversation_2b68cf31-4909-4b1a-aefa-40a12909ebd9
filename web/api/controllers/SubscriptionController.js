const SubscriptionAddonService = require("../services/SubscriptionAddonService");
const SubscriptionService = require("../services/SubscriptionService");

class SubscriptionController {
  creditAddons = async (req, res) => {
    try {
      const groups = await SubscriptionAddonService.getCreditAddons();
      return res.success({ groups });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  creditAddonsPurchase = async (req, res) => {
    try {
      const { session } = res.locals.shopify;
      const { addons } = req.body;
      const { appPurchaseOneTime, confirmationUrl } = await SubscriptionService.creditPurchaseCreate(session, addons);
      return res.success({ appPurchaseOneTime, confirmationUrl });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };
}

module.exports = new SubscriptionController();
