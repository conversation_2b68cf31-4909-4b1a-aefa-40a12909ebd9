// const { Industry } = require("../sequelize/models");
const { Industry: IndustryModel, SubscriptionPlan } = require("../../sequelize");
const { ACTIVE } = require("storeseo-enums/planStatus");
const toastMessages = require("storeseo-enums/toastMessages");

/**
 * @type {typeof import('sequelize').Model}
 */
const Industry = IndustryModel;

class CommonController {
  industries = async (req, res) => {
    try {
      const result = await Industry.findAll();
      return res.success({ industries: result.map((r) => r.toJSON()) });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  packages = async (req, res) => {
    try {
      const packages = await SubscriptionPlan.findAll({ where: { status: ACTIVE } });
      return res.success({ packages: packages.map((p) => p.toJSON()) });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };
}

module.exports = new CommonController();
