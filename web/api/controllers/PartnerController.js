const toastMessages = require("storeseo-enums/toastMessages");
const partnerService = require("../services/PartnerService");

class PartnerController {
  /**
   * @param {import("express").Request} req
   * @param {import("express").Response} res
   */

  getPartnersList = async (req, res) => {
    try {
      const { count, partners } = await partnerService.getPartnersList();
      return res.success({ count, partners });
    } catch (err) {
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };
}

module.exports = new PartnerController();
