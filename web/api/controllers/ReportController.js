const ProductService = require("../services/ProductService");
const reportService = require("../services/ReportService");
const PageService = require("../services/PageService");
const ArticleService = require("../services/ArticleService");
const toastMessages = require("storeseo-enums/toastMessages");
const CollectionService = require("../services/collections/CollectionService");
const DocService = require("../services/docs/DocService");
const { PRODUCT, PAGE, ARTICLE, COLLECTION, DOC } = require("storeseo-enums/analysisEntityTypes");

class ReportController {
  reports = async (req, res) => {
    try {
      const { shopId } = req.user;
      const result = await Promise.allSettled([
        reportService.getBlogArticlesReportData(shopId),
        reportService.getProductsReportData(shopId),
        reportService.getPagesReportData(shopId),
        reportService.getCollectionsReportData(shopId),
        reportService.getDocsReportData(shopId),
      ]);

      const productStats = result.find((r) => r.value?.type === PRODUCT)?.value || {};
      const pageStats = result.find((r) => r.value?.type === PAGE)?.value || {};
      const blogArticleStats = result.find((r) => r.value?.type === ARTICLE)?.value || {};
      const collectionStats = result.find((r) => r.value?.type === COLLECTION)?.value || {};
      const docStats = result.find((r) => r.value?.type === DOC)?.value || {};

      const fields = ["id", "title", "score"];
      const [
        topScoredProducts,
        leastScoredProducts,
        topScoredCollections,
        leastScoredCollections,
        topScoredPages,
        leastScoredPages,
        topScoredBlogs,
        leastScoredBlogs,
        topScoredDocs,
        leastScoredDocs,
      ] = await Promise.allSettled([
        ProductService.getTopScoredProducts(shopId, { fields }),
        ProductService.getLeastScoredProducts(shopId, { fields }),
        CollectionService.getTopScored(shopId, { fields }),
        CollectionService.getLeastScored(shopId, { fields }),
        PageService.getTopScoredPages(shopId, { fields }),
        PageService.getLeastScoredPages(shopId, { fields }),
        ArticleService.getTopScoredArticles(shopId, { fields }),
        ArticleService.getLeastScoredArticles(shopId, { fields }),
        DocService.getTopScored(shopId, { fields }),
        DocService.getLeastScored(shopId, { fields }),
      ]);

      return res.success({
        productStats: {
          ...productStats,
          topScoredItems: topScoredProducts.value || [],
          leastScoredItems: leastScoredProducts.value || [],
        },
        pageStats: {
          ...pageStats,
          topScoredItems: topScoredPages.value || [],
          leastScoredItems: leastScoredPages.value || [],
        },
        blogArticleStats: {
          ...blogArticleStats,
          topScoredItems: topScoredBlogs.value || [],
          leastScoredItems: leastScoredBlogs.value || [],
        },
        collectionStats: {
          ...collectionStats,
          topScoredItems: topScoredCollections.value || [],
          leastScoredItems: leastScoredCollections.value || [],
        },
        docStats: {
          ...docStats,
          topScoredItems: topScoredDocs.value || [],
          leastScoredItems: leastScoredDocs.value || [],
        },
      });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };
}

module.exports = new ReportController();
