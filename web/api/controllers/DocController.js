const toastMessages = require("storeseo-enums/toastMessages");
const { QUEUE_NAMES } = require("../queue/config");
const { dispatchQueue } = require("../queue/queueDispatcher");
const DocService = require("../services/docs/DocService");
const { getFocusKeywordSuggestions } = require("../utils/helper");
const { serializeDocOptimizationDetails } = require("../serializers/DocSerializer");
const DocAnalysisService = require("../services/docs/DocAnalysisService");
const logger = require("storeseo-logger");
const { validateGeneralSEOUpdateData } = require("../utils/dataValidator");
const BetterDocsService = require("../services/betterdocs/BetterDocsService");
const { METAFIELD_KEYS } = require("storeseo-enums/metafields");
const SitemapService = require("../services/SitemapService");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const cache = require("../cache");
const activityLogService = require("../../admin/services/activityLog.service");
const LogOriginTypes = require("storeseo-enums/logOriginTypes");

class DocController {
  getDoc = async (req, res) => {
    try {
      const { shopId, url, shop } = req.user;
      const { id: docId } = req.params;

      const [docPromise, paginationPromise] = await Promise.allSettled([
        DocService.getByCondition(shopId, { doc_id: docId }),
        DocService.getPagination(shopId, docId),
      ]);

      if (docPromise.status === "rejected" || paginationPromise.status === "rejected")
        throw new Error("Error fetching doc details");

      let doc = docPromise.value;
      const pagination = paginationPromise.value;

      const focusKeywordSuggestions = doc.focus_keyword_suggestions;

      const optimizationData = serializeDocOptimizationDetails(doc?.analysis);

      return res.success({ doc, pagination, focusKeywordSuggestions, optimizationData });
    } catch (err) {
      console.log("Error", err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG, err });
    }
  };

  getDocs = async (req, res) => {
    try {
      const { shopId } = req.user;
      const { docs, pagination } = await DocService.getDocs(shopId, req.query, [
        "id",
        "handle",
        "categories",
        "title",
        "focus_keyword",
        "created_at",
        "issues",
        "score",
      ]);
      const docCount = await DocService.count(shopId);
      return res.success({ docs, pagination, docCount });
    } catch (error) {
      console.log(error);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getDocOptimization = async (req, res) => {
    try {
      const { shopId } = req.user;
      const { id: docId } = req.params;
      const doc = await DocService.serializeForAnalysis(shopId, docId, req.body);
      const { analysis, score } = await DocAnalysisService.calculateScore({
        shopId,
        doc,
      });

      const optimizationData = serializeDocOptimizationDetails(analysis);

      return res.success({ optimizationData, score });
    } catch (err) {
      console.log("Error", err);
      return res.failed({ message: err.message });
    }
  };

  updateDoc = async (req, res) => {
    const { error } = await validateGeneralSEOUpdateData(req.body);
    if (error) {
      return res.failedValidation({ errors: error.details });
    }
    try {
      const { id: docId } = req.params;
      const { shopId, shop: shopDomain } = req.user;
      const { session } = res.locals.shopify;

      const oldDoc = await DocService.getByCondition(shopId, { doc_id: docId });
      let doc = await DocService.updateDoc(shopId, shopDomain, docId, req.body, session);
      const analyzedDoc = await DocAnalysisService.analysis({ shopId, doc, oldDoc });

      return res.success({ doc: analyzedDoc, message: toastMessages.DOC_UPDATED });
    } catch (err) {
      logger.error(err, { ...req.body, domain: req.user.shop });
      console.log("Error", err);
      return res.failed({ message: err.message });
    }
  };

  syncDoc = async (req, res) => {
    const { id: docId } = req.params;
    const { shopId, shop: shopDomain } = req.user;

    try {
      const syncData = await DocService.syncDoc(docId, shopDomain);
      const { doc: post } = syncData.data;
      const doc = await DocService.upsertRelatedData(shopId, post);
      if (doc) {
        await DocAnalysisService.analysis({ shopId, doc });
      }
      return res.success({
        message: toastMessages.SINGLE_DOC_SYNCED,
      });
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  syncDocs = async (req, res) => {
    try {
      const { shop } = req.user;

      console.log(`\n\n---[${req.user.shop}] - dispatching regular sync for doc \n---\n\n`);

      activityLogService.addActivityLog({
        req,
        prevData: null,
        updatedData: null,
        subject: "Docs Sync Started",
        logOrigin: LogOriginTypes.APP,
        domain: shop,
      });

      dispatchQueue({
        queueName: QUEUE_NAMES.DOC_SYNC_QUEUE,
        message: {
          user: req.user,
          page: 1,
        },
      });

      return res.success({
        message: toastMessages.DOCS_SYNC_STARTED,
      });
    } catch (err) {
      console.log("err: ", err);
      return res.failed({ message: err.message });
    }
  };

  docsCount = async (req, res) => {
    const { shop: shopDomain } = req.user;

    try {
      const count = await BetterDocsService.docsCount(shopDomain);
      return res.success({ totalDocsCount: count });
    } catch (error) {
      if (error?.status === 401) {
        return res.send({ totalDocsCount: 0 });
      }
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  toggleNoIndexNoFollow = async (req, res) => {
    const { shopId, shop: shopDomain } = req.user;
    const { id: docId } = req.params;
    const { nofollow, noindex } = req.body;

    const doc = await DocService.getByCondition(shopId, { doc_id: docId });
    const meta = doc.metafields || [];

    const metaRobots = await BetterDocsService.updatedNoFollowNoIndex(docId, shopDomain, nofollow, noindex);
    const updatedMeta = meta.slice(0, 2);

    updatedMeta.push(
      { key: "no_index", value: metaRobots?.noindex ? "1" : "0" },
      { key: "no_follow", value: metaRobots?.nofollow ? "1" : "0" }
    );

    const updatedDoc = await DocService.update(shopId, doc.id, { metafields: updatedMeta });

    const noIndexMeta = updatedDoc?.metafields?.find((m) => m.key === METAFIELD_KEYS.NO_INDEX);
    const noFollowMeta = updatedDoc?.metafields?.find((m) => m.key === METAFIELD_KEYS.NO_FOLLOW);

    if (typeof noindex === "boolean") {
      await SitemapService.updateSitemapData({
        shopId,
        resourceId: updatedDoc.id,
        resourceType: analysisEntityTypes.DOC,
        metaKey: METAFIELD_KEYS.NO_INDEX,
        metaStatus: noIndexMeta?.value,
      });

      return res.success({
        noIndexStatus: Number(noIndexMeta?.value),
        message: toastMessages.NO_INDEX_STATUS_UPDATED,
      });
    }

    if (typeof nofollow == "boolean") {
      await SitemapService.updateSitemapData({
        shopId,
        resourceId: updatedDoc.id,
        resourceType: analysisEntityTypes.DOC,
        metaKey: METAFIELD_KEYS.NO_FOLLOW,
        metaStatus: noFollowMeta?.value,
      });
      return res.success({
        noFollowStatus: Number(noFollowMeta?.value),
        message: toastMessages.NO_FOLLOW_STATUS_UPDATED,
      });
    }
  };

  checkAppInstall = async (req, res) => {
    const { shop: shopDomain } = req.user;

    try {
      let betterDocsInstallStatus = await cache.betterDocInstallationStatus(shopDomain);

      if (!betterDocsInstallStatus) {
        betterDocsInstallStatus = await BetterDocsService.checkAppInstall(shopDomain);
        if (betterDocsInstallStatus) {
          await cache.betterDocInstallationStatus(shopDomain, betterDocsInstallStatus);
        }
      }
      return res.success({ status: betterDocsInstallStatus });
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };
}

module.exports = new DocController();
