const ArticleService = require("../services/ArticleService");
const BlogService = require("../services/BlogService");
const FileService = require("../services/FileService");
const ShopifyService = require("../services/ShopifyService");
const AnalysisService = require("../services/AnalysisService");
const {
  updateOrInsertMetaInfoInArray,
  getFocusKeywordSuggestions,
  getMetaInfo,
  getFileNameFromGoogleBucketPublicURL,
} = require("../utils/helper");

const logger = require("storeseo-logger");
const { isEmpty, isNull } = require("lodash");
const { validateGeneralSEOUpdateData } = require("../utils/dataValidator");
const { dispatchQueue } = require("../queue/queueDispatcher");
const { QUEUE_NAMES } = require("../queue/config");
const toastMessages = require("storeseo-enums/toastMessages");
const responseCodes = require("../config/responseCodes");
const {
  NAMESPACE,
  METAFIELD_KEYS,
  METAFIELD_TYPES,
  METAFIELD_DEFINITIONS,
  metafieldKeysFilterArray,
} = require("storeseo-enums/metafields");
const ArticleMetaService = require("../services/ArticleMetaService");
const SitemapService = require("../services/SitemapService");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const { ArticleNotFoundFromShopifyError } = require("../../errors");
const ArticleSerializer = require("../serializers/ArticleSerializer");
const BlogSerializer = require("../serializers/BlogSerializer");
const resourceType = require("storeseo-enums/resourceType");

class ArticleController {
  getArticle = async (req, res) => {
    try {
      const { shopId, url } = req.user;
      const { id: articleId } = req.params;

      // const [articlePromise, idPromise] = await Promise.allSettled([
      //   ArticleService.getArticle(shopId, articleId),
      //   ArticleService.idsOfThePrevAndNextArticle(shopId, articleId),
      // ]);

      let article = await ArticleService.getArticleByShopifyId(shopId, articleId);
      const pagination = await ArticleService.getPaginationOfArticle(shopId, articleId);
      const blog = await BlogService.getBlog(shopId, article.blog_id);

      article = {
        ...article,
        url: `${url}/blogs/${blog?.handle}/${article?.handle}`,
        prefix: `${url}/blogs/${blog?.handle}/`,
        ...ArticleService.getSocialMediaPreviewImages(article),
      };

      const focusKeywordSuggestions = article.focus_keyword_suggestions;
      const optimizationData = ArticleService.serializeArticleOptimizationDetails(article?.analysisData);

      return res.success({ article, focusKeywordSuggestions, optimizationData, pagination });
    } catch (err) {
      // logger.error(err)
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG, err });
    }
  };

  updateArticle = async (req, res) => {
    const { error } = await validateGeneralSEOUpdateData(req.body);
    if (error) {
      return res.failedValidation({ errors: error.details });
    }

    try {
      const { id: articleId } = req.params;
      const { shopId, url } = req.user;
      const { session } = res.locals.shopify;

      let article = await ArticleService.getArticleByShopifyId(shopId, articleId);
      const blog = await BlogService.getBlog(shopId, article.blog_id);

      const {
        metaTitle,
        metaDescription,
        focusKeyword,
        tags = article.tags || [],
        image,
        handle = undefined,
        createRedirectUrl = false,
      } = req.body;

      if (createRedirectUrl) {
        await ShopifyService.createRedirectURL(session.shop, {
          oldPath: `/blogs/${blog.handle}/${article.handle}`,
          newPath: `/blogs/${blog.handle}/${handle}`,
        });
      }
      const metaInput = [
        {
          key: METAFIELD_KEYS.TITLE_TAG,
          value: metaTitle,
          ownerId: article.shopify_gql_id,
        },
        {
          key: METAFIELD_KEYS.DESCRIPTION_TAG,
          value: metaDescription,
          ownerId: article.shopify_gql_id,
        },
      ];
      await ShopifyService.setMetafields(session.shop, metaInput);

      let shopifyArticle = await ShopifyService.onlineStore.getArticle(session.shop, {
        id: article.shopify_gql_id,
        metafieldKeys: metafieldKeysFilterArray,
      });
      const updateData = {
        handle,
        body: shopifyArticle.body,
        tags,
      };

      if (image) {
        updateData.image = {
          url: article.image.src,
          altText: image.alt,
        };
      }
      shopifyArticle = await ShopifyService.onlineStore.updateArticle(session.shop, {
        id: article.shopify_gql_id,
        article: updateData,
        metafieldKeys: metafieldKeysFilterArray,
      });

      const serializedShopifyArticle = ArticleSerializer.serializeShopifyArticleData(shopId, blog.id, shopifyArticle);
      const savedArticle = await ArticleService.saveOrUpdateArticle({
        ...serializedShopifyArticle,
        focus_keyword: focusKeyword,
      });

      const analysedArticle = await AnalysisService.analyseEachArticle({
        shopId,
        article: savedArticle,
        shopUrl: url,
      });

      const articleUrl = `${url}/blogs/${blog?.handle}/${savedArticle?.handle}`;

      const socialMediaImages = ArticleService.getSocialMediaPreviewImages(article);

      return res.success({
        article: {
          ...analysedArticle,
          ...savedArticle,
          url: articleUrl,
          prefix: `${url}/blogs/${blog?.handle}/`,
          ...socialMediaImages,
        },
        message: toastMessages.ARTICLE_UPDATED,
      });
    } catch (err) {
      if (err?.response?.code === responseCodes.VALIDATION_ERROR) {
        return res.failedValidation({
          errors: [{ message: `Handle ${err?.response?.body?.errors?.handle[0]}`, path: ["handle"] }],
        });
      }
      console.error(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  updateFeaturedImageAltText = async (req, res) => {
    try {
      const { id: blogId, articleId } = req.params;
      const { shopId, shop, accessToken, url } = req.user;
      const session = { shop, accessToken };

      const blog = await BlogService.getBlog(shopId, blogId);

      const article = await ArticleService.updateArticleData(shopId, articleId, req.body, session);

      const analysedArticle = await AnalysisService.analyseEachArticle({ shopId, article, shopURL: url });

      return res.success({
        article: {
          ...analysedArticle,
          meta: article.meta,
          image: article.image,
          url: `${url}/blogs/${blog?.handle}/${article?.handle}`,
          ...ArticleService.getSocialMediaPreviewImages(article),
        },
        message: toastMessages.ARTICLE_UPDATED,
      });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getArticleOptimization = async (req, res) => {
    try {
      const { shopId, url } = req.user;
      const article = await ArticleService.serializeArticleForAnalysis(shopId, req.params.id, req.body);
      const { analysis, score } = await AnalysisService.getSingleArticleAnalysis({
        shopId,
        article,
        shopURL: url,
        uniqueCheckEnable: true,
      });

      const optimizationData = ArticleService.serializeArticleOptimizationDetails(analysis);

      return res.success({ optimizationData, score });
    } catch (err) {
      return res.failed({ message: err.message });
    }
  };

  updateSocialMediaPreviewImage = async (req, res) => {
    try {
      const { shop: shopDomain, shopId } = req.user;
      const { id } = req.params;
      const { facebook_preview_image, twitter_preview_image } = req.files ?? {};
      const namespace = NAMESPACE.STORE_SEO;
      const facebookImagePreviewMetaKey = METAFIELD_KEYS.FACEBOOK_PREVIEW_IMAGE_URL;
      const twitterImagePreviewMetaKey = METAFIELD_KEYS.TWITTER_PREVIEW_IMAGE_URL;
      const metaFieldType = METAFIELD_TYPES.SINGLE_LINE_TEXT;

      let facebookImagePreviewURL = "";
      let twitterImagePreviewURL = "";

      const article = await ArticleService.getArticle(shopId, id);
      let meta = isEmpty(article?.meta) ? [] : article.meta;

      /** @type {{key: string, value: string, ownerId: string}[]} */
      const metaInput = [];

      const prevImages = [];

      if (facebook_preview_image) {
        const prevMetaInfo = getMetaInfo(namespace, facebookImagePreviewMetaKey, meta);
        prevImages.push(prevMetaInfo?.value);

        const { url } = await FileService.saveUploadedFileForShop(shop, facebook_preview_image);
        metaInput.push({
          key: METAFIELD_KEYS.FACEBOOK_PREVIEW_IMAGE_URL,
          value: url,
          ownerId: article.shopify_gql_id,
        });
        facebookImagePreviewURL = url;
      }

      if (twitter_preview_image) {
        const prevMetaInfo = getMetaInfo(namespace, twitterImagePreviewMetaKey, meta);
        prevImages.push(prevMetaInfo?.value);

        const { url } = await FileService.saveUploadedFileForShop(shop, twitter_preview_image);
        metaInput.push({
          key: METAFIELD_KEYS.TWITTER_PREVIEW_IMAGE_URL,
          value: url,
          ownerId: article.shopify_gql_id,
        });
        twitterImagePreviewURL = url;
      }

      if (facebook_preview_image || twitter_preview_image) {
        await ShopifyService.setMetafields(req.user.shop, metaInput);
        const shopifyArticle = await ShopifyService.onlineStore.getArticle(req.user.shop, {
          id: article.shopify_gql_id,
          metafieldKeys: metafieldKeysFilterArray,
        });
        const serializedShopifyArticle = ArticleSerializer.serializeShopifyArticleData(
          shopId,
          article?.blog_id,
          shopifyArticle
        );
        await ArticleService.saveOrUpdateArticle(serializedShopifyArticle);

        prevImages
          .filter((img) => img)
          .map((img) => getFileNameFromGoogleBucketPublicURL(img))
          .forEach((fileName) => FileService.deleteFile(fileName, true));
      }

      return res.success({ facebookImagePreviewURL, twitterImagePreviewURL });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: "Error saving uploaded social media preview image",
        articleId: req.params.id,
        files,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  toggleNoFollowStatus = async (req, res) => {
    try {
      const { shopId } = req.user;
      const { id: articleId } = req.params;

      const article = await ArticleService.getArticleByShopifyId(shopId, articleId);
      let noFollowMeta = article?.meta?.find(
        (m) => m.namespace === NAMESPACE.STORE_SEO && m.key === METAFIELD_KEYS.NO_FOLLOW
      );

      let newStatus = noFollowMeta?.value === "1" ? "0" : "1";
      const metaInput = [
        {
          key: METAFIELD_KEYS.NO_FOLLOW,
          value: newStatus,
          ownerId: article.shopify_gql_id,
        },
      ];
      await ShopifyService.setMetafields(req.user.shop, metaInput);

      const shopifyArticle = await ShopifyService.onlineStore.getArticle(req.user.shop, {
        id: article.shopify_gql_id,
        metafieldKeys: metafieldKeysFilterArray,
      });
      const serializedShopifyArticle = ArticleSerializer.serializeShopifyArticleData(
        shopId,
        article?.blog_id,
        shopifyArticle
      );
      await ArticleService.saveOrUpdateArticle(serializedShopifyArticle);

      // Update sitemap data
      await SitemapService.updateSitemapData({
        shopId,
        resourceId: article.id,
        resourceType: analysisEntityTypes.ARTICLE,
        metaKey: METAFIELD_KEYS.NO_FOLLOW,
        metaStatus: newStatus,
      });

      return res.success({
        noFollowStatus: Number(newStatus),
        message: toastMessages.NO_FOLLOW_STATUS_UPDATED,
      });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: `Failed to toggle no-follow status for shopId ${req.user?.shop} -> articleId ${req.params?.articleId}.`,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  toggleNoIndexStatus = async (req, res) => {
    try {
      const { shopId } = req.user;
      const { id: articleId } = req.params;

      const article = await ArticleService.getArticleByShopifyId(shopId, articleId);
      let nodIndexMeta = article?.meta?.find(
        (m) => m.namespace === NAMESPACE.STORE_SEO && m.key === METAFIELD_KEYS.NO_INDEX
      );

      let newStatus = nodIndexMeta?.value === "1" ? "0" : "1";
      const metaInput = [
        {
          key: METAFIELD_KEYS.NO_INDEX,
          value: newStatus,
          ownerId: article.shopify_gql_id,
        },
      ];
      await ShopifyService.setMetafields(req.user.shop, metaInput);

      const shopifyArticle = await ShopifyService.onlineStore.getArticle(req.user.shop, {
        id: article.shopify_gql_id,
        metafieldKeys: metafieldKeysFilterArray,
      });
      const serializedShopifyArticle = ArticleSerializer.serializeShopifyArticleData(
        shopId,
        article?.blog_id,
        shopifyArticle
      );
      await ArticleService.saveOrUpdateArticle(serializedShopifyArticle);

      // Update sitemap data
      await SitemapService.updateSitemapData({
        shopId,
        resourceId: article.id,
        resourceType: analysisEntityTypes.ARTICLE,
        metaKey: METAFIELD_KEYS.NO_INDEX,
        metaStatus: newStatus,
      });

      return res.success({
        noIndexStatus: Number(newStatus),
        message: toastMessages.NO_INDEX_STATUS_UPDATED,
      });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: `Failed to toggle no-index status for shopId ${req.user?.shop} -> articleId ${req.params?.articleId}.`,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  updateCanonicalUrl = async (req, res) => {
    try {
      const { shop, shopId } = req.user;
      const { id: articleId } = req.params;
      const { canonicalUrl } = req.body;

      // const namespace = NAMESPACE.STORE_SEO;
      // const canonicalUrlMetaKey = "cannonical_url";
      // const metaFieldType = METAFIELD_TYPES.SINGLE_LINE_TEXT;

      // const article = await ArticleService.getArticleByShopifyId(shopId, articleId);
      // const blog = await BlogService.getBlog(shopId, article?.blog_id);
      // let meta = isEmpty(article?.metafields) ? [] : article.metafields;

      // meta = updateOrInsertMetaInfoInArray({
      //   metaArr: meta,
      //   metaInfo: {
      //     namespace,
      //     key: canonicalUrlMetaKey,
      //     value: canonicalUrl,
      //     type: metaFieldType,
      //   },
      //   namespace,
      //   key: canonicalUrlMetaKey,
      // });

      // await ShopifyService.updateOrCreateShopifyArticleMetafields({
      //   user: req.user,
      //   blogId: blog.blog_id,
      //   articleId: article.article_id,
      //   metaArr: meta,
      // });
      // await ArticleService.updateArticle(articleId, { metafields: meta });
      // dispatchQueue({
      //   queueName: QUEUE_NAMES.ARTICLE_METAFIELD_SYNC,
      //   message: { user: req.user, shopId, articleId: article.id },
      //   ttl: 1000,
      // });

      return res.success({ canonicalUrl });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: `Error updating canonical url for product id`,
        productId: req.params.id,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  syncArticleFromShopify = async (req, res) => {
    const { id: articleId } = req.params;
    const { shopId, url, shop, accessToken } = req.user;

    try {
      const user = {
        shop,
        accessToken,
        url,
      };

      const article = await ArticleService.getArticleByShopifyId(shopId, articleId);
      const shopifyArticle = await ShopifyService.onlineStore.getArticle(user.shop, {
        id: article.shopify_gql_id,
        metafieldKeys: metafieldKeysFilterArray,
      });
      if (!shopifyArticle) {
        throw Error("Article not found.");
      }

      const serializedShopifyBlog = BlogSerializer.serializeShopifyBlogData(shopId, shopifyArticle.blog);
      const savedBlog = await BlogService.saveOrUpdateBlog(serializedShopifyBlog);

      const serializedShopifyArticle = ArticleSerializer.serializeShopifyArticleData(
        shopId,
        savedBlog.id,
        shopifyArticle
      );
      const savedArticle = await ArticleService.saveOrUpdateArticle(serializedShopifyArticle);

      await AnalysisService.analyseEachArticle({
        shopId,
        article: savedArticle,
        shopUrl: url,
      });
      await SitemapService.storeSitemapData(savedArticle, resourceType.ARTICLE);

      const updatedArticle = await ArticleService.getArticleByShopifyId(shopId, articleId);

      return res.success({ article: updatedArticle, message: toastMessages.SINGLE_ARTICLE_SYNCED });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: `Error syncing article from shopify`,
        blogId: req.params.id,
        articleId: req.params.articleId,
      });

      if (err instanceof ArticleNotFoundFromShopifyError) {
        res.status(404).json({ message: err.message });
        return await ArticleService.deleteArticleById(shopId, 0);
      }

      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  countShopifyArticles = async (req, res) => {
    try {
      const session = { shop: req.user.shop, accessToken: req.user.accessToken };
      const count = await ShopifyService.countArticles(session);
      return res.success({ count });
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: `Error getting shopify articles count` });
      res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };
}

module.exports = new ArticleController();
