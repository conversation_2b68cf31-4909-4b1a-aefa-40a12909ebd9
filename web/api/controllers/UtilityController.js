const googleApiService = require("../services/GoogleApiService");
const { formatGoogleKeywordIdeasResults, crawler } = require("../utils/helper");

// const pdfService = require("../services/pdfService");
const { createHmac } = require("../utils/crpyto");
const experimentalFeatureSanitizer = require("../utils/experimentalFeatureSanitizer");
const logger = require("storeseo-logger");
// const { generateTransactionID } = require("../../src/utility/payment");
const ShopifyService = require("../services/ShopifyService");
const socketEvents = require("storeseo-enums/socketEvents");
const { handleNotificationRead, handleAllNotificationRead } = require("../services/EventService");
const UtilityService = require("../services/UtilityService");
const toastMessages = require("storeseo-enums/toastMessages");
const google = require("../config/google");

const eventTriggerMap = {
  [socketEvents.NOTIFICATION_READ]: handleNotificationRead,
  [socketEvents.NOTIFICATION_ALL_READ]: handleAllNotificationRead,
};

class UtilityController {
  getAddressAutocompleteSuggestions = async (req, res) => {
    const { inputText } = req.query;
    const result = (await googleApiService.getPlaceAutocompleteSuggestions(inputText)) ?? [];
    const suggestions = result?.map((r) => ({ description: r.description, placeId: r.place_id }));
    return res.success({ suggestions });
  };

  getAddressDetailsByPlaceId = async (req, res) => {
    const { placeId } = req.query;
    const addressDetails = await googleApiService.getPlaceDetailByPlaceId(placeId);
    return res.success({ addressDetails });
  };

  getKeywordMetrics = async (req, res) => {
    try {
      const { keyword } = req.query;
      const customerId = google.ads.customerId;

      const results = await googleApiService.generateKeywordIdeas(customerId, [keyword]);
      const formattedResult = formatGoogleKeywordIdeasResults(results);
      return res.success({ keywordMetrics: formattedResult });
    } catch (err) {
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getInvoicePdf = async (req, res) => {
    try {
      const { transactionId, hmac } = req.query;
      const generatedHmac = createHmac(transactionId);

      if (hmac !== generatedHmac) {
        return res.status(401).send(toastMessages.UNAUTHORIZED);
      }
      // const stream = await pdfService.getInvoicePdfStream(transactionId);
      // res.attachment(`${generateTransactionID(transactionId)}.pdf`);
      // stream.pipe(res);
    } catch (err) {
      // console.log("err: ", err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getListOfInstalledSeoApps = async (req, res) => {
    try {
      const products = await ShopifyService.getProductsFromShopify(req.user.shop);
      const url = products.edges[0].node.onlineStorePreviewUrl;

      const installedApps = await UtilityService.listOfInstalledSeoApps(req.user.shop, url);
      return res.success({ installedApps });
    } catch (err) {
      // console.error(`Error getting list of installed SEO apps via crawling for shop ${req.user.shop}. ${err}`);
      return res.success({ installedApps: [] });
    }
  };

  triggerEvent = async (req, res) => {
    try {
      const { eventName, eventDetails } = req.body;

      if (eventTriggerMap[eventName]) eventTriggerMap[eventName](eventDetails);
      res.json({ success: true });
    } catch (err) {
      // console.error(`Error triggering event ${req.body.eventName} for shop ${req.user.shop}. ${err}`);
      res.status(500);
      res.json({});
    }
  };
}

module.exports = new UtilityController();
