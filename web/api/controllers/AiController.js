const toastMessages = require("storeseo-enums/toastMessages");
const AiService = require("../services/AiService");
const { validateAiOptimizerInput, validateImageAltTextGenerationInput, validate } = require("../utils/dataValidator");
const logger = require("storeseo-logger");
const { IrrelevantAiContentInputError } = require("../../errors");
const { COLLECTION, PAGE, ARTICLE } = require("storeseo-enums/analysisEntityTypes");
const { serializeImageAltTexts } = require("../serializers/aiContentSerializer");
const { IMAGE } = require("storeseo-enums/aiContentTypes");
const aiContentTypes = require("storeseo-enums/aiContentTypes");
const cache = require("../cache");
const subscriptionAddonGroup = require("storeseo-enums/subscriptionAddonGroup");
const ProductImageService = require("../services/ProductImageService");
const altTextOptimizationStatus = require("storeseo-enums/altTextOptimization");
const { AxiosError } = require("axios");
const ShopService = require("../services/ShopService");
const settingKeys = require("storeseo-enums/settingKeys");
const ResourceOptimizationService = require("../services/resource/ResourceOptimizationService");
const ResourceDataBackupService = require("../services/resource/ResourceDataBackupService");
const { METAFIELD_KEYS } = require("storeseo-enums/metafields");
const ResourceType = require("storeseo-enums/resourceType");
const { generateImageAltTextObjectByMediaId } = require("../serializers/ProductImageSerializer");
const { autoAiOptimizationAPISchema } = require("storeseo-schema/settings/autoAiOptimization");
const resourceOPType = require("storeseo-enums/resourceOPType");
const bulkOperationTrackerService = require("../services/BulkOperationTrackService");
const activityLogService = require("../../admin/services/activityLog.service");
const LogOriginTypes = require("storeseo-enums/logOriginTypes");
const OpenAiService = require("../services/openai/OpenAiService");
const { InternalServerError, BadRequestError, APIConnectionError } = require("openai");

/**
 * @typedef {keyof Pick<typeof ResourceType, "PRODUCT" | "COLLECTION" | "PAGE" | "ARTICLE">} AiResourceType
 */

class AiController {
  prepareStreamingHeaders = (res) => {
    res.set({
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
      "Content-Type": "text/event-stream",
      "X-Accel-Buffering": "no",
    });
    res.flushHeaders();
  };

  streamAiContents = async (res, creditUsage, output) => {
    this.prepareStreamingHeaders(res);

    let longestLength = Math.max(
      output?.meta_description?.length ?? null,
      output?.meta_title?.length ?? null,
      output?.tags?.length ?? null
    );
    const longestContentKey = Object.keys(output).find((key) => output[key].length === longestLength);

    let partialContent = {
      meta_description: "",
      meta_title: "",
      ...(output.tags && { tags: [] }),
    };

    while (longestLength > 0) {
      if (output?.meta_description?.length > 0) {
        partialContent.meta_description = output?.meta_description
          .split(" ")
          .splice(0, partialContent.meta_description.split(" ").length + 1)
          .join(" ");
      }
      if (output?.meta_title?.length > 0) {
        partialContent.meta_title = output?.meta_title
          .split(" ")
          .splice(0, partialContent.meta_title.split(" ").length + 1)
          .join(" ");
      }
      if (output?.tags?.length > 0) {
        partialContent.tags.push(...output?.tags.splice(0, partialContent.tags.length + 1));
      }
      // Write the content to the response stream and flush the headers in every 100ms
      await new Promise((resolve) => {
        setTimeout(() => {
          res.write(`data: {"content": ${JSON.stringify(partialContent)}, "creditUsage": ${creditUsage}}\n\n`);
          res.flush();
          resolve(true);
        }, 100);
      });

      longestLength--;

      if (longestContentKey && partialContent[longestContentKey] === output[longestContentKey]) {
        res.end();
        break;
      }
    }

    if (longestLength <= 0) {
      res.end();
    }

    res.on("close", () => {
      res.end();
    });
  };

  streamImageAltTexts = async (res, creditUsage, imageAltTexts) => {
    this.prepareStreamingHeaders(res);

    let altTextLength = imageAltTexts?.altText?.length ?? 0;

    let partialAltText = "";

    while (altTextLength > 0) {
      partialAltText = imageAltTexts.altText
        .split(" ")
        .splice(0, partialAltText.split(" ").length + 1)
        .join(" ");

      await new Promise((resolve) => {
        setTimeout(() => {
          res.write(`data: {"content": ${JSON.stringify(partialAltText)}, "creditUsage": ${creditUsage}}\n\n`);
          res.flush();
          resolve(true);
        }, 100);
      });

      altTextLength--;

      if (partialAltText === imageAltTexts.altText) {
        res.end();
        break;
      }
    }

    if (altTextLength <= 0) {
      res.end();
    }

    res.on("close", () => {
      res.end();
    });
  };

  generateProductAiContent = async (req, res) => {
    const { error } = await validateAiOptimizerInput(req.body);

    if (error) {
      return res.failedValidation({ errors: error.details });
    }

    const { shop, addons } = req.user;

    const aiContentGeneratorLanguage = await cache.shop.aiContentGeneratorLanguage(shop);

    const { title, description, focusKeyword, language = aiContentGeneratorLanguage.name } = req.body;

    const hasAiLimit = AiService.hasAiLimit(addons);

    if (!hasAiLimit) {
      return res.failed({ message: "AI Credit limit exceeded." });
    }

    try {
      const { data } = await AiService.generateProductContent({ title, description, focusKeyword, language });

      if (data.status !== "success") {
        throw Error(data.error);
      }

      const { output, usage } = data;

      if (!output) throw new IrrelevantAiContentInputError();

      const creditUsage = await AiService.increaseCreditUsage(shop, usage);

      AiService.logAiContentOptimization({ shop, input: req.body, output, usage });

      await this.streamAiContents(res, creditUsage, output);
    } catch (err) {
      if (err instanceof IrrelevantAiContentInputError) {
        return res.status(400).json({ message: err.message, isIrrelevantInput: true });
      }
      logger.error(err, { domain: req.user.shop });
      res.failed({
        message: toastMessages.SOMETHING_WENT_WRONG,
      });
    }
  };

  generateCollectionAiContent = async (req, res) => {
    const { error } = await validateAiOptimizerInput(req.body, COLLECTION);

    if (error) {
      return res.failedValidation({ errors: error.details });
    }

    const { shop, addons } = req.user;

    const aiContentGeneratorLanguage = await cache.shop.aiContentGeneratorLanguage(shop);

    const { title, description, focusKeyword, language = aiContentGeneratorLanguage.name } = req.body;

    const hasAiLimit = AiService.hasAiLimit(addons);

    if (!hasAiLimit) {
      return res.failed({ message: "AI Credit limit exceeded." });
    }

    try {
      const data = await OpenAiService.generateCollectionContent({ title, description, focusKeyword, language });

      const { output, usage } = data;

      if (!output) throw new IrrelevantAiContentInputError();

      const creditUsage = await AiService.increaseCreditUsage(shop, usage);

      AiService.logAiContentOptimization({ shop, input: req.body, output, usage });

      await this.streamAiContents(res, creditUsage, output);
    } catch (err) {
      logger.error(err, { domain: req.user.shop });

      if (err instanceof InternalServerError || err instanceof BadRequestError || err instanceof APIConnectionError) {
        return res.status(500).json({ message: err.message, status: err.status });
      }

      if (err instanceof IrrelevantAiContentInputError) {
        return res.status(400).json({ message: err.message, isIrrelevantInput: true });
      }

      res.failed({
        message: toastMessages.SOMETHING_WENT_WRONG,
      });
    }
  };

  generatePageAiContent = async (req, res) => {
    const { error } = await validateAiOptimizerInput(req.body, PAGE);

    if (error) {
      return res.failedValidation({ errors: error.details });
    }

    const { shop, addons } = req.user;

    const aiContentGeneratorLanguage = await cache.shop.aiContentGeneratorLanguage(shop);

    const { title, description, focusKeyword, language = aiContentGeneratorLanguage.name } = req.body;

    const hasAiLimit = AiService.hasAiLimit(addons);

    if (!hasAiLimit) {
      return res.failed({ message: "AI Credit limit exceeded." });
    }

    try {
      const { data } = await AiService.generatePageContent({ title, content: description, focusKeyword, language });

      if (data.status !== "success") {
        throw Error(data.error);
      }

      const { output, usage } = data;

      if (!output) throw new IrrelevantAiContentInputError();

      const creditUsage = await AiService.increaseCreditUsage(shop, usage);

      AiService.logAiContentOptimization({ shop, input: req.body, output, usage });

      await this.streamAiContents(res, creditUsage, output);
    } catch (err) {
      if (err instanceof IrrelevantAiContentInputError) {
        return res.status(400).json({ message: err.message, isIrrelevantInput: true });
      }
      logger.error(err, { domain: req.user.shop });
      res.failed({
        message: toastMessages.SOMETHING_WENT_WRONG,
      });
    }
  };

  generateArticleAiContent = async (req, res) => {
    const { error } = await validateAiOptimizerInput(req.body, ARTICLE);

    if (error) {
      return res.failedValidation({ errors: error.details });
    }

    const { shop, addons } = req.user;

    const aiContentGeneratorLanguage = await cache.shop.aiContentGeneratorLanguage(shop);

    const { title, description, focusKeyword, language = aiContentGeneratorLanguage.name } = req.body;

    const hasAiLimit = AiService.hasAiLimit(addons);

    if (!hasAiLimit) {
      return res.failed({ message: "AI Credit limit exceeded." });
    }

    try {
      const { data } = await AiService.generateArticleContent({ title, content: description, focusKeyword, language });

      if (data.status !== "success") {
        throw Error(data.error);
      }

      const { output, usage } = data;

      if (!output) throw new IrrelevantAiContentInputError();

      const creditUsage = await AiService.increaseCreditUsage(shop, usage);

      AiService.logAiContentOptimization({ shop, input: req.body, output, usage });

      await this.streamAiContents(res, creditUsage, output);
    } catch (err) {
      if (err instanceof IrrelevantAiContentInputError) {
        return res.status(400).json({ message: err.message, isIrrelevantInput: true });
      }
      logger.error(err, { domain: req.user.shop });
      res.failed({
        message: toastMessages.SOMETHING_WENT_WRONG,
      });
    }
  };

  generateImageAltText = async (req, res) => {
    const { error } = await validateImageAltTextGenerationInput(req.body);

    if (error) {
      return res.failedValidation({ errors: error.details });
    }

    const { shop } = req.user;

    const aiContentGeneratorLanguage = await cache.shop.aiContentGeneratorLanguage(shop);

    const { image, language = aiContentGeneratorLanguage.name } = req.body;

    const currentCreditUsage = await cache.addons.usageCount(shop, { addon: subscriptionAddonGroup.AI_OPTIMIZER });
    const currentLimit = await cache.addons.usageLimit(shop, { addon: subscriptionAddonGroup.AI_OPTIMIZER });

    const hasSufficientCredit = currentLimit >= Number(currentCreditUsage);

    if (!hasSufficientCredit) {
      return res.failed({ message: "AI Credit limit exceeded." });
    }

    try {
      const { product, ...restImageProps } = image;
      const { title, description, focusKeyword } = product;

      const { data: imageAltData } = await AiService.generateImageAltTextGPT4o({
        domain: shop,
        images: [restImageProps] ?? [],
        title,
        description,
        focusKeyword,
        language,
      });

      const { image_alt_texts, status } = imageAltData;
      const imageAltTexts = status === "success" ? serializeImageAltTexts(image_alt_texts) : [];

      let totalCreditUsages = 0;

      try {
        if (image_alt_texts.length > 0) {
          for (const imageAltText of image_alt_texts) {
            const { usage, ...output } = imageAltText;

            const creditUsage = await AiService.increaseCreditUsage(shop, usage, IMAGE);
            totalCreditUsages += Number(creditUsage);

            AiService.logAiImageAltTextOptimization({ shop, input: req.body, output, usage });
          }
        }
      } catch (error) {
        throw error;
      }

      await this.streamImageAltTexts(res, totalCreditUsages, imageAltTexts[0]);
    } catch (err) {
      console.log("err generating alt-text: ", err);
      if (err instanceof AxiosError) {
        const errorMessages = [];
        const errorImage = err.response.data.message.image_details["0"];
        for (let key in errorImage) {
          errorMessages.push(`${key.replace("_", " ")}`);
        }
        return res.status(400).json({
          message: `Please update your following product details (${errorMessages.join(", ")}) then try again! `,
          isIrrelevantInput: true,
        });
      }
      logger.error(err, { domain: req.user.shop });
      res.failed({
        message: toastMessages.SOMETHING_WENT_WRONG,
      });
    }
  };

  generateImageAltTextByQueue = async (req, res) => {
    try {
      const { shop, shopId } = req.user;
      const { images } = req.body;

      const perImageCreditUsage = AiService.calculateCreditUsage({}, aiContentTypes.IMAGE);
      const estimatedCreditUsage = Number(perImageCreditUsage) * images.length;

      const currentCreditUsage = await cache.addons.usageCount(shop, { addon: subscriptionAddonGroup.AI_OPTIMIZER });
      const currentLimit = await cache.addons.usageLimit(shop, { addon: subscriptionAddonGroup.AI_OPTIMIZER });

      const hasSufficientCredit = currentLimit >= Number(currentCreditUsage) + estimatedCreditUsage;
      if (!hasSufficientCredit) {
        return res.status(400).json({
          status: false,
          message: "Insufficient AI credit",
          images,
          creditUsage: 0,
        });
      }

      for (let img of images) {
        if (img.alt_text_optimization_status !== altTextOptimizationStatus.OPTIMIZED) {
          // Backup image alt text data if the image is not already AI optimized

          const imagesObjectByMediaId = generateImageAltTextObjectByMediaId(img);

          await ResourceDataBackupService.upsert({
            shop_id: shopId,
            resource_id: img.id,
            resource_type: ResourceType.PRODUCT_IMAGE,
            resource_op_type: resourceOPType.AI_OPTIMIZATION,
            data: imagesObjectByMediaId,
          });
        }

        await ProductImageService.updateImage(img.id, {
          alt_text_optimization_status: altTextOptimizationStatus.PENDING,
        });
      }

      await cache.addons.incrementUsageCount(shop, {
        addon: subscriptionAddonGroup.AI_OPTIMIZER,
        incrementBy: estimatedCreditUsage,
      });
      await cache.addons.incrementTotalUsageCount(shop, {
        addon: subscriptionAddonGroup.AI_OPTIMIZER,
        incrementBy: estimatedCreditUsage,
      });
      await cache.addons.incrementTotalAppUsageCount(subscriptionAddonGroup, estimatedCreditUsage);

      await cache.altTextOptimizer.addStoreToPendingOptimizationQueue(shop);

      const shopDetails = {
        id: shopId,
        domain: shop,
      };
      await bulkOperationTrackerService.initializeBulkOperationTracker(
        shopDetails,
        ResourceType.PRODUCT_IMAGE,
        resourceOPType.AI_OPTIMIZATION,
        {
          batch_size: images.length,
          resource: images.map((img) => ({ gId: img.media_id })),
        }
      );

      return res.success({
        status: true,
        message: "Alt-text generation started",
        images,
        creditUsage: estimatedCreditUsage,
      });
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      res.failed({
        message: toastMessages.SOMETHING_WENT_WRONG,
      });
    }
  };

  getAutoAiContentGenerationSettings = async (req, res) => {
    const { shopId } = req.user;

    try {
      const result = await ShopService.getShopSetting(shopId, settingKeys.AI_CONTENT_SETTINGS);

      return res.success({ data: result?.value });
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: "Error getting auto ai content generation settings" });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  updateAutoAiContentGenerationSettings = async (req, res) => {
    if (req.body?.settings) {
      const { error } = await validate(autoAiOptimizationAPISchema, req.body);
      if (error) {
        return res.failedValidation({ errors: error.details });
      }
    }

    const { shopId, shop } = req.user;
    const settings = req.body;

    try {
      const data = {
        shop_id: shopId,
        key: settingKeys.AI_CONTENT_SETTINGS,
        value_type: "json",
        value: JSON.stringify(settings),
      };

      const prevAutoAiOptimizationSetting = await ShopService.getShopSetting(shopId, settingKeys.AI_CONTENT_SETTINGS);
      const result = await ShopService.updateShopSetting(shopId, data);

      activityLogService.addActivityLog({
        req,
        prevData: {
          ...prevAutoAiOptimizationSetting?.value?.[ResourceType.PRODUCT]?.settings,
          status: prevAutoAiOptimizationSetting?.value?.[ResourceType.PRODUCT]?.status,
        },
        updatedData: {
          ...result?.value?.[ResourceType.PRODUCT]?.settings,
          status: result?.value?.[ResourceType.PRODUCT]?.status,
        },
        subject: "Auto AI Optimizer Setting Updated",
        logOrigin: LogOriginTypes.APP,
        domain: shop,
      });

      return res.success({ message: "Settings updated", data: result?.value });
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: "Error updating auto ai content generation settings" });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  generateAiContentByQueue = async (req, res) => {
    try {
      const { shop: shopDomain, shopId } = req.user;
      const { settings, resourceList } = req.body;
      const { type } = req.params;
      /**
       * @type {AiResourceType}
       */
      const resourceType = type.toUpperCase();

      const { resources, totalApproximateCreditUsage, resourcesApproximateCreditUsageMap } =
        await AiService.prepareBulkAiContentByType(
          {
            shopId,
            resourceList,
            settings,
          },
          resourceType
        );

      const currentCreditUsage = await cache.addons.usageCount(shopDomain, {
        addon: subscriptionAddonGroup.AI_OPTIMIZER,
      });
      const currentLimit = await cache.addons.usageLimit(shopDomain, { addon: subscriptionAddonGroup.AI_OPTIMIZER });

      const hasSufficientCredit = currentLimit >= Number(currentCreditUsage) + totalApproximateCreditUsage;
      if (!hasSufficientCredit) {
        return res.status(400).json({
          status: false,
          message: "Insufficient AI credit",
          resources: resourceList,
          creditUsage: 0,
        });
      }

      for (let resource of resources) {
        const approximateIncrementUsageCountValue = resourcesApproximateCreditUsageMap.get(resource.id) || 0;
        try {
          await AiService.makedResourcesAsPending(shopId, resource.id, resourceType);

          // Find the resource optimization data for stats
          const resourceOptimization = await ResourceOptimizationService.getByCondition({
            shop_id: shopId,
            resource_id: resource.id,
            resource_type: resourceType,
            resource_op_type: resourceOPType.AI_OPTIMIZATION,
          });

          const optimization_stats = {
            optimization_count: resourceOptimization
              ? resourceOptimization?.optimization_stats?.optimization_count + 1
              : 1,
            restore_count: resourceOptimization?.optimization_stats?.restore_count ?? 0,
            last_queued_date: new Date(),
            last_queue_process_completed_date:
              resourceOptimization?.optimization_stats?.last_queue_process_completed_date ?? null,
          };

          await ResourceOptimizationService.upsert({
            shop_id: shopId,
            resource_id: resource.id,
            resource_type: resourceType,
            resource_op_type: resourceOPType.AI_OPTIMIZATION,
            optimization_setting: settings,
            approximate_credit_usage: Number(approximateIncrementUsageCountValue),
            optimization_stats,
          });

          // Backup image alt text data if the image is not already AI optimized
          // TODO: support backup for other resource types
          if (resourceType === ResourceType.PRODUCT) {
            const filteredImages = resource.images.filter(
              (img) => img.alt_text_optimization_status !== altTextOptimizationStatus.OPTIMIZED
            );
            for (let img of filteredImages) {
              const imagesObjectByMediaId = generateImageAltTextObjectByMediaId(img);
              await ResourceDataBackupService.upsert({
                shop_id: shopId,
                resource_id: img.id,
                resource_type: ResourceType.PRODUCT_IMAGE,
                resource_op_type: resourceOPType.AI_OPTIMIZATION,
                data: imagesObjectByMediaId,
              });
            }
          }

          // Backup meta title and description data if the resource is not already AI optimized
          if (resource.ai_optimization_status !== altTextOptimizationStatus.OPTIMIZED) {
            const findMetaTitle = resource.meta.find((item) => item.key === METAFIELD_KEYS.TITLE_TAG);
            const findMetaDescription = resource.meta.find((item) => item.key === METAFIELD_KEYS.DESCRIPTION_TAG);

            const backupData = {
              metaTitle: findMetaTitle?.value ?? "",
              metaDescription: findMetaDescription?.value ?? "",
              tags: resource?.tags ?? null,
            };

            await ResourceDataBackupService.upsert({
              shop_id: shopId,
              resource_id: resource.id,
              resource_type: resourceType,
              resource_op_type: resourceOPType.AI_OPTIMIZATION,
              data: backupData,
            });
          }
        } catch (error) {
          console.log("error", error);
        }

        await cache.addons.incrementUsageCount(shopDomain, {
          addon: subscriptionAddonGroup.AI_OPTIMIZER,
          incrementBy: approximateIncrementUsageCountValue, // Approximate value. This will sync after AI content generation
        });
        await cache.addons.incrementTotalUsageCount(shopDomain, {
          addon: subscriptionAddonGroup.AI_OPTIMIZER,
          incrementBy: approximateIncrementUsageCountValue, // Approximate value. This will sync after AI content generation
        });
        await cache.addons.incrementTotalAppUsageCount(
          subscriptionAddonGroup.AI_OPTIMIZER,
          approximateIncrementUsageCountValue
        ); // Approximate value. This will sync after AI content generation
      }

      await AiService.addStoreToPendingOptimizationQueue(shopDomain, resourceType);

      const shopDetails = {
        id: shopId,
        domain: shopDomain,
      };
      await bulkOperationTrackerService.initializeBulkOperationTracker(
        shopDetails,
        resourceType,
        resourceOPType.AI_OPTIMIZATION,
        {
          batch_size: resourceList.length,
          resource: resourceList.map((resource) => ({ gId: resource.gql_id })),
        }
      );

      return res.success({
        status: true,
        message: "Ai content generation started",
        resources: resourceList,
        creditUsage: totalApproximateCreditUsage,
      });
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      res.failed({
        message: toastMessages.SOMETHING_WENT_WRONG,
      });
    }
  };

  /**
   * Restores AI optimized content for a given resource type
   * @param {import("express").Request} req
   * @param {import("express").Response} res
   */
  restoreAiContentByQueue = async (req, res) => {
    try {
      const { shop, shopId, accessToken } = req.user;
      const { resourceList } = req.body;
      const { type } = req.params;
      /**
       * @type {AiResourceType}
       */
      const resourceType = type.toUpperCase();
      const session = {
        shop,
        accessToken,
      };

      const responseResources = [];

      for (let resource of resourceList) {
        try {
          const resourceData = await AiService.getResourceDetailsForRestore(shopId, resource.id, resourceType);

          if (!resourceData) continue;

          const { resourceDetails, backupData, resourceOptimization } = resourceData;

          const restoredResource = await AiService.restoreResourceContent(
            shopId,
            resourceDetails,
            backupData,
            resourceOptimization,
            resourceType,
            session
          );

          responseResources.push(restoredResource);
        } catch (error) {
          console.log("error", error);
        }
      }

      return res.success({
        status: true,
        message: "Ai content restored",
        resources: responseResources,
      });
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      res.failed({
        message: toastMessages.SOMETHING_WENT_WRONG,
      });
    }
  };
}

module.exports = new AiController();
