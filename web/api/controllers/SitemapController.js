const logger = require("storeseo-logger");
const yup = require("yup");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const toastMessages = require("storeseo-enums/toastMessages");
const ProductService = require("../services/ProductService");
const CollectionService = require("../services/collections/CollectionService");
const PageService = require("../services/PageService");
const ArticleService = require("../services/ArticleService");
const ShopService = require("../services/ShopService");
const settingKeys = require("storeseo-enums/settingKeys");

class SitemapController {
  getSitemaps = async (req, res) => {
    try {
      const { shopId } = req.user;
      const { resourceType } = req.params;
      let sitemaps, totalCount;

      // Check if the resource type and call the appropriate service method
      switch (resourceType) {
        case analysisEntityTypes.PRODUCT.toLowerCase(): // Check if the resource type is product
          sitemaps = await ProductService.getSitemaps(shopId, req.query);
          totalCount = await ProductService.countProducts(shopId);
          break;

        case analysisEntityTypes.COLLECTION.toLowerCase(): // Check if the resource type is collection
          sitemaps = await CollectionService.getSitemaps(shopId, req.query);
          totalCount = await CollectionService.count(shopId);
          break;

        case analysisEntityTypes.PAGE.toLowerCase(): // Check if the resource type is page
          sitemaps = await PageService.getSitemaps(shopId, req.query);
          totalCount = await PageService.count(shopId);
          break;

        case analysisEntityTypes.ARTICLE.toLowerCase(): // Check if the resource type is blog posts/article
          sitemaps = await ArticleService.getSitemaps(shopId, req.query);
          totalCount = await ArticleService.count(shopId);
          break;
        default:
          return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
      }
      return res.success({ ...sitemaps, totalCount });
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  submitSitemaps = async (req, res) => {
    const schema = yup.object().shape({
      sitemap: yup.object().required("Please select at least 1 product to update."),
    });

    try {
      await schema.validate(req.body, { abortEarly: false });
    } catch (error) {
      return res.failedValidation({ errors: error.inner.map((item) => ({ path: item.path, message: item.message })) });
    }

    try {
      const { resourceType } = req.params;
      const { sitemap } = req.body;
      const { shopId, shop, accessToken } = req.user;
      const session = {
        shop,
        accessToken,
      };
      // Check if the resource type and call the appropriate service method
      switch (resourceType) {
        case analysisEntityTypes.PRODUCT.toLowerCase(): // Check if the resource type is product
          await ProductService.updateSitemap({ shopId, session, sitemap });
          break;

        case analysisEntityTypes.COLLECTION.toLowerCase(): // Check if the resource type is collection
          await CollectionService.updateSitemap({ shopId, session, sitemap });
          break;

        case analysisEntityTypes.PAGE.toLowerCase(): // Check if the resource type is collection
          await PageService.updateSitemap({ shopId, session, sitemap });
          break;

        case analysisEntityTypes.ARTICLE.toLowerCase(): // Check if the resource type is blog posts/article
          await ArticleService.updateSitemap({ shopId, session, sitemap });
          break;

        default:
          return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
      }

      return res.success({
        sitemapStatus: Number(sitemap.status),
        message: toastMessages.SITEMAP_UPDATED,
      });
    } catch (err) {
      console.error(err.toString());
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getShopSitemapInfo = async (req, res) => {
    try {
      const { shopId } = req.user;
      const sitemap = await ShopService.getShopSitemapData(shopId);
      return res.success(sitemap);
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  submitSitemapsToGoogle = async (req, res) => {
    const GoogleAuthService = require("../services/GoogleAuthService");
    const GoogleApiService = require("../services/GoogleApiService");

    try {
      const { shopId, permission } = req.user;

      if (permission.google_console === false) {
        return res.failed({ message: toastMessages.PERMISSION_DENIED });
      }

      let shop = await ShopService.getShopById(shopId);
      const { value: google_integration_info } = await ShopService.getShopSetting(
        shopId,
        settingKeys.GOOGLE_INTEGRATION_INFO
      );
      const authenticatedUser = await GoogleAuthService.getAuthenticatedUser(google_integration_info?.googleUserEmail);

      const status = await GoogleApiService.submitSitemapForShop(shop?.url, authenticatedUser);
      shop = await ShopService.updateSitemapSubmitStatus(shop.domain, { sitemapSubmitted: status });

      if (status) {
        return res.success({
          message: toastMessages.SITEMAP_SUBMITTED,
          shop,
        });
      } else {
        return res.failed({
          message: toastMessages.SITEMAP_SUBMIT_FAILED,
        });
      }
    } catch (err) {
      // return this.sendFailedResponseres, {message: rr.toString();
      // console.log("sitemap error:", err.response.body);
      return res.failed({
        message: toastMessages.SITE_NOT_VERIFIED,
      });
    }
  };
}

module.exports = new SitemapController();
