const queryRowProperties = require("storeseo-enums/searchConsole/queryRowProperties");
const logger = require("storeseo-logger");
const {
  serializeSingleDimensionReportIntoMetricsArray,
  serializeAnalyticsToPageReports,
  craftRequestOptionsForKeywordsReport,
  serializeAnalyticsDataToKeywordsReport,
  attachPrevDataToCurrentDataArr,
  serializeSearchConsoleAnalyticsQueryData,
  mapDateWiseSearchConsoleAnalyticsQueryDataToSpecificKey,
} = require("../serializers/AnalyticsDataSerializer");
const GoogleAnalyticsService = require("../services/GoogleAnalyticsService");
const GoogleAuthService = require("../services/GoogleAuthService");
const GoogleSearchConsoleService = require("../services/GoogleSearchConsoleService");
const ShopService = require("../services/ShopService");
const { prevDateRange } = require("../utils/helper");
const searchConsoleDimensions = require("storeseo-enums/searchConsole/dimensions");
const settingKeys = require("storeseo-enums/settingKeys");
const toastMessages = require("storeseo-enums/toastMessages");
const { isSubscriptionTestMode } = require("../config/app");

class AnalyticsController {
  /**
   *
   * @param {import("express").Router.Request} req
   * @param {import("express").Router.Response} res
   */
  runReportByDate = async (req, res) => {
    try {
      /**
       * req body:
       * ----
       * {
       *  metrics: [string],
       *  dateRange: {
       *    startDate: string,
       *    endDate: string
       *  }
       * }
       */

      const { shopId } = req.user;
      const { metrics, dateRange } = req.body;

      let authenticatedUser = await ShopService.getGoogleIntegrationInfoWithAuthUserDetails(shopId);
      if (isSubscriptionTestMode) {
        authenticatedUser = {
          ...authenticatedUser,
          ...GoogleAuthService.getDefaultAuthenticatedUser(),
        };
      }

      const serviceJsonSetting = await ShopService.getShopSetting(shopId, settingKeys.GOOGLE_SERVICE_JSON);
      if (serviceJsonSetting) {
        authenticatedUser.serviceJson = serviceJsonSetting.value;
      }

      const prevRange = prevDateRange(dateRange);
      const { reports } = await GoogleAnalyticsService.batchRunReportsByDate(
        authenticatedUser,
        authenticatedUser.analyticsPropertyId,
        [
          { metrics, dateRange },
          { metrics, dateRange: prevRange },
        ]
      );
      const serializedDataForCurrentRange = serializeSingleDimensionReportIntoMetricsArray({
        report: reports[0],
        dateRange,
      });
      const serializedDataForPrevRange = serializeSingleDimensionReportIntoMetricsArray({
        report: reports[1],
        dateRange: prevRange,
      });

      const serializedData = attachPrevDataToCurrentDataArr({
        currentArr: serializedDataForCurrentRange,
        prevArr: serializedDataForPrevRange,
        dataKey: "metricName",
      });

      return res.json(serializedData);
    } catch (err) {
      console.log(`Error running analytics report by date for shop ${req.user.shop}`, err);
      res.failed({
        message: toastMessages.SOMETHING_WENT_WRONG,
      });
    }
  };

  /**
   *
   * @param {import("express").Router.Request} req
   * @param {import("express").Router.Response} res
   */
  runProductsReport = async (req, res) => {
    try {
      /**
       * req body:
       * ----
       * {
       *  dateRange: {
       *    startDate: string,
       *    endDate: string
       *  },
       *  limit: number,
       *  page: number
       * }
       */

      const { shopId } = req.user;
      const { dateRange, limit = 5, page = 1 } = req.body;
      const offset = (page - 1) * limit;

      let authenticatedUser = await ShopService.getGoogleIntegrationInfoWithAuthUserDetails(shopId);
      if (isSubscriptionTestMode) {
        authenticatedUser = {
          ...authenticatedUser,
          ...GoogleAuthService.getDefaultAuthenticatedUser(),
        };
      }
      const serviceJsonSetting = await ShopService.getShopSetting(shopId, settingKeys.GOOGLE_SERVICE_JSON);
      if (serviceJsonSetting) {
        authenticatedUser.serviceJson = serviceJsonSetting.value;
      }

      const reportForCurrentRange = await GoogleAnalyticsService.runProductsReport(
        authenticatedUser,
        authenticatedUser.analyticsPropertyId,
        { dateRange, limit, offset }
      );
      const serializedDataForCurrentRange = serializeAnalyticsToPageReports({ report: reportForCurrentRange });

      const prevRange = prevDateRange(dateRange);
      const topProductsPaths = serializedDataForCurrentRange.map((d) => d.path);
      const reportForPreviousRange = await GoogleAnalyticsService.runProductsReport(
        authenticatedUser,
        authenticatedUser.analyticsPropertyId,
        {
          dateRange: prevRange,
          limit,
          offset,
          selectedPaths: topProductsPaths,
        }
      );
      const serializedDataForPrevRange = serializeAnalyticsToPageReports({ report: reportForPreviousRange });

      const serializedData = attachPrevDataToCurrentDataArr({
        currentArr: serializedDataForCurrentRange,
        prevArr: serializedDataForPrevRange,
        dataKey: "path",
      });

      return res.json(serializedData);
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: `Error running products analytics report.` });
      res.failed({
        message: toastMessages.SOMETHING_WENT_WRONG,
      });
    }
  };

  /**
   *
   * @param {import("express").Router.Request} req
   * @param {import("express").Router.Response} res
   *
   * **req body**:
   * ---
   * {
   *  dateRange: {
   *    startDate: string,
   *    endDate: string
   *  }
   * }
   */
  runKeywordsReport = async (req, res) => {
    try {
      const { shopId } = req.user;
      const { dateRange } = req.body;

      let authenticatedUser = await ShopService.getGoogleIntegrationInfoWithAuthUserDetails(shopId);
      if (isSubscriptionTestMode) {
        authenticatedUser = {
          ...authenticatedUser,
          ...GoogleAuthService.getDefaultAuthenticatedUser(),
        };
      }

      const serviceJsonSetting = await ShopService.getShopSetting(shopId, settingKeys.GOOGLE_SERVICE_JSON);
      if (serviceJsonSetting) {
        authenticatedUser.serviceJson = serviceJsonSetting.value;
      }

      const prevRange = prevDateRange(dateRange);
      const analyticsReportRequestOptions = [dateRange, prevRange].map((range) =>
        craftRequestOptionsForKeywordsReport({ dateRange: range })
      );

      const { reports } = await GoogleAnalyticsService.batchRunReports(
        authenticatedUser,
        authenticatedUser.analyticsPropertyId,
        analyticsReportRequestOptions
      );
      const serializedDataForCurrentRange = serializeAnalyticsDataToKeywordsReport({ report: reports[0], dateRange });
      const serializedDataForPrevRange = serializeAnalyticsDataToKeywordsReport({
        report: reports[1],
        dateRange: prevRange,
      });

      return res.json({
        ...serializedDataForCurrentRange,
        prevData: serializedDataForPrevRange,
      });
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: `Error running products analytics report.` });
      res.failed({
        message: toastMessages.SOMETHING_WENT_WRONG,
      });
    }
  };

  /**
   *
   * @param {import("express").Router.Request} req
   * @param {import("express").Router.Response} res
   *
   * **req body**:
   * ---
   * {
   *  dateRange: {
   *    startDate: string,
   *    endDate: string
   *  }
   * }
   */
  runSearchConsoleQueriesPerformanceReport = async (req, res) => {
    try {
      const { shopId, url } = req.user;
      const { dateRange } = req.body;

      let authenticatedUser = await ShopService.getGoogleIntegrationInfoWithAuthUserDetails(shopId);
      let siteUrl = url;
      if (isSubscriptionTestMode) {
        authenticatedUser = {
          ...authenticatedUser,
          ...GoogleAuthService.getDefaultAuthenticatedUser(),
        };
        siteUrl = "https://raymond-car-wash.myshopify.com";
      }

      const serviceJsonSetting = await ShopService.getShopSetting(shopId, settingKeys.GOOGLE_SERVICE_JSON);
      if (serviceJsonSetting) {
        authenticatedUser.serviceJson = serviceJsonSetting.value;
      }

      const dimensions = [searchConsoleDimensions.QUERY];

      const dataForCurrentRange = await GoogleSearchConsoleService.runAnalyticsQuery(authenticatedUser, siteUrl, {
        dateRange,
        dimensions,
      });
      const serializedDataForCurrentRange = serializeSearchConsoleAnalyticsQueryData({
        data: dataForCurrentRange,
        dimensions,
      });

      const prevRange = prevDateRange(dateRange);
      const dataForPrevRange = await GoogleSearchConsoleService.runAnalyticsQueryAgainstSepecificQueryValues(
        authenticatedUser,
        siteUrl,
        {
          dateRange,
          dimensions,
          values: serializedDataForCurrentRange.map((d) => d.query),
        }
      );
      const serializedDataForPrevRange = serializeSearchConsoleAnalyticsQueryData({
        data: dataForPrevRange,
        dimensions,
      });

      let serializedData = attachPrevDataToCurrentDataArr({
        currentArr: serializedDataForCurrentRange,
        prevArr: serializedDataForPrevRange,
        dataKey: searchConsoleDimensions.QUERY,
      });

      const dateWiseBatchData = await GoogleSearchConsoleService.runDateWiseAnalyticsAgainstSepecificQueryValues(
        authenticatedUser,
        siteUrl,
        {
          dateRange,
          values: serializedDataForCurrentRange.map((d) => d.query),
        }
      );

      const serializedDateWiseData = dateWiseBatchData
        .filter((d) => d.ok)
        .map((d) => {
          return {
            query: d.query,
            positionByDates: mapDateWiseSearchConsoleAnalyticsQueryDataToSpecificKey({
              data: d.data.rows || [],
              key: queryRowProperties.POSITION,
            }),
          };
        });

      serializedData = attachPrevDataToCurrentDataArr({
        currentArr: serializedData,
        prevArr: serializedDateWiseData,
        dataKey: searchConsoleDimensions.QUERY,
        merge: true,
      });

      return res.json(serializedData);
    } catch (err) {
      logger.error(err, { domain: req.user.shop, message: `Error running products analytics report.` });
      res.failed({
        message: toastMessages.SOMETHING_WENT_WRONG,
      });
    }
  };
}

module.exports = new AnalyticsController();
