const { SUCCESS, FAILED, VALIDATION_ERROR } = require("../config/responseCodes");

class BaseController {
  /**
   *
   * @param res
   * @param {string | null} message
   * @param {object} contents
   * @param {Number} status
   */
  sendResponse(res, { message = "", ...contents }, status = SUCCESS) {
    return res.status(status).json({
      ...contents,
      message,
    });
  }

  /**
   *
   * @param res
   * @param {string | null} message
   * @param {object} contents
   * @param {Number} status
   */
  sendFailedResponse(res, { message = "", errors = undefined }, status = FAILED) {
    return res.status(status).json({
      message,
      errors,
    });
  }

  /**
   *
   * @param res
   * @param {string | null} message
   * @param {object} contents
   * @param {Number} status
   */
  sendValidationFailedResponse(res, { message = "", errors = undefined }, status = VALIDATION_ERROR) {
    errors = errors.reduce((obj, item) => ({ ...obj, [item.path]: item.message }), {});
    return res.status(status).json({
      message: message || Object.values(errors)[0],
      errors,
    });
  }
}

module.exports = BaseController;
