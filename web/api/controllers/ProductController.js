const ProductService = require("../services/ProductService");
const ShopifyService = require("../services/ShopifyService");
const ShopService = require("../services/ShopService");
const ProductAnalysisService = require("../services/ProductAnalysisService");
const SitemapService = require("../services/SitemapService");
const logger = require("storeseo-logger");
const { updateOrInsertMetaInfoInArray, getMetaInfo, getFileNameFromGoogleBucketPublicURL } = require("../utils/helper");
const FileService = require("../services/FileService");
const { isEmpty } = require("lodash");
const yup = require("yup");
const { validateGeneralSEOUpdateData } = require("../utils/dataValidator");
const {
  serializeProductOptimizationDetails,
  serializeMulitLanguageProductOptimizationDetails,
  serializeMulitLanguageShopifyProductData,
} = require("../serializers/ProductSerializer");
const toastMessages = require("storeseo-enums/toastMessages");
const { NAMESPACE, METAFIELD_KEYS, METAFIELD_TYPES } = require("storeseo-enums/metafields");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const cache = require("../cache");
const { flattenMediaImages, generateImageAltTextObjectByMediaId } = require("../serializers/ProductImageSerializer");
const bulkOperationTypes = require("storeseo-enums/bulkOperationTypes");
const ProductImageService = require("../services/ProductImageService");
const altTextOptimizationStatus = require("storeseo-enums/altTextOptimization");
const ResourceDataBackupService = require("../services/resource/ResourceDataBackupService");
const resourceType = require("storeseo-enums/resourceType");
const imageOptimization = require("storeseo-enums/imageOptimization");
const ResourceOptimizationService = require("../services/resource/ResourceOptimizationService");
const resourceOPType = require("storeseo-enums/resourceOPType");
const activityLogService = require("../../admin/services/activityLog.service");
const LogOriginTypes = require("storeseo-enums/logOriginTypes");

class ProductController {
  products = async (req, res) => {
    try {
      const { shopId } = req.user;
      const { products, pagination } = await ProductService.getProducts(shopId, req.query);
      const productCount = await ProductService.countProducts(shopId);
      return res.success({ products, pagination, productCount });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  productDetails = async (req, res) => {
    try {
      const { shopId } = req.user;
      const { languageCode } = req.query;

      const [productPromise, paginationPromise] = await Promise.allSettled([
        languageCode
          ? ProductService.getMultiLanguageProductByCondition(shopId, {
              "$originalVersion.product_id$": `gid://shopify/Product/${req.params.id}`,
              language_code: languageCode,
            })
          : ProductService.getProductDetailsByShopifyId(shopId, req.params.id),
        ProductService.getPaginationOfProduct(shopId, req.params.id),
      ]);

      let product = productPromise.value;
      let pagination = paginationPromise.value;

      if (!product && languageCode) {
        product = await ProductService.getProductDetailsByShopifyId(shopId, req.params.id);
      }

      const focusKeywordSuggestions = product.focus_keyword_suggestions;

      const optimizationData = languageCode
        ? serializeMulitLanguageProductOptimizationDetails(product.analysis)
        : serializeProductOptimizationDetails(product.analysis);
      return res.success({ product, optimizationData, focusKeywordSuggestions, pagination });
    } catch (err) {
      console.error("err =", err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getProductOptimization = async (req, res) => {
    const pas = require("../services/ProductAnalysisService");
    try {
      const { shopId } = req.user;
      const { languageCode } = req.query;

      let product = languageCode
        ? await ProductService.getMultiLanguageProductByCondition(shopId, {
            "$originalVersion.product_id$": `gid://shopify/Product/${req.params.id}`,
            language_code: languageCode,
          })
        : await ProductService.getProductDetailsByShopifyId(shopId, req.params.id);

      if (!product && languageCode) {
        product = await ProductService.getProductDetailsByShopifyId(shopId, req.params.id);
      }
      product = await ProductService.serializeProductForAnalysis(product, req.body);

      const { analysis, score } = languageCode
        ? await pas.calculateMulitLanguageProductScore({
            shopId,
            product,
          })
        : await pas.calculateProductScore({
            shopId,
            product,
          });

      const optimizationData = languageCode
        ? serializeMulitLanguageProductOptimizationDetails(analysis)
        : serializeProductOptimizationDetails(analysis);

      return res.success({ optimizationData, score });
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return res.failed({ message: err.message });
    }
  };

  updateProduct = async (req, res) => {
    // validation
    const { error } = await validateGeneralSEOUpdateData(req.body);

    if (error) {
      return res.failedValidation({ errors: error.details });
    }

    try {
      const { id: shopifyId } = req.params;
      const { shopId, shop, accessToken } = req.user;
      const { images = [] } = req.body;

      const { session } = res.locals.shopify;

      const oldProduct = await ProductService.getProductDetailsByShopifyId(shopId, shopifyId);

      let product = await ProductService.updateProductDataToShopify(shopId, oldProduct.id, req.body, session);

      if (images.length) {
        product = await ProductService.updateProductImageAltData(shopId, oldProduct.id, images, session);
      }

      const analysedProduct = await ProductAnalysisService.analyseEachProduct({ product, shopId, oldProduct });
      return res.success({ product: analysedProduct, message: toastMessages.PRODUCT_UPDATED });
    } catch (err) {
      console.error(err, { ...req.body, shopDomain: req.user.shop });
      return res.failed({ message: err.message });
    }
  };

  updateTranslation = async (req, res) => {
    // validation
    const { error } = await validateGeneralSEOUpdateData(req.body);

    if (error) {
      return res.failedValidation({ errors: error.details });
    }

    try {
      const { id: shopifyId, languageCode } = req.params;

      let oldProduct = await ProductService.getMultiLanguageProductByCondition(req.user.shopId, {
        "$originalVersion.product_id$": `gid://shopify/Product/${req.params.id}`,
        language_code: languageCode,
      });

      if (!oldProduct) {
        const gId = `gid://shopify/Product/${shopifyId}`;

        const languageData = await ShopifyService.localaizations.getTranslationsByResourceIds(req.user.shop, {
          locale: languageCode,
          resourceIds: [gId],
        });

        const translatedProductData = languageData.edges[0]?.node;

        if (translatedProductData) {
          const originalProduct = await ProductService.getProductDetailsByShopifyId(req.user.shopId, shopifyId);
          const serializedMultiLanguageProduct = serializeMulitLanguageShopifyProductData({
            shopId: req.user.shopId,
            dbProductId: originalProduct.id,
            languageCode,
            mulitLangShopifyProductData: translatedProductData,
          });
          delete serializedMultiLanguageProduct.focus_keyword;

          oldProduct = await ProductService.upsertMulitLanguageProduct(serializedMultiLanguageProduct);
          await ProductAnalysisService.analyseMulitLanguageProduct({
            shopId: req.user.shopId,
            product: oldProduct,
          });
        }
      }

      const {
        metaTitle: meta_title,
        metaDescription: meta_description,
        handle,
        focusKeyword: focus_keyword,
        createRedirectUrl,
      } = req.body;

      if (createRedirectUrl) {
        await ShopifyService.createRedirectURL(req.user, {
          oldPath: `/${languageCode}/products/${oldProduct.handle}`,
          newPath: `/${languageCode}/products/${handle}`,
        });
      }

      const {
        edges: {
          0: {
            node: { translatableContent },
          },
        },
      } = await ShopifyService.localaizations.getTranslationsByResourceIds(req.user.shop, {
        locale: languageCode,
        resourceIds: [`gid://shopify/Product/${shopifyId}`],
      });

      const hashDigestMap = translatableContent.reduce((map, c) => ({ ...map, [c.key]: c.digest }), {});

      const translations = [
        {
          locale: languageCode,
          key: hashDigestMap.meta_title ? "meta_title" : "title",
          value: meta_title,
          translatableContentDigest: hashDigestMap.meta_title || hashDigestMap.title,
        },
        {
          locale: languageCode,
          key: hashDigestMap.meta_description ? "meta_description" : "body_html",
          value: meta_description,
          translatableContentDigest: hashDigestMap.meta_description || hashDigestMap.body_html,
        },
        {
          locale: languageCode,
          key: "handle",
          value: handle,
          translatableContentDigest: hashDigestMap.handle,
        },
      ];

      const result = await ShopifyService.localaizations.registerTranslationsByResourceId(req.user.shop, {
        resourceId: `gid://shopify/Product/${shopifyId}`,
        translations,
      });

      const updatedData = {
        meta_title,
        meta_description,
        handle,
        focus_keyword,
      };
      if (!hashDigestMap.meta_title) updatedData.title = meta_title;
      if (!hashDigestMap.meta_description) updatedData.body_html = meta_description;

      const updatedProduct = await ProductService.updateMulitLanguageProduct(
        req.user.shopId,
        oldProduct.id,
        updatedData
      );

      const analysedProduct = await ProductAnalysisService.analyseMulitLanguageProduct({
        shopId: req.user.shopId,
        product: updatedProduct,
      });

      return res.success({ product: analysedProduct, message: toastMessages.PRODUCT_UPDATED });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  updateImageAltTags = async (req, res) => {
    try {
      const { id } = req.params;
      const { shopId, shop, accessToken } = req.user;
      const { images } = req.body;
      const session = {
        shop,
        accessToken,
      };

      const oldProduct = await ProductService.getProductDetails(shopId, id);
      const product = await ProductService.updateProductImageAltData(shopId, oldProduct.id, images, session);

      const analysedProduct = await ProductAnalysisService.analyseEachProduct({ product, shopId, oldProduct });
      return res.success({
        message: toastMessages.IMAGE_UPDATED,
        product: analysedProduct,
      });
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  optimizeImageAltTags = async (req, res) => {
    try {
      const { id } = req.params;
      const { shopId, shop, accessToken } = req.user;
      const { image } = req.body;
      const session = {
        shop,
        accessToken,
      };

      // Backup image alt text data if the image is not already AI optimized
      if (image.alt_text_optimization_status !== altTextOptimizationStatus.OPTIMIZED) {
        const imagesObjectByMediaId = generateImageAltTextObjectByMediaId({ ...image, alt_text: image.prev_alt_text });

        await ResourceDataBackupService.upsert({
          shop_id: shopId,
          resource_id: image.id,
          resource_type: resourceType.PRODUCT_IMAGE,
          resource_op_type: resourceOPType.AI_OPTIMIZATION,
          data: imagesObjectByMediaId,
        });
      }

      const oldProduct = await ProductService.getProductDetails(shopId, id);
      const product = await ProductService.updateProductImageAltData(shopId, oldProduct.id, [image], session);

      // Update image alt text optimization status and stats
      const resourceOptimization = await ResourceOptimizationService.getByCondition({
        shop_id: shopId,
        resource_id: image.id,
        resource_type: resourceType.PRODUCT_IMAGE,
        resource_op_type: resourceOPType.AI_OPTIMIZATION,
      });

      const alt_text_optimization_stats = {
        ...(resourceOptimization ? resourceOptimization.optimization_stats : imageOptimization.optimizationStats),
        optimization_count: resourceOptimization
          ? resourceOptimization.optimization_stats?.optimization_count + 1
          : imageOptimization.optimizationStats.optimization_count + 1,
      };

      await ProductImageService.updateImage(image.id, {
        alt_text_optimized_at: new Date(),
        alt_text_optimization_status: altTextOptimizationStatus.OPTIMIZED,
      });

      await ResourceOptimizationService.upsert({
        shop_id: shopId,
        resource_id: image.id,
        resource_type: resourceType.PRODUCT_IMAGE,
        resource_op_type: resourceOPType.AI_OPTIMIZATION,
        optimization_stats: alt_text_optimization_stats,
      });

      // Re-analyse product after image alt text optimization
      const analysedProduct = await ProductAnalysisService.analyseEachProduct({ product, shopId, oldProduct });
      return res.success({
        message: toastMessages.IMAGE_UPDATED,
        product: analysedProduct,
      });
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  restoreOptimizeImageAltTags = async (req, res) => {
    try {
      const { images } = req.body;
      const { shopId, shop, accessToken } = req.user;
      const session = {
        shop,
        accessToken,
      };

      let updatedImages = [];

      for (let image of images) {
        const { productId: id, imageId } = image;

        const oldProduct = await ProductService.getProductDetails(shopId, id);
        const dbImage = await ProductImageService.getImage(imageId);

        const productImageBackupData = await ResourceDataBackupService.getByCondition({
          shop_id: shopId,
          resource_id: imageId,
          resource_type: resourceType.PRODUCT_IMAGE,
          resource_op_type: resourceOPType.AI_OPTIMIZATION,
        });

        const updateImage = {
          ...dbImage,
          alt_text: productImageBackupData?.data[dbImage.media_id] || "",
        };

        updatedImages.push(updateImage);

        const product = await ProductService.updateProductImageAltData(shopId, oldProduct.id, [updateImage], session);

        if (dbImage) {
          const resourceOptimization = await ResourceOptimizationService.getByCondition({
            shop_id: shopId,
            resource_id: dbImage.id,
            resource_type: resourceType.PRODUCT_IMAGE,
            resource_op_type: resourceOPType.AI_OPTIMIZATION,
          });

          const alt_text_optimization_stats = {
            ...(resourceOptimization ? resourceOptimization.optimization_stats : imageOptimization.optimizationStats),
            restore_count: resourceOptimization
              ? resourceOptimization.optimization_stats?.restore_count + 1
              : imageOptimization.optimizationStats.restore_count + 1,
          };

          await ProductImageService.updateImage(dbImage.id, {
            alt_text_optimization_status: altTextOptimizationStatus.RESTORED,
          });

          await ResourceOptimizationService.upsert({
            shop_id: shopId,
            resource_id: dbImage.id,
            resource_type: resourceType.PRODUCT_IMAGE,
            resource_op_type: resourceOPType.AI_OPTIMIZATION,
            optimization_stats: alt_text_optimization_stats,
          });
        }
        await ProductAnalysisService.analyseEachProduct({ product, shopId, oldProduct });
      }

      return res.success({
        message: toastMessages.IMAGE_UPDATED,
        images: updatedImages,
      });
    } catch (error) {
      console.log(error);
      res.failed({ message: "Something went wrong!" });
    }
  };

  getSitemaps = async (req, res) => {
    try {
      const { shopId } = req.user;
      const { sitemaps, pagination } = await ProductService.getSitemaps(shopId, req.query);

      const sitemap = await ShopService.getShopSitemapData(shopId);

      const totalCount = await ProductService.countProducts(shopId);

      return res.success({ sitemaps, pagination, sitemap, totalCount });
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  submitSitemaps = async (req, res) => {
    const schema = yup.object().shape({
      sitemap: yup.object().required("Please select at least 1 product to update."),
      limit: yup.number(),
    });

    try {
      await schema.validate(req.body, { abortEarly: false });
    } catch (error) {
      return res.failedValidation({ errors: error.inner.map((item) => ({ path: item.path, message: item.message })) });
    }

    try {
      const { sitemap } = req.body;
      const { shopId, shop, accessToken } = req.user;
      const session = {
        shop,
        accessToken,
      };

      await ProductService.updateSitemap({ shopId, session, sitemap });

      return res.success({
        sitemapStatus: Number(sitemap.status),
        message: toastMessages.SITEMAP_UPDATED,
      });
    } catch (err) {
      console.error(err.toString());
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  syncProducts = async (req, res) => {
    try {
      const { migrateDataFromApp, shopId, shop } = req.user;

      activityLogService.addActivityLog({
        req,
        prevData: null,
        updatedData: null,
        subject: "Products Sync Started",
        logOrigin: LogOriginTypes.APP,
        domain: shop,
      });

      const boService = require("../services/BulkOperationService");
      const hasRunningProductSyncBulkOperation = await boService.hasRunningBulkOperation(
        shopId,
        bulkOperationTypes.PRODUCT_SYNC
      );
      if (hasRunningProductSyncBulkOperation) {
        throw new Error("Product sync running.");
      }

      console.log(`\n\n---[${req.user.shop}] - creating bulk sync...\n---\n\n`);
      await boService.startBulkQueryOperation({
        shopId,
        session: req.user,
        operationType: bulkOperationTypes.PRODUCT_SYNC,
        limit: req.user.permission.products,
      });

      await cache.product.syncOngoing(shop, true);
      await cache.product.migrateDataFromApp(shop, migrateDataFromApp);

      return res.success({
        message: toastMessages.PRODUCTS_SYNC_STARTED,
      });
    } catch (err) {
      console.log("err: ", err);
      return res.failed({ message: err.message });
    }
  };

  syncProduct = async (req, res) => {
    const { id: productId } = req.params;
    try {
      const gId = `gid://shopify/Product/${productId}`;
      const shopifyProduct = await ShopifyService.getProductFromShopify(req.user.shop, gId);
      if (isEmpty(shopifyProduct)) {
        throw Error("Product not found.");
      }

      shopifyProduct.mediaImages = flattenMediaImages(shopifyProduct);
      const savedProduct = await ProductService.saveOrUpdateProduct(req.user.shopId, shopifyProduct);

      if (savedProduct) {
        await SitemapService.storeSitemapData(savedProduct, analysisEntityTypes.PRODUCT);
        await ProductAnalysisService.analyseEachProduct({
          shopId: req.user.shopId,
          product: savedProduct,
          isLiveUpdate: false,
        });
      }

      return res.success({
        message: toastMessages.SINGLE_PRODUCT_SYNCED,
      });
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  syncTranslation = async (req, res) => {
    const { id: productId, languageCode } = req.params;

    try {
      const gId = `gid://shopify/Product/${productId}`;

      const languageData = await ShopifyService.localaizations.getTranslationsByResourceIds(req.user.shop, {
        locale: languageCode,
        resourceIds: [gId],
      });

      const translatedProductData = languageData.edges[0]?.node;

      if (translatedProductData) {
        const originalProduct = await ProductService.getProductDetailsByShopifyId(req.user.shopId, productId);
        const serializedMultiLanguageProduct = serializeMulitLanguageShopifyProductData({
          shopId: req.user.shopId,
          dbProductId: originalProduct.id,
          languageCode,
          mulitLangShopifyProductData: translatedProductData,
        });
        delete serializedMultiLanguageProduct.focus_keyword;

        const savedMultiLanguageProduct =
          await ProductService.upsertMulitLanguageProduct(serializedMultiLanguageProduct);
        await ProductAnalysisService.analyseMulitLanguageProduct({
          shopId: req.user.shopId,
          product: savedMultiLanguageProduct,
        });
      }

      return res.success({
        message: toastMessages.TRANSLATION_SYNCED,
      });
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  // getNoIndexStatus = async (req, res) => {
  //   try {
  //     const { shopId } = req.user;
  //     const { id: productId } = req.params;

  //     const seoMetaSettings = await SitemapService.getProductSitemapDetails(shopId, productId);
  //     return res.success({ noIndexStatus: seoMetaSettings?.no_index });
  //   } catch (err) {
  //     logger.error(
  //       `Failed to get no-index status for shopId ${req.user?.shop} -> productId ${req.params?.id}. Error ${err}`
  //     );
  //     return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
  //   }
  // };

  // getNoFollowStatus = async (req, res) => {
  //   try {
  //     const { shopId } = req.user;
  //     const { id: productId } = req.params;

  //     const seoMetaSettings = await SitemapService.getProductSitemapDetails(shopId, productId);
  //     return res.success({ noFollowStatus: seoMetaSettings?.no_follow });
  //   } catch (err) {
  //     logger.error(
  //       `Failed to get no-follow status for shopId ${req.user?.shop} -> productId ${req.params?.id}. Error ${err}`
  //     );
  //     return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
  //   }
  // };

  toggleNoIndexStatus = async (req, res) => {
    //todo: simplify this method and move logics to service method
    try {
      const { shopId } = req.user;
      const { id: productId } = req.params;

      const product = await ProductService.getProductDetailsByShopifyId(shopId, productId);
      const meta = product?.meta || [];
      let newStatus = "0";

      let noIndexMeta = meta.find((m) => m.namespace === NAMESPACE.STORE_SEO && m.key === METAFIELD_KEYS.NO_INDEX);

      if (noIndexMeta) {
        newStatus = noIndexMeta.value === "1" ? "0" : "1";
        noIndexMeta.value = newStatus;
      } else {
        meta.push({
          namespace: NAMESPACE.STORE_SEO,
          key: METAFIELD_KEYS.NO_INDEX,
          value: "1",
          type: METAFIELD_TYPES.NUMBER_INTEGER,
        });
        newStatus = "1";
      }

      const {
        productUpdate: { product: shopifyProduct, userErrors },
      } = await ShopifyService.updateProductMetaFields(req.user.shop, {
        productId: product.product_id,
        metaFieldDefinitions: meta,
      });

      if (userErrors.length > 0) {
        throw new Error(userErrors[0]?.message);
      }

      await ProductService.upsertProductMetadata(shopId, product.id, shopifyProduct);
      await SitemapService.updateSitemapData({
        shopId,
        resourceId: product.id,
        resourceType: analysisEntityTypes.PRODUCT,
        metaKey: METAFIELD_KEYS.NO_INDEX,
        metaStatus: newStatus,
      });

      return res.success({
        noIndexStatus: Number(newStatus),
        message: toastMessages.NO_INDEX_STATUS_UPDATED,
      });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: "Failed to toggle no-index status",
        productId: req.params?.id,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  toggleNoFollowStatus = async (req, res) => {
    //todo: simplify this method and move logics to service method
    try {
      const { shopId } = req.user;
      const { id: productId } = req.params;

      const product = await ProductService.getProductDetailsByShopifyId(shopId, productId);
      const meta = product?.meta || [];
      let newStatus = "0";

      let noFollowMeta = meta.find((m) => m.namespace === NAMESPACE.STORE_SEO && m.key === METAFIELD_KEYS.NO_FOLLOW);
      if (noFollowMeta) {
        newStatus = noFollowMeta.value === "1" ? "0" : "1";
        noFollowMeta.value = newStatus;
      } else {
        meta.push({
          namespace: NAMESPACE.STORE_SEO,
          key: METAFIELD_KEYS.NO_FOLLOW,
          value: "1",
          type: METAFIELD_TYPES.NUMBER_INTEGER,
        });
        newStatus = "1";
      }

      const {
        productUpdate: { product: shopifyProduct, userErrors },
      } = await ShopifyService.updateProductMetaFields(req.user.shop, {
        productId: product.product_id,
        metaFieldDefinitions: meta,
      });

      if (userErrors.length > 0) {
        throw new Error(userErrors[0]?.message);
      }

      await ProductService.upsertProductMetadata(shopId, product.id, shopifyProduct);

      await SitemapService.updateSitemapData({
        shopId,
        resourceId: product.id,
        resourceType: analysisEntityTypes.PRODUCT,
        metaKey: METAFIELD_KEYS.NO_FOLLOW,
        metaStatus: newStatus,
      });

      return res.success({
        noFollowStatus: Number(newStatus),
        message: toastMessages.NO_FOLLOW_STATUS_UPDATED,
      });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: "Failed to toggle no-index status",
        productId: req.params?.id,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  updateSocialMediaPreviewImage = async (req, res) => {
    try {
      const { shop: shopDomain, shopId } = req.user;
      const { id: productId } = req.params;
      const { facebook_preview_image, twitter_preview_image } = req.files ?? {};
      const namespace = NAMESPACE.STORE_SEO;
      const facebookImagePreviewMetaKey = METAFIELD_KEYS.FACEBOOK_PREVIEW_IMAGE_URL;
      const twitterImagePreviewMetaKey = METAFIELD_KEYS.TWITTER_PREVIEW_IMAGE_URL;
      const metaFieldType = METAFIELD_TYPES.SINGLE_LINE_TEXT;

      let facebookImagePreviewURL = "";
      let twitterImagePreviewURL = "";

      const product = await ProductService.getProductDetails(shopId, productId);
      let meta = isEmpty(product?.meta) ? [] : product.meta;

      const prevImages = [];

      if (facebook_preview_image) {
        const prevMetaInfo = getMetaInfo(namespace, facebookImagePreviewMetaKey, meta);
        prevImages.push(prevMetaInfo?.value);

        const { url } = await FileService.saveUploadedFileForShop(shopDomain, facebook_preview_image);
        meta = updateOrInsertMetaInfoInArray({
          metaArr: meta,
          namespace,
          key: facebookImagePreviewMetaKey,
          metaInfo: {
            namespace,
            key: facebookImagePreviewMetaKey,
            value: url,
            type: metaFieldType,
          },
        });
        facebookImagePreviewURL = url;
      }

      if (twitter_preview_image) {
        const prevMetaInfo = getMetaInfo(namespace, twitterImagePreviewMetaKey, meta);
        prevImages.push(prevMetaInfo?.value);

        const { url } = await FileService.saveUploadedFileForShop(shopDomain, twitter_preview_image);
        meta = updateOrInsertMetaInfoInArray({
          metaArr: meta,
          namespace,
          key: twitterImagePreviewMetaKey,
          metaInfo: {
            namespace,
            key: twitterImagePreviewMetaKey,
            value: url,
            type: metaFieldType,
          },
        });
        twitterImagePreviewURL = url;
      }

      if (facebook_preview_image || twitter_preview_image) {
        const {
          productUpdate: { product: shopifyProduct, userErrors },
        } = await ShopifyService.updateProductMetaFields(req.user.shop, {
          productId: product.product_id,
          metaFieldDefinitions: meta,
        });

        if (userErrors.length > 0) {
          throw new Error(userErrors[0]?.message);
        }

        await ProductService.upsertProductMetadata(shopId, productId, shopifyProduct);

        prevImages
          .filter((img) => img)
          .map((img) => getFileNameFromGoogleBucketPublicURL(img))
          .forEach((fileName) => FileService.deleteFile(fileName, true));
      }

      return res.success({ facebookImagePreviewURL, twitterImagePreviewURL });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: "Error saving uploaded social media preview image",
        productId: req.params?.id,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  updateCanonicalUrl = async (req, res) => {
    try {
      const { shop, shopId } = req.user;
      const { id: productId } = req.params;
      const { canonicalUrl } = req.body;

      const namespace = NAMESPACE.STORE_SEO;
      const canonicalUrlMetaKey = METAFIELD_KEYS.CANNONICAL_URL;
      const metaFieldType = METAFIELD_TYPES.SINGLE_LINE_TEXT;

      const product = await ProductService.getProductDetails(shopId, productId);
      let meta = isEmpty(product?.meta) ? [] : product.meta;

      meta = updateOrInsertMetaInfoInArray({
        metaArr: meta,
        metaInfo: {
          namespace,
          key: canonicalUrlMetaKey,
          value: canonicalUrl,
          type: metaFieldType,
        },
        namespace,
        key: canonicalUrlMetaKey,
      });

      const {
        productUpdate: { product: shopifyProduct, userErrors },
      } = await ShopifyService.updateProductMetaFields(req.user.shop, {
        productId: product.product_id,
        metaFieldDefinitions: meta,
      });

      if (userErrors.length > 0) {
        throw new Error(userErrors[0]?.message);
      }

      await ProductService.upsertProductMetadata(shopId, productId, shopifyProduct);

      return res.success({ canonicalUrl });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: "Error updating canonical url",
        productId: req.params?.id,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };
}

module.exports = new ProductController();
