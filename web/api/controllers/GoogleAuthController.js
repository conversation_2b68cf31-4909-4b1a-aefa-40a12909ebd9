const googleIntegrationSteps = require("storeseo-enums/googleIntegrationSteps");
const logger = require("storeseo-logger");
const GoogleApiService = require("../services/GoogleApiService");
const GoogleAuthService = require("../services/GoogleAuthService");
const GoogleIntegrationService = require("../services/GoogleIntegrationService");
const ShopService = require("../services/ShopService");
const { verifyJWTToken } = require("../utils/helper");

class GoogleAuthController {
  handleOAuthCallback = async (req, res) => {
    const { code, state } = req.query;
    const { shopDomain, authTargetAction, redirectURL } = verifyJWTToken(state);

    try {
      const authenticatedUser = await GoogleAuthService.storeAuthenticatedUserDetails(code, shopDomain);
      const shop = await ShopService.getShop(shopDomain);

      if (authenticatedUser && shop.plan_rules.google_console) {
        await ShopService.updateGoogleIntegrationUserInfo(shopDomain, authenticatedUser);
        await this.takeActionBasedOnAuthTarget(shopDomain, authenticatedUser, authTargetAction);
      }

      res.redirect(`${redirectURL}&success=true`);
    } catch (err) {
      logger.error(err, {
        domain: shopDomain,
        message: `Failed to take action ${authTargetAction} after oauth2callback.`,
      });
      res.redirect(redirectURL);
    }
  };

  takeActionBasedOnAuthTarget = async (shopDomain, authenticatedUser, authTargetAction) => {
    const shop = await ShopService.getShop(shopDomain);

    if (authTargetAction === googleIntegrationSteps.AUTHENTICATE) {
      await ShopService.updateGoogleIntegrationStatus(shopDomain, { authenticated: true });
    } else if (authTargetAction === googleIntegrationSteps.SITE_VERIFICATION) {
      await GoogleIntegrationService.doSiteVerification(shopDomain, authenticatedUser);
    } else if (authTargetAction === googleIntegrationSteps.SEARCH_CONSOLE_ADD_SITE) {
      const status = await GoogleApiService.addSiteToSearchConsole(shop?.url, authenticatedUser);
      await ShopService.updateGoogleIntegrationStatus(shopDomain, { searchConsoleAdded: status });
    } else if (authTargetAction === googleIntegrationSteps.SITEMAP_SUBMISSION) {
      const status = await GoogleApiService.submitSitemapForShop(shop?.url, authenticatedUser);
      await ShopService.updateSitemapSubmitStatus(shopDomain, { sitemapSubmitted: status });
    } else if (authTargetAction === googleIntegrationSteps.INSTANT_INDEXING) {
      await ShopService.updateGoogleIntegrationStatus(shopDomain, { indexingPermission: true });
    } else if (authTargetAction === googleIntegrationSteps.ANALYTICS_DATA) {
      await ShopService.updateGoogleIntegrationStatus(shopDomain, { analyticsDataPermission: true });
    }
  };
}

module.exports = new GoogleAuthController();
