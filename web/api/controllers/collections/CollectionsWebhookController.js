//@ts-check
const logger = require("storeseo-logger");
const { QUEUE_NAMES } = require("../../queue/config");
const { dispatchQueue } = require("../../queue/queueDispatcher");

class CollectionWebhookController {
  /**
   * Collection create webhook handler
   * @param {*} req
   * @param {*} res
   * @returns {Promise<boolean>}
   */
  collectionCreate = async (req, res) => {
    const { user } = req;
    res.status(200);
    res.json({ message: "Success" });
    try {
      const { headers, body } = req;
      const message = {
        headers,
        body,
        user,
      };
      dispatchQueue({
        queueName: QUEUE_NAMES.WEBHOOK_COLLECTION_CREATE,
        message,
      });

      return true;
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return false;
    }
  };
  /**
   * Collection update webhook handler
   * @param {*} req
   * @param {*} res
   * @returns {Promise<boolean>}
   */
  collectionUpdate = async (req, res) => {
    const { user } = req;
    res.status(200);
    res.json({ message: "Success" });
    try {
      const { headers, body } = req;
      const message = {
        headers,
        body,
        user,
      };
      dispatchQueue({
        queueName: QUEUE_NAMES.WEBHOOK_COLLECTION_UPDATE,
        message,
      });

      return true;
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return false;
    }
  };

  /**
   * Collection delete webhook handler
   * @param {*} req
   * @param {*} res
   * @returns {Promise<boolean>}
   */
  collectionDelete = async (req, res) => {
    res.status(200);
    res.json({ message: "Success" });
    try {
      const { headers, body } = req;
      const message = {
        headers,
        body,
      };
      dispatchQueue({
        queueName: QUEUE_NAMES.WEBHOOK_COLLECTION_DELETE,
        message,
      });

      return true;
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return false;
    }
  };
}

module.exports = new CollectionWebhookController();
