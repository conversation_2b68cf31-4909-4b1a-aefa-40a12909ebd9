// // @ts-check
const toastMessages = require("storeseo-enums/toastMessages");
const CollectionService = require("../../services/collections/CollectionService");
const ShopifyService = require("../../services/ShopifyService");
const logger = require("storeseo-logger");
const { QUEUE_NAMES } = require("../../queue/config");
const { dispatchQueue } = require("../../queue/queueDispatcher");
const { serializeCollectionOptimizationDetails } = require("../../serializers/CollectionSerializer");
const { validateGeneralSEOUpdateData } = require("../../utils/dataValidator");
const CollectionMetaService = require("../../services/collections/CollectionMetaService");
const { METAFIELD_KEYS, NAMESPACE } = require("storeseo-enums/metafields");
const SitemapService = require("../../services/SitemapService");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const LogOriginTypes = require("storeseo-enums/logOriginTypes");
const activityLogService = require("../../../admin/services/activityLog.service");
class CollectionController {
  /**
   * Get collection with optimization data by collection id
   * @param {*} req
   * @param {*} res
   * @returns
   */
  getCollection = async (req, res) => {
    try {
      const { shopId } = req.user;
      const { id: collectionId } = req.params;

      const collectionGID = `gid://shopify/Collection/${collectionId}`;
      const [collectionPromise, paginationPromise] = await Promise.allSettled([
        CollectionService.getByCondition(shopId, { collection_id: collectionGID }),
        CollectionService.getPagination(shopId, collectionId),
      ]);

      if (collectionPromise.status === "rejected" || paginationPromise.status === "rejected")
        throw new Error("Error fetching collection details");

      let collection = collectionPromise.value;
      const pagination = paginationPromise.value;

      const focusKeywordSuggestions = collection.focus_keyword_suggestions;

      const optimizationData = serializeCollectionOptimizationDetails(collection?.analysis);

      return res.success({ collection, focusKeywordSuggestions, optimizationData, pagination });
    } catch (err) {
      console.log("Error", err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG, err });
    }
  };
  /**
   * Get collections for the shop
   * @param {*} req
   * @param {*} res
   * @returns
   */
  getCollections = async (req, res) => {
    try {
      const { shopId } = req.user;
      const { collections, pagination } = await CollectionService.getCollections(shopId, req.query, [
        "id",
        "handle",
        "title",
        "focus_keyword",
        "created_at",
        "issues",
        "score",
        "ai_optimization_status",
      ]);
      const collectionCount = await CollectionService.count(shopId);
      return res.success({ collections, pagination, collectionCount });
    } catch (err) {
      console.log(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  /**
   * Get shopify collection count for the shop
   * @param {*} req
   * @param {*} res
   * @returns
   */
  getCollectionCount = async (req, res) => {
    try {
      const user = { shop: req.user.shop, accessToken: req.user.accessToken };
      const collectionsCount = await ShopifyService.getShopifyCollectionCount(user.shop);
      return res.success({ collectionsCount });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: "Error getting collection count",
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  /**
   * Sync collections from shopify
   * @param {*} req
   * @param {*} res
   * @returns
   */
  syncCollections = async (req, res) => {
    try {
      const { shop } = req.user;

      console.log(`\n\n---[${req.user.shop}] - dispatching regular sync for collection \n---\n\n`);

      activityLogService.addActivityLog({
        req,
        prevData: null,
        updatedData: null,
        subject: "Collections Sync Started",
        logOrigin: LogOriginTypes.APP,
        domain: shop,
      });

      dispatchQueue({
        queueName: QUEUE_NAMES.COLLECTION_SYNC_QUEUE,
        message: {
          user: req.user,
        },
      });

      await CollectionService.setSyncOngoingStatus(shop, true);

      return res.success({
        message: toastMessages.COLLECTIONS_SYNC_STARTED,
      });
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return res.failed({ message: err.message });
    }
  };

  /**
   * get collection optimization data
   * @param {*} req
   * @param {*} res
   * @returns
   */
  getCollectionOptimization = async (req, res) => {
    const CollectionAnalysisService = require("../../services/collections/CollectionAnalysisService");
    try {
      const { shopId } = req.user;
      const { id: collectionId } = req.params;
      const collection = await CollectionService.serializeForAnalysis(shopId, collectionId, req.body);
      const { analysis, score } = await CollectionAnalysisService.calculateScore({
        shopId,
        collection,
      });

      const optimizationData = serializeCollectionOptimizationDetails(analysis);

      return res.success({ optimizationData, score });
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return res.failed({ message: err.message });
    }
  };

  /**
   * Update collection optimization data
   * @param {*} req
   * @param {*} res
   */
  updateCollection = async (req, res) => {
    const CollectionAnalysisService = require("../../services/collections/CollectionAnalysisService");
    const { error } = await validateGeneralSEOUpdateData(req.body);
    if (error) {
      return res.failedValidation({ errors: error.details });
    }

    try {
      const { id: collectionId } = req.params;
      const { shopId, url } = req.user;
      const { session } = res.locals.shopify;
      const collectionGID = `gid://shopify/Collection/${collectionId}`;

      const oldCollection = await CollectionService.getByCondition(shopId, { collection_id: collectionGID });
      let collection = await CollectionService.updateAndSyncWithShopify(shopId, collectionGID, req.body, session);

      const analysedCollection = await CollectionAnalysisService.analysis({ collection, shopId, oldCollection });
      return res.success({ collection: analysedCollection, message: toastMessages.COLLECTIONS_UPDATE });
    } catch (err) {
      logger.error(err, { ...req.body, domain: req.user.shop });
      return res.failed({ message: err.message });
    }
  };

  /**
   * Toggle shopify collection custom metafield for no-index status
   * @param {*} req
   * @param {*} res
   * @returns
   */
  toggleNoIndexStatus = async (req, res) => {
    try {
      const { shopId } = req.user;
      const { id: collectionId } = req.params;

      const collectionGID = `gid://shopify/Collection/${collectionId}`;

      const collection = await CollectionService.getByCondition(shopId, { collection_id: collectionGID });
      const meta = collection?.meta || [];
      let newStatus = "1";

      let noIndexMeta = meta.find((m) => m.namespace === NAMESPACE.STORE_SEO && m.key === METAFIELD_KEYS.NO_INDEX);
      if (noIndexMeta) {
        newStatus = noIndexMeta?.value === "1" ? "0" : "1";
      }

      const {
        metafieldsSet: { metafields },
      } = await ShopifyService.setMetafield(req.user.shop, {
        ownerId: collectionGID,
        key: METAFIELD_KEYS.NO_INDEX,
        value: newStatus,
      });
      // console.log("metafields update", metafields);

      await CollectionMetaService.upsert(shopId, collection.id, metafields);

      await SitemapService.updateSitemapData({
        shopId,
        resourceId: collection.id,
        resourceType: analysisEntityTypes.COLLECTION,
        metaKey: METAFIELD_KEYS.NO_INDEX,
        metaStatus: newStatus,
      });

      return res.success({
        noIndexStatus: Number(newStatus),
        message: toastMessages.NO_INDEX_STATUS_UPDATED,
      });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: "Failed to toggle no-index status",
        collectionId: req.params.id,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  /**
   * Toggle shopify collection custom metafield for no-follow status
   * @param {*} req
   * @param {*} res
   * @returns
   */
  toggleNoFollowStatus = async (req, res) => {
    try {
      const { shopId } = req.user;
      const { id: collectionId } = req.params;

      const collectionGID = `gid://shopify/Collection/${collectionId}`;

      const collection = await CollectionService.getByCondition(shopId, { collection_id: collectionGID });
      const meta = collection?.meta || [];
      let newStatus = "1";

      let noFollowMeta = meta.find((m) => m.namespace === NAMESPACE.STORE_SEO && m.key === METAFIELD_KEYS.NO_FOLLOW);
      if (noFollowMeta) {
        newStatus = noFollowMeta?.value === "1" ? "0" : "1";
      }

      const {
        metafieldsSet: { metafields },
      } = await ShopifyService.setMetafield(req.user.shop, {
        ownerId: collectionGID,
        key: METAFIELD_KEYS.NO_FOLLOW,
        value: newStatus,
      });
      // console.log("metafields update", metafields);

      await CollectionMetaService.upsert(shopId, collection.id, metafields);

      await SitemapService.updateSitemapData({
        shopId,
        resourceId: collection.id,
        resourceType: analysisEntityTypes.COLLECTION,
        metaKey: METAFIELD_KEYS.NO_FOLLOW,
        metaStatus: newStatus,
      });

      return res.success({
        noFollowStatus: Number(newStatus),
        message: toastMessages.NO_FOLLOW_STATUS_UPDATED,
      });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: "Failed to toggle no-follow status",
        collectionId: req.params.id,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };
}

module.exports = new CollectionController();
