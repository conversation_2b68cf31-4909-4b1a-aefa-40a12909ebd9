const { ONBOARD_STEPS } = require("storeseo-enums/settingKeys");
const ShopService = require("../services/ShopService");
const toastMessages = require("storeseo-enums/toastMessages");
const AnalysisService = require("../services/AnalysisService");
const cache = require("../cache");
const ProductService = require("../services/ProductService");
const AppDataMigrateService = require("../services/AppDataMigrateService");
const BulkOperationTypes = require("storeseo-enums/bulkOperationTypes");

class OnboardingController {
  getOnboardingStatus = async (req, res) => {
    try {
      const { shopId } = req.user;

      const settings = await ShopService.getOnboardingSteps(shopId);

      return res.success({
        message: "Onboarding steps fetched",
        status: settings ? JSON.parse(settings.value) : [],
      });
    } catch (err) {
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  updateOnboardingStatus = async (req, res) => {
    try {
      const { shopId, shop: shopDomain } = req.user;
      const { stepName, status } = req.body;

      const value = await ShopService.getOnboardingSteps(shopId);

      value.steps = { ...value.steps, [stepName]: status };
      value.currentStep = stepName;
      value.isCompleted = Object.values(value.steps).every((step) => step);

      const data = {
        key: ONBOARD_STEPS,
        value: JSON.stringify(value),
        value_type: "json",
      };

      const settings = await ShopService.updateShopSetting(shopId, data);

      await cache.isOnboardingCompleted(shopDomain, value.isCompleted);

      return res.success({
        message: "Onboarding steps updated",
        status: settings ? settings.value : [],
      });
    } catch (err) {
      console(err);

      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getAnalysisData = async (req, res) => {
    try {
      const { shopId } = req.user;
      const analysis = await AnalysisService.getOnboardingAnalysisData(shopId);
      return res.success({ analysis });
    } catch (err) {
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  migrateDataFromApp = async (req, res) => {
    try {
      const { shopId, shop: shopDomain, onboardStep } = req.user;
      const { selectedApp = null } = req.body;

      const updateData = {
        data_migrated_from_app: selectedApp,
      };

      const shop = await ShopService.updateShop(shopId, updateData);

      // await cache.onboardStep(shopDomain, updateData.onboard_step);
      await cache.product.syncOngoing(shop.domain, true);
      await cache.product.migrateDataFromApp(shop.domain, selectedApp);

      // manually sync a few products from Shopify
      await ProductService.storeProductsFromShopify(req.user, shopId, selectedApp);
      // dispatch product sync queue to save remaining products
      const boService = require("../services/BulkOperationService");
      const hasRunningProductSyncBulkOperation = await boService.hasRunningBulkOperation(
        shopId,
        BulkOperationTypes.PRODUCT_SYNC
      );
      if (!hasRunningProductSyncBulkOperation) {
        console.log(`\n\n---[${req.user.shop}] - creating bulk sync...\n---\n\n`);
        await boService.startBulkQueryOperation({
          shopId,
          session: req.user,
          operationType: BulkOperationTypes.PRODUCT_SYNC,
        });
      }

      // migrate homepage seo data
      if (selectedApp) AppDataMigrateService.collectAndMergeHomepageData(selectedApp, req.user.shop, req.user.url);

      return res.success({
        shop,
        message: toastMessages.PRODUCTS_SYNC_STARTED,
      });
    } catch (err) {
      console.error(err);
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };
}

module.exports = new OnboardingController();
