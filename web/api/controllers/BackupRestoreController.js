const BackupService = require("../services/BackupService");
const { validate } = require("../utils/dataValidator");
const { backupAndRestoreSchema } = require("storeseo-schema/settings/backupAndRestore");
const logger = require("storeseo-logger");
const activityLogService = require("../../admin/services/activityLog.service");
const LogOriginTypes = require("storeseo-enums/logOriginTypes");

class BackupRestoreController {
  static async getBackupRestoreData(req, res) {
    try {
      const { shopId } = req.user;
      const data = await BackupService.manualBackupList(shopId, req.query);
      return res.success({ message: "Get backup and restore data", data });
    } catch (error) {
      return res.failed({ message: error?.message, errors: error });
    }
  }

  static async getStoreBackupRestoreInProgressStatus(req, res) {
    const { shopId } = req.user;

    try {
      const backupInProgress = await BackupService.hasAnyBackupInProgress(shopId);
      const restoreInProgress = await BackupService.hasAnyRestoreInProgress(shopId);
      res.send({
        message: "Get store backup in progress status",
        hasBackupInProgress: backupInProgress,
        hasRestoreInProgress: restoreInProgress,
      });
    } catch (error) {
      res.failed({ message: error?.message, errors: error });
    }
  }

  static async createBackup(req, res) {
    const { shopId, shop } = req.user;
    const body = req.body;

    if (body) {
      const { error } = await validate(backupAndRestoreSchema, body);
      if (error) {
        return res.failedValidation({ errors: error.details });
      }
    }

    try {
      const backupCount = await BackupService.getAllManualBackupByShop(shopId);
      if (backupCount.length >= 5) {
        return res.failedValidation({
          errors: [
            {
              path: "formError",
              message: "You can only have 5 backups at a time",
            },
          ],
        });
      }

      const data = await BackupService.createManualBackup(shopId, body);

      activityLogService.addActivityLog({
        req,
        prevData: null,
        updatedData: data.toJSON(),
        subject: "Backup Created",
        logOrigin: LogOriginTypes.APP,
        domain: shop,
      });
      res.send({ message: "Creating backup", data });
    } catch (error) {
      res.failed({ message: error?.message, errors: error });
    }
  }

  static async restoreBackup(req, res) {
    try {
      const { shopId, shop } = req.user;
      const { id: backupId } = req.params;

      BackupService.restoreManualBackup(shopId, shop, parseInt(backupId));

      activityLogService.addActivityLog({
        req,
        prevData: null,
        updatedData: { backupId },
        subject: "Backup Restore Initialized",
        logOrigin: LogOriginTypes.APP,
        domain: shop,
      });

      res.send({ message: "Restore initialized" });
    } catch (error) {
      res.failed({ message: error?.message, errors: error });
    }
  }

  static async deleteBackup(req, res) {
    const { shopId, shop } = req.user;
    const { id } = req.params;

    try {
      activityLogService.addActivityLog({
        req,
        prevData: { backupId: id },
        updatedData: null,
        subject: "Backup Deleted",
        logOrigin: LogOriginTypes.APP,
        domain: shop,
      });

      await BackupService.deleteManualBackup(shopId, id);
      res.send({ message: "Backup deleted successfully" });
    } catch (error) {
      logger.error(error, {
        domain: shop,
        message: "Error deleting backup:",
        description: `Failed to delete backup: ${id} for shop: ${shopId}`,
        transaction: "BackupRestoreController.deleteBackup",
      });
      res.failed({
        message: error?.message || "Failed to delete backup",
        errors: error,
      });
    }
  }
}

module.exports = BackupRestoreController;
