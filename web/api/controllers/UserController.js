const userService = require("../services/UserService");
const CampaignService = require("../services/CampaignService");
const NotificationService = require("../services/NotificationService");
const cache = require("../cache");
const toastMessages = require("storeseo-enums/toastMessages");
const logger = require("storeseo-logger");

class UserController {
  userFind = async (req, res) => {
    try {
      const user = await userService.findUser(req.params.id);
      return res.success({ user });
    } catch (err) {
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  findAuthUser = async (req, res) => {
    try {
      const user = userService.sanitizeUserData(req.user);

      user.blogSyncOngoing = await cache.blogSyncOngoing(req.user.shop);
      user.pageSyncOngoing = await cache.pageSyncOngoing(req.user.shop);

      const unreadNotifications = await NotificationService.countUnread(user.shopId);
      const optimizationTaskOngoing = await cache.optimizationTaskRunning(req.user.shop);

      const campaign = await CampaignService.getRunningCampaign();

      const hiddenBanner = user.hiddenBanner;

      delete user["hiddenBanner"];

      return res.success({
        user,
        unreadNotifications,
        optimizationTaskOngoing,
        campaign: campaign?.toFormattedJSON(),
        hiddenBanner,
      });
    } catch (err) {
      logger.error(err, { user: { email: req.user.email, id: req.user.shop } });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };
}

module.exports = new UserController();
