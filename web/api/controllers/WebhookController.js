// // @ts-check
const logger = require("storeseo-logger");
const ShopService = require("../services/ShopService");
const { dispatchQueue } = require("../queue/queueDispatcher");
const { QUEUE_NAMES } = require("../queue/config");

class WebhookController {
  getAccessToken = async (headers) => {
    const shopDomain = headers["x-shopify-shop-domain"];
    const { access_token, id } = await ShopService.getShop(shopDomain);
    return {
      shopId: id,
      shop: shopDomain,
      accessToken: access_token,
    };
  };

  productUpdateOrCreate = async (req, res) => {
    res.status(200);
    res.json({ message: "Success" });
    try {
      const { body, url: requestPath, headers } = req;
      const message = {
        headers,
        body,
      };

      const { admin_graphql_api_id: productGqlId, status, id } = body;
      console.log(`[${req.user.shop}] --- Update webhook called for product --- ${productGqlId}.`);

      if (status === "active" && requestPath === "/product/create") {
        dispatchQueue({
          queueName: QUEUE_NAMES.WEBHOOK_PRODUCT_CREATE,
          message,
        });
      } else if (status === "active" && requestPath === "/product/update") {
        dispatchQueue({
          queueName: QUEUE_NAMES.WEBHOOK_PRODUCT_UPDATE,
          message,
        });
      } else {
        dispatchQueue({
          queueName: QUEUE_NAMES.WEBHOOK_PRODUCT_DELETE,
          message: {
            ...message,
            body: { id },
          },
        });
      }

      return true;
    } catch (err) {
      console.error("Product Update Or Create Webhook", err);
      return false;
    }
  };

  productDelete = async (req, res) => {
    res.status(200);
    res.json({ message: "Success" });
    try {
      const { headers, body } = req;

      // const hasWebhook = await xyz.hasWebhookId(headers);
      // if (hasWebhook) {
      //   return true;
      // }

      const { id } = body;
      const { shopId } = req.user;

      dispatchQueue({
        queueName: QUEUE_NAMES.WEBHOOK_PRODUCT_DELETE,
        message: {
          headers,
          body,
        },
      });

      return true;
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return false;
    }
  };

  appSubscriptionUpdate = async (req, res) => {
    res.status(200);
    res.json({ message: "Success" });

    try {
      const message = {
        headers: req.headers,
        body: req.body,
      };

      logger.debug("Subscription update webhook called.", {
        domain: req.headers["x-shopify-shop-domain"],
        webhookId: req.headers["x-shopify-webhook-id"],
        ...req.body.app_subscription,
      });

      dispatchQueue({
        queueName: QUEUE_NAMES.WEBHOOK_APP_SUBSCRIPTION_UPDATE_QUEUE,
        message,
      });
    } catch (error) {
      logger.error(error, { ...req.body });
    }
  };

  appPurchaseUpdate = async (req, res) => {
    res.status(200);
    res.json({ message: "Success" });

    try {
      const message = {
        headers: req.headers,
        body: req.body,
      };

      logger.debug("Purchase update webhook called.", {
        domain: req.headers["x-shopify-shop-domain"],
        webhookId: req.headers["x-shopify-webhook-id"],
        ...req.body.app_purchase_one_time,
      });

      dispatchQueue({
        queueName: QUEUE_NAMES.WEBHOOK_APP_PURCHASE_ONETIME,
        message,
      });
    } catch (error) {
      logger.error(error, { ...req.body });
    }
  };

  customerDataRequest = async (req, res) => {
    res.status(200);
    res.json({ message: "Success" });
    // const {request: {header, body}} = ctx;
  };

  customerRedact = async (req, res) => {
    res.status(200);
    res.json({ message: "Success" });
    // const {request: {header, body}} = ctx;
  };

  shopRedact = async (req, res) => {
    res.status(200);
    res.json({ message: "Success" });

    const { shop_domain: shopDomain } = req.body;
    dispatchQueue({
      queueName: QUEUE_NAMES.DELETE_SHOP_DATA_QUEUE,
      message: { shopDomain },
    });
    // const {request: {header, body}} = ctx;
  };

  handleThemeChange = async (req, res) => {
    res.status(200);
    res.json({ message: "Success" });

    const message = {
      headers: req.headers,
      body: req.body,
    };

    dispatchQueue({
      queueName: QUEUE_NAMES.WEBHOOK_THEME_PUBLISH_QUEUE,
      message,
    });
  };

  handleShopUpdate = async (req, res) => {
    res.status(200);
    res.json({ message: "Success" });

    const message = {
      headers: req.headers,
      body: req.body,
    };

    dispatchQueue({
      queueName: QUEUE_NAMES.WEBHOOK_SHOP_UPDATE_QUEUE,
      message,
    });
    dispatchQueue({
      queueName: QUEUE_NAMES.SYNC_SHOP_LOCALES_LIST,
      message,
    });
  };

  handleAppUninstall = async (req, res) => {
    res.status(200);
    res.json({ message: "Success" });

    const message = {
      headers: req.headers,
      body: req.body,
    };

    dispatchQueue({
      queueName: QUEUE_NAMES.WEBHOOK_APP_UNINSTALL_QUEUE,
      message,
    });
  };

  handleLocationCreate = async (req, res) => {
    res.status(200);
    res.json({ message: "Success" });

    const message = {
      headers: req.headers,
      body: req.body,
    };

    dispatchQueue({
      queueName: QUEUE_NAMES.WEBHOOK_LOCATION_CREATE_QUEUE,
      message,
    });
  };

  handleLocationUpdate = async (req, res) => {
    res.status(200);
    res.json({ message: "Success" });

    const message = {
      headers: req.headers,
      body: req.body,
    };

    dispatchQueue({
      queueName: QUEUE_NAMES.WEBHOOK_LOCATION_UPDATE_QUEUE,
      message,
    });
  };

  handleLocationDelete = async (req, res) => {
    res.status(200);
    res.json({ message: "Success" });

    const message = {
      headers: req.headers,
      body: req.body,
    };

    dispatchQueue({
      queueName: QUEUE_NAMES.WEBHOOK_LOCATION_DELETE_QUEUE,
      message,
    });
  };

  handleBulkOperationFinish = async (req, res) => {
    res.status(200);
    res.json({ message: "Success" });

    const message = {
      headers: req.headers,
      body: req.body,
    };

    dispatchQueue({
      queueName: QUEUE_NAMES.WEBHOOK_BULK_OPERATION_FINISH_QUEUE,
      message,
    });
  };

  handleLocaleCreate = async (req, res) => {
    res.status(200);
    res.json({ message: "Success" });

    const message = {
      headers: req.headers,
      body: req.body,
    };

    dispatchQueue({
      queueName: QUEUE_NAMES.SYNC_SHOP_LOCALES_LIST,
      message,
    });
  };

  handleLocaleUpdate = async (req, res) => {
    res.status(200);
    res.json({ message: "Success" });

    const message = {
      headers: req.headers,
      body: req.body,
    };

    dispatchQueue({
      queueName: QUEUE_NAMES.SYNC_SHOP_LOCALES_LIST,
      message,
    });
  };
}

module.exports = new WebhookController();
