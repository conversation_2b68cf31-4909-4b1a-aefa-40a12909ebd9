const logger = require("storeseo-logger");
const { dispatchQueue } = require("../queue/queueDispatcher");
const {
  getFocusKeywordSuggestions,
  updateOrInsertMetaInfoInArray,
  getMetaInfo,
  getFileNameFromGoogleBucketPublicURL,
  sleep,
} = require("../utils/helper");
const { isNull } = require("lodash/lang");
const analysisService = require("../services/AnalysisService");
const { isEmpty } = require("lodash");
const PageService = require("../services/PageService");
const FileService = require("../services/FileService");
const ShopifyService = require("../services/ShopifyService");
const EventService = require("../services/EventService");
const cache = require("../cache");
const { validateGeneralSEOUpdateData } = require("../utils/dataValidator");
const pageType = require("storeseo-enums/pageType");
const { QUEUE_NAMES } = require("../queue/config");
const PageMetaService = require("../services/PageMetaService");
const toastMessages = require("storeseo-enums/toastMessages");
const responseCodes = require("../config/responseCodes");
const { NAMESPACE, METAFIELD_KEYS, METAFIELD_TYPES, metafieldKeysFilterArray } = require("storeseo-enums/metafields");
const SitemapService = require("../services/SitemapService");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const { PageNotFoundFromShopifyError } = require("../../errors");
const DocService = require("../services/docs/DocService");
const resourceType = require("storeseo-enums/resourceType");
const { serializeShopifyPageData } = require("../serializers/PageSerializer");
const activityLogService = require("../../admin/services/activityLog.service");
const LogOriginTypes = require("storeseo-enums/logOriginTypes");

class PageController {
  getPages = async (req, res) => {
    try {
      const { shopId } = req.user;

      const { pages, pagination } = await PageService.getAllPagesWithPagination(shopId, req.query, [
        "id",
        "page_id",
        "title",
        "focus_keyword",
        "issues",
        "score",
        "created_at",
        "published_at",
      ]);
      const pageCount = await PageService.count(shopId);
      return res.success({
        pages,
        pagination,
        pageCount,
      });
    } catch (error) {
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getShopifyPageCount = async (req, res) => {
    try {
      const session = { shop: req.user.shop, accessToken: req.user.accessToken };
      const count = await ShopifyService.countPages(session);

      return res.success({ count });
    } catch (err) {
      logger.error(err, { domain: res.user.shop, message: "Error getting shopify page count" });
      res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  pageDetails = async (req, res) => {
    try {
      const { shopId, url } = req.user;

      // const [pagePromise, idPromise] = await Promise.allSettled([
      //   PageService.getPage(shopId, req.params.id),
      //   PageService.idsOfThePrevAndNextPage(shopId, req.params.id),
      // ]);

      let page = await PageService.getPageByPageId(shopId, req.params.id);
      const pagination = await PageService.getPaginationOfPage(shopId, req.params.id);

      page = {
        ...page,
        url: `${url}/pages/${page.handle}`,
        meta: page.meta || [],
        ...PageService.getSocialMediaPreviewImages(page),
      };

      const focusKeywordSuggestions = page.focus_keyword_suggestions;

      const optimizationData =
        page.page_type === pageType.HOMEPAGE || page.page_type === pageType.BETTERDOCS_HOMEPAGE
          ? PageService.serializeHomePageOptimizationDetails(page.analysisData)
          : PageService.serializePageOptimizationDetails(page.analysisData);

      return res.success({ page, optimizationData, focusKeywordSuggestions, pagination });
    } catch (err) {
      console.log(err);
      // logger.error(err, {domain: req.user.shop});
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  updatePage = async (req, res) => {
    try {
      const { id } = req.params;
      const { shopId, url, shop: shopDomain } = req.user;
      const { session } = res.locals.shopify;
      let page = await PageService.getPage(shopId, id);

      if (page?.page_type === pageType.BETTERDOCS_HOMEPAGE) {
        try {
          const { page, socialMediaImages, analysedPage } = await DocService.updateDocHomepageMeta(
            shopId,
            id,
            shopDomain,
            req.body,
            url,
            session
          );
          return res.success({
            page: { ...analysedPage, ...socialMediaImages, meta: page.meta },
            message: toastMessages.PAGE_UPDATED,
          });
        } catch (error) {
          console.error(error);
          return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
        }
      }

      if (page?.page_type === pageType.PAGE) {
        const { error } = await validateGeneralSEOUpdateData(req.body);
        if (error) {
          return res.failedValidation({ errors: error.details });
        }
      }

      const {
        metaTitle,
        metaDescription,
        focusKeyword,
        tags,
        handle = undefined,
        createRedirectUrl = false,
      } = req.body;
      const socialMediaImages = PageService.getSocialMediaPreviewImages(page);

      if (page?.page_type === pageType.HOMEPAGE) {
        const updateData = {
          focus_keyword: focusKeyword,
          tags: tags.length > 0 ? tags : null,
        };
        await PageService.updatePage(page.id, updateData);
        page = {
          ...page,
          ...updateData,
        };
      } else {
        if (createRedirectUrl) {
          await ShopifyService.createRedirectURL(session.shop, {
            oldPath: `/pages/${page.handle}`,
            newPath: `/pages/${handle}`,
          });
        }
        const metaInput = [
          {
            key: METAFIELD_KEYS.TITLE_TAG,
            value: metaTitle,
            ownerId: page.shopify_gql_id,
          },
          {
            key: METAFIELD_KEYS.DESCRIPTION_TAG,
            value: metaDescription,
            ownerId: page.shopify_gql_id,
          },
        ];
        await ShopifyService.setMetafields(session.shop, metaInput);

        let shopifyPage = await ShopifyService.onlineStore.getPage(session.shop, {
          id: page.shopify_gql_id,
          metafieldKeys: metafieldKeysFilterArray,
        });
        shopifyPage = await ShopifyService.onlineStore.updatePage(session.shop, {
          id: page.shopify_gql_id,
          page: {
            handle,
            body: shopifyPage.body,
          },
          metafieldKeys: metafieldKeysFilterArray,
        });
        const serializedShopifyPage = serializeShopifyPageData(shopId, shopifyPage);
        page = await PageService.saveOrUpdatePage(shopId, { ...serializedShopifyPage, focus_keyword: focusKeyword });
      }

      const analysedPage = await analysisService.analyseEachPage({ shopId, page, url });

      return res.success({
        page: { ...analysedPage, ...socialMediaImages, meta: page.meta },
        message: toastMessages.PAGE_UPDATED,
      });
    } catch (err) {
      if (err?.response?.code === responseCodes.VALIDATION_ERROR) {
        return res.failedValidation({
          errors: [{ message: `Handle ${err?.response?.body?.errors?.handle[0]}`, path: ["handle"] }],
        });
      }
      console.error(err, { ...req.body, shopDomain: req.user.shop });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  toggleNoFollowStatus = async (req, res) => {
    //todo: simplify this method and move logics to service method
    try {
      const { shopId } = req.user;
      const { id: pageId } = req.params;

      const page = await PageService.getPageByPageId(shopId, pageId);
      let noFollowMeta = page?.meta?.find(
        (m) => m.namespace === NAMESPACE.STORE_SEO && m.key === METAFIELD_KEYS.NO_FOLLOW
      );

      let newStatus = noFollowMeta?.value === "1" ? "0" : "1";
      const metaInput = [
        {
          key: METAFIELD_KEYS.NO_FOLLOW,
          value: newStatus,
          ownerId: page.shopify_gql_id,
        },
      ];

      await ShopifyService.setMetafields(req.user.shop, metaInput);
      const shopifyPage = await ShopifyService.onlineStore.getPage(req.user.shop, {
        id: page.shopify_gql_id,
        metafieldKeys: metafieldKeysFilterArray,
      });
      const serializedShopifyPage = serializeShopifyPageData(shopId, shopifyPage);
      await PageService.saveOrUpdatePage(shopId, serializedShopifyPage);

      await SitemapService.updateSitemapData({
        shopId,
        resourceId: page.id,
        resourceType: analysisEntityTypes.PAGE,
        metaKey: METAFIELD_KEYS.NO_FOLLOW,
        metaStatus: newStatus,
      });

      return res.success({
        noFollowStatus: Number(newStatus),
        message: toastMessages.NO_FOLLOW_STATUS_UPDATED,
      });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: `Failed to toggle no-follow status`,
        pageId: req.params?.id,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  toggleNoIndexStatus = async (req, res) => {
    //todo: simplify this method and move logics to service method
    try {
      const { shopId } = req.user;
      const { id: pageId } = req.params;

      const page = await PageService.getPageByPageId(shopId, pageId);
      let nodIndexMeta = page?.meta?.find(
        (m) => m.namespace === NAMESPACE.STORE_SEO && m.key === METAFIELD_KEYS.NO_INDEX
      );

      let newStatus = nodIndexMeta?.value === "1" ? "0" : "1";
      const metaInput = [
        {
          key: METAFIELD_KEYS.NO_INDEX,
          value: newStatus,
          ownerId: page.shopify_gql_id,
        },
      ];

      await ShopifyService.setMetafields(req.user.shop, metaInput);
      const shopifyPage = await ShopifyService.onlineStore.getPage(req.user.shop, {
        id: page.shopify_gql_id,
        metafieldKeys: metafieldKeysFilterArray,
      });
      const serializedShopifyPage = serializeShopifyPageData(shopId, shopifyPage);
      await PageService.saveOrUpdatePage(shopId, serializedShopifyPage);

      await SitemapService.updateSitemapData({
        shopId,
        resourceId: page.id,
        resourceType: analysisEntityTypes.PAGE,
        metaKey: METAFIELD_KEYS.NO_INDEX,
        metaStatus: newStatus,
      });

      return res.success({
        noIndexStatus: Number(newStatus),
        message: toastMessages.NO_INDEX_STATUS_UPDATED,
      });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: `Failed to toggle no-index status`,
        pageId: req.params?.id,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  getPageOptimization = async (req, res) => {
    try {
      const { shopId, url } = req.user;
      const page = await PageService.serializePageForAnalysis(shopId, req.params.id, req.body);
      const { analysis, score } = await analysisService.getSinglePageAnalysis({
        shopId,
        page,
        uniqueCheckEnable: true,
        url,
      });

      const optimizationData =
        page.page_type === pageType.HOMEPAGE || page.page_type === pageType.BETTERDOCS_HOMEPAGE
          ? PageService.serializeHomePageOptimizationDetails(analysis)
          : PageService.serializePageOptimizationDetails(analysis);

      return res.success({ optimizationData, score });
    } catch (err) {
      return res.failed({ message: err.message });
    }
  };

  syncPagesFromShopify = async (req, res) => {
    res.success({ message: toastMessages.PAGES_SYNC_STARTED });

    try {
      const { shopId, shop, accessToken, url } = req.user;
      const user = {
        shop,
        accessToken,
        url,
        shopId,
      };

      activityLogService.addActivityLog({
        req,
        prevData: null,
        updatedData: null,
        subject: "Pages Sync Started",
        logOrigin: LogOriginTypes.APP,
        domain: shop,
      });

      await PageService.markPagesAsNotSynced(shopId);
      await cache.pageSyncOngoing(shop, true);

      EventService.handlePageSyncUpdate({ shop, message: "Syncing homepage..." });
      const homepage = await PageService.saveOrUpdateHomepage(user, shopId, shop);
      await analysisService.analyseEachPage({ shopId, page: homepage, url });
      if (homepage && !homepage.focus_keyword) {
        dispatchQueue({
          queueName: QUEUE_NAMES.FOCUS_KEYWORD_GENERATOR,
          message: { shopId, resourceType: resourceType.PAGE, dbResourceId: homepage.id },
        });
      }

      const isBetterDocsInstalled = await cache.betterDocInstallationStatus(shop);
      if (isBetterDocsInstalled) {
        const docHomepage = await DocService.upsertDocHomepage(shop, shopId);
        await analysisService.analyseEachPage({ shopId, page: docHomepage, url });
        if (docHomepage && !docHomepage.focus_keyword) {
          dispatchQueue({
            queueName: QUEUE_NAMES.FOCUS_KEYWORD_GENERATOR,
            message: { shopId, resourceType: resourceType.PAGE, dbResourceId: docHomepage.id },
          });
        }
      }

      EventService.handlePageSyncUpdate({ shop, message: "Syncing all pages..." });
      await SitemapService.deleteAllSitemaps(shopId, analysisEntityTypes.PAGE);
      await sleep();

      dispatchQueue({
        queueName: QUEUE_NAMES.PAGE_SYNC,
        message: {
          shopId: user.shopId,
          shopDomain: user.shop,
          shopUrl: user.url,
          session: user,
          cursor: null,
        },
      });
    } catch (err) {
      console.log(err);
      logger.error(err, { domain: req.user.shop });
      // res.json(this.failedResponse("Something went wrong.", err));
    }
  };

  syncPageFromShopify = async (req, res) => {
    const { shopId, shop, accessToken, url } = req.user;
    const { page_id: pageId } = req.params;
    try {
      const user = {
        shop,
        accessToken,
        url,
      };

      let page = await PageService.getPage(shopId, pageId);

      if (page?.page_type === pageType.HOMEPAGE) {
        const updatedPage = await PageService.saveOrUpdateHomepage(user, shopId);
        await analysisService.analyseEachPage({ shopId, page: updatedPage, url });

        return res.success({ message: toastMessages.SINGLE_PAGE_SYNCED });
      }

      if (page?.page_type === pageType.BETTERDOCS_HOMEPAGE) {
        const updatedPage = await DocService.upsertDocHomepage(shop, shopId);
        await analysisService.analyseEachPage({ shopId, page: updatedPage, url });

        return res.success({ message: toastMessages.SINGLE_PAGE_SYNCED });
      }

      const shopifyPage = await ShopifyService.onlineStore.getPage(user.shop, {
        id: page.shopify_gql_id,
        metafieldKeys: metafieldKeysFilterArray,
      });
      // todo: check this condition is required here or top
      if (!shopifyPage) {
        throw Error("Page not found.");
      }
      const serializedShopifyPage = serializeShopifyPageData(shopId, shopifyPage);
      const savedPage = await PageService.saveOrUpdatePage(shopId, serializedShopifyPage);
      await analysisService.analyseEachPage({
        shopId,
        page: savedPage,
        url: user.url,
      });
      await SitemapService.storeSitemapData(savedPage, resourceType.PAGE);

      return res.success({
        message: toastMessages.SINGLE_PAGE_SYNCED,
      });
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      if (err instanceof PageNotFoundFromShopifyError) {
        res.status(404).json({ message: err.message });
        return await PageService.deletePageById(shopId, pageId);
      }
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  updateSocialMediaPreviewImage = async (req, res) => {
    try {
      const { shop, shopId } = req.user;
      const { id: pageId } = req.params;
      const { facebook_preview_image, twitter_preview_image } = req.files ?? {};

      const namespace = NAMESPACE.STORE_SEO;
      const facebookImagePreviewMetaKey = METAFIELD_KEYS.FACEBOOK_PREVIEW_IMAGE_URL;
      const twitterImagePreviewMetaKey = METAFIELD_KEYS.TWITTER_PREVIEW_IMAGE_URL;

      let facebookImagePreviewURL = "";
      let twitterImagePreviewURL = "";

      const page = await PageService.getPage(shopId, pageId);
      let meta = isEmpty(page?.meta) ? [] : page.meta;

      /** @type {{key: string, value: string, ownerId: string}[]} */
      const metaInput = [];

      const prevImages = [];

      if (facebook_preview_image) {
        const prevMetaInfo = getMetaInfo(namespace, facebookImagePreviewMetaKey, meta);
        prevImages.push(prevMetaInfo?.value);

        const { url } = await FileService.saveUploadedFileForShop(shop, facebook_preview_image);
        metaInput.push({
          key: METAFIELD_KEYS.FACEBOOK_PREVIEW_IMAGE_URL,
          value: url,
          ownerId: page.shopify_gql_id,
        });
        facebookImagePreviewURL = url;
      }

      if (twitter_preview_image) {
        const prevMetaInfo = getMetaInfo(namespace, twitterImagePreviewMetaKey, meta);
        prevImages.push(prevMetaInfo?.value);

        const { url } = await FileService.saveUploadedFileForShop(shop, twitter_preview_image);
        metaInput.push({
          key: METAFIELD_KEYS.TWITTER_PREVIEW_IMAGE_URL,
          value: url,
          ownerId: page.shopify_gql_id,
        });
        twitterImagePreviewURL = url;
      }

      if (facebook_preview_image || twitter_preview_image) {
        await ShopifyService.setMetafields(req.user.shop, metaInput);
        const shopifyPage = await ShopifyService.onlineStore.getPage(req.user.shop, {
          id: page.shopify_gql_id,
          metafieldKeys: metafieldKeysFilterArray,
        });
        const serializedShopifyPage = serializeShopifyPageData(shopId, shopifyPage);
        await PageService.saveOrUpdatePage(shopId, serializedShopifyPage);

        prevImages
          .filter((img) => img)
          .map((img) => getFileNameFromGoogleBucketPublicURL(img))
          .forEach((fileName) => FileService.deleteFile(fileName, true));
      }

      return res.success({ facebookImagePreviewURL, twitterImagePreviewURL });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: "Error saving uploaded social media preview image for shop.",
        pageId: req.params.id,
        files: req.files,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };

  updateCanonicalUrl = async (req, res) => {
    try {
      const { shop, shopId } = req.user;
      const { id: pageId } = req.params;
      const { canonicalUrl } = req.body;

      // const namespace = NAMESPACE.STORE_SEO;
      // const canonicalUrlMetaKey = METAFIELD_KEYS.CANNONICAL_URL;
      // const metaFieldType = METAFIELD_TYPES.SINGLE_LINE_TEXT;

      // const page = await PageService.getPage(shopId, pageId);
      // let meta = isEmpty(page?.metafields) ? [] : page.metafields;

      // meta = updateOrInsertMetaInfoInArray({
      //   metaArr: meta,
      //   metaInfo: {
      //     namespace,
      //     key: canonicalUrlMetaKey,
      //     value: canonicalUrl,
      //     type: metaFieldType,
      //   },
      //   namespace,
      //   key: canonicalUrlMetaKey,
      // });

      // await ShopifyService.updateOrCreateShopifyPageMetafields(req.user, page.page_id, meta);
      // const { metafields } = await ShopifyService.getShopifyPageMetafields(req.user, page.page_id);
      // await PageService.updatePage(pageId, { metafields: metafields });

      return res.success({ canonicalUrl });
    } catch (err) {
      logger.error(err, {
        domain: req.user.shop,
        message: "Error updating canonical url for product.",
        productId: req.params.id,
      });
      return res.failed({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  };
}

module.exports = new PageController();
