const settingKeys = require("storeseo-enums/settingKeys");
const ShopService = require("../services/ShopService");
const SitemapGenerator = require("../services/SitemapGenerator");

class AppProxyController {
  generateHtmlSitemap = async (req, res) => {
    try {
      const { shop, path_prefix } = req.query;

      const { id, url: shopUrl } = await ShopService.getShop(shop);
      const setting = await ShopService.getShopSetting(id, settingKeys.HTML_SITEMAP);

      if (!setting) return res.redirect(shopUrl);

      const sitemap = await SitemapGenerator.htmlSitemapForShop(shop, path_prefix);

      return res.liquid(sitemap);
    } catch (err) {
      console.log("Error in generating html sitemap: ", err);
      res.status(500);
    }
  };

  generateProductsHtmlSitemap = async (req, res) => {
    try {
      const { shop, path_prefix } = req.query;

      const sitemap = await SitemapGenerator.htmlSitemapForProducts(shop, path_prefix);

      return res.liquid(sitemap);
    } catch (err) {
      console.log("Error in generating html sitemap: ", err);
      res.status(500);
    }
  };

  generateCollectionsHtmlSitemap = async (req, res) => {
    try {
      const { shop, path_prefix } = req.query;

      const sitemap = await SitemapGenerator.htmlSitemapForCollections(shop, path_prefix);

      return res.liquid(sitemap);
    } catch (err) {
      console.log("Error in generating html sitemap: ", err);
      res.status(500);
    }
  };

  generatePagesHtmlSitemap = async (req, res) => {
    try {
      const { shop, path_prefix } = req.query;

      const sitemap = await SitemapGenerator.htmlSitemapForPages(shop, path_prefix);

      return res.liquid(sitemap);
    } catch (err) {
      console.log("Error in generating html sitemap: ", err);
      res.status(500);
    }
  };

  generateBlogsHtmlSitemap = async (req, res) => {
    try {
      const { shop, path_prefix } = req.query;

      const sitemap = await SitemapGenerator.htmlSitemapForBlogs(shop, path_prefix);

      return res.liquid(sitemap);
    } catch (err) {
      console.log("Error in generating html sitemap: ", err);
      res.status(500);
    }
  };
}

module.exports = new AppProxyController();
