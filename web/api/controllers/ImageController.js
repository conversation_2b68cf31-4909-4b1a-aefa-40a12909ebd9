const settingKeys = require("storeseo-enums/settingKeys");
const ImageService = require("../services/ImageService");
const ShopifyService = require("../services/ShopifyService");
const ShopifyFileSerializer = require("../serializers/ShopifyFileSerializer");
const { METAFIELD_KEYS } = require("storeseo-enums/metafields");
const imageOptimization = require("storeseo-enums/imageOptimization");
const ProductImageService = require("../services/ProductImageService");
const { omit } = require("lodash");
const cache = require("../cache");
const { imageSizeHasDecreased, isValidImageUrl } = require("../utils/imageOptimizer");
const logger = require("storeseo-logger");
const { dispatchQueue } = require("../queue/queueDispatcher");
const { QUEUE_NAMES } = require("../queue/config");
const { sizeToBytes } = require("../utils/helper");
const bulkOperationTrackerService = require("../services/BulkOperationTrackService");
const resourceType = require("storeseo-enums/resourceType");
const resourceOPType = require("storeseo-enums/resourceOPType");
const ResourceType = require("storeseo-enums/resourceType");

/**
 * @typedef {keyof Pick<typeof ResourceType, "PRODUCT" | "COLLECTION" | "PAGE" | "ARTICLE">} ImageResourceType
 */

class ImageController {
  getImages = async (req, res) => {
    try {
      const { shopId } = req.user;
      const query = req.query;
      const { type } = req.params;
      /**
       * @type {ImageResourceType}
       */
      const resourceType = type.toUpperCase();

      const data = await ImageService.getImages(resourceType, { shopId, ...query });

      if (resourceType === "PRODUCT") {
        const imagesWithoutFileSize = data.images?.filter((img) => img.file_size === null) || [];

        if (imagesWithoutFileSize.length > 0) {
          dispatchQueue({
            queueName: QUEUE_NAMES.IMAGE_FILE_SIZE_SYNC,
            message: {
              session: req.user,
              images: imagesWithoutFileSize.map((img) => ({
                id: img.id,
                media_id: img.media_id,
              })),
            },
          });
        }
      }

      return res.success(data);
    } catch (err) {
      console.log("Error getting list of images for shop:", req.user.shop, err);
      return res.failed({ message: "Failed to get list of images" });
    }
  };

  getAutomateSettings = async (req, res) => {
    const { shopId } = req.user;
    const ShopService = require("../services/ShopService");

    let data = await ShopService.getShopSetting(shopId, settingKeys.AUTOMATE_IMAGE_OPTIMIZE);

    if (!data) {
      data = {
        enabled: false,
        compressionType: "none",
        format: "none",
        resize: "none",
      };
    } else {
      data = data.value;
    }

    return res.success({ data });
  };

  updateAutomateSettings = async (req, res) => {
    const { shopId } = req.user;
    const settings = { ...req.body };
    console.log(settings);

    const ShopService = require("../services/ShopService");

    const data = {
      shop_id: shopId,
      key: settingKeys.AUTOMATE_IMAGE_OPTIMIZE,
      value_type: "json",
      value: JSON.stringify(settings),
    };

    await ShopService.updateShopSetting(shopId, data);

    return res.json({ data: settings });
  };

  optimizeImage = async (req, res) => {
    try {
      const { shop, shopId, accessToken } = req.user;
      const { type } = req.params;
      /**
       * @type {ImageResourceType}
       */
      const resourceType = type.toUpperCase();

      const session = { shop, accessToken };

      const { image_details, ...setting } = req.body;
      const inputImage = image_details[0];

      if (!(await isValidImageUrl(inputImage.image_url))) {
        await cache.incrementImageOptimizerInvalidImageCount(shop);
        await ImageService.deleteImage(resourceType, {
          id: inputImage.image_id,
          shopId,
          resourceId: inputImage.resource_id,
        });

        throw new Error("Invalid image url");
      }

      if (resourceType === "PRODUCT" && !inputImage.media_id) {
        const {
          files: {
            edges: {
              0: { node: shopifyImageFile },
            },
          },
        } = await ShopifyService.getImageFilesFromShopify(session.shop, {
          limit: 1,
          filename: inputImage.title,
          productId: inputImage.resource_id.split("/").reverse()[0],
        });

        inputImage.media_id = shopifyImageFile.id;
      }

      const optimizeImagePayload = {
        ...req.body,
        shop_domain: shop,
        image_details: [omit(inputImage, "media_id", "resource_id", "title")],
        resource_type: resourceType !== "PRODUCT" ? resourceType.toLowerCase() : "",
      };
      // console.info("optimize image payload: ", optimizeImagePayload);

      const [optimizedImage] = await ImageService.optimizeImages(optimizeImagePayload);
      // console.info("optimized image: ", optimizedImage);

      if (!imageSizeHasDecreased(optimizedImage.percentage_optimized)) {
        const updatedImage = await ImageService.updateImage(resourceType, inputImage.image_id, {
          shop_id: shopId,
          optimization_status: imageOptimization.ALREADY_OPTIMIZED,
          optimization_meta: {},
          optimized_at: new Date(),
          optimization_setting: setting,
          media_id: inputImage.media_id,
          resource_id: inputImage.resource_id,
          src: inputImage.image_url,
        });

        await cache.updateImageOptimizerUsageCount(shop);

        // console.info("Size not decreased: ", { currentDbImage: updatedImage });

        return res.success({ images: [updatedImage] });
      }

      const fileId =
        resourceType === "PRODUCT"
          ? inputImage.media_id
          : resourceType === "COLLECTION" || resourceType === "ARTICLE"
            ? inputImage.resource_id
            : null;

      const fileUpdatePayload = {
        id: fileId,
        src: optimizedImage.processed_image_url,
      };

      // console.info("shopify image payload: ", {
      //   payload: fileUpdatePayload,
      // });

      const updatedImages = await ImageService.updateShopifyImages(session.shop, resourceType, [fileUpdatePayload]);

      // console.info("updated shopify file: ", JSON.stringify(updatedImages, null, 2));

      const { image_id, media_id } = inputImage;
      const data = {
        shop_id: shopId,
        media_id,
        resource_id: inputImage.resource_id,
        src: updatedImages[0].url.split("?")[0],
        optimization_status: imageOptimization.OPTIMIZED,
        optimization_meta: optimizedImage,
        optimization_setting: setting,
        file_size: sizeToBytes(optimizedImage["processed_size"]),
        optimized_at: new Date(),
      };

      // console.info("image data to update", data);

      const updatedImage = await ImageService.updateImage(resourceType, image_id, data);

      // console.info("final image in db: ", {
      //   dbImage: updatedImage,
      // });

      await cache.updateImageOptimizerUsageCount(shop);

      return res.success({ images: [updatedImage] });
    } catch (error) {
      console.log(error);
      return res.failed({ message: "Something went wrong" });
    }
  };

  optimizeImagesViaQueue = async (req, res) => {
    try {
      const { shop, shopId } = req.user;
      const { images, setting } = req.body;

      if (!setting) {
        throw new Error("Settings not found for queue image processing request!");
      }

      const updatedImages = [];
      for (let img of images) {
        const { id } = img;
        const updatedImage = await ProductImageService.updateImage(id, {
          optimization_status: imageOptimization.PENDING,
          optimization_meta: {},
          optimization_setting: setting,
        });
        updatedImages.push(updatedImage);

        await cache.updateImageOptimizerUsageCount(shop);
      }
      const shopDetails = {
        id: shopId,
        domain: shop,
      };

      await cache.addStoreToPendingImageOptimizationQueue(shop);

      await bulkOperationTrackerService.initializeBulkOperationTracker(
        shopDetails,
        resourceType.PRODUCT_IMAGE,
        resourceOPType.IMAGE_OPTIMIZATION,
        {
          batch_size: images.length,
          resource: images.map((img) => ({ gId: img.media_id })),
        }
      );

      return res.success({ images: updatedImages });
    } catch (error) {
      logger.error(error, { domain: req.user.shop });
      res.failed({ message: "Something went wrong" });
    }
  };

  saveOptimizedImages = async (req, res) => {
    try {
      const { shop, accessToken } = req.user;
      const session = { shop, accessToken };

      /**
       * @type {import("storeseo-enums/metafields").Metafield[]}
       */
      const optimizationMetaData = req.body.map((img) => {
        /**
         * @type {import("storeseo-enums/metafields").Metafield}
         */
        const data = {
          key: METAFIELD_KEYS.OPTIMIZATION_META,
          ownerId: `gid://shopify/MediaImage/${img.image_id}`,
          value: JSON.stringify(img),
        };

        return data;
      });

      await ShopifyService.setMetafields(session.shop, optimizationMetaData);

      const files = req.body.map((img) => ({
        id: `gid://shopify/MediaImage/${img.image_id}`,
        originalSource: img.processed_image_url,
      }));
      const {
        fileUpdate: { files: updatedImages },
      } = await ShopifyService.updateImageFiles(session.shop, files);

      const images = updatedImages.map((imageFile) => ShopifyFileSerializer.serializeImageFile(imageFile));
      res.success({ images });
    } catch (err) {
      console.log("Failed to save optimized images", err);
      res.failed({ message: "Something went wrong" });
    }
  };

  restoreImage = async (req, res) => {
    try {
      const { shop, shopId, accessToken } = req.user;
      const session = { shop, accessToken };
      const restoreData = req.body;
      /**
       * @type {ImageResourceType}
       */
      const resourceType = req.params.type.toUpperCase();

      const imageUpdatePayloads = restoreData.map((img) => {
        const fileId =
          resourceType === "PRODUCT"
            ? img.media_id
            : resourceType === "COLLECTION" || resourceType === "ARTICLE"
              ? img.resource_id
              : null;
        return {
          id: fileId,
          src: img.originalSource,
        };
      });

      const updatedImagesUrl = await ImageService.updateShopifyImages(session.shop, resourceType, imageUpdatePayloads);

      const updatedImages = [];

      for (let i = 0; i < restoreData.length; i++) {
        const data = {
          shop_id: shopId,
          optimization_status: imageOptimization.RESTORED,
          optimization_meta: {},
          src: updatedImagesUrl[i].url.split("?")[0],
          file_size: sizeToBytes(restoreData[i].file_size),
          resource_id: restoreData[i].resource_id,
          media_id: restoreData[i].media_id,
        };

        const image = await ImageService.updateImage(resourceType, restoreData[i].id, data);
        updatedImages.push(image);
      }

      res.success({ images: updatedImages });
    } catch (error) {
      console.log(error);
      res.failed({ message: "Something went wrong!" });
    }
  };
}

module.exports = new ImageController();
