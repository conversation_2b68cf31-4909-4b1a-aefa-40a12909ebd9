const { QUEUE_NAMES } = require("../queue/config");
const { dispatchQueue } = require("../queue/queueDispatcher");

class DocWebhookController {
  /**
   * Doc create webhook handler
   * @param {*} req
   * @param {*} res
   * @returns {Promise<boolean>}
   */
  docCreate = async (req, res) => {
    const { user } = req;

    res.status(200);
    res.json({ message: "Success" });

    try {
      const { headers, body } = req;

      const message = {
        headers,
        body,
        user,
      };

      dispatchQueue({
        queueName: QUEUE_NAMES.WEBHOOK_DOC_CREATE,
        message,
      });

      return true;
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return false;
    }
  };

  /**
   * doc update webhook handler
   * @param {*} req
   * @param {*} res
   * @returns {Promise<boolean>}
   */
  docUpdate = async (req, res) => {
    const { user } = req;
    res.status(200);
    res.json({ message: "Success" });
    try {
      const { headers, body } = req;

      const message = {
        headers,
        body,
        user,
      };
      dispatchQueue({
        queueName: QUEUE_NAMES.WEBHOOK_DOC_UPDATE,
        message,
      });

      return true;
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return false;
    }
  };

  /**
   * doc delete webhook handler
   * @param {*} req
   * @param {*} res
   * @returns {Promise<boolean>}
   */
  docDelete = async (req, res) => {
    res.status(200);
    res.json({ message: "Success" });
    try {
      const { headers, body } = req;

      const message = {
        headers,
        body,
      };
      dispatchQueue({
        queueName: QUEUE_NAMES.WEBHOOK_DOC_DELETE,
        message,
      });

      return true;
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return false;
    }
  };

  appUninstall = async (req, res) => {
    res.status(200);
    res.json({ message: "Success" });
    try {
      const { headers, body } = req;

      const message = {
        headers,
        body,
      };
      dispatchQueue({
        queueName: QUEUE_NAMES.WEBHOOK_BETTERDOCS_UNINSTALLED,
        message,
      });

      return true;
    } catch (err) {
      logger.error(err, { domain: req.user.shop });
      return false;
    }
  };
}

module.exports = new DocWebhookController();
