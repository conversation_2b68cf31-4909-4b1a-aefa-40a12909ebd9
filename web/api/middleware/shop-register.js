const cache = require("../cache");
const { QUEUE_NAMES } = require("../queue/config");
const { dispatchQueue } = require("../queue/queueDispatcher");
const AuthService = require("../services/AuthService");
const WebhookService = require("../services/WebhookService");
const { INSTALLED } = require("storeseo-enums/mailchimp/staticTags");
const logger = require("storeseo-logger");

const shopRegister = async (req, res, next) => {
  const { session } = res.locals.shopify;
  try {
    const { shop: shopData, isNewShop } = await AuthService.register(session);

    const isNewStore = await cache.isNewStore(session.shop);

    const querystring = req.url.split("?")[1];

    console.log("\n\n");
    console.log("req.url => ", req.url);
    console.log("querystring => ", querystring);
    console.log("isNewStore => ", isNewStore);
    console.log("\n\n");

    await cache.webhooks.addShopToPendingWebhookRegistrationList(session.shop);

    if (isNewShop) {
      dispatchQueue({
        queueName: QUEUE_NAMES.SETUP_NEW_SHOP,
        message: { shop: session.shop },
      });

      dispatchQueue({
        queueName: QUEUE_NAMES.MAILCHIMP_ADD_MEMBER,
        message: {
          domain: session.shop,
          tags: [INSTALLED],
        },
      });

      dispatchQueue({
        queueName: QUEUE_NAMES.FLUENT_CRM_ADD_CONTACT,
        message: { domain: session.shop },
      });

      dispatchQueue({
        queueName: QUEUE_NAMES.DEFINE_INDUSTRY,
        message: { domain: session.shop },
      });
    }

    if (isNewStore && !req.originalUrl.startsWith("/onboarding")) {
      res.redirect(`/onboarding?${querystring}`);
    } else {
      next();
    }
  } catch (error) {
    // logger.error(error, { message: "Shop registration error", domain: session.shop });
    console.error("Shop registration error =", error); // in practice these should be handled more gracefully
  }
};

module.exports = shopRegister;
