const AuthService = require("../services/AuthService");
const logger = require("storeseo-logger");

module.exports = async function (req, res, next) {
  const { session } = res.locals.shopify;
  try {
    req.user = await AuthService.getUserFromSession(session);
    next();
  } catch (e) {
    // logger.error(e, { domain: session.shop });
    res.status(403).json({
      status: 403,
      error: "Unauthorised.",
    });
  }
};
