const { SUCCESS, FAILED, VALIDATION_ERROR } = require("../config/responseCodes");
module.exports = async (req, res, next) => {
  res.success = ({ message, ...data }) => {
    res.status(SUCCESS).json({ ...data, message });
    return res;
  };

  res.failed = ({ message = "", errors = undefined }) => {
    res.status(FAILED).json({ errors, message });
    return res;
  };

  res.failedValidation = ({ message = "", errors = undefined }) => {
    errors = errors.reduce((obj, item) => ({ ...obj, [item.path]: item.message }), {});
    res.status(VALIDATION_ERROR).json({ errors, message: message || Object.values(errors)[0] || null });
    return res;
  };

  res.liquid = (data) => res.set("Content-Type", "application/liquid").send(data);

  next();
};
