const { generateHtml } = require("../utils/htmlGenerator");

const redirectToLoginIfNoShopQuery = async (req, res, next) => {
  if (req.path === "/login") {
    const fileContent = await generateHtml(req);
    return res.status(200).set("Content-Type", "text/html").send(fileContent);
  }
  if (!req.query.shop) {
    return res.status(301).redirect("/login");
  } else next();
};
module.exports = {
  redirectToLoginIfNoShopQuery,
};
