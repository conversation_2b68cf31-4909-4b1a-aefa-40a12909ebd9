const cache = require("../cache");

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 */
const checkPlanAndOnboardStatus = async (req, res, next) => {
  // if (req.path === "/exitiframe") return next();

  const { shop } = req.query;
  const querystring = req.url.split("?")[1];

  // const isNewStore = await cache.isNewStore(shop);

  // console.log("isNewStore => ", isNewStore);
  // console.log("req.originalUrl => ", req.originalUrl.startsWith("/onboarding"));

  if (req.originalUrl.startsWith("/onboarding")) {
    // res.redirect(`/onboarding?${querystring}`);
    setTimeout(() => cache.isNewStore(shop, false), 3000);
    // return;
  }

  next();
};

module.exports = checkPlanAndOnboardStatus;
