const morgan = require("morgan");

module.exports = morgan(
  (tokens, req, res) => {
    const status = tokens.status(req, res);

    return [
      tokens.method(req, res),
      "-",
      status,
      "-",
      tokens.url(req, res),
      "-",
      tokens["response-time"](req, res) + "ms",
    ].join(" ");
  },
  {
    skip: (req, res) => {
      return req.url.includes("_next") || req.url.includes("img") || req.url.includes("locales");
    },
  }
);
