const crypto = require("crypto");
const logger = require("storeseo-logger");
const ShopService = require("../services/ShopService");
const { isEmpty } = require("lodash");

const configs = require("../config");

const verifyShopifyOAuthCallback = (req, res, next) => {
  try {
    const { hmac, shop } = req.query;
    const queryString = req.originalUrl.split("?")[1].replace(/&hmac=[^&]*/, "");

    const generatedHmac = crypto.createHmac("sha256", configs.shopify.api_secret).update(queryString).digest("hex");
    if (hmac === generatedHmac) {
      next();
    } else {
      res.status(403);
      res.json({ message: "Invalid hmac signature " });
    }
  } catch (err) {
    logger.error(err, {
      domain: shop,
      message: "Error verifying shopify oauth callback",
      query: req.query,
      headers: req.headers,
    });
    res.json({ message: "Invalid oauth callback." });
  }
};

const verifyWebhook = async (req, res, next) => {
  const shopDomain = req.headers["x-shopify-shop-domain"];
  const topic = req.headers["x-shopify-topic"];
  const hmac = req.headers["x-shopify-hmac-sha256"];

  try {
    const generatedHmac = crypto.createHmac("sha256", configs.shopify.api_secret).update(req.textBody).digest("base64");

    if (hmac === generatedHmac) {
      const { access_token, id, plan_id, plan_rules } = await ShopService.getShop(shopDomain);

      const excludedTopics = [
        "app_subscriptions/update",
        "app_purchases_one_time/update",
        "app/uninstalled",
        "shop/redact",
        "customers/redact",
        "customers/data_request",
        "bulk_operations/finish",
        "shop/update",
        "locales/create",
        "locales/update",
      ];

      if (!excludedTopics.includes(topic) && (!plan_id || isEmpty(plan_rules))) {
        // logger.error(`🐵 Missing plan id or rules.`, { domain: shopDomain, topic });
        return res.status(200).json({ message: "Success!" });
      }

      req.user = {
        shopId: id,
        shop: shopDomain,
        accessToken: access_token,
      };

      next();
    } else {
      res.status(403);
      res.json({ message: "Invalid hmac signature " });
      logger.error(`Invalid hmac signature.`, {
        domain: shopDomain,
        topic,
        hmac,
        generatedHmac,
      });
    }
  } catch (err) {
    logger.error(err, { domain: shopDomain, topic, transport: "Error verifying shopify webhook" });
    res.json({ message: "Invalid webhook." });
  }
};

module.exports = {
  verifyShopifyOAuthCallback,
  verifyWebhook,
};
