const seoApps = require("storeseo-enums/seoApps");
const experimentalFeatures = require("../config/experimentalFeatures");

const restrictInstalledAppsList = (appList, shopDomain) => {
  let sanitizedAppList = appList;

  const dataMigrateFeatureConfig = experimentalFeatures.dataMigrateFeature;
  const { enabled: experimentalFlagOn, apps } = dataMigrateFeatureConfig;

  // console.log("experimentalFlagOn: ", experimentalFlagOn);
  // console.log("apps: ", apps);

  if (experimentalFlagOn) {
    sanitizedAppList = sanitizedAppList.filter((app) => !(app in apps) || apps[app]?.includes(shopDomain));
  }

  return sanitizedAppList;
};

module.exports = {
  restrictInstalledAppsList,
};
