const ARTICLE_SCORES = require("../config/articleSeoScores");
const PAGE_SCORES = require("../config/pageSeoScores");
const PRODUCT_SCORES = require("../config/seoScores");
const COLLECTION_SCORES = require("../config/collectionSeoScores");
const types = require("storeseo-enums/analysisEntityTypes");
const { isEmpty, words } = require("lodash");
const {
  extractUrlsFromHTMLString,
  extractImagesFromHTMLString,
  extractInternalLinksFromHtmlString,
  stripTags,
  normalizeString,
} = require("./helper");
const { decode } = require("html-entities");

const SCORES = {
  [types.PAGE]: PAGE_SCORES,
  [types.ARTICLE]: ARTICLE_SCORES,
  [types.PRODUCT]: PRODUCT_SCORES,
  [types.COLLECTION]: COLLECTION_SCORES,
};

module.exports = {
  calculateFocusKeywordInIntroduction(content, keyword, entityType = types.PAGE) {
    if (!isEmpty(content) && !isEmpty(keyword)) {
      const contentInWords = content.split(" ");
      const wordCount = contentInWords.length;
      const tenPercentOfContent = Math.round(wordCount * 0.1);
      const introduction = contentInWords.slice(0, tenPercentOfContent - 1).join(" ");

      if (introduction.toLowerCase().includes(keyword?.toLowerCase())) {
        return SCORES[entityType].FOCUS_KEYWORD_IN_INTRODUCTION;
      }
    }
    return 0;
  },

  calculateContentMinWord({ content, minWord = 300, entityType = types.PAGE }) {
    const scoreKey = `CONTENT_MORE_THEN_${minWord}_WORDS`;

    if (!isEmpty(content)) {
      if (words(content).length >= minWord) {
        return SCORES[entityType][scoreKey];
      }
    }
    return 0;
  },

  calculateFocusKeywordDensity(content, keyword, entityType = types.PAGE) {
    if (!isEmpty(content) && !isEmpty(keyword)) {
      const contentInWords = words(content);
      const wordCount = contentInWords.length;
      const onePercentOfContent = Math.round(wordCount * 0.01);
      const twoPercentOfContent = Math.round(wordCount * 0.02);
      const occurrence = (content.match(new RegExp(keyword, "gi")) || []).length;

      if (occurrence >= onePercentOfContent && occurrence <= twoPercentOfContent) {
        return SCORES[entityType].FOCUS_KEYWORD_DENSITY;
      }
    }
    return 0;
  },

  calculateFocusKeywordInAltText(altText, keyword, entityType = types.PAGE) {
    if (!isEmpty(altText) && !isEmpty(keyword)) {
      if (altText.toLowerCase().includes(keyword.toLowerCase())) {
        return SCORES[entityType].FOCUS_KEYWORD_IN_IMG_ALT_TEXT;
      }
    }
    return 0;
  },

  calculateFocusKeywordInSubheading(content, keyword, entityType = types.PAGE) {
    if (!isEmpty(content) && !isEmpty(keyword)) {
      const headings = content.match(/(<h[1-6]([\w\W]+?)<\/h[1-6]>)+?/gim);
      if (headings && headings.length > 0) {
        for (let i = 0; i < headings.length; i++) {
          if (decode(stripTags(headings[i])).toLowerCase().includes(keyword)) {
            return SCORES[entityType].FOCUS_KEYWORD_IN_SUBHEADING;
          }
        }
      }
    }
    return 0;
  },

  calculateFocusKeywordInMetaDesc(metaDescription, keyword, entityType = types.PAGE) {
    if (metaDescription?.toLowerCase()?.match(new RegExp(keyword, "gim"))) {
      return SCORES[entityType].FOCUS_KEYWORD_IN_META_DESC;
    }
    return 0;
  },

  calculateFocusKeywordInUrl(handle, keyword, entityType = types.PAGE) {
    if (isEmpty(handle) || isEmpty(keyword)) return 0;

    let escapedKeyword = normalizeString(keyword).replace(/[.*+?^${}()|[\]\\]/g, "\\$&");

    // Create a regular expression pattern for the keyword
    const regexPattern = escapedKeyword
      .split(/\s+/) // Split keyword into words
      // .map((word) => `\\b${word}\\b`) // Add word boundaries to each word
      .join(".*"); // Allow any characters in between words

    // Create a case-insensitive regular expression
    const regex = new RegExp(regexPattern, "i");

    // Test if the keyword pattern exists in the URL
    return regex.test(handle.normalize("NFD")) ? SCORES[entityType].FOCUS_KEYWORD_IN_URL : 0;
  },

  calculateMetaDescLength(metaDescription, maxLengthLimit = 165, entityType = types.PAGE) {
    if (metaDescription) {
      if (metaDescription.length > 0 && metaDescription.length <= maxLengthLimit) {
        return Math.round((SCORES[entityType].META_DESC_WITHIN_160_CHAR * metaDescription.length) / maxLengthLimit);
      } else {
        return Math.round(SCORES[entityType].META_DESC_WITHIN_160_CHAR * 0.6);
      }
    }
    return 0;
  },

  calculateFocusKeywordInMetaTitle(metaTitle, keyword, entityType = types.PAGE) {
    if (metaTitle?.toLowerCase()?.includes(keyword)) {
      return SCORES[entityType].FOCUS_KEYWORD_IN_META_TITLE;
    }
    return 0;
  },

  calculateInternalLink(content, url, entityType = types.PAGE) {
    if (!isEmpty(content) && !isEmpty(url)) {
      const internalLinks = extractInternalLinksFromHtmlString(content);
      if (internalLinks.length) return SCORES[entityType].INTERNAL_LINK_IN_CONTENT;

      const urls = extractUrlsFromHTMLString(content);
      if (urls && urls.length > 0) {
        for (let i = 0; i < urls.length; i++) {
          if (urls[i].includes(url)) {
            return SCORES[entityType].INTERNAL_LINK_IN_CONTENT;
          }
        }
      }
    }
    return 0;
  },

  calculateExternalLink(content, url, entityType = types.PAGE) {
    if (!isEmpty(content) && !isEmpty(url)) {
      const urls = extractUrlsFromHTMLString(content);
      if (urls && urls.length > 0) {
        for (let i = 0; i < urls.length; i++) {
          if (!urls[i].includes(url)) {
            return SCORES[entityType].EXTERNAL_LINK_IN_CONTENT;
          }
        }
      }
    }
    return 0;
  },

  calculateImgAltText(content, entityType = types.PAGE) {
    if (!isEmpty(content)) {
      const images = extractImagesFromHTMLString(content);
      if (images && images.length > 0) {
        for (let i = 0; i < images.length; i++) {
          const altText = images[i].match(/alt="(.*?)"/i);
          if (altText && altText[1].length === 0) {
            return 0;
          }
        }
      }
      return SCORES[entityType].FOCUS_KEYWORD_IN_IMG_ALT_TEXT;
    }
    return 0;
  },
};
