const fs = require("fs").promises;
const path = require("path");
const Handlebars = require("handlebars");
const eventTopics = require("storeseo-enums/eventTopics");

const STORESEO_LOGO = "https://cdn.storeseo.com/logo/v2/StoreSEO_Email.png";

const htmlTemplates = {
  [eventTopics.WEEKLY_STORE_REPORT]: null,
  [eventTopics.GOOGLE_ANALYTICS_INTEGRATION]: null,
  [eventTopics.GOOGLE_SEARCH_CONSOLE_INTEGRATION]: null,
  [eventTopics.HTML_SITEMAP_INTEGRATION]: null,
  [eventTopics.AUTO_IMAGE_OPTIMIZER_REMINDER]: null,
  [eventTopics.PRODUCT_SYNC_COMPLETE]: null,
  [eventTopics.IMAGE_OPTIMIZER_USAGE_NOTIFICATION]: null,
  [eventTopics.AI_CONTENT_OPTIMIZER_USAGE_NOTIFICATION]: null,
};

/**
 *
 * @param {string} topic
 * @returns {Promise<string>}
 */
const loadTemplates = async (topic) => {
  const templateDir = path.join(__dirname, "../templates");

  htmlTemplates[topic] = await fs.readFile(path.join(templateDir, `${topic}.html`), "utf-8");
  return htmlTemplates[topic];
};

/**
 * Weekly email report template
 * @param {string} appName
 * @param {string} topic
 * @param {object} data
 * @returns {Promise<string}
 */
const emailReportTemplate = async (appName, topic, data) => {
  const emailProvider = process.env.MAIL_PROVIDER || "";
  // Custom Handlebars helper to compare scores
  Handlebars.registerHelper("ifScore", function (score, value, options) {
    if (score >= value) {
      return options.fn(this);
    }
    return options.inverse(this);
  });

  Handlebars.registerHelper("ifMailGunProvider", function (provider, options) {
    if (provider === "mailgun") {
      return options.fn(this);
    } else {
      return options.inverse(this);
    }
  });

  let template;

  if (htmlTemplates[topic]) {
    template = htmlTemplates[topic];
  } else {
    template = await loadTemplates(topic);
  }

  return Handlebars.compile(template)({ ...data, appName, emailProvider, STORESEO_LOGO });
};

module.exports = {
  emailReportTemplate,
};
