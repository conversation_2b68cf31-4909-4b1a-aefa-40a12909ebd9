const { isEmpty, words } = require("lodash");
const { Doc, sequelize, Op } = require("../../sequelize");
const SCORES = require("../config/docSeoScores");
const { calculateIsOptimized, constructRegexWithSpecialCharacters } = require("./helper");
const { METAFIELD_KEYS } = require("storeseo-enums/metafields");

class DocScoreCalculation {
  /**
   * Calculate collection doc scores
   * @param {object} doc
   * @returns {object}
   */
  calculateAnalysisScores = (doc) => {
    return Object.entries(doc).reduce(
      (output, val2) => {
        const key2 = val2[0];
        const isOptimized = calculateIsOptimized(val2[1], SCORES[key2.toUpperCase()]);

        return {
          issues: output.issues + (!isOptimized ? 1 : 0),
          passed: output.passed + (isOptimized ? 1 : 0),
          point: output.point + val2[1],
        };
      },
      { passed: 0, issues: 0, point: 0 }
    );
  };

  /**
   * Calculate unique focus keyword score
   * @param {number} shopId - shop id
   * @param {string} keyword
   * @param {number | null} docId - DB collection id
   * @returns {Promise<number>}
   */
  calculateUniqueFocusKeyword = async (shopId, keyword, docId = null) => {
    if (!isEmpty(keyword)) {
      const count = await Doc.count({
        where: {
          id: { [Op.not]: docId },
          shop_id: shopId,
          [Op.and]: sequelize.where(
            sequelize.fn("LOWER", sequelize.col("focus_keyword")),
            keyword.trim().toLowerCase()
          ),
        },
      });
      return count === 0 ? SCORES.UNIQUE_FOCUS_KEYWORD : 0;
    }
    return 0;
  };

  /**
   * Calculate focus keyword in title score
   * @param {string} title
   * @param {string} keyword
   * @returns {number}
   */
  // calculateFocusKeywordInTitle = (title, keyword) => {
  //   if (!isEmpty(keyword)) {
  //     return title.toLowerCase().includes(keyword.toLowerCase()) ? SCORES.FOCUS_KEYWORD_IN_TITLE : 0;
  //   }
  //   return 0;
  // };

  /**
   * Calculate focus keyword in meta description score
   * @param {object} doc
   * @param {string} keyword
   * @returns {number}
   */
  calculateFocusKeywordInMetaDesc = (doc, keyword) => {
    const metaDescription = this.getDescriptionTagFromDoc(doc);
    const keywordRegex = constructRegexWithSpecialCharacters(keyword);

    if (metaDescription && !isEmpty(keyword) && metaDescription.toLowerCase().match(keywordRegex))
      return SCORES.FOCUS_KEYWORD_IN_META_DESC;

    return 0;
  };

  /**
   * Calculate unique title score
   * @param {object} doc - doc object from DB
   * @returns {number}
   */
  calculateFocusKeywordInMetaTitle = (doc) => {
    const keyword = doc?.focus_keyword;
    if (!isEmpty(keyword)) {
      const titleTag = this.getTitleTagFromDoc(doc);

      if (titleTag.toLowerCase().includes(keyword.toLowerCase())) {
        return SCORES.FOCUS_KEYWORD_IN_META_TITLE;
      }
    }
    return 0;
  };

  /**
   * Calculate focus keyword in URL score
   * @param {string} handle
   * @param {string} keyword
   * @returns {number}
   */
  calculateFocusKeywordInURL = (handle, keyword) => {
    if (isEmpty(keyword) || isEmpty(handle)) {
      return 0;
    }

    const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");

    // Create a regular expression pattern for the keyword
    const regexPattern = escapedKeyword
      .split(/\s+/) // Split keyword into words
      .map((word) => `\\b${word}\\b`) // Add word boundaries to each word
      .join(".*"); // Allow any characters in between words

    // Create a case-insensitive regular expression
    const regex = new RegExp(regexPattern, "i");

    // Test if the keyword pattern exists in the URL
    return regex.test(handle) ? SCORES.FOCUS_KEYWORD_IN_URL : 0;
  };

  /**
   * @param {string} description
   * @param {number} minWord
   * @returns {number}
   */
  // calculateContentLength = (content, minWord = 200) => {
  //   if (!isEmpty(content)) {
  //     if (words(content).length >= minWord) {
  //       return SCORES.CONTENT_MORE_THEN_200_WORDS;
  //     }
  //   }
  //   return 0;
  // };

  calculateContentLength = (content, maxWord) => {
    if (!isEmpty(content)) {
      const strippedContent = this.stripTags(content);
      const wordCount = words(strippedContent).length;

      if (wordCount >= maxWord) {
        return SCORES.CONTENT_MORE_THEN_200_WORDS;
      }

      // If content is between 0 and maxWord, calculate the percentage
      const result = Math.round((wordCount / maxWord) * SCORES.CONTENT_MORE_THEN_200_WORDS);
      return result;
    }

    // Return 0 if content is empty
    return 0;
  };

  /**
   * @param {string} content
   * @param {string} keyword
   * @returns {number}
   */
  calculateFocusKeywordInIntroduction = (content, keyword) => {
    if (!isEmpty(content) && !isEmpty(keyword)) {
      const contentInWords = this.findWords(content);
      const wordCount = contentInWords.length;
      const tenPercentOfContent = Math.round(wordCount * 0.1);
      const introduction = contentInWords.slice(0, tenPercentOfContent - 1).join(" ");

      if (introduction.includes(keyword)) {
        return SCORES.FOCUS_KEYWORD_IN_INTRODUCTION;
      }
    }
    return 0;
  };

  /**
   * @param {object} image
   * @param {string} content
   * @param {string} keyword
   * @returns {number}
   */
  calculateFocusKeywordInImgAltText = (image, content, keyword) => {
    const images = this.extractImages(content);

    if ((isEmpty(images) && isEmpty(image)) || isEmpty(keyword)) {
      return SCORES.FOCUS_KEYWORD_IN_IMG_ALT_TEXT;
    }

    if (isEmpty(images) || isEmpty(image)) {
      // console.log("either one block");
      if (image?.altText.toLowerCase().includes(keyword.toLowerCase())) {
        return SCORES.FOCUS_KEYWORD_IN_IMG_ALT_TEXT;
      }
      if (images && images.length > 0) {
        for (let i = 0; i < images.length; i++) {
          const altText = images[i].match(/alt="(.*?)"/i);
          // console.log("alt text", altText);
          if (!altText || !altText[1].toLowerCase().includes(keyword.toLowerCase())) {
            console.log("any one missing");
            return 0;
          }
        }
        return SCORES.FOCUS_KEYWORD_IN_IMG_ALT_TEXT;
      }
    }

    if (!isEmpty(images) && !isEmpty(image)) {
      if (image?.altText.toLowerCase().includes(keyword.toLowerCase()) && images.length > 0) {
        for (let i = 0; i < images.length; i++) {
          const altText = images[i].match(/alt="(.*?)"/i);
          if (!altText || !altText[1].toLowerCase().includes(keyword.toLowerCase())) {
            return 0;
          }
        }
        return SCORES.FOCUS_KEYWORD_IN_IMG_ALT_TEXT;
      }
    }
    return 0;
  };

  /**
   * @param {string} string
   * @param {string} keyword
   * @returns {number}
   */
  calculateFocusKeywordDensity(content, keyword) {
    if (!isEmpty(content) && !isEmpty(keyword)) {
      const contentInWords = words(content);
      const wordCount = contentInWords.length;
      const onePercentOfContent = Math.round(wordCount * 0.01);
      const twoPercentOfContent = Math.round(wordCount * 0.02);
      const occurrence = (content.match(new RegExp(keyword, "gi")) || []).length;

      if (occurrence >= onePercentOfContent && occurrence <= twoPercentOfContent) {
        return SCORES.FOCUS_KEYWORD_DENSITY;
      }
    }
    return 0;
  }

  plainUrls = (urls) => {
    return urls.map((url) => {
      const urlMatch = url.match(/href="(.*?)"/)[1];
      const cleanedUrl = urlMatch.replace(/https?:\/\//, "");
      return cleanedUrl.split(".com")[0];
    });
  };

  /**
   * @param {string} content
   * @param {string} url
   * @returns {number}
   */
  calculateInternalLink = (content) => {
    if (!isEmpty(content)) {
      const urls = this.extractUrls(content);
      const trimmedUrls = this.plainUrls(urls);
      const internalLink = trimmedUrls.some((url) => url.endsWith("myshopify"));
      if (internalLink) {
        return SCORES.INTERNAL_LINK_IN_CONTENT;
      }
      return 0;
    }
    return 0;
  };

  /**
   * @param {string} content
   * @param {string} url
   * @returns {number}
   */
  calculateExternalLink = (content) => {
    if (!isEmpty(content)) {
      const urls = this.extractUrls(content);
      const trimmedUrls = this.plainUrls(urls);
      const externalLink = trimmedUrls.some((url) => !url.endsWith("myshopify"));
      if (externalLink) {
        return SCORES.EXTERNAL_LINK_IN_CONTENT;
      }
      return 0;
    }
    return 0;
  };

  /**
   * @param {string} content
   * @param {string} keyword
   * @returns {number}
   */
  calculateFocusKeywordInSubheading = (content, keyword) => {
    if (!isEmpty(content) && !isEmpty(keyword)) {
      const headings = content.match(/<h[1-6].*?>(.*?)<\/h[1-6]>/gim);

      if (headings && headings.length > 0) {
        const headingTexts = headings.map((heading) => this.stripTags(heading));

        for (let i = 0; i < headingTexts.length; i++) {
          if (headingTexts[i].toLowerCase().includes(keyword.toLowerCase())) {
            return SCORES.FOCUS_KEYWORD_IN_SUBHEADING;
          }
        }
      }
    }
    return 0;
  };

  calculateImgAltText = (image, content) => {
    const images = this.extractImages(content);

    if (isEmpty(images) && isEmpty(image)) {
      return SCORES.ALT_TEXT_IN_ALL_IMG;
    }

    if (isEmpty(images) || isEmpty(image)) {
      if (image?.altText) {
        return SCORES.ALT_TEXT_IN_ALL_IMG;
      }
      if (images && images.length > 0) {
        for (let i = 0; i < images.length; i++) {
          const altText = images[i].match(/alt="(.*?)"/i);
          if (!altText || (altText && altText[1].length === 0)) {
            return 0;
          }
        }
        return SCORES.ALT_TEXT_IN_ALL_IMG;
      }
    }

    if (!isEmpty(images) && !isEmpty(image)) {
      if (image?.altText && images.length > 0) {
        for (let i = 0; i < images.length; i++) {
          const altText = images[i].match(/alt="(.*?)"/i);
          if (!altText || (altText && altText[1].length === 0)) {
            return 0;
          }
        }
        return SCORES.ALT_TEXT_IN_ALL_IMG;
      }
    }
    return 0;
  };

  /**
   * @param {string} string
   * @returns {string}
   */
  stripTags = (string) => {
    return string
      ?.replace(/(<([^>]+)>)/gi, "")
      .replace(/ +(?= )/g, "")
      .trim();
  };

  findWords(content) {
    return content.split(" ");
  }

  extractUrls = (content) => {
    return (
      content.match(
        /href="[(http(s)?):\/\/(www\.)?a-zA-Z0-9@:%._\-\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)"/gi
      ) || []
    );
  };

  extractImages = (content) => {
    if (content) {
      return content.match(/<img([\w\W]+?)>/gi) || [];
    }
    return [];
  };

  /**
   * Get description tag from doc
   * @param {object} doc
   * @returns {string}
   */
  getDescriptionTagFromDoc = (doc) => {
    if (!isEmpty(doc?.metafields)) {
      const metaDescription = doc?.metafields.find((metaItem) => metaItem.key === METAFIELD_KEYS.DESCRIPTION_TAG);

      if (metaDescription) {
        return metaDescription.value;
      }
    }
    return this.stripTags(doc?.description)?.trim()?.substring(0, 165);
  };

  /**
   * Calculate meta title length score based on character limit
   * @param {object} doc
   * @param {number} minLength
   * @param {number} maxLength
   * @returns {number}
   */
  calculateMetaDescLength = (doc, minLength, maxLength) => {
    const partialScore = Math.round(SCORES.META_DESC_WITHIN_160_CHAR * 0.6);
    const metaDescription = this.getDescriptionTagFromDoc(doc);

    if (metaDescription) {
      const { length } = metaDescription;

      if (length < minLength) {
        return Math.round((SCORES.META_DESC_WITHIN_160_CHAR * length) / maxLength);
      } else if (length >= minLength && length <= maxLength) {
        return Math.round(
          SCORES.META_DESC_WITHIN_160_CHAR * (0.7 + (0.3 * (length - minLength)) / (maxLength - minLength))
        );
      } else {
        return partialScore;
      }
    }

    return 0;
  };

  /**
   * Calculate doc meta title length score based on character limit
   * @param {object} doc
   * @param {number} maxLength
   * @returns {number}
   */
  calculateMetaTitleLength = (doc, maxLength) => {
    const partialScore = Math.round(SCORES.META_TITLE_WITHIN_CHAR_LIMIT * 0.6);

    const titleTag = this.getTitleTagFromDoc(doc);

    if (titleTag.length > 0 && titleTag.length <= maxLength) {
      const score = Math.round((SCORES.META_TITLE_WITHIN_CHAR_LIMIT * titleTag.length) / maxLength);
      if (score < 7 && titleTag.length >= 30) return 7;
      else return score;
    }
    if (titleTag.length > maxLength) {
      return partialScore;
    }
    return 0;
  };

  /**
   * Get title tag from doc
   * @param {object} doc
   * @returns {string}
   */
  getTitleTagFromDoc = (doc) => {
    if (!isEmpty(doc.metafields)) {
      const metaTitle = doc.metafields.find((metaItem) => metaItem.key === METAFIELD_KEYS.TITLE_TAG);
      if (metaTitle) {
        return metaTitle.value;
      }
    }
    return doc.title;
  };

  /**
   * Total score of doc analysis
   * @returns {number}
   */
  totalScore = () => {
    return Object.values(SCORES).reduce((a, b) => a + b, 0);
  };

  /**
   * @param {object} doc - doc object from DB
   * @returns {number}
   */
  // calculateFocusKeywordInMetaTitle = (doc) => {
  //   const keyword = doc?.focus_keyword;
  //   if (!isEmpty(keyword)) {
  //     const titleTag = this.getTitleTagFromDoc(doc);

  //     if (titleTag.toLowerCase().includes(keyword.toLowerCase())) {
  //       return SCORES.FOCUS_KEYWORD_IN_META_TITLE;
  //     }
  //   }
  //   return 0;
  // };
}

module.exports = new DocScoreCalculation();
