const { Article: ArticleModel, Op, Sequelize } = require("../../sequelize");
const SCORES = require("../config/articleSeoScores");
const { isEmpty } = require("lodash");
const { calculateIsOptimized } = require("./helper");

/**
 * @type {typeof import('sequelize').Model}
 */
const Article = ArticleModel;

module.exports = {
  async calculateUniqueFocusKeyword(shopId, keyword, id) {
    if (!isEmpty(keyword)) {
      const count = await Article.count({
        where: {
          shop_id: shopId,
          id: {
            [Op.ne]: id,
          },
          [Op.and]: [Sequelize.literal("LOWER(focus_keyword) = $keyword")],
        },
        bind: {
          keyword,
        },
      });

      return count === 0 ? SCORES.UNIQUE_FOCUS_KEYWORD : 0;
    }
    return 0;
  },

  calculateAnalysisScores(analysis) {
    return Object.entries(analysis).reduce(
      (data, entry) => {
        const key = entry[0];
        const val = entry[1];
        const isOptimized = calculateIsOptimized(val, SCORES[key.toUpperCase()]);

        return {
          issues: data.issues + (!isOptimized ? 1 : 0),
          passed: data.passed + (isOptimized ? 1 : 0),
          point: data.point + val,
        };
      },
      { passed: 0, issues: 0, point: 0 }
    );
  },

  totalScore() {
    return Object.values(SCORES).reduce((score, val) => score + val, 0);
  },
};
