const rakeKeywordExtractor = require("node-rake");
const jwt = require("jsonwebtoken");
const sanitizeHtml = require("sanitize-html");
const { URL } = require("url");
const { words, isArray, uniq, isEmpty } = require("lodash");
const axios = require("axios");
const planType = require("storeseo-enums/planType");
const planInterval = require("storeseo-enums/planInterval");
const shopStatus = require("storeseo-enums/shopStatus");
const logger = require("storeseo-logger");
const moment = require("moment");

const SECRET = "woeifjelfjel";
const ONE_MILLION = 1000000;
const ONE_THOUSAND = 1000;

require("../config");

const { performance } = require("perf_hooks");
const shopify = require("../../shopify");
const { GraphqlQueryError, Session } = require("@shopify/shopify-api");
const redisClient = require("../cache/client");
const { HOST } = require("storeseo-enums/cacheKeys");
const { NAMESPACE, METAFIELD_KEYS } = require("storeseo-enums/metafields");
const { IMAGE_OPTIMIZER, AI_OPTIMIZER } = require("storeseo-enums/subscriptionAddonGroup");
const { features } = require("../config/subscription-plan");
const specialCharsMap = require("../config/specialCharsMap");
const { dailyStatusReportDegfaultValue } = require("../../admin/config/reportDafaultValues");
const { url, optimizePercent } = require("../config/app");

const execTimeTracker = () => {
  let start = performance.now();

  return {
    get() {
      let end = performance.now();
      let execTimeInMS = Math.round(end - start);
      let execTimeInSec = execTimeInMS / 1000;
      return execTimeInSec >= 1 ? `${execTimeInSec} sec` : `${execTimeInMS} ms`;
    },
  };
};

const downloadFile = async (url, targetFile) => {
  const Fs = require("fs");
  const Https = require("https");
  const path = targetFile.split("/").slice(0, -1).join("/");

  if (Fs.existsSync(path)) {
    Fs.rmdirSync(path, { recursive: true, force: true });
  }
  Fs.mkdirSync(path, { recursive: true }, (resp) => {});
  return await new Promise((resolve, reject) => {
    Https.get(url, (response) => {
      const code = response.statusCode ?? 0;

      if (code >= 400) {
        return reject(new Error(response.statusMessage));
      }

      // handle redirects
      if (code > 300 && code < 400 && !!response.headers.location) {
        return downloadFile(response.headers.location, targetFile);
      }

      // save the file to disk
      const fileWriter = Fs.createWriteStream(targetFile).on("finish", () => {
        resolve({});
      });

      response.pipe(fileWriter);
      console.log("Download complete");
    }).on("error", (error) => {
      reject(error);
    });
  });
};

const handleShopifyAdminLinkRequest = async (req, res, next) => {
  let targetId = req.query.id || null;

  const baseUrl = req.originalUrl.split("?")[0];
  const querystring = req.url.split("?")[1];

  if (!targetId) {
    return next();
  }

  if (baseUrl && !targetId) {
    return res.redirect(`${baseUrl}?${querystring.replace(/&?id=[^&]*/g, "")}`);
  }

  return res.redirect(`${baseUrl}/${targetId}?${querystring.replace(/&?id=[^&]*/g, "")}`);
};

const stripTags = (string) => {
  if (string) {
    return String(string)
      .replace(/(<([^>]+)>)/gi, "")
      .replace(/ +(?= )/g, "")
      .trim();
  }
  return "";
};

/**
 *
 * @param {import("storeseo-enums/analytics/jsDocTypes").dateRange} dateRange
 * @param {{ format?: string }} param1
 * @returns {import("storeseo-enums/analytics/jsDocTypes").dateRange} previous date range equivalent to input range
 */
const prevDateRange = (dateRange, { format = "YYYY-MM-DD" } = {}) => {
  const { startDate, endDate } = dateRange;

  const rangeDifference = moment(endDate, format).diff(moment(startDate, format), "d");

  const prevEndDate = moment(startDate, format).subtract(1, "d").format(format);
  const prevStartDate = moment(prevEndDate).subtract(rangeDifference, "d").format(format);

  return {
    startDate: prevStartDate,
    endDate: prevEndDate,
  };
};

module.exports = {
  execTimeTracker,
  downloadFile,
  handleShopifyAdminLinkRequest,
  prevDateRange,
  crawler() {
    const authToken = Buffer.from(
      `${process.env.CRAWLER_API_USER_HANDLE}:${process.env.CRAWLER_API_USER_PASSWORD}`
    ).toString("base64");

    return axios.create({
      baseURL: process.env.CRAWLER_API_URL,
      headers: {
        Authorization: `Basic ${authToken}`,
      },
    });
  },
  setCookie(cookies, key, value, age = 1) {
    const maxAge = age * 1000 * 60 * 60 * 24;
    return cookies.set(key, value, {
      maxAge,
      httpOnly: false,
      secure: true,
      sameSite: "none",
    });
  },
  getCookie(cookies, key) {
    return cookies.get(key);
  },
  removeCookie(cookies, key) {
    return cookies.set(key, "");
  },

  stripTags,

  stripPunctuations(string = "") {
    if (string) return string.replace(/[^\w\s]/g, "");
    return "";
  },

  stripSpecialChars(string = "") {
    return string?.replace(/&[^;]*;/gim, "") || "";
  },

  async getUrl(location) {
    if (location) {
      const cacheHost = await redisClient.get(HOST);
      const host = cacheHost || url;
      const newUrl = new URL(location, host);
      return newUrl.href;
    }
    return "";
  },

  async registerWebHooks(hooks) {},

  // calculateKeywordDensity(string, keyword) {
  //   const keywordCount = module.exports.stripTags(string)
  //     .toLowerCase()
  //     .split(" ")
  //     .reduce((count, str) => {
  //       if (str === keyword.toLowerCase()) {
  //         count++;
  //       }
  //       return count;
  //     }, 0);
  //
  //   const wordCount = module.exports.stripTags(string)
  //     .toLowerCase()
  //     .split(" ").length;
  //
  //   const density = (keywordCount / wordCount) * 100;
  //
  //   return density >= 1 && density <= 2;
  // },

  countWords(string) {
    return words(module.exports.stripTags(string)).length;
  },

  calculateScorePercent(mark, totalProduct, fullMark) {
    return Math.round((mark * 100) / (totalProduct * fullMark));
  },

  calculateIsOptimized(mark = 0, fullMark = 0, optimizePercentage = optimizePercent) {
    if (mark && fullMark) {
      return (mark / fullMark) * 100 >= optimizePercentage;
    }
    return 0;
  },

  getPercentValue(value = 0, outOf = 0) {
    if (value && outOf) {
      return Math.round((value / outOf) * 100);
    }
    return 0;
  },

  replaceSpecialChars(string) {
    if (string) {
      return string.replace(/[,\/#!$%\^\*;:{}=_`~()]/g, "").replace(/\s+/g, " ");
    }
    return "";
  },

  extractFocusKeyword(text, caseSensitive = false, letter = "normal") {
    try {
      const purifiedText = module.exports.stripTags(String(text));
      const trimmedText = purifiedText.replace(/\r?\n|\r|\n/g, "");
      const keywords = purifiedText ? rakeKeywordExtractor.generate(trimmedText) : [""];
      return keywords[0]?.trim();
    } catch (err) {
      // logger.error(`extractFocusKeyword: ${err.message}`);
      return words(text).slice(0, 2).join(" ");
    }
  },

  /**
   *
   * @param {string} text
   */
  constructRegexWithSpecialCharacters(text) {
    let inputText = text;
    const specialCharsInText = inputText?.match(new RegExp(/[\*\+\$\(\)]/gim));
    if (specialCharsInText) {
      for (let specialChar of uniq(specialCharsInText)) {
        inputText = inputText.replaceAll(specialChar, "\\" + specialChar);
      }
    }

    return new RegExp(inputText, "gim");
  },

  extractKeywords(text) {
    try {
      const purifiedText = module.exports.stripTags(String(text));
      const trimmedText = purifiedText.replace(/\r?\n|\r|\n/g, "");
      const keywords = purifiedText ? rakeKeywordExtractor.generate(trimmedText) : [""];
      return keywords;
    } catch (err) {
      return [""];
    }
  },

  calculatePlanValidity(type, interval, duration = 1) {
    const d = new Date();
    const year = d.getFullYear();
    const month = d.getMonth();
    const day = d.getDate();

    if (type === planType.FREE && interval === planInterval.LIFETIME) {
      return new Date(year + 5, month, day).toISOString();
    }

    if (type === planType.PRO && interval === planInterval.ANNUALLY) {
      return new Date(year + 1, month, day).toISOString();
    }

    if (type === planType.PRO && interval === planInterval.USAGE) {
      return new Date(year, month, day + duration * 30).toISOString();
    }

    return new Date(year, month, day + 30).toISOString();
  },

  extractAddonRules(addons = []) {
    return addons?.reduce(
      (features, addon) => {
        return {
          ...features,
          [addon.group.toLowerCase()]: addon.limit,
        };
      },
      {
        [IMAGE_OPTIMIZER.toLowerCase()]: null,
        [AI_OPTIMIZER.toLowerCase()]: null,
      }
    );
  },

  /**
   *
   * @param {import('../../../types').PlanRules} currentPlanRules
   * @param {object} subscriptionPlan
   * @param {[object]} addons
   * @param {boolean} shouldUpdateRules
   * @returns {PlanRules} plan_rules
   */
  extractRulesFromPlan(currentPlanRules, subscriptionPlan, addons = [], shouldUpdateRules = true) {
    const addonFeatures = module.exports.extractAddonRules(addons);
    const planRules = {
      products: currentPlanRules?.products || subscriptionPlan.rules.products,
    };

    if (shouldUpdateRules) {
      planRules["products"] = subscriptionPlan.rules.products;
      for (let feature of Object.values(features)) {
        planRules[feature] = subscriptionPlan.rules[feature];
      }
    }

    return {
      ...planRules,
      ...addonFeatures,
    };
  },

  isValidAddress: ({ address1, address2, city, zip, country }) => city?.trim() && country?.trim(),

  buildQueryStringForGoogleMapsAPI: (addressObj) => {
    const addressString = Object.values(addressObj)
      .filter((v) => v?.trim())
      .map((v) =>
        v
          .trim()
          .replace(/[^A-Z,a-z,0-9,\s]/g, "")
          .replace(/\s/g, "+")
      )
      .join(",+");
    return `key=${process.env.GOOGLE_MAPS_API_KEY}&address=${addressString}`;
  },

  isSameAddress: (address1Obj, address2Obj) => {
    const keys = ["address1", "address2", "city", "zip", "country"];

    for (let key of keys) {
      if (address1Obj[key]?.trim()?.toLowerCase() !== address2Obj[key]?.trim()?.toLowerCase()) return false;
    }

    return true;
  },

  extractAddressObjFromGooglePlaceDetailAPIResponse: ({ address_components = [], geometry = {} }) => {
    const { lat: latitude = "", lng: longitude = "" } = geometry?.location ?? {};

    let address1 = "";
    let address2 = "";
    let city = "";
    let zip = "";
    let state = "";
    let country = "";

    for (let component of address_components) {
      const type = component.types[0];

      switch (type) {
        case "subpremise":
          address2 = component.long_name;
          break;
        case "premise":
          address2 += " " + component.long_name;
          break;
        case "street_number": {
          address1 = `${component.long_name} ${address1}`;
          break;
        }

        case "route": {
          address1 += component.short_name;
          break;
        }

        case "postal_code": {
          zip = component.long_name;
          break;
        }

        case "postal_code_suffix": {
          zip = `${zip}-${component.long_name}`;
          break;
        }

        case "locality":
          city = component.long_name;
          break;

        case "administrative_area_level_1": {
          state = component.short_name;
          break;
        }

        case "country":
          country = component.long_name;
          break;
      }
    }

    return {
      address1,
      address2,
      city,
      zip,
      state,
      country,
      latitude,
      longitude,
    };
  },

  generateJWTToken: (payload) => jwt.sign(payload, SECRET),

  verifyJWTToken: (token) => {
    try {
      return jwt.verify(token, SECRET);
    } catch (err) {
      return null;
    }
  },

  sanitizeInput: (input) => {
    if (!input || typeof input !== "object") return sanitizeHtml(input);
    else if (isArray(input)) {
      return input.map((el) => module.exports.sanitizeInput(el));
    } else {
      const obj = { ...input };
      for (let key in obj) {
        obj[key] = module.exports.sanitizeInput(obj[key]);
      }
      return obj;
    }
  },

  updateOrInsertMetaInfoInArray: ({ metaArr = [], metaInfo = {}, namespace = NAMESPACE.GLOBAL, key }) => {
    const newMetaArr = [...metaArr];
    const existingMetaIndex = newMetaArr?.findIndex((m) => m.namespace === namespace && m.key === key);

    if (existingMetaIndex >= 0) {
      newMetaArr[existingMetaIndex] = {
        ...newMetaArr[existingMetaIndex],
        ...metaInfo,
      };
    } else {
      newMetaArr.push(metaInfo);
    }

    return newMetaArr;
  },

  getFocusKeywordSuggestions({ title, description, meta }) {
    try {
      const suggestedKeywords = [];
      const metaTags = [METAFIELD_KEYS.TITLE_TAG, METAFIELD_KEYS.DESCRIPTION_TAG];
      if (isArray(meta)) {
        const metaText = meta
          ?.filter((m) => metaTags.includes(m?.key) && m?.value)
          .map((m) => m?.value)
          .join(". ")
          .toLowerCase();
        suggestedKeywords.push(...module.exports.extractKeywords(metaText));
      }
      suggestedKeywords.push(...module.exports.extractKeywords(title?.toLowerCase()));
      suggestedKeywords.push(...module.exports.extractKeywords(description?.toLowerCase()));

      return uniq(suggestedKeywords.filter((k) => k?.trim()).filter((k) => k.toLowerCase() !== "undefined")).slice(
        0,
        5
      );
    } catch (err) {
      return [];
    }
  },

  sleep(ms = 5000, log = true) {
    return new Promise((resolve, reject) => {
      if (log) console.log(`Sleeping for ${ms}ms...`);
      setTimeout(() => resolve(true), ms);
    });
  },

  getShopifyPageLinkTokensFromHeader: (headers) => {
    const headerValue = headers?.Link?.[0];

    let prevPageToken = "";
    let nextPageToken = "";

    headerValue?.split(",")?.forEach((v) => {
      const tokenInfo = v.match(/(page_info=[^>]*)/g)?.[0];
      const tokenValue = tokenInfo?.replace("page_info=", "");
      const rel = v.match(/rel=".*"/g)?.[0];

      if (rel?.includes("previous")) {
        prevPageToken = tokenValue;
      } else {
        nextPageToken = tokenValue;
      }
    });

    return { prevPageToken, nextPageToken };
  },

  /**
   *
   * @param {Array<any>} metaArr
   * @param {string} title
   * @returns {string}
   */
  getMetaTitle: (metaArr = [], title = "") => {
    return metaArr.reduce(
      (acc, metafield) =>
        metafield.namespace === NAMESPACE.GLOBAL && metafield.key === METAFIELD_KEYS.TITLE_TAG ? metafield.value : acc,
      title
    );
  },

  getMetaInfo: (namespace, key, metaArr) => {
    const r = metaArr.find((m) => m.namespace === namespace && m.key === key);
    return r;
  },

  getMetaTitleAndDescription: (item, { useDefaultValue = true } = {}) => {
    if (isEmpty(item)) return { metaTitle: "", metaDescription: "" };

    const metaArr = item.meta || item.metafields;

    let metaTitle = metaArr?.find((m) => m.key === METAFIELD_KEYS.TITLE_TAG)?.value || "";
    let metaDescription = metaArr?.find((m) => m.key === METAFIELD_KEYS.DESCRIPTION_TAG)?.value || "";

    if (useDefaultValue && !metaTitle) {
      metaTitle ||= item.title || "";
    }

    if (useDefaultValue && !metaDescription) {
      metaDescription ||= module.exports.stripTags(item.description) || module.exports.stripTags(item.body_html) || "";
      metaDescription = metaDescription.trim().substring(0, 165);
    }

    return {
      metaTitle: metaTitle.trim(),
      metaDescription: metaDescription.trim(),
    };
  },

  getFileNameFromGoogleBucketPublicURL: (currentPath) => {
    const regexp = new RegExp(`https://storage.googleapis.com/${process.env.GOOGLE_BUCKET_NAME}/`, "i");
    const filePath = decodeURIComponent(decodeURI(currentPath)).replace(regexp, "");
    return filePath;
  },

  /**
   *
   * @param {Array<any>} metaArr
   * @param {string} description
   * @returns {string}
   */
  getMetaDescription: (metaArr = [], description = "") => {
    return metaArr.reduce(
      (acc, metafield) =>
        metafield.namespace === NAMESPACE.GLOBAL && metafield.key === METAFIELD_KEYS.DESCRIPTION_TAG
          ? metafield.value
          : acc,
      description
    );
  },

  extractUrlsFromHTMLString: (htmlContent) => {
    return (
      htmlContent.match(
        /href="[(http(s)?):\/\/(www\.)?a-zA-Z0-9@:%._\-\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)"/gi
      ) || []
    );
  },

  /**
   *
   * @param {string} htmlContent
   * @returns
   */
  extractInternalLinksFromHtmlString: (htmlContent) => {
    return htmlContent.match(/href="(?:\.?\.?\/?)(?!https:\/\/)(?!www.).*"/gim) || [];
  },

  extractImagesFromHTMLString: (htmlContent) => {
    return htmlContent.match(/<img([\w\W]+?)>/gi) || [];
  },

  dataGet(data, key, defaultValue = "") {
    const keys = key.split(".");

    let output = data;
    for (let i = 0; i < keys.length; i++) {
      output = output[keys[i]] || defaultValue;
    }
    return output;
  },

  generatePayloadForGoogleKeywordIdeasAPI: ({ keywords, geoTargetConstants = [], pageSize = 5 }) => {
    return {
      geoTargetConstants,
      pageSize,
      keywordSeed: {
        keywords,
      },
      historicalMetricsOptions: {
        includeAverageCpc: true,
      },
      keywordAnnotation: ["KEYWORD_CONCEPT", "UNSPECIFIED", "UNKNOWN"],
    };
  },

  getFormattedStringFromNumber: (num) => {
    if (isNaN(Number(num)) || num == null) {
      return "0";
    } else if (num >= ONE_MILLION) {
      const shortedNumber = Number((num / ONE_MILLION).toFixed(2));
      return `${shortedNumber}M`;
    } else if (num >= ONE_THOUSAND) {
      const shortedNumber = Number((num / ONE_THOUSAND).toFixed(2));
      return `${shortedNumber}K`;
    } else {
      return num;
    }
  },

  convertGoogleAdsCpcValueToDollarString: (cpc) => {
    if (isNaN(Number(cpc))) {
      return "$1";
    }

    return `$${Number((cpc / ONE_MILLION).toFixed(2))}`;
  },

  formatKeywordIdeaMetrics: (keywordIdeaMetrics) => {
    const { avgMonthlySearches, averageCpcMicros, competitionIndex } = keywordIdeaMetrics || {};

    return {
      searchVolume: module.exports.getFormattedStringFromNumber(avgMonthlySearches),
      cpc: module.exports.convertGoogleAdsCpcValueToDollarString(averageCpcMicros),
      paidDifficulty: competitionIndex || "1",
    };
  },

  formatGoogleKeywordIdeasResults: (results) => {
    const [originalKeywordData, ...relatedKeywordIdeasData] = results || [];
    const { avgMonthlySearches, averageCpcMicros, competitionIndex } = originalKeywordData?.keywordIdeaMetrics || {};
    return {
      ...module.exports.formatKeywordIdeaMetrics(originalKeywordData?.keywordIdeaMetrics),
      relatedKeywords: relatedKeywordIdeasData.map((keyword) => ({
        text: keyword.text,
        ...module.exports.formatKeywordIdeaMetrics(keyword?.keywordIdeaMetrics),
      })),
    };
  },

  /**
   * Prepare pagination for dashboard
   * @param count
   * @param page
   * @param limit
   * @returns {{pageCount: number, pageSize: number, page: number, rowCount: number}}
   */
  preparePagination: (count, page, limit) => {
    count = parseInt(count);
    limit = parseInt(limit);
    page = parseInt(page);
    return {
      page,
      pageCount: Math.ceil(count / limit),
      pageSize: limit,
      rowCount: count,
    };
  },

  removeTrailingSlash(url) {
    return url.replace(/\/$/, "");
  },

  shopStatusByApiResponseCode(code = null) {
    switch (code) {
      case 401:
        return shopStatus.UNINSTALLED;
      case 402:
        return shopStatus.CLOSED;
      default:
        return shopStatus.UNINSTALLED;
    }
  },

  shopStatusByPlanName(planName) {
    switch (planName) {
      case "cancelled":
        return shopStatus.CLOSED;
      default:
        return shopStatus.ACTIVE;
    }
  },

  prepareIntervalText(interval) {
    switch (interval) {
      case planInterval.MONTHLY:
      case planInterval.USAGE:
        return "month";
      case planInterval.ANNUALLY:
        return "year";
      default:
        return null;
    }
  },

  preparePlanDescription(interval, totalPrice, months = 1) {
    switch (interval) {
      case planInterval.MONTHLY:
        return "Billed every month";
      case planInterval.ANNUALLY:
        return "Billed every year";
      case planInterval.USAGE:
        return `Billed $${totalPrice.toFixed(2)} for ${months} months`;
      default:
        return null;
    }
  },

  formatFromDateForQuery(from = "2020-01-01") {
    return moment(from || "2020-01-01")
      .startOf("day")
      .toISOString();
  },

  formatToDateForQuery(to = undefined) {
    return moment(to || undefined)
      .endOf("day")
      .toISOString();
  },

  extractShopifyIdFromGqlId(gqlId) {
    try {
      let splitted = gqlId.split("/");
      return Number(splitted[splitted.length - 1]);
    } catch (err) {
      // console.error(err);
      return gqlId;
    }
  },

  formatBytes(bytes, decimals = 2) {
    if (!+bytes) return "0 Bytes";

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
  },

  escapeRegExp(string) {
    return string.replace(/[,*+?^${}()|[\]\\]/g, "\\$&"); // $& means the whole matched string
  },

  truncateTextAtSentenceEnd(text, maxLength) {
    if (text.length <= maxLength) {
      return text;
    }

    const truncated = text.slice(0, maxLength);
    const sentenceEndIndex = Math.max(
      truncated.lastIndexOf("."),
      truncated.lastIndexOf("!"),
      truncated.lastIndexOf("?")
    );

    if (sentenceEndIndex > 0) {
      return truncated.slice(0, sentenceEndIndex + 1);
    }

    return truncated.slice(0, maxLength);
  },

  normalizeString(str) {
    return (
      str
        .toLowerCase() // Convert to lowercase
        .replace(/[äöüßéèêëàáâãåçñùúûüôöòóøõíìîïýÿœæ]/g, (match) => specialCharsMap[match]) // Replace special chars
        .normalize("NFD") // Normalize to decomposed form
        .replace(/[\u0300-\u036f]/g, "") // Remove remaining diacritics
        // .replace(/[^a-z0-9\s-]/g, "") // Remove special characters except hyphens
        .trim()
    ); // Remove leading/trailing spaces
  },

  /**
   *
   * @param {string} title
   * @returns {string}
   */
  minifyTitle(title) {
    if (title.length <= 50) return title;

    return title.substring(0, 24) + "..." + title.substring(title.length - 24);
  },

  async asyncFilter(arr, predicate) {
    return Promise.all(arr.map(predicate)).then((results) => arr.filter((_v, index) => results[index]));
  },

  sumStatsWithExclusions(data) {
    // Use the date and type of the first object
    const { date, type } = data[0] || {};
    const statsTemplate = JSON.parse(JSON.stringify(data[0]?.stats || dailyStatusReportDegfaultValue)); // Clone stats structure

    // Keys to exclude from summation
    const excludedKeys = [
      "yearlyActiveStores",
      "visionaryAciveStores",
      "monthlyActiveStores",
      "onetimeCharges",
      "onetimeChargesAll",
    ];

    const sumArrayObjects = (arrays) => {
      const aggregated = {};
      arrays.flat().forEach(({ title, key, count }) => {
        if (!aggregated[title]) {
          aggregated[title] = { title, key: key || 0, count: parseInt(count) || 0 };
        } else {
          aggregated[title].key += key || 0;
          aggregated[title].count += parseInt(count) || 0;
        }
      });
      return Object.values(aggregated);
    };

    const resultStats = {};
    for (const key in statsTemplate) {
      if (excludedKeys.includes(key)) {
        // Use the value from the last item for excluded keys
        resultStats[key] = data[data.length - 1]?.stats[key] || 0;
      } else if (Array.isArray(statsTemplate[key])) {
        // Sum arrays of objects
        resultStats[key] = sumArrayObjects(data.map((item) => item.stats[key] || []));
      } else if (!isNaN(statsTemplate[key])) {
        // Sum numeric fields
        resultStats[key] = data.reduce((sum, item) => sum + parseFloat(item.stats[key] || 0), 0);
      }
    }

    return { date, type, stats: resultStats };
  },

  /**
   *
   * @param {string} size
   * @returns {number}
   * @description Convert size to bytes
   * @example
   * sizeToBytes("1 MB") // 1048576
   */
  sizeToBytes(size) {
    const [value, unit] = size.split(" ");
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const index = sizes.indexOf(unit);
    return Math.round(value * 1000 ** index);
  },

  /**
   *
   * @param {Session} session
   * @returns
   */
  isValidSession(session) {
    return session && session.accessToken && session.shop;
  },

  generateShopifyDomainHandle(domain) {
    return domain.replace(".myshopify.com", "");
  },
  normalizeLogoUrlPath(url) {
    if (url) return url.split("myshopify.com%")[1];
  },
};
