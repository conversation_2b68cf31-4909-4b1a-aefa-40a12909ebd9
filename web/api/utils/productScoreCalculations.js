const { isEmpty, words, startsWith } = require("lodash");
const { Op } = require("sequelize");
const SCORES = require("../config/seoScores");
const { stripTags, constructRegexWithSpecialCharacters, normalizeString } = require("./helper");
const { Product, MultiLanguageProducts, sequelize } = require("../../sequelize");
const logger = require("storeseo-logger");
const { calculateIsOptimized } = require("../utils/helper");
const { decode } = require("html-entities");
const { METAFIELD_KEYS } = require("storeseo-enums/metafields");

const getProductDescriptionsByPagination = async ({ shopId, pageSize, page, excludeProductId }) => {
  const { count, rows: products } = await Product.findAndCountAll({
    attributes: ["description"],
    where: {
      shop_id: shopId,
      id: {
        [Op.not]: excludeProductId,
      },
    },
    order: [["id", "asc"]],
    limit: pageSize,
    offset: (page - 1) * pageSize,
  });

  return products.map((p) => p.toJSON());
};

module.exports = {
  calculateDescWordLength(desc) {
    if (desc) {
      const wordCount = words(stripTags(desc)).length;
      if (wordCount > 0 && wordCount <= 50) {
        return Math.round((SCORES.DESC_MIN_WORD_COUNT_300 * wordCount) / 50);
      }
      if (wordCount > 50) {
        return SCORES.DESC_MIN_WORD_COUNT_300;
      }
    }
    return 0;
  },

  calculateKeywordDensity(string, keyword) {
    if (isEmpty(string) || isEmpty(keyword)) {
      return 0;
    }

    try {
      const decodedString = decode(stripTags(string));

      const keywordRegex = constructRegexWithSpecialCharacters(keyword);
      const keywordCount = decodedString.match(keywordRegex) ? decodedString.match(keywordRegex).length : 0;

      if (keywordCount > 0 && keywordCount <= 6) {
        return Math.round((SCORES.KEYWORD_DENSITY_IN_DESC * keywordCount) / 6);
      }

      return 0;
    } catch (err) {
      logger.error(err, { keyword, string });
      return 0;
    }
  },

  calculateFocusKeywordInTitle(title, keyword) {
    if (!isEmpty(keyword)) {
      return title.toLowerCase().includes(keyword.toLowerCase()) ? SCORES.FOCUS_KEYWORD_IN_TITLE : 0;
    }
    return 0;
  },

  calculateFocusKeywordInAltText(images, keyword) {
    const imgLen = images?.length || 0;

    if (imgLen === 0) {
      return SCORES.FOCUS_KEYWORD_IN_IMG_ALT_TEXT;
    }

    if (isEmpty(keyword)) {
      return 0;
    }

    return images.some((img) => !isEmpty(img.altText) && img.altText.toLowerCase().includes(keyword.toLowerCase()))
      ? SCORES.FOCUS_KEYWORD_IN_IMG_ALT_TEXT
      : 0;
  },

  calculateAltTextInImg(images) {
    const imgLen = images?.length || 0;

    if (imgLen === 0) {
      return SCORES.FOCUS_KEYWORD_IN_IMG_ALT_TEXT;
    }

    return images.some((img) => isEmpty(img.altText)) ? 0 : SCORES.ALT_TEXT_IN_ALL_IMG;
  },

  calculateAllImageOptimized(images) {
    const imgLen = images?.length || 0;

    if (imgLen === 0) {
      return SCORES.OPTIMIZED_ALL_IMAGES;
    }

    return images.some((img) => !(img.isOptimized || img.isAlreadyOptimized)) ? 0 : SCORES.OPTIMIZED_ALL_IMAGES;
  },

  calculateFocusKeywordInMetaTitle(product, keyword) {
    if (!isEmpty(keyword)) {
      const titleTag = module.exports.getTitleTagFromProduct(product);

      if (titleTag.toLowerCase().includes(keyword.toLowerCase())) {
        return SCORES.FOCUS_KEYWORD_IN_META_TITLE;
      }
    }
    return 0;
  },

  calculateFocusKeywordInURL(handle, keyword) {
    if (isEmpty(keyword) || isEmpty(handle)) {
      return 0;
    }

    let escapedKeyword = normalizeString(keyword).replace(/[.*+?^${}()|[\]\\]/g, "\\$&");

    // Create a regular expression pattern for the keyword
    const regexPattern = escapedKeyword
      .split(/\s+/) // Split keyword into words
      // .map((word) => `\\b${word}\\b`) // Add word boundaries to each word
      .join(".*"); // Allow any characters in between words

    // Create a case-insensitive regular expression
    const regex = new RegExp(regexPattern, "i");

    // Test if the keyword pattern exists in the URL
    return regex.test(handle.normalize("NFD")) ? SCORES.FOCUS_KEYWORD_IN_URL : 0;
  },

  calculateFocusKeywordInMetaDesc(product, keyword) {
    const metaDescription = module.exports.getMetaDescriptionFromProduct(product);
    const keywordRegex = constructRegexWithSpecialCharacters(keyword);

    if (!isEmpty(keyword) && metaDescription && metaDescription.toLowerCase().match(keywordRegex))
      return SCORES.FOCUS_KEYWORD_IN_META_DESC;

    return 0;
  },

  calculateMetaDescLength(product) {
    const metaDescription = module.exports.getMetaDescriptionFromProduct(product);

    if (!metaDescription) return 0;

    if (metaDescription.length > 0 && metaDescription.length <= 165) {
      return Math.round((SCORES.META_DESC_WITHIN_CHAR_LIMIT * metaDescription.length) / 165);
    }

    return Math.round(SCORES.META_DESC_WITHIN_CHAR_LIMIT * 0.6);
  },

  getTitleTagFromProduct(product) {
    if (!isEmpty(product.meta)) {
      const mTitle = product.meta.find((pm) => pm.key === METAFIELD_KEYS.TITLE_TAG);

      if (mTitle) {
        return mTitle.value;
      }
    }
    return product.title;
  },

  calculateMetaTitleLength(product) {
    const titleTag = module.exports.getTitleTagFromProduct(product);
    if (titleTag.length > 0 && titleTag.length <= 70) {
      const score = Math.round((SCORES.META_DESC_WITHIN_CHAR_LIMIT * titleTag.length) / 70);
      if (score >= 7 && titleTag.length < 50) return 6;
      else return score;
    }
    if (titleTag.length > 70) {
      return Math.round(SCORES.META_DESC_WITHIN_CHAR_LIMIT * 0.6);
    }
    return 0;
  },

  getMetaDescriptionFromProduct(product) {
    if (!isEmpty(product.meta)) {
      const metaDescription = product.meta.find((pm) => pm.key === "description_tag");

      if (metaDescription) {
        return metaDescription.value;
      }
    }
    return module.exports.stripTags(product.description)?.trim()?.substring(0, 165);
  },

  stripTags(string) {
    return string
      ?.replace(/(<([^>]+)>)/gi, "")
      .replace(/ +(?= )/g, "")
      .trim();
  },

  calculateFocusKeywordAtTheBeginningOfMetaTitle(product, keyword) {
    if (!isEmpty(keyword)) {
      const titleTag = module.exports.getTitleTagFromProduct(product);

      if (titleTag) {
        if (startsWith(titleTag.toLowerCase(), keyword.toLowerCase())) {
          return SCORES.FOCUS_KEYWORD_AT_THE_BEGINNING_OF_META_TITLE;
        }
      }
    }
    return 0;
  },

  async calculateUniqueFocusKeyword(shopId, keyword, id = null) {
    if (!isEmpty(keyword)) {
      const count = await Product.count({
        where: {
          shop_id: shopId,
          id: { [Op.not]: id },
          [Op.and]: sequelize.where(
            sequelize.fn("LOWER", sequelize.col("focus_keyword")),
            keyword.trim().toLowerCase()
          ),
        },
      });
      return count === 0 ? SCORES.UNIQUE_FOCUS_KEYWORD : 0;
    }
    return 0;
  },

  async calculateMulitLanguageUniqueFocusKeyword(shopId, keyword, languageCode, id = null) {
    if (!isEmpty(keyword)) {
      const count = await MultiLanguageProducts.count({
        where: {
          shop_id: shopId,
          id: { [Op.not]: id },
          language_code: languageCode,
          [Op.and]: sequelize.where(
            sequelize.fn("LOWER", sequelize.col("focus_keyword")),
            keyword.trim().toLowerCase()
          ),
        },
      });
      return count === 0 ? SCORES.UNIQUE_FOCUS_KEYWORD : 0;
    }
    return 0;
  },

  async calculateUniqueTitle(shopId, title) {
    if (!isEmpty(title)) {
      return (await Product.count({ where: { shop_id: shopId, title } })) === 1 ? SCORES.UNIQUE_TITLE : 0;
    }
    return 0;
  },

  totalScore(scoreConfig = SCORES) {
    return Object.values(scoreConfig).reduce((a, b) => a + b, 0);
  },

  /*calculateAnalysisScores(analysis) {
    return Object.entries(analysis).reduce(
      (data, val) => {
        const key1 = val[0];

        const output1 = Object.entries(val[1]).reduce(
          (output, val2) => {
            const key2 = val2[0];
            const isOptimized = calculateIsOptimized(val2[1], SCORES[key1][key2]);

            return {
              issues: output.issues + (!isOptimized ? 1 : 0),
              passed: output.passed + (isOptimized ? 1 : 0),
              point: output.point + val2[1],
            };
          },
          { passed: 0, issues: 0, point: 0 }
        );

        return {
          issues: data.issues + output1.issues,
          passed: data.passed + output1.passed,
          point: data.point + output1.point,
        };
      },
      { passed: 0, issues: 0, point: 0 }
    );
  },*/

  calculateAnalysisScores(analysis) {
    return Object.entries(analysis).reduce(
      (output, val2) => {
        const key2 = val2[0];
        const isOptimized = calculateIsOptimized(val2[1], SCORES[key2.toUpperCase()]);

        return {
          issues: output.issues + (!isOptimized ? 1 : 0),
          passed: output.passed + (isOptimized ? 1 : 0),
          point: output.point + val2[1],
        };
      },
      { passed: 0, issues: 0, point: 0 }
    );
  },
  getProductDescriptionsByPagination,
};
