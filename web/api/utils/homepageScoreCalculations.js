const SCORES = require("../config/homepageSeoScores");
const { calculateIsOptimized } = require("./helper");

module.exports = {
  calculateFocusKeywordInMetaDesc(metaDescription, keyword) {
    if (metaDescription?.toLowerCase().match(new RegExp(keyword, "gim"))) {
      return SCORES.FOCUS_KEYWORD_IN_META_DESC;
    }
    return 0;
  },

  calculateFocusKeywordInMetaTitle(metaTitle, keyword) {
    if (metaTitle?.toLowerCase().includes(keyword.toLowerCase())) {
      return SCORES.FOCUS_KEYWORD_IN_META_TITLE;
    }
    return 0;
  },

  calculateMetaTitleLength(metaTitle, maxCharLimit = 70) {
    if (metaTitle?.length > 0 && metaTitle.length <= maxCharLimit) {
      return Math.round((SCORES.META_TITLE_WITHIN_CHAR_LIMIT * metaTitle.length) / maxCharLimit);
    } else if (metaTitle) {
      return Math.round(SCORES.META_TITLE_WITHIN_CHAR_LIMIT * 0.6);
    } else {
      return 0;
    }
  },

  calculateMetaDescLength(metaDesc, maxCharLimit = 165) {
    if (metaDesc?.length > 0 && metaDesc.length <= maxCharLimit) {
      return Math.round((SCORES.META_DESC_WITHIN_CHAR_LIMIT * metaDesc.length) / maxCharLimit);
    } else if (metaDesc) {
      return Math.round(SCORES.META_DESC_WITHIN_CHAR_LIMIT * 0.6);
    } else {
      return 0;
    }
  },

  calculateAnalysisScores(analysis) {
    return Object.entries(analysis).reduce(
      (data, val) => {
        const key = val[0];

        const isOptimized = calculateIsOptimized(val[1], SCORES[key.toUpperCase()]);

        return {
          issues: data.issues + (!isOptimized ? 1 : 0),
          passed: data.passed + (isOptimized ? 1 : 0),
          point: data.point + val[1],
        };
      },
      { passed: 0, issues: 0, point: 0 }
    );
  },
};
