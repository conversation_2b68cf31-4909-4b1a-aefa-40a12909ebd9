const { isEmpty, words, startsWith } = require("lodash");
const SCORES = require("../config/pageSeoScores");
const { Page: PageModel, Op, Sequelize } = require("../../sequelize");
const { calculateIsOptimized, normalizeString } = require("../utils/helper");
const { METAFIELD_KEYS } = require("storeseo-enums/metafields");

/**
 * @type {typeof import('sequelize').Model}
 */
const Page = PageModel;

const getTitleTag = (page) => {
  if (!isEmpty(page.meta)) {
    const mTitle = page.meta.find((pm) => pm.key === METAFIELD_KEYS.TITLE_TAG);

    if (mTitle) {
      return mTitle.value;
    }
  }
  return page.title;
};

const extractUrls = (content) => {
  return (
    content.match(
      /href="[(http(s)?):\/\/(www\.)?a-zA-Z0-9@:%._\-\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)"/gi
    ) || []
  );
};

const extractImages = (content) => {
  return content.match(/<img([\w\W]+?)>/gi) || [];
};

module.exports = {
  async calculateUniqueFocusKeyword(shopId, keyword, id = null) {
    if (!isEmpty(keyword)) {
      const count = await Page.count({
        where: {
          shop_id: shopId,
          id: {
            [Op.ne]: id,
          },
          [Op.and]: [Sequelize.literal("LOWER(focus_keyword) = $keyword")],
        },
        bind: {
          keyword,
        },
      });

      return count === 0 ? SCORES.UNIQUE_FOCUS_KEYWORD : 0;
    }
    return 0;
  },

  findWords(content) {
    return content.split(" ");
  },

  calculateFocusKeywordInIntroduction(content, keyword) {
    if (!isEmpty(content) && !isEmpty(keyword)) {
      const contentInWords = this.findWords(content);
      const wordCount = contentInWords.length;
      const tenPercentOfContent = Math.round(wordCount * 0.1);
      const introduction = contentInWords.slice(0, tenPercentOfContent - 1).join(" ");

      if (introduction.includes(keyword)) {
        return SCORES.FOCUS_KEYWORD_IN_INTRODUCTION;
      }
    }
    return 0;
  },

  calculateContentMinWord(content, minWord = 300) {
    if (!isEmpty(content)) {
      if (words(content).length >= minWord) {
        return SCORES.CONTENT_MORE_THEN_300_WORDS;
      }
    }
    return 0;
  },

  calculateFocusKeywordDensity(content, keyword) {
    if (!isEmpty(content) && !isEmpty(keyword)) {
      const contentInWords = words(content);
      const wordCount = contentInWords.length;
      const onePercentOfContent = Math.round(wordCount * 0.01);
      const twoPercentOfContent = Math.round(wordCount * 0.02);
      const occurrence = (content.match(new RegExp(keyword, "gi")) || []).length;

      if (occurrence >= onePercentOfContent && occurrence <= twoPercentOfContent) {
        return SCORES.FOCUS_KEYWORD_DENSITY;
      }
    }
    return 0;
  },

  calculateFocusKeywordInAltText(content, keyword) {
    if (isEmpty(content) || isEmpty(keyword)) return SCORES.FOCUS_KEYWORD_IN_IMG_ALT_TEXT;

    const images = extractImages(content);
    if (images.length) {
      for (let i = 0; i < images.length; i++) {
        const altText = images[i].match(/alt="(.*?)"/i);
        if (altText && altText[1] && altText[1].toLowerCase().includes(keyword.toLowerCase())) {
          return SCORES.FOCUS_KEYWORD_IN_IMG_ALT_TEXT;
        }
      }
    } else return SCORES.FOCUS_KEYWORD_IN_IMG_ALT_TEXT;

    return 0;
  },

  calculateImgAltText(content) {
    if (!isEmpty(content)) {
      const images = extractImages(content);
      if (images && images.length > 0) {
        for (let i = 0; i < images.length; i++) {
          const altText = images[i].match(/alt="(.*?)"/i);
          if (!altText || (altText && altText[1].length === 0)) {
            return 0;
          }
        }
      }
      return SCORES.FOCUS_KEYWORD_IN_IMG_ALT_TEXT;
    }
    return 0;
  },

  calculateFocusKeywordInSubheading(content, keyword) {
    if (!isEmpty(content) && !isEmpty(keyword)) {
      const headings = content.match(/<h[1-6].*?>(.*?)<\/h[1-6]>/gim);
      if (headings && headings.length > 0) {
        for (let i = 0; i < headings.length; i++) {
          if (headings[i].toLowerCase().includes(keyword)) {
            return SCORES.FOCUS_KEYWORD_IN_SUBHEADING;
          }
        }
      }
    }
    return 0;
  },

  calculateFocusKeywordInMetaDesc(meta, keyword) {
    if (!isEmpty(meta) && !isEmpty(keyword)) {
      const mDesc = meta.find((pm) => pm.key === "description_tag");

      if (mDesc) {
        if (mDesc.value.toLowerCase().match(new RegExp(keyword, "gim"))) {
          return SCORES.FOCUS_KEYWORD_IN_META_DESC;
        }
      }
    }
    return 0;
  },

  calculateFocusKeywordInUrl(handle, keyword) {
    if (isEmpty(handle) || isEmpty(keyword)) return 0;

    let escapedKeyword = normalizeString(keyword).replace(/[.*+?^${}()|[\]\\]/g, "\\$&");

    // Create a regular expression pattern for the keyword
    const regexPattern = escapedKeyword
      .split(/\s+/) // Split keyword into words
      // .map((word) => `\\b${word}\\b`) // Add word boundaries to each word
      .join(".*"); // Allow any characters in between words

    // Create a case-insensitive regular expression
    const regex = new RegExp(regexPattern, "i");

    // Test if the keyword pattern exists in the URL
    return regex.test(handle.normalize("NFD")) ? SCORES.FOCUS_KEYWORD_IN_URL : 0;
  },

  calculateMetaDescLength(meta) {
    if (!isEmpty(meta)) {
      const mDesc = meta.find((pm) => pm.key === "description_tag");

      if (mDesc) {
        const { length } = mDesc.value;
        if (length > 0 && length <= 165) {
          return Math.round((SCORES.META_DESC_WITHIN_160_CHAR * length) / 165);
        }
        if (length > 165) {
          return Math.round(SCORES.META_DESC_WITHIN_160_CHAR * 0.6);
        }
      }
    }
    return 0;
  },

  calculateFocusKeywordInMetaTitle(page, keyword) {
    if (!isEmpty(keyword)) {
      const titleTag = getTitleTag(page);

      if (titleTag.toLowerCase().includes(keyword.toLowerCase())) {
        return SCORES.FOCUS_KEYWORD_IN_META_TITLE;
      }
    }
    return 0;
  },

  calculateInternalLink(content, url) {
    if (!isEmpty(content) && !isEmpty(url)) {
      const urls = extractUrls(content);
      if (urls && urls.length > 0) {
        for (let i = 0; i < urls.length; i++) {
          if (urls[i].includes(url)) {
            return SCORES.INTERNAL_LINK_IN_CONTENT;
          }
        }
      }
    }
    return 0;
  },

  calculateExternalLink(content, url) {
    if (!isEmpty(content) && !isEmpty(url)) {
      const urls = extractUrls(content);
      if (urls && urls.length > 0) {
        for (let i = 0; i < urls.length; i++) {
          if (!urls[i].includes(url)) {
            return SCORES.EXTERNAL_LINK_IN_CONTENT;
          }
        }
      }
    }
    return 0;
  },

  calculateAnalysisScores(analysis) {
    return Object.entries(analysis).reduce(
      (data, val) => {
        const key = val[0];

        const isOptimized = calculateIsOptimized(val[1], SCORES[key.toUpperCase()]);

        return {
          issues: data.issues + (!isOptimized ? 1 : 0),
          passed: data.passed + (isOptimized ? 1 : 0),
          point: data.point + val[1],
        };
      },
      { passed: 0, issues: 0, point: 0 }
    );
  },

  totalScore() {
    return Object.values(SCORES).reduce((score, item) => score + item, 0);
  },
};
