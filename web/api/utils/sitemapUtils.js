const { isEmpty } = require("lodash");

/**
 * Find sitemap relational filter for product
 * @param filterOn
 * @param filterValue
 * @returns {undefined}
 */
const findSitemapFilter = (filterOn = "", filterValue = "-1") => {
  const sitemapWhere = {};

  if (filterOn === "sitemap" && Number(filterValue) >= 0) {
    sitemapWhere.sitemap_disabled = Number(filterValue);
  } else if (filterOn === "noindex" && Number(filterValue) >= 0) {
    sitemapWhere.no_index = Number(filterValue);
  } else if (filterOn === "nofollow" && Number(filterValue) >= 0) {
    sitemapWhere.no_follow = Number(filterValue);
  }

  return !isEmpty(sitemapWhere) ? sitemapWhere : undefined;
};

module.exports = {
  findSitemapFilter,
};
