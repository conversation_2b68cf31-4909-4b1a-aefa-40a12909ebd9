const { serializeShopifyProductImages } = require("../serializers/ProductImageSerializer");
const { serializeShopifyProductMetas } = require("../serializers/ProductMetaSerializer");
const { serializeShopifyProduct } = require("../serializers/ProductSerializer");

/**
 * Check if the array includes the shopify image
 * @param {import("../../jsDocTypes").ShopifyImage[]} arr images array
 * @param {import("../../jsDocTypes").ShopifyImage} img shopify image to search against
 * @returns {boolean}
 */
const hasExactlySameShopifyImage = (arr, img) =>
  arr.reduce(
    (hasImg, i) =>
      hasImg ||
      (i.media_id === img.id && i.altText === img.altText && i.src === img.src && i.position === img.position),
    false
  );

/**
 * Compare two shopify image arrays
 * @param {import("../../jsDocTypes").ShopifyImage[]} shopifyImages shopify returned images array
 * @param {import("../../jsDocTypes").ShopifyImage[]} existingImages existing image array from database
 * @returns {boolean} 'true' if values are same, 'false' otherwise
 */
const compareShopifyImgArray = (shopifyImages, existingImages) => {
  if (shopifyImages.length !== existingImages.length) return false;

  for (let img of shopifyImages) {
    const hasImage = hasExactlySameShopifyImage(existingImages, img);
    if (!hasImage) return false;
  }

  return true;
};

/**
 * Check if array includes shopify metafield
 * @param {import("../../jsDocTypes").Metafield[]} metaArr metafield array
 * @param {import("../../jsDocTypes").Metafield} meta shopify metafield to search agains
 * @returns {boolean}
 */
const hasExactlySameShopifyMetaField = (metaArr, meta) =>
  metaArr.reduce(
    (hasMeta, m) =>
      hasMeta ||
      (m.id === meta.id &&
        m.key === meta.key &&
        m.namespace === meta.namespace &&
        m.type === meta.type &&
        m.value === meta.value),
    false
  );

/**
 * Compare two shopify meta arrays
 * @param {import("../../jsDocTypes").Metafield[]} shopifyMetas
 * @param {import("../../jsDocTypes").Metafield[]} existingMetas
 * @returns {boolean} 'true' if values are same, 'false' otherwise
 */
const compareShopifyMetafields = (shopifyMetas, existingMetas) => {
  if (shopifyMetas.length !== existingMetas.length) return false;

  for (let meta of shopifyMetas) {
    const hasMeta = hasExactlySameShopifyMetaField(existingMetas, meta);
    if (!hasMeta) return false;
  }

  return true;
};

/**
 * Compare shopify tags with already stored tags
 * @param {string[]} shopifyTags
 * @param {string[]} existingTags
 */
const compareShopifyTags = (shopifyTags, existingTags) => {
  if (shopifyTags.length !== existingTags.length) return false;

  const setOfTags = new Set(existingTags);
  for (let tag of shopifyTags) if (!setOfTags.has(tag)) return false;

  return true;
};

/**
 * Compare Shopify graphql query returned product data with database product data.
 * @param {*} shopifyProduct shopify graphql query returned product data
 * @param {*} dbProduct product stored in database
 * @returns {boolean} 'true' if product data has not changed, 'false' otherwise
 */
const compareShopifyProductWithDbProduct = (shopifyProduct, dbProduct) => {
  try {
    const { id, tags: existingTags, ...existingProduct } = dbProduct;
    // map the shopify product data to database returend product data structure
    const {
      tags: shopifyTags,
      cursor,
      online_store_preview_url,
      online_store_url,
      ...serializedShopifyProduct
    } = serializeShopifyProduct(existingProduct?.shop_id, shopifyProduct);

    for (let key in serializedShopifyProduct) {
      if (serializedShopifyProduct[key] !== existingProduct[key]) return false;
    }

    if (!compareShopifyTags(shopifyTags, existingTags)) return false;

    const shopifyProductImages = serializeShopifyProductImages(shopifyProduct);
    if (!compareShopifyImgArray(shopifyProductImages, dbProduct.images)) return false;

    const shopifyProductMetas = serializeShopifyProductMetas(shopifyProduct);
    if (!compareShopifyMetafields(shopifyProductMetas, dbProduct.meta)) return false;

    return true;
  } catch (err) {
    console.log(err);
    return false;
  }
};

module.exports = {
  compareShopifyProductWithDbProduct,
};
