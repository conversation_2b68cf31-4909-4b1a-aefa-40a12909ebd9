// // @ts-check
const { Collection, sequelize, Op } = require("../../sequelize");
const SCORES = require("../config/collectionSeoScores");
const { isEmpty, startsWith } = require("lodash");
const { constructRegexWithSpecialCharacters, calculateIsOptimized, normalizeString } = require("./helper");
const imageOptimization = require("storeseo-enums/imageOptimization");
const { METAFIELD_KEYS } = require("storeseo-enums/metafields");

class CollectionScoreCalculations {
  /**
   * Get collection and products featured images from DB
   * @param {object} collection
   * @returns {Promise<object[]>}
   */
  getCollectionAndProductsFeatureImages = async (collection) => {
    const CollectionService = require("../services/collections/CollectionService");

    const collectionImage = collection?.image;
    // Get collection with products
    const collectionWithProducts = await CollectionService.getByCondition(
      Number(collection.shop_id),
      {
        id: Number(collection.id),
      },
      true,
      [
        {
          model: sequelize.model("Product"),
          as: "products",
          include: {
            model: sequelize.model("ProductImage"),
            as: "featuredImage",
          },
        },
      ]
    );
    // Get all products featured images
    const collectionProductsImages =
      collectionWithProducts?.products?.map((product) => product?.featuredImage).filter((img) => img !== null) || [];

    // Combine all images from collection and products featured images
    const images = [...collectionProductsImages];
    if (collectionImage) images.push(collectionImage);
    return images;
  };
  /**
   * Calculate unique focus keyword score
   * @param {number} shopId - shop id
   * @param {string} keyword
   * @param {number | null} collectionId - DB collection id
   * @returns {Promise<number>}
   */
  calculateUniqueFocusKeyword = async (shopId, keyword, collectionId = null) => {
    if (!isEmpty(keyword)) {
      const count = await Collection.count({
        where: {
          id: { [Op.not]: collectionId },
          shop_id: shopId,
          [Op.and]: sequelize.where(
            sequelize.fn("LOWER", sequelize.col("focus_keyword")),
            keyword.trim().toLowerCase()
          ),
        },
      });
      return count === 0 ? SCORES.UNIQUE_FOCUS_KEYWORD : 0;
    }
    return 0;
  };
  /**
   * Get title tag from collection
   * @param {object} collection
   * @returns {string}
   */
  getTitleTagFromCollection = (collection) => {
    if (!isEmpty(collection.meta)) {
      const metaTitle = collection.meta.find((metaItem) => metaItem.key === METAFIELD_KEYS.TITLE_TAG);

      if (metaTitle) {
        return metaTitle.value;
      }
    }
    return collection.title;
  };

  stripTags(string) {
    return string
      ?.replace(/(<([^>]+)>)/gi, "")
      .replace(/ +(?= )/g, "")
      .trim();
  }

  /**
   * Get description tag from collection
   * @param {object} collection
   * @returns {string}
   */
  getDescriptionTagFromCollection = (collection) => {
    if (!isEmpty(collection.meta)) {
      const metaDescription = collection.meta.find((metaItem) => metaItem.key === METAFIELD_KEYS.DESCRIPTION_TAG);

      if (metaDescription) {
        return metaDescription.value;
      }
    }
    return this.stripTags(collection.description)?.trim()?.substring(0, 165);
  };
  /**
   * Calculate unique title score
   * @param {object} collection - collection object from DB
   * @returns {number}
   */
  calculateFocusKeywordInMetaTitle = (collection) => {
    const keyword = collection?.focus_keyword;
    if (!isEmpty(keyword)) {
      const titleTag = this.getTitleTagFromCollection(collection);

      if (titleTag.toLowerCase().includes(keyword.toLowerCase())) {
        return SCORES.FOCUS_KEYWORD_IN_META_TITLE;
      }
    }
    return 0;
  };
  /**
   * Calculate meta title length score based on character limit
   * @param {object} collection
   * @param {number} minLength
   * @param {number} maxLength
   * @returns {number}
   */
  calculateMetaDescLength = (collection, minLength, maxLength) => {
    const partialScore = Math.round(SCORES.META_DESC_WITHIN_CHAR_LIMIT * 0.6);
    const metaDescription = this.getDescriptionTagFromCollection(collection);

    if (metaDescription) {
      const { length } = metaDescription;

      if (length < minLength) {
        return Math.round((SCORES.META_DESC_WITHIN_CHAR_LIMIT * length) / maxLength);
      } else if (length >= minLength && length <= maxLength) {
        return Math.round(
          SCORES.META_DESC_WITHIN_CHAR_LIMIT * (0.7 + (0.3 * (length - minLength)) / (maxLength - minLength))
        );
      } else {
        return partialScore;
      }
    }

    return 0;
  };
  /**
   * Calculate collection meta title length score based on character limit
   * @param {object} collection
   * @param {number} maxLength
   * @returns {number}
   */
  calculateMetaTitleLength = (collection, maxLength) => {
    const partialScore = Math.round(SCORES.META_TITLE_WITHIN_CHAR_LIMIT * 0.6);

    const titleTag = this.getTitleTagFromCollection(collection);

    if (titleTag.length > 0 && titleTag.length <= maxLength) {
      const score = Math.round((SCORES.META_TITLE_WITHIN_CHAR_LIMIT * titleTag.length) / maxLength);
      if (score < 7 && titleTag.length >= 30) return 7;
      else return score;
    }
    if (titleTag.length > maxLength) {
      return partialScore;
    }
    return 0;
  };

  /**
   * Calculate focus keyword in image alt text score. The score is also dependent on the collection products featured image alt text.
   * @param {object} collection
   * @param {string} keyword
   * @returns {Promise<number>}
   */
  calculateFocusKeywordInAltText = async (collection, keyword) => {
    try {
      const images = await this.getCollectionAndProductsFeatureImages(collection);
      const imagesLength = images?.length || 0;

      if (!imagesLength) {
        return SCORES.FOCUS_KEYWORD_IN_IMG_ALT_TEXT;
      }

      if (isEmpty(keyword)) {
        return 0;
      }
      const score = images.some(
        (img) => !isEmpty(img?.alt_text) && img?.alt_text?.toLowerCase().includes(keyword?.toLowerCase())
      )
        ? SCORES.FOCUS_KEYWORD_IN_IMG_ALT_TEXT
        : 0;
      // return the final score
      // TODO: calculate total images with alt text containing the keyword and return a partial score
      return score;
    } catch (error) {
      console.error(`Error in calculateFocusKeywordInAltText for ${collection.title}`, error);
      return 0;
    }
  };
  /**
   * Calculate focus keyword in title score
   * @param {string} title
   * @param {string} keyword
   * @returns {number}
   */
  calculateFocusKeywordInTitle = (title, keyword) => {
    if (!isEmpty(keyword)) {
      return title.toLowerCase().includes(keyword.toLowerCase()) ? SCORES.FOCUS_KEYWORD_IN_TITLE : 0;
    }
    return 0;
  };

  /**
   * Calculate focus keyword in meta description score
   * @param {object} collection
   * @param {string} keyword
   * @returns {number}
   */
  calculateFocusKeywordInMetaDesc = (collection, keyword) => {
    const metaDescription = this.getDescriptionTagFromCollection(collection);
    const keywordRegex = constructRegexWithSpecialCharacters(keyword);

    if (metaDescription && !isEmpty(keyword) && metaDescription.toLowerCase().match(keywordRegex))
      return SCORES.FOCUS_KEYWORD_IN_META_DESC;

    return 0;
  };
  /**
   * Calculate focus keyword in URL score
   * @param {string} handle
   * @param {string} keyword
   * @returns {number}
   */
  calculateFocusKeywordInURL = (handle, keyword) => {
    if (isEmpty(keyword) || isEmpty(handle)) {
      return 0;
    }

    const escapedKeyword = normalizeString(keyword).replace(/[.*+?^${}()|[\]\\]/g, "\\$&");

    // Create a regular expression pattern for the keyword
    const regexPattern = escapedKeyword
      .split(/\s+/) // Split keyword into words
      // .map((word) => `\\b${word}\\b`) // Add word boundaries to each word
      .join(".*"); // Allow any characters in between words

    // Create a case-insensitive regular expression
    const regex = new RegExp(regexPattern, "i");

    // Test if the keyword pattern exists in the URL
    return regex.test(handle.normalize("NFD")) ? SCORES.FOCUS_KEYWORD_IN_URL : 0;
  };
  /**
   * Calculate focus keyword at the beginning of meta title score
   * @param {object} collection
   * @param {string} keyword
   * @returns {number}
   */
  calculateFocusKeywordAtTheBeginningOfMetaTitle = (collection, keyword) => {
    if (!isEmpty(keyword)) {
      const titleTag = this.getTitleTagFromCollection(collection);

      if (titleTag) {
        if (startsWith(titleTag.toLowerCase(), keyword.toLowerCase())) {
          return SCORES.FOCUS_KEYWORD_AT_THE_BEGINNING_OF_META_TITLE;
        }
      }
    }
    return 0;
  };
  /**
   * Calculate alt text in collection image score. The score is also dependent on the collection products featured image alt text.
   * @param {object} collection
   * @returns {Promise<number>}
   */
  calculateAltTextInImg = async (collection) => {
    try {
      const images = await this.getCollectionAndProductsFeatureImages(collection);
      const imagesLength = images?.length || 0;
      if (!imagesLength) return SCORES.ALT_TEXT_IN_IMG;

      const score = images.some((img) => isEmpty(img?.alt_text)) ? 0 : SCORES.ALT_TEXT_IN_IMG;
      // return the final score
      // TODO: calculate total images with alt text and return a partial score
      return score;
    } catch (error) {
      console.error(`Error in calculateAltTextInImg for ${collection.title}`, error);
      return 0;
    }
  };

  /**
   * Calculate image optimization score. The score is also dependent on the collection products featured image optimization status.
   * @param {object} collection
   * @returns {Promise<number>}
   */
  calculateAllImageOptimized = async (collection) => {
    try {
      const images = await this.getCollectionAndProductsFeatureImages(collection);
      const imagesLength = images?.length || 0;

      if (!imagesLength) return SCORES.OPTIMIZED_IMAGE;

      const score = images.some((image) => {
        const isOptimized = image?.optimization_status === imageOptimization.OPTIMIZED || false;
        const isAlreadyOptimized = image?.optimization_status === imageOptimization.ALREADY_OPTIMIZED || false;
        return !(isOptimized || isAlreadyOptimized);
      })
        ? 0
        : SCORES.OPTIMIZED_IMAGE;
      // return the final score
      // TODO: calculate total images optimized and return a partial score
      return score;
    } catch (error) {
      console.error(`Error in calculateAllImageOptimized for ${collection.title}`, error);
      return 0;
    }
  };

  /**
   * Total score of collection analysis
   * @returns {number}
   */
  totalScore = () => {
    return Object.values(SCORES).reduce((a, b) => a + b, 0);
  };
  /**
   * Calculate collection analysis scores
   * @param {object} analysis
   * @returns {object}
   */
  calculateAnalysisScores = (analysis) => {
    return Object.entries(analysis).reduce(
      (output, val2) => {
        const key2 = val2[0];
        const isOptimized = calculateIsOptimized(val2[1], SCORES[key2.toUpperCase()]);

        return {
          issues: output.issues + (!isOptimized ? 1 : 0),
          passed: output.passed + (isOptimized ? 1 : 0),
          point: output.point + val2[1],
        };
      },
      { passed: 0, issues: 0, point: 0 }
    );
  };
}
module.exports = new CollectionScoreCalculations();
