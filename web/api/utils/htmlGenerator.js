const { readFileSync } = require("fs");
const path = require("path");
// const ShopService = require("../services/ShopService");
// const ProductService = require("../services/ProductService");
// const PageService = require("../services/PageService");
// const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
// const { truncate } = require("lodash");

const STATIC_PATH =
  process.env.NODE_ENV === "production" ? `${process.cwd()}/frontend/dist/` : `${process.cwd()}/frontend/`;

const filePaths = {
  index: STATIC_PATH + "index.html",
  subscription: STATIC_PATH + "html_skeletons/subscription.html",
  onboard: STATIC_PATH + "onboard.html",
};

let INDEX_HTML = readFileSync(path.resolve(__dirname, filePaths.index), { encoding: "utf-8" });
INDEX_HTML = INDEX_HTML.replace("SHOPIFY_API_KEY", process.env.SHOPIFY_API_KEY);

if (process.env.NODE_ENV !== "production") {
  INDEX_HTML = INDEX_HTML.replace(
    "<!-- VITE PLUGING CODE HERE -->",
    `<script type="module">
  import RefreshRuntime from "/@react-refresh"
  RefreshRuntime.injectIntoGlobalHook(window)
  window.$RefreshReg$ = () => {}
  window.$RefreshSig$ = () => (type) => type
  window.__vite_plugin_react_preamble_installed__ = true
  </script>`
  );
}

const DYNAMIC_CONTENT_COMMENT = `<!-- INSERT DYNAMIC HTML HERE BASE ON ROUTE -->`;

const [INDEX_HTML_FIRST_PART, INDEX_HTML_SECOND_PART] = INDEX_HTML.split(DYNAMIC_CONTENT_COMMENT);

const PAGE_SKELETON = `
<!--<div class="ss-header"><div style="padding: 0 var(&#45;&#45;p-space-600) 0 0;"><div style="display: flex; align-items: center; justify-content: space-between; flex-wrap: wrap; gap: 12px;"><div class="ss-nav-wrap"><button class="Polaris-Button Polaris-Button&#45;&#45;iconOnly" type="button"><span class="Polaris-Button__Content"><span class="Polaris-Button__Icon"><span class="Polaris-Icon"><span class="Polaris-Text&#45;&#45;root Polaris-Text&#45;&#45;visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="M3 4.75a.75.75 0 0 1 .75-.75h12.5a.75.75 0 0 1 0 1.5h-12.5a.75.75 0 0 1-.75-.75Z"></path><path fill-rule="evenodd" d="M3 10a.75.75 0 0 1 .75-.75h12.5a.75.75 0 0 1 0 1.5h-12.5a.75.75 0 0 1-.75-.75Z"></path><path fill-rule="evenodd" d="M3 15.25a.75.75 0 0 1 .75-.75h12.5a.75.75 0 0 1 0 1.5h-12.5a.75.75 0 0 1-.75-.75Z"></path></svg></span></span></span></button><ul class="ss-nav "><li class="nav-item active"><h1 type="button" class="Polaris-Link Polaris-Link&#45;&#45;monochrome Polaris-Link&#45;&#45;removeUnderline">Dashboard</h1></li><li class="nav-item undefined"><button type="button" class="Polaris-Link Polaris-Link&#45;&#45;monochrome Polaris-Link&#45;&#45;removeUnderline">Products</button></li><li class="nav-item undefined"><button type="button" class="Polaris-Link Polaris-Link&#45;&#45;monochrome Polaris-Link&#45;&#45;removeUnderline"><div class="Polaris-InlineStack" style="&#45;&#45;pc-inline-stack-wrap: wrap; &#45;&#45;pc-inline-stack-gap-xs: var(&#45;&#45;p-space-100);">Analytics</div></button></li><li class="nav-item undefined"><button type="button" class="Polaris-Link Polaris-Link&#45;&#45;monochrome Polaris-Link&#45;&#45;removeUnderline">Pages</button></li><li class="nav-item undefined"><button type="button" class="Polaris-Link Polaris-Link&#45;&#45;monochrome Polaris-Link&#45;&#45;removeUnderline">Blog Posts</button></li><li class="nav-item undefined"><button type="button" class="Polaris-Link Polaris-Link&#45;&#45;monochrome Polaris-Link&#45;&#45;removeUnderline">Reports</button></li><li class="nav-item undefined"><button type="button" class="Polaris-Link Polaris-Link&#45;&#45;monochrome Polaris-Link&#45;&#45;removeUnderline">Sitemaps</button></li><li class="nav-item undefined"><button type="button" class="Polaris-Link Polaris-Link&#45;&#45;monochrome Polaris-Link&#45;&#45;removeUnderline">Settings</button></li></ul></div><div style="display: flex; align-items: center; justify-content: center; flex-wrap: wrap; gap: 12px;"><div><div style="position: relative;"><span class=""><button class="Polaris-Button Polaris-Button&#45;&#45;monochrome Polaris-Button&#45;&#45;plain Polaris-Button&#45;&#45;iconOnly" type="button" tabindex="0" aria-describedby=":r2:" data-polaris-tooltip-activator="true" aria-controls=":r1:" aria-owns=":r1:" aria-expanded="false" aria-haspopup="false"><span class="Polaris-Button__Content"><span class="Polaris-Button__Icon"><span class="Polaris-Icon"><span class="Polaris-Text&#45;&#45;root Polaris-Text&#45;&#45;visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="m7.252 14.424-2.446-.281c-1.855-.213-2.38-2.659-.778-3.616l.065-.038a2.887 2.887 0 0 0 1.407-2.48v-.509a4.5 4.5 0 0 1 9 0v.51c0 1.016.535 1.958 1.408 2.479l.065.038c1.602.957 1.076 3.403-.778 3.616l-2.543.292v.365a2.7 2.7 0 0 1-5.4 0v-.376Zm3.9.076h-2.4v.3a1.2 1.2 0 0 0 2.4 0v-.3Zm-3.152-1.5h4l3.024-.348a.452.452 0 0 0 .18-.837l-.065-.038a4.414 4.414 0 0 1-.747-.562 4.387 4.387 0 0 1-1.392-3.205v-.51a3 3 0 0 0-6 0v.51a4.387 4.387 0 0 1-2.138 3.767l-.065.038a.452.452 0 0 0 .18.838l3.023.347Z"></path></svg></span></span></span></button></span><span style="width: 10px; height: 10px; background: var(&#45;&#45;p-color-bg-inverse); border-radius: var(&#45;&#45;p-border-radius-full); position: absolute; top: 0px; right: 0px;"></span></div></div><button class="Polaris-Button" type="button"><span class="Polaris-Button__Content"><span class="Polaris-Button__Icon"><span class="Polaris-Icon"><span class="Polaris-Text&#45;&#45;root Polaris-Text&#45;&#45;visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="M6.75 4.5c-1.283 0-2.213 1.025-2.044 2.127.384 2.498 1.296 4.459 2.707 5.89 1.41 1.43 3.373 2.389 5.96 2.786 1.101.17 2.126-.76 2.126-2.044v-.727a.25.25 0 0 0-.187-.242l-1.9-.498a.25.25 0 0 0-.182.022l-1.067.576c-.69.373-1.638.492-2.422-.056a8.678 8.678 0 0 1-2.071-2.09c-.542-.787-.423-1.735-.045-2.428l.57-1.047a.252.252 0 0 0 .022-.182l-.498-1.9a.25.25 0 0 0-.242-.187h-.726Zm-3.526 2.355c-.334-2.174 1.497-3.856 3.527-3.855h.726a1.75 1.75 0 0 1 1.693 1.306l.498 1.9c.113.43.058.885-.153 1.276l-.001.002-.572 1.05c-.191.351-.169.668-.036.86a7.184 7.184 0 0 0 1.694 1.71c.187.13.498.156.85-.034l1.067-.576a1.75 1.75 0 0 1 1.276-.153l1.9.498a1.75 1.75 0 0 1 1.306 1.693v.727c0 2.03-1.68 3.86-3.854 3.527-2.838-.436-5.12-1.511-6.8-3.216-1.68-1.703-2.701-3.978-3.121-6.715Z"></path></svg></span></span><span class="Polaris-Button__Text">Get Free SEO Consultation</span></span></button></div></div></div></div>-->
<div class="Polaris-BlockStack" style="--pc-block-stack-inline-align: center; --pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"> <div class="Polaris-Box" role="status" aria-label="Page loading" style="--pc-box-max-width: var(--pc-skeleton-page-max-width); --pc-box-padding-block-end-xs: var(--p-space-0); --pc-box-padding-block-start-xs: var(--p-space-0); --pc-box-padding-inline-start-sm: var(--p-space-600); --pc-box-padding-inline-end-sm: var(--p-space-600); --pc-box-width: 100%;"> <div class="Polaris-BlockStack" style="--pc-block-stack-order: column;"> <div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-end-md: var(--p-space-500); --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-start-md: var(--p-space-500); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-start-sm: var(--p-space-0); --pc-box-padding-inline-end-xs: var(--p-space-400); --pc-box-padding-inline-end-sm: var(--p-space-0); --pc-box-width: 100%;"> <div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-block-align: center; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-400);"> <div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-400);"> <div class="Polaris-Page-Header__TitleWrapper"> <h1 class="Polaris-Header-Title"></h1> </div></div><div class="Polaris-Box" id="SkeletonPage-PrimaryAction" style="--pc-box-background: var(--p-color-bg-fill-tertiary); --pc-box-border-radius: var(--p-border-radius-100); --pc-box-min-height: 2.25rem; --pc-box-min-width: 6.25rem;"> </div></div></div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-200); --pc-box-width: 100%;"> <div class="Polaris-Layout"> <div class="Polaris-Layout__Section"> <div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"> <div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"> <div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden; --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"> <div class="Polaris-SkeletonBodyText__SkeletonBodyTextContainer"> <div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div></div></div></div></div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"> <div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"> <div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden; --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"> <div class="Polaris-SkeletonDisplayText__DisplayText Polaris-SkeletonDisplayText--sizeSmall"> </div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"> </div><div class="Polaris-SkeletonBodyText__SkeletonBodyTextContainer"> <div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div></div></div></div></div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"> <div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"> <div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden; --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"> <div class="Polaris-SkeletonDisplayText__DisplayText Polaris-SkeletonDisplayText--sizeSmall"> </div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"> </div><div class="Polaris-SkeletonBodyText__SkeletonBodyTextContainer"> <div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div></div></div></div></div></div><div class="Polaris-Layout__Section Polaris-Layout__Section--oneThird"> <div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"> <div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"> <div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden; --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"> <div class="Polaris-SkeletonDisplayText__DisplayText Polaris-SkeletonDisplayText--sizeSmall"> </div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"> </div><div class="Polaris-SkeletonBodyText__SkeletonBodyTextContainer"> <div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div></div></div></div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"> </div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"> <div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden; --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"> <div class="Polaris-SkeletonBodyText__SkeletonBodyTextContainer"> <div class="Polaris-SkeletonBodyText"></div></div></div></div></div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"> <div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"> <div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden; --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"> <div class="Polaris-SkeletonDisplayText__DisplayText Polaris-SkeletonDisplayText--sizeSmall"> </div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"> </div><div class="Polaris-SkeletonBodyText__SkeletonBodyTextContainer"> <div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div></div></div></div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"> </div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"> <div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden; --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"> <div class="Polaris-SkeletonBodyText__SkeletonBodyTextContainer"> <div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div></div></div></div></div></div></div></div></div></div></div>
`;

const DASHBOARD_SKELETON = `
<div class="Polaris-Page"><div class="Polaris-Page__Content"><div class="Polaris-BlockStack" style="--pc-block-stack-order:column;--pc-block-stack-gap-xs:var(--p-space-400)"><div class="Polaris-BlockStack" style="--pc-block-stack-align:center;--pc-block-stack-order:column;--pc-block-stack-gap-xs:var(--p-space-200)"><h1 class="Polaris-Text--root Polaris-Text--heading2xl">Welcome to StoreSEO</h1><p class="Polaris-Text--root Polaris-Text--bodyLg">Drive sales and traffic with the power of StoreSEO and improve your search engine ranking</p></div><div class="Polaris-BlockStack" style="--pc-block-stack-order:column;--pc-block-stack-gap-xs:var(--p-space-400)"><div class="Polaris-InlineStack" style="--pc-inline-stack-align:space-between;--pc-inline-stack-block-align:center;--pc-inline-stack-wrap:wrap;--pc-inline-stack-gap-xs:var(--p-space-400);--pc-inline-stack-flex-direction-xs:row"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap:wrap;--pc-inline-stack-gap-xs:var(--p-space-200);--pc-inline-stack-flex-direction-xs:row"><h3 class="Polaris-Text--root Polaris-Text--headingLg">Store Details</h3></div><button class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--iconWithText Polaris-Button--disabled" aria-disabled="true" type="button" tabindex="-1"><span class="Polaris-Button__Icon"><span class="Polaris-Icon"><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="M7.75 3.5a.75.75 0 0 0-1.5 0v.407a3.075 3.075 0 0 0-.702.252 3.75 3.75 0 0 0-1.64 1.639c-.226.444-.32.924-.365 1.47-.043.531-.043 1.187-.043 2v1.464c0 .813 0 1.469.043 2 .045.546.14 1.026.366 1.47a3.75 3.75 0 0 0 1.639 1.64c.444.226.924.32 1.47.365.531.043 1.187.043 2 .043h3.383c.323 0 .542 0 .735-.02a3.75 3.75 0 0 0 3.344-3.344c.02-.193.02-.412.02-.735v-2.883c0-.813 0-1.469-.043-2-.045-.546-.14-1.026-.366-1.47a3.75 3.75 0 0 0-1.639-1.64 3.076 3.076 0 0 0-.702-.251v-.407a.75.75 0 0 0-1.5 0v.259c-.373-.009-.794-.009-1.268-.009h-1.964c-.474 0-.895 0-1.268.009v-.259Zm-1.521 1.995c.197-.1.458-.17.912-.207.462-.037 1.057-.038 1.909-.038h1.9c.853 0 1.447 0 1.91.038.453.037.714.107.912.207.423.216.767.56.983.984.1.197.17.458.207.912.014.18.024.38.029.609h-9.982c.006-.228.015-.429.03-.61.036-.453.106-.714.206-.911a2.25 2.25 0 0 1 .984-.984Zm-1.229 4.005v1.2c0 .853 0 1.447.038 1.91.037.453.107.714.207.912.216.423.56.767.984.983.197.1.458.17.912.207.462.037 1.057.038 1.909.038h3.306c.385 0 .52-.001.626-.012a2.25 2.25 0 0 0 2.006-2.006c.011-.106.012-.241.012-.626v-2.606h-10Z"></path></svg></span></span><span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">All time</span></button></div><div class="Polaris-Grid" style="--pc-grid-columns-xs:1;--pc-grid-columns-sm:2;--pc-grid-columns-md:2;--pc-grid-columns-lg:4;--pc-grid-columns-xl:4"><div class="Polaris-Grid-Cell"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index:32;--pc-shadow-bevel-content-xs:'';--pc-shadow-bevel-box-shadow-xs:var(--p-shadow-100);--pc-shadow-bevel-border-radius-xs:var(--p-border-radius-300)"><div class="Polaris-Box" style="--pc-box-background:var(--p-color-bg-surface);--pc-box-min-height:100%;--pc-box-overflow-x:clip;--pc-box-overflow-y:clip;--pc-box-padding-block-start-xs:var(--p-space-400);--pc-box-padding-block-end-xs:var(--p-space-400);--pc-box-padding-inline-start-xs:var(--p-space-400);--pc-box-padding-inline-end-xs:var(--p-space-400)"><div class="Polaris-InlineStack" style="--pc-inline-stack-align:space-between;--pc-inline-stack-block-align:center;--pc-inline-stack-wrap:wrap;--pc-inline-stack-gap-xs:var(--p-space-200);--pc-inline-stack-flex-direction-xs:row"><div class="Polaris-BlockStack" style="--pc-block-stack-order:column;--pc-block-stack-gap-xs:var(--p-space-200)"><div class="skeleton-wrapper"><div class="skeleton-box" style="width:40px;height:22px"></div></div><h6 class="Polaris-Text--root Polaris-Text--headingSm">Total products</h6></div><div class="skeleton-wrapper"><div class="skeleton-box" style="width:50px;height:50px;border-radius:50px"></div></div></div></div></div></div><div class="Polaris-Grid-Cell"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index:32;--pc-shadow-bevel-content-xs:'';--pc-shadow-bevel-box-shadow-xs:var(--p-shadow-100);--pc-shadow-bevel-border-radius-xs:var(--p-border-radius-300)"><div class="Polaris-Box" style="--pc-box-background:var(--p-color-bg-surface);--pc-box-min-height:100%;--pc-box-overflow-x:clip;--pc-box-overflow-y:clip;--pc-box-padding-block-start-xs:var(--p-space-400);--pc-box-padding-block-end-xs:var(--p-space-400);--pc-box-padding-inline-start-xs:var(--p-space-400);--pc-box-padding-inline-end-xs:var(--p-space-400)"><div class="Polaris-InlineStack" style="--pc-inline-stack-align:space-between;--pc-inline-stack-block-align:center;--pc-inline-stack-wrap:wrap;--pc-inline-stack-gap-xs:var(--p-space-200);--pc-inline-stack-flex-direction-xs:row"><div class="Polaris-BlockStack" style="--pc-block-stack-order:column;--pc-block-stack-gap-xs:var(--p-space-200)"><div class="skeleton-wrapper"><div class="skeleton-box" style="width:40px;height:22px"></div></div><h6 class="Polaris-Text--root Polaris-Text--headingSm">Overall score</h6></div><div class="skeleton-wrapper"><div class="skeleton-box" style="width:50px;height:50px;border-radius:50px"></div></div></div></div></div></div><div class="Polaris-Grid-Cell"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index:32;--pc-shadow-bevel-content-xs:'';--pc-shadow-bevel-box-shadow-xs:var(--p-shadow-100);--pc-shadow-bevel-border-radius-xs:var(--p-border-radius-300)"><div class="Polaris-Box" style="--pc-box-background:var(--p-color-bg-surface);--pc-box-min-height:100%;--pc-box-overflow-x:clip;--pc-box-overflow-y:clip;--pc-box-padding-block-start-xs:var(--p-space-400);--pc-box-padding-block-end-xs:var(--p-space-400);--pc-box-padding-inline-start-xs:var(--p-space-400);--pc-box-padding-inline-end-xs:var(--p-space-400)"><div class="Polaris-InlineStack" style="--pc-inline-stack-align:space-between;--pc-inline-stack-block-align:center;--pc-inline-stack-wrap:wrap;--pc-inline-stack-gap-xs:var(--p-space-200);--pc-inline-stack-flex-direction-xs:row"><div class="Polaris-BlockStack" style="--pc-block-stack-order:column;--pc-block-stack-gap-xs:var(--p-space-200)"><div class="skeleton-wrapper"><div class="skeleton-box" style="width:40px;height:22px"></div></div><h6 class="Polaris-Text--root Polaris-Text--headingSm">SEO issues</h6></div><div class="skeleton-wrapper"><div class="skeleton-box" style="width:50px;height:50px;border-radius:50px"></div></div></div></div></div></div><div class="Polaris-Grid-Cell"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index:32;--pc-shadow-bevel-content-xs:'';--pc-shadow-bevel-box-shadow-xs:var(--p-shadow-100);--pc-shadow-bevel-border-radius-xs:var(--p-border-radius-300)"><div class="Polaris-Box" style="--pc-box-background:var(--p-color-bg-surface);--pc-box-min-height:100%;--pc-box-overflow-x:clip;--pc-box-overflow-y:clip;--pc-box-padding-block-start-xs:var(--p-space-400);--pc-box-padding-block-end-xs:var(--p-space-400);--pc-box-padding-inline-start-xs:var(--p-space-400);--pc-box-padding-inline-end-xs:var(--p-space-400)"><div class="Polaris-InlineStack" style="--pc-inline-stack-align:space-between;--pc-inline-stack-block-align:center;--pc-inline-stack-wrap:wrap;--pc-inline-stack-gap-xs:var(--p-space-200);--pc-inline-stack-flex-direction-xs:row"><div class="Polaris-BlockStack" style="--pc-block-stack-order:column;--pc-block-stack-gap-xs:var(--p-space-200)"><div class="skeleton-wrapper"><div class="skeleton-box" style="width:40px;height:22px"></div></div><h6 class="Polaris-Text--root Polaris-Text--headingSm">Already optimized</h6></div><div class="skeleton-wrapper"><div class="skeleton-box" style="width:50px;height:50px;border-radius:50px"></div></div></div></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index:32;--pc-shadow-bevel-content-xs:'';--pc-shadow-bevel-box-shadow-xs:var(--p-shadow-100);--pc-shadow-bevel-border-radius-xs:var(--p-border-radius-300)"><div class="Polaris-Box" style="--pc-box-background:var(--p-color-bg-surface);--pc-box-min-height:100%;--pc-box-overflow-x:clip;--pc-box-overflow-y:clip;--pc-box-padding-block-start-xs:var(--p-space-400);--pc-box-padding-block-end-xs:var(--p-space-400);--pc-box-padding-inline-start-xs:var(--p-space-400);--pc-box-padding-inline-end-xs:var(--p-space-400)"><div class="Polaris-BlockStack" style="--pc-block-stack-order:column;--pc-block-stack-gap-xs:var(--p-space-300)"><h3 class="Polaris-Text--root Polaris-Text--headingSm">Get started</h3><p class="Polaris-Text--root Polaris-Text--subdued">Optimize your product for search engines instantly and rank them on top of the search results</p><div><button class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantPrimary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--disabled" aria-disabled="true" type="button" tabindex="-1"><span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Optimize your products</span></button></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index:32;--pc-shadow-bevel-content-xs:'';--pc-shadow-bevel-box-shadow-xs:var(--p-shadow-100);--pc-shadow-bevel-border-radius-xs:var(--p-border-radius-300)"><div class="Polaris-Box" style="--pc-box-background:var(--p-color-bg-surface);--pc-box-min-height:100%;--pc-box-overflow-x:clip;--pc-box-overflow-y:clip;--pc-box-padding-block-start-xs:var(--p-space-400);--pc-box-padding-block-end-xs:var(--p-space-400);--pc-box-padding-inline-start-xs:var(--p-space-400);--pc-box-padding-inline-end-xs:var(--p-space-400)"><div class="Polaris-BlockStack" style="--pc-block-stack-order:column;--pc-block-stack-gap-xs:var(--p-space-300)"><p class="Polaris-Text--root Polaris-Text--subdued">To enable some advanced features (i.e., SEO Schema, Google verification, Noindex/Nofollow), you need to enable the App Embed for StoreSEO from your Shopify settings. Go to the settings page and follow the instructions to enable it.</p><div><button class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--disabled" aria-disabled="true" type="button" tabindex="-1"><span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">App Embed Settings</span></button></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index:32;--pc-shadow-bevel-content-xs:'';--pc-shadow-bevel-box-shadow-xs:var(--p-shadow-100);--pc-shadow-bevel-border-radius-xs:var(--p-border-radius-300)"><div class="Polaris-Box" style="--pc-box-background:var(--p-color-bg-surface);--pc-box-min-height:100%;--pc-box-overflow-x:clip;--pc-box-overflow-y:clip;--pc-box-padding-block-start-xs:var(--p-space-400);--pc-box-padding-block-end-xs:var(--p-space-400);--pc-box-padding-inline-start-xs:var(--p-space-400);--pc-box-padding-inline-end-xs:var(--p-space-400)"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs:var(--p-space-400)">🎉 What's New</div><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs:var(--p-space-400);--pc-bleed-margin-inline-end-xs:var(--p-space-400)"><hr class="Polaris-Divider" style="border-block-start:var(--p-border-width-025) solid var(--p-color-border-secondary)"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order:column"><div class="Polaris-Box" style="--pc-box-padding-block-start-xs:var(--p-space-200);--pc-box-padding-block-end-xs:var(--p-space-200)"><div class="Polaris-InlineStack" style="--pc-inline-stack-align:space-between;--pc-inline-stack-block-align:center;--pc-inline-stack-wrap:wrap;--pc-inline-stack-gap-xs:var(--p-space-200);--pc-inline-stack-flex-direction-xs:row"><p class="Polaris-Text--root Polaris-Text--subdued">Try our AI Content Optimizer to make your meta title, meta description, and tags SEO-friendly with just a single click</p><div class="Polaris-Box" align="end" style="--pc-box-width:150px"><button class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--disabled" aria-disabled="true" type="button" tabindex="-1"><span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Check it out</span></button></div></div></div><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs:var(--p-space-400);--pc-bleed-margin-inline-end-xs:var(--p-space-400)"><hr class="Polaris-Divider" style="border-block-start:var(--p-border-width-025) solid var(--p-color-border-secondary)"></div><div class="Polaris-Box" style="--pc-box-padding-block-start-xs:var(--p-space-200);--pc-box-padding-block-end-xs:var(--p-space-200)"><div class="Polaris-InlineStack" style="--pc-inline-stack-align:space-between;--pc-inline-stack-block-align:center;--pc-inline-stack-wrap:wrap;--pc-inline-stack-gap-xs:var(--p-space-200);--pc-inline-stack-flex-direction-xs:row"><p class="Polaris-Text--root Polaris-Text--subdued">Optimize your store images instantly with Image Optimizer &amp; skyrocket your page speed</p><div class="Polaris-Box" align="end" style="--pc-box-width:150px"><button class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--disabled" aria-disabled="true" type="button" tabindex="-1"><span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Check it out</span></button></div></div></div><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs:var(--p-space-400);--pc-bleed-margin-inline-end-xs:var(--p-space-400)"><hr class="Polaris-Divider" style="border-block-start:var(--p-border-width-025) solid var(--p-color-border-secondary)"></div><div class="Polaris-Box" style="--pc-box-padding-block-start-xs:var(--p-space-200);--pc-box-padding-block-end-xs:var(--p-space-200)"><div class="Polaris-InlineStack" style="--pc-inline-stack-align:space-between;--pc-inline-stack-block-align:center;--pc-inline-stack-wrap:wrap;--pc-inline-stack-gap-xs:var(--p-space-200);--pc-inline-stack-flex-direction-xs:row"><p class="Polaris-Text--root Polaris-Text--subdued">Generate an HTML Sitemap page instantly on your store &amp; help your visitors find all of your pages in one place</p><div class="Polaris-Box" align="end" style="--pc-box-width:150px"><button class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--disabled" aria-disabled="true" type="button" tabindex="-1"><span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Check it out</span></button></div></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index:32;--pc-shadow-bevel-content-xs:'';--pc-shadow-bevel-box-shadow-xs:var(--p-shadow-100);--pc-shadow-bevel-border-radius-xs:var(--p-border-radius-300)"><div class="Polaris-Box" style="--pc-box-background:var(--p-color-bg-surface);--pc-box-min-height:100%;--pc-box-overflow-x:clip;--pc-box-overflow-y:clip;--pc-box-padding-block-start-xs:var(--p-space-0);--pc-box-padding-block-end-xs:var(--p-space-0);--pc-box-padding-inline-start-xs:var(--p-space-0);--pc-box-padding-inline-end-xs:var(--p-space-0)"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap:nowrap;--pc-inline-stack-flex-direction-xs:row"><div style="height:180px;min-width:30%"><div class="skeleton-wrapper"><div class="skeleton-box" style="height:180px;width:100%"></div></div></div><div class="Polaris-Box" style="--pc-box-padding-block-start-xs:var(--p-space-400);--pc-box-padding-block-end-xs:var(--p-space-400);--pc-box-padding-inline-start-xs:var(--p-space-400);--pc-box-padding-inline-end-xs:var(--p-space-400)"><div class="Polaris-BlockStack" style="--pc-block-stack-order:column;--pc-block-stack-gap-xs:var(--p-space-200)"><div class="Polaris-InlineStack" style="--pc-inline-stack-block-align:center;--pc-inline-stack-wrap:wrap;--pc-inline-stack-gap-xs:var(--p-space-100);--pc-inline-stack-flex-direction-xs:row"><div class="Polaris-Box"><span class="Polaris-Icon Polaris-Icon--toneMagic"><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path d="M5.702 4.253a.625.625 0 0 1 1.096 0l.196.358c.207.378.517.688.895.895l.358.196a.625.625 0 0 1 0 1.097l-.358.196a2.25 2.25 0 0 0-.895.894l-.196.359a.625.625 0 0 1-1.096 0l-.196-.359a2.25 2.25 0 0 0-.895-.894l-.358-.196a.625.625 0 0 1 0-1.097l.358-.196a2.25 2.25 0 0 0 .895-.895l.196-.358Z"></path><path fill-rule="evenodd" d="M12.948 7.89c-.18-1.167-1.852-1.19-2.064-.029l-.03.164a3.756 3.756 0 0 1-3.088 3.031c-1.15.189-1.173 1.833-.03 2.054l.105.02a3.824 3.824 0 0 1 3.029 3.029l.032.165c.233 1.208 1.963 1.208 2.196 0l.025-.129a3.836 3.836 0 0 1 3.077-3.045c1.184-.216 1.12-1.928-.071-2.107a3.789 3.789 0 0 1-3.18-3.154Zm-.944 6.887a5.34 5.34 0 0 1 2.542-2.647 5.305 5.305 0 0 1-2.628-2.548 5.262 5.262 0 0 1-2.488 2.508 5.329 5.329 0 0 1 2.574 2.687Z"></path></svg></span></div><h3 class="Polaris-Text--root Polaris-Text--headingSm Polaris-Text--magic">Introducing Our Latest AI SEO Optimization Feature 🎉</h3></div><p class="Polaris-Text--root">Don't know how to optimize your products for SEO? Or want to speed up your workflow? Try our AI Content Optimizer to make your Meta Titles, Meta Descriptions, and Tags SEO-friendly within the blink of an eye.</p><div class="Polaris-Box"><button class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--disabled" aria-disabled="true" type="button" tabindex="-1"><span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Check it out</span></button></div></div></div></div></div></div></div></div></div>
`;

const SETTINGS_SKELETON = `
<div class="Polaris-Page"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-end-md: var(--p-space-600); --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-start-md: var(--p-space-600); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-start-sm: var(--p-space-0); --pc-box-padding-inline-end-xs: var(--p-space-400); --pc-box-padding-inline-end-sm: var(--p-space-0); position: relative;"><div class="Polaris-Page-Header--isSingleRow Polaris-Page-Header--mobileView Polaris-Page-Header--mediumTitle"><div class="Polaris-Page-Header__Row"><div class="Polaris-Page-Header__BreadcrumbWrapper"><div class="Polaris-Box Polaris-Box--printHidden" style="--pc-box-max-width: 100%; --pc-box-padding-inline-end-xs: var(--p-space-100);"><nav role="navigation"><button class="Polaris-Button Polaris-Button--iconOnly" type="button"><span class="Polaris-Button__Content"><span class="Polaris-Button__Icon"><span class="Polaris-Icon"><span class="Polaris-Text--root Polaris-Text--visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="M16.75 10a.75.75 0 0 1-.75.75h-9.69l2.72 2.72a.75.75 0 0 1-1.06 1.06l-4-4a.75.75 0 0 1 0-1.06l4-4a.75.75 0 0 1 1.06 1.06l-2.72 2.72h9.69a.75.75 0 0 1 .75.75Z"></path></svg></span></span></span></button></nav></div></div><div class="Polaris-Page-Header__TitleWrapper"><h1 class="Polaris-Header-Title">Settings</h1></div></div></div></div><div class=""><div class="settings-layout"><div class="settings-sidebar "><div class="mobile-nav"><div class="Polaris-InlineStack" style="--pc-inline-stack-block-align: center; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-400);"><button class="Polaris-Button Polaris-Button--iconOnly" type="button"><span class="Polaris-Button__Content"><span class="Polaris-Button__Icon"><span class="Polaris-Icon"><span class="Polaris-Text--root Polaris-Text--visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="M3 4.75a.75.75 0 0 1 .75-.75h12.5a.75.75 0 0 1 0 1.5h-12.5a.75.75 0 0 1-.75-.75Z"></path><path fill-rule="evenodd" d="M3 10a.75.75 0 0 1 .75-.75h12.5a.75.75 0 0 1 0 1.5h-12.5a.75.75 0 0 1-.75-.75Z"></path><path fill-rule="evenodd" d="M3 15.25a.75.75 0 0 1 .75-.75h12.5a.75.75 0 0 1 0 1.5h-12.5a.75.75 0 0 1-.75-.75Z"></path></svg></span></span></span></button><h4 class="Polaris-Text--root Polaris-Text--headingMd">General Settings</h4></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden;"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400); --pc-box-background: var(--p-color-bg-fill-disabled);"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200);"><span><span class="Polaris-Thumbnail Polaris-Thumbnail--sizeSmall"><img src="" alt=""></span></span><div style="width: 100%;"><div class="Polaris-SkeletonBodyText__SkeletonBodyTextContainer"><div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div></div></div></div></div><nav class="Polaris-Navigation"><div class="Polaris-Navigation__LogoContainer"><a data-polaris-unstyled="true" class="Polaris-Navigation__LogoLink" style="width: 104px;"><img alt="" src="" class="Polaris-Navigation__Logo" style="width: 104px;"></a></div><div class="Polaris-Navigation__PrimaryNavigation Polaris-Scrollable Polaris-Scrollable--vertical Polaris-Scrollable--horizontal" data-polaris-scrollable="true"><li class="Polaris-Navigation__ListItem"><div class="Polaris-Navigation__ItemWrapper"><div class="Polaris-Navigation__ItemInnerWrapper Polaris-Navigation__ItemInnerWrapper--selected"><a data-polaris-unstyled="true" class="Polaris-Navigation__Item Polaris-Navigation__Item--selected Polaris-Navigation--subNavigationActive" tabindex="0"><span class="Polaris-Navigation__Text">General Settings</span></a></div></div></li><li class="Polaris-Navigation__ListItem"><div class="Polaris-Navigation__ItemWrapper"><div class="Polaris-Navigation__ItemInnerWrapper"><a data-polaris-unstyled="true" class="Polaris-Navigation__Item" tabindex="0"><span class="Polaris-Navigation__Text">SEO Settings</span></a></div></div></li><li class="Polaris-Navigation__ListItem"><div class="Polaris-Navigation__ItemWrapper"><div class="Polaris-Navigation__ItemInnerWrapper"><a data-polaris-unstyled="true" class="Polaris-Navigation__Item" tabindex="0"><span class="Polaris-Navigation__Text">SEO Migrator</span></a></div></div></li><li class="Polaris-Navigation__ListItem"><div class="Polaris-Navigation__ItemWrapper"><div class="Polaris-Navigation__ItemInnerWrapper"><a data-polaris-unstyled="true" class="Polaris-Navigation__Item" tabindex="0"><span class="Polaris-Navigation__Text">Google Integration</span></a></div></div></li><li class="Polaris-Navigation__ListItem"><div class="Polaris-Navigation__ItemWrapper"><div class="Polaris-Navigation__ItemInnerWrapper"><a data-polaris-unstyled="true" class="Polaris-Navigation__Item" tabindex="0"><span class="Polaris-Navigation__Text">Google Analytics</span></a></div></div></li><li class="Polaris-Navigation__ListItem"><div class="Polaris-Navigation__ItemWrapper"><div class="Polaris-Navigation__ItemInnerWrapper"><a data-polaris-unstyled="true" class="Polaris-Navigation__Item" tabindex="0"><span class="Polaris-Navigation__Text">SEO Schema</span></a></div></div></li></div></nav></div></div></div><div class="settings-content"><div class="Polaris-Layout"><div class="Polaris-Layout__Section"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden; --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-SkeletonBodyText__SkeletonBodyTextContainer"><div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div></div></div></div></div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden; --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-SkeletonDisplayText__DisplayText Polaris-SkeletonDisplayText--sizeSmall"></div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"></div><div class="Polaris-SkeletonBodyText__SkeletonBodyTextContainer"><div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div></div></div></div></div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden; --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-SkeletonDisplayText__DisplayText Polaris-SkeletonDisplayText--sizeSmall"></div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"></div><div class="Polaris-SkeletonBodyText__SkeletonBodyTextContainer"><div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div></div></div></div></div></div><div class="Polaris-Layout__Section Polaris-Layout__Section--oneThird"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden; --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-SkeletonDisplayText__DisplayText Polaris-SkeletonDisplayText--sizeSmall"></div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"></div><div class="Polaris-SkeletonBodyText__SkeletonBodyTextContainer"><div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div></div></div></div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden; --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-SkeletonBodyText__SkeletonBodyTextContainer"><div class="Polaris-SkeletonBodyText"></div></div></div></div></div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden; --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-SkeletonDisplayText__DisplayText Polaris-SkeletonDisplayText--sizeSmall"></div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"></div><div class="Polaris-SkeletonBodyText__SkeletonBodyTextContainer"><div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div></div></div></div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden; --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-SkeletonBodyText__SkeletonBodyTextContainer"><div class="Polaris-SkeletonBodyText"></div><div class="Polaris-SkeletonBodyText"></div></div></div></div></div></div></div></div></div></div></div>
`;

const ITEM_FIX_SKELETON = `
<div class="Polaris-Page"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-end-md: var(--p-space-600); --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-start-md: var(--p-space-600); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-start-sm: var(--p-space-0); --pc-box-padding-inline-end-xs: var(--p-space-400); --pc-box-padding-inline-end-sm: var(--p-space-0); position: relative;"><div class=""><div class="Polaris-Page-Header__Row"><div class="Polaris-Page-Header__BreadcrumbWrapper"><div class="Polaris-Box Polaris-Box--printHidden" style="--pc-box-max-width: 100%; --pc-box-padding-inline-end-xs: var(--p-space-100);"><nav role="navigation"><button class="Polaris-Button Polaris-Button--iconOnly" aria-label="Products" type="button"><span class="Polaris-Button__Content"><span class="Polaris-Button__Icon"><span class="Polaris-Icon"><span class="Polaris-Text--root Polaris-Text--visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="M16.75 10a.75.75 0 0 1-.75.75h-9.69l2.72 2.72a.75.75 0 0 1-1.06 1.06l-4-4a.75.75 0 0 1 0-1.06l4-4a.75.75 0 0 1 1.06 1.06l-2.72 2.72h9.69a.75.75 0 0 1 .75.75Z"></path></svg></span></span></span></button></nav></div></div><div class="Polaris-Page-Header__TitleWrapper"><h1 class="Polaris-Header-Title"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 26px; width: 40%;"></div></div></h1></div><div class="Polaris-Page-Header__RightAlign"><div class="Polaris-Page-Header__Actions"><div class="Polaris-Page-Header__PaginationWrapper"><div class="Polaris-Box Polaris-Box--printHidden"><nav aria-label="Pagination" class="Polaris-Pagination"><div class="Polaris-ButtonGroup Polaris-ButtonGroup--variantSegmented" data-buttongroup-variant="segmented"><div class="Polaris-ButtonGroup__Item"><button id="previousURL" class="Polaris-Button Polaris-Button--disabled Polaris-Button--iconOnly" aria-label="Previous" aria-disabled="true" type="button" tabindex="-1"><span class="Polaris-Button__Content"><span class="Polaris-Button__Icon"><span class="Polaris-Icon"><span class="Polaris-Text--root Polaris-Text--visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="M11.78 5.47a.75.75 0 0 1 0 1.06l-3.47 3.47 3.47 3.47a.75.75 0 1 1-1.06 1.06l-4-4a.75.75 0 0 1 0-1.06l4-4a.75.75 0 0 1 1.06 0Z"></path></svg></span></span></span></button></div><div class="Polaris-ButtonGroup__Item"><button id="nextURL" class="Polaris-Button Polaris-Button--disabled Polaris-Button--iconOnly" aria-label="Next" aria-disabled="true" type="button" tabindex="-1"><span class="Polaris-Button__Content"><span class="Polaris-Button__Icon"><span class="Polaris-Icon"><span class="Polaris-Text--root Polaris-Text--visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="M7.72 14.53a.75.75 0 0 1 0-1.06l3.47-3.47-3.47-3.47a.75.75 0 0 1 1.06-1.06l4 4a.75.75 0 0 1 0 1.06l-4 4a.75.75 0 0 1-1.06 0Z"></path></svg></span></span></span></button></div></div></nav></div></div></div></div></div></div></div><div class=""><div class="Polaris-Grid"><div class="Polaris-Grid-Cell Polaris-Grid-Cell--cell_6ColumnXs Polaris-Grid-Cell--cell_6ColumnSm Polaris-Grid-Cell--cell_4ColumnMd Polaris-Grid-Cell--cell_8ColumnLg Polaris-Grid-Cell--cell_8ColumnXl"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden; --pc-box-padding-block-end-xs: var(--p-space-0); --pc-box-padding-block-start-xs: var(--p-space-0); --pc-box-padding-inline-start-xs: var(--p-space-0); --pc-box-padding-inline-end-xs: var(--p-space-0);"><div class="collapse-header" aria-expanded="true" aria-controls="basic-collapsible" style="display: flex; align-items: center; justify-content: space-between; padding: 16px; cursor: pointer;"><h6 class="Polaris-Text--root Polaris-Text--headingSm">General SEO</h6><div><span class="Polaris-Icon Polaris-Icon--toneBase Polaris-Icon--applyColor"><span class="Polaris-Text--root Polaris-Text--visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="M14.53 12.28a.75.75 0 0 1-1.06 0l-3.47-3.47-3.47 3.47a.75.75 0 0 1-1.06-1.06l4-4a.75.75 0 0 1 1.06 0l4 4a.75.75 0 0 1 0 1.06Z"></path></svg></span></div></div><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"><div id="basic-collapsible" class="Polaris-Collapsible Polaris-Collapsible--expandOnPrint" aria-hidden="false" style="transition-duration: 500ms; transition-timing-function: ease-in-out; max-height: none; overflow: visible;"><div style="padding: 16px;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div><div style="margin-bottom: 8px;"></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 80px; width: 80px; border-radius: 8px;"></div></div></div><div class="Polaris-Labelled--disabled"><div class="Polaris-Labelled__LabelWrapper"><div class="Polaris-Label"><label id=":r1:Label" for=":r1:" class="Polaris-Label__Text">Meta Title</label></div></div><div class="Polaris-Connected"><div class="Polaris-Connected__Item Polaris-Connected__Item--primary"><div class="Polaris-TextField Polaris-TextField--disabled"><input id=":r1:" disabled="" class="Polaris-TextField__Input" maxlength="70" type="text" aria-describedby=":r1:-CharacterCounter" aria-labelledby=":r1:Label" aria-invalid="false" value=""><div id=":r1:-CharacterCounter" class="Polaris-TextField__CharacterCount" aria-label="0 of 70 characters used" aria-live="off" aria-atomic="true">0/70</div><div class="Polaris-TextField__Backdrop"></div></div></div></div></div><div class="Polaris-Labelled--disabled"><div class="Polaris-Labelled__LabelWrapper"><div class="Polaris-Label"><label id=":r2:Label" for=":r2:" class="Polaris-Label__Text Polaris-Label__RequiredIndicator">Meta Description</label></div></div><div class="Polaris-Connected"><div class="Polaris-Connected__Item Polaris-Connected__Item--primary"><div class="Polaris-TextField Polaris-TextField--disabled Polaris-TextField--multiline"><textarea id=":r2:" disabled="" autocomplete="off" class="Polaris-TextField__Input" maxlength="165" type="text" rows="4" aria-describedby=":r2:-CharacterCounter" aria-labelledby=":r2:Label" aria-invalid="false" aria-required="true" aria-multiline="true" data-1p-ignore="true" data-lpignore="true" data-form-type="other" style="height: 106px;"></textarea><div id=":r2:-CharacterCounter" class="Polaris-TextField__CharacterCount Polaris-TextField__AlignFieldBottom" aria-label="0 of 165 characters used" aria-live="off" aria-atomic="true">0/165</div><div class="Polaris-TextField__Backdrop"></div><div aria-hidden="true" class="Polaris-TextField__Resizer"><div class="Polaris-TextField__DummyInput"><br></div><div class="Polaris-TextField__DummyInput"><br><br><br><br></div></div></div></div></div></div><div class="Polaris-Labelled--disabled"><div class="Polaris-Labelled__LabelWrapper"><div class="Polaris-Label"><label id=":r3:Label" for=":r3:" class="Polaris-Label__Text Polaris-Label__RequiredIndicator">Focus Keyword</label></div></div><div class="Polaris-Connected"><div class="Polaris-Connected__Item Polaris-Connected__Item--primary"><div class="Polaris-TextField Polaris-TextField--disabled"><input id=":r3:" disabled="" autocomplete="off" class="Polaris-TextField__Input" type="text" aria-labelledby=":r3:Label" aria-invalid="false" aria-required="true" data-1p-ignore="true" data-lpignore="true" data-form-type="other" value=""><div class="Polaris-TextField__Backdrop"></div></div></div><div class="Polaris-Connected__Item"><button class="Polaris-Button Polaris-Button--primary Polaris-Button--disabled" aria-disabled="true" type="button" tabindex="-1"><span class="Polaris-Button__Content"><span class="Polaris-Button__Text">Keyword Analytics</span></span></button></div></div></div><div class="Polaris-Labelled--disabled"><div class="Polaris-Labelled__LabelWrapper"><div class="Polaris-Label"><label id=":rq:Label" for=":rq:" class="Polaris-Label__Text">Tags</label></div></div><div class="Polaris-Connected"><div class="Polaris-Connected__Item Polaris-Connected__Item--primary"><div class="Polaris-TextField Polaris-TextField--disabled"><input id=":rq:" disabled="" placeholder="Add Tags" autocomplete="off" class="Polaris-TextField__Input" type="text" aria-labelledby=":rq:Label" aria-invalid="false" data-1p-ignore="true" data-lpignore="true" data-form-type="other" value=""><div class="Polaris-TextField__Backdrop"></div></div></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-align: start; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 20px; width: 80px; border-radius: 8px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 20px; width: 100px; border-radius: 8px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 20px; width: 70px; border-radius: 8px;"></div></div></div></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden; --pc-box-padding-block-end-xs: var(--p-space-0); --pc-box-padding-block-start-xs: var(--p-space-0); --pc-box-padding-inline-start-xs: var(--p-space-0); --pc-box-padding-inline-end-xs: var(--p-space-0);"><div class="collapse-header" aria-expanded="true" aria-controls="basic-collapsible" style="display: flex; align-items: center; justify-content: space-between; padding: 16px; cursor: pointer;"><h6 class="Polaris-Text--root Polaris-Text--headingSm">Image Alt Text</h6><div><span class="Polaris-Icon Polaris-Icon--toneBase Polaris-Icon--applyColor"><span class="Polaris-Text--root Polaris-Text--visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="M14.53 12.28a.75.75 0 0 1-1.06 0l-3.47-3.47-3.47 3.47a.75.75 0 0 1-1.06-1.06l4-4a.75.75 0 0 1 1.06 0l4 4a.75.75 0 0 1 0 1.06Z"></path></svg></span></div></div><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"><div id="basic-collapsible" class="Polaris-Collapsible Polaris-Collapsible--expandOnPrint" aria-hidden="false" style="transition-duration: 500ms; transition-timing-function: ease-in-out; max-height: none; overflow: visible;"><div style="padding: 16px;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-InlineStack" style="--pc-inline-stack-block-align: center; --pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-400);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 80px; width: 80px; border-radius: 8px;"></div></div><div style="flex: 1 1 0%;"><div class="Polaris-Labelled--disabled"><div class="Polaris-Labelled__LabelWrapper"><div class="Polaris-Label"><label id=":r5:Label" for=":r5:" class="Polaris-Label__Text">Alt text</label></div></div><div class="Polaris-Connected"><div class="Polaris-Connected__Item Polaris-Connected__Item--primary"><div class="Polaris-TextField Polaris-TextField--disabled"><input id=":r5:" disabled="" autocomplete="off" class="Polaris-TextField__Input" type="text" aria-labelledby=":r5:Label" aria-invalid="false" data-1p-ignore="true" data-lpignore="true" data-form-type="other" value=""><div class="Polaris-TextField__Backdrop"></div></div></div></div></div></div></div></div></div></div></div></div></div></div><div class="Polaris-Grid-Cell Polaris-Grid-Cell--cell_6ColumnXs Polaris-Grid-Cell--cell_6ColumnSm Polaris-Grid-Cell--cell_2ColumnMd Polaris-Grid-Cell--cell_4ColumnLg Polaris-Grid-Cell--cell_4ColumnXl"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden; --pc-box-padding-block-end-xs: var(--p-space-0); --pc-box-padding-block-start-xs: var(--p-space-0); --pc-box-padding-inline-start-xs: var(--p-space-0); --pc-box-padding-inline-end-xs: var(--p-space-0);"><div class="collapse-header" style="display: flex; align-items: center; justify-content: space-between; padding: 16px; cursor: pointer;"><h6 class="Polaris-Text--root Polaris-Text--headingSm">SEO Score</h6></div><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"><div style="padding: 16px; height: 232px;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 100%;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 100%;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 90%;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 10px; width: 60%;"></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden; --pc-box-padding-block-end-xs: var(--p-space-0); --pc-box-padding-block-start-xs: var(--p-space-0); --pc-box-padding-inline-start-xs: var(--p-space-0); --pc-box-padding-inline-end-xs: var(--p-space-0);"><div class="collapse-header" aria-expanded="true" aria-controls="basic-collapsible" style="display: flex; align-items: center; justify-content: space-between; padding: 16px; cursor: pointer;"><h6 class="Polaris-Text--root Polaris-Text--headingSm">Basic SEO Analysis  <span class="Polaris-Badge Polaris-Badge--toneAttention"><span class="Polaris-Text--root Polaris-Text--visuallyHidden">Attention</span><span class="Polaris-Text--root Polaris-Text--bodySm">5 Issues</span></span></h6><div><span class="Polaris-Icon Polaris-Icon--toneBase Polaris-Icon--applyColor"><span class="Polaris-Text--root Polaris-Text--visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="M14.53 12.28a.75.75 0 0 1-1.06 0l-3.47-3.47-3.47 3.47a.75.75 0 0 1-1.06-1.06l4-4a.75.75 0 0 1 1.06 0l4 4a.75.75 0 0 1 0 1.06Z"></path></svg></span></div></div><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"><div id="basic-collapsible" class="Polaris-Collapsible Polaris-Collapsible--expandOnPrint" aria-hidden="false" style="transition-duration: 500ms; transition-timing-function: ease-in-out; max-height: none; overflow: visible;"><div class="seo-analytics-list"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column;"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-200); --pc-box-padding-block-start-xs: var(--p-space-200); --pc-box-padding-inline-start-xs: var(--p-space-200); --pc-box-padding-inline-end-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200);"><span><span class="Polaris-Icon Polaris-Icon--toneCritical Polaris-Icon--applyColor"><span class="Polaris-Text--root Polaris-Text--visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path d="M12.72 13.78a.75.75 0 1 0 1.06-1.06l-2.72-2.72 2.72-2.72a.75.75 0 0 0-1.06-1.06l-2.72 2.72-2.72-2.72a.75.75 0 0 0-1.06 1.06l2.72 2.72-2.72 2.72a.75.75 0 1 0 1.06 1.06l2.72-2.72 2.72 2.72Z"></path></svg></span></span><p class="Polaris-Text--root">Focus keyword is unique.</p></div></div><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-200); --pc-box-padding-block-start-xs: var(--p-space-200); --pc-box-padding-inline-start-xs: var(--p-space-200); --pc-box-padding-inline-end-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200);"><span><span class="Polaris-Icon Polaris-Icon--toneCritical Polaris-Icon--applyColor"><span class="Polaris-Text--root Polaris-Text--visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path d="M12.72 13.78a.75.75 0 1 0 1.06-1.06l-2.72-2.72 2.72-2.72a.75.75 0 0 0-1.06-1.06l-2.72 2.72-2.72-2.72a.75.75 0 0 0-1.06 1.06l2.72 2.72-2.72 2.72a.75.75 0 1 0 1.06 1.06l2.72-2.72 2.72 2.72Z"></path></svg></span></span><p class="Polaris-Text--root">Focus keyword is used in the meta title.</p></div></div><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-200); --pc-box-padding-block-start-xs: var(--p-space-200); --pc-box-padding-inline-start-xs: var(--p-space-200); --pc-box-padding-inline-end-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200);"><span><span class="Polaris-Icon Polaris-Icon--toneCritical Polaris-Icon--applyColor"><span class="Polaris-Text--root Polaris-Text--visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path d="M12.72 13.78a.75.75 0 1 0 1.06-1.06l-2.72-2.72 2.72-2.72a.75.75 0 0 0-1.06-1.06l-2.72 2.72-2.72-2.72a.75.75 0 0 0-1.06 1.06l2.72 2.72-2.72 2.72a.75.75 0 1 0 1.06 1.06l2.72-2.72 2.72 2.72Z"></path></svg></span></span><p class="Polaris-Text--root">Meta description is within 120-165 characters.</p></div></div><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-200); --pc-box-padding-block-start-xs: var(--p-space-200); --pc-box-padding-inline-start-xs: var(--p-space-200); --pc-box-padding-inline-end-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200);"><span><span class="Polaris-Icon Polaris-Icon--toneCritical Polaris-Icon--applyColor"><span class="Polaris-Text--root Polaris-Text--visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path d="M12.72 13.78a.75.75 0 1 0 1.06-1.06l-2.72-2.72 2.72-2.72a.75.75 0 0 0-1.06-1.06l-2.72 2.72-2.72-2.72a.75.75 0 0 0-1.06 1.06l2.72 2.72-2.72 2.72a.75.75 0 1 0 1.06 1.06l2.72-2.72 2.72 2.72Z"></path></svg></span></span><p class="Polaris-Text--root">Meta title is within 50-70 characters.</p></div></div><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-200); --pc-box-padding-block-start-xs: var(--p-space-200); --pc-box-padding-inline-start-xs: var(--p-space-200); --pc-box-padding-inline-end-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200);"><span><span class="Polaris-Icon Polaris-Icon--toneCritical Polaris-Icon--applyColor"><span class="Polaris-Text--root Polaris-Text--visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path d="M12.72 13.78a.75.75 0 1 0 1.06-1.06l-2.72-2.72 2.72-2.72a.75.75 0 0 0-1.06-1.06l-2.72 2.72-2.72-2.72a.75.75 0 0 0-1.06 1.06l2.72 2.72-2.72 2.72a.75.75 0 1 0 1.06 1.06l2.72-2.72 2.72 2.72Z"></path></svg></span></span><p class="Polaris-Text--root">Product title is unique.</p></div></div></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: hidden; --pc-box-overflow-y: hidden; --pc-box-padding-block-end-xs: var(--p-space-0); --pc-box-padding-block-start-xs: var(--p-space-0); --pc-box-padding-inline-start-xs: var(--p-space-0); --pc-box-padding-inline-end-xs: var(--p-space-0);"><div class="collapse-header" aria-expanded="true" aria-controls="basic-collapsible" style="display: flex; align-items: center; justify-content: space-between; padding: 16px; cursor: pointer;"><h6 class="Polaris-Text--root Polaris-Text--headingSm">Detailed SEO Analysis <span class="Polaris-Badge Polaris-Badge--toneAttention"><span class="Polaris-Text--root Polaris-Text--visuallyHidden">Attention</span><span class="Polaris-Text--root Polaris-Text--bodySm">5 Issues</span></span></h6><div><span class="Polaris-Icon Polaris-Icon--toneBase Polaris-Icon--applyColor"><span class="Polaris-Text--root Polaris-Text--visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="M14.53 12.28a.75.75 0 0 1-1.06 0l-3.47-3.47-3.47 3.47a.75.75 0 0 1-1.06-1.06l4-4a.75.75 0 0 1 1.06 0l4 4a.75.75 0 0 1 0 1.06Z"></path></svg></span></div></div><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"><div id="basic-collapsible" class="Polaris-Collapsible Polaris-Collapsible--expandOnPrint" aria-hidden="false" style="transition-duration: 500ms; transition-timing-function: ease-in-out; max-height: none; overflow: visible;"><div class="seo-analytics-list"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column;"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-200); --pc-box-padding-block-start-xs: var(--p-space-200); --pc-box-padding-inline-start-xs: var(--p-space-200); --pc-box-padding-inline-end-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200);"><span><span class="Polaris-Icon Polaris-Icon--toneCritical Polaris-Icon--applyColor"><span class="Polaris-Text--root Polaris-Text--visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path d="M12.72 13.78a.75.75 0 1 0 1.06-1.06l-2.72-2.72 2.72-2.72a.75.75 0 0 0-1.06-1.06l-2.72 2.72-2.72-2.72a.75.75 0 0 0-1.06 1.06l2.72 2.72-2.72 2.72a.75.75 0 1 0 1.06 1.06l2.72-2.72 2.72 2.72Z"></path></svg></span></span><p class="Polaris-Text--root">Focus keyword is used in image alt text.</p></div></div><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-200); --pc-box-padding-block-start-xs: var(--p-space-200); --pc-box-padding-inline-start-xs: var(--p-space-200); --pc-box-padding-inline-end-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200);"><span><span class="Polaris-Icon Polaris-Icon--toneCritical Polaris-Icon--applyColor"><span class="Polaris-Text--root Polaris-Text--visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path d="M12.72 13.78a.75.75 0 1 0 1.06-1.06l-2.72-2.72 2.72-2.72a.75.75 0 0 0-1.06-1.06l-2.72 2.72-2.72-2.72a.75.75 0 0 0-1.06 1.06l2.72 2.72-2.72 2.72a.75.75 0 1 0 1.06 1.06l2.72-2.72 2.72 2.72Z"></path></svg></span></span><p class="Polaris-Text--root">Focus keyword is used in the product title.</p></div></div><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-200); --pc-box-padding-block-start-xs: var(--p-space-200); --pc-box-padding-inline-start-xs: var(--p-space-200); --pc-box-padding-inline-end-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200);"><span><span class="Polaris-Icon Polaris-Icon--toneCritical Polaris-Icon--applyColor"><span class="Polaris-Text--root Polaris-Text--visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path d="M12.72 13.78a.75.75 0 1 0 1.06-1.06l-2.72-2.72 2.72-2.72a.75.75 0 0 0-1.06-1.06l-2.72 2.72-2.72-2.72a.75.75 0 0 0-1.06 1.06l2.72 2.72-2.72 2.72a.75.75 0 1 0 1.06 1.06l2.72-2.72 2.72 2.72Z"></path></svg></span></span><p class="Polaris-Text--root">Focus keyword found in meta description.</p></div></div><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-200); --pc-box-padding-block-start-xs: var(--p-space-200); --pc-box-padding-inline-start-xs: var(--p-space-200); --pc-box-padding-inline-end-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200);"><span><span class="Polaris-Icon Polaris-Icon--toneCritical Polaris-Icon--applyColor"><span class="Polaris-Text--root Polaris-Text--visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path d="M12.72 13.78a.75.75 0 1 0 1.06-1.06l-2.72-2.72 2.72-2.72a.75.75 0 0 0-1.06-1.06l-2.72 2.72-2.72-2.72a.75.75 0 0 0-1.06 1.06l2.72 2.72-2.72 2.72a.75.75 0 1 0 1.06 1.06l2.72-2.72 2.72 2.72Z"></path></svg></span></span><p class="Polaris-Text--root">Focus keyword is at the beginning of meta title.</p></div></div><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-200); --pc-box-padding-block-start-xs: var(--p-space-200); --pc-box-padding-inline-start-xs: var(--p-space-200); --pc-box-padding-inline-end-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200);"><span><span class="Polaris-Icon Polaris-Icon--toneCritical Polaris-Icon--applyColor"><span class="Polaris-Text--root Polaris-Text--visuallyHidden"></span><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path d="M12.72 13.78a.75.75 0 1 0 1.06-1.06l-2.72-2.72 2.72-2.72a.75.75 0 0 0-1.06-1.06l-2.72 2.72-2.72-2.72a.75.75 0 0 0-1.06 1.06l2.72 2.72-2.72 2.72a.75.75 0 1 0 1.06 1.06l2.72-2.72 2.72 2.72Z"></path></svg></span></span><p class="Polaris-Text--root">Focus keyword density is 3-6 times for product description. Edit product</p></div></div></div></div></div></div></div></div></div></div></div></div>
`;

const ONBOARD_SKELETON = `<div class="Polaris-Frame" data-polaris-layer="true"><div class="Polaris-Frame__Skip"><a href="#AppFrameMain"><span class="Polaris-Text--root Polaris-Text--bodyLg Polaris-Text--medium">Skip to content</span></a></div><div class="Polaris-Frame__ContextualSaveBar Polaris-Frame-CSSAnimation--startFade"></div><main class="Polaris-Frame__Main" id="AppFrameMain" data-has-global-ribbon="false"><div class="Polaris-Frame__Content"><ui-nav-menu><a rel="home" href="/">Home</a><a rel="" href="/optimize-seo">Optimize SEO</a><a rel="" href="/image-optimizer">Image Optimizer</a><a rel="" href="/image-alt-text">Image Alt Text Generator</a><a rel="" href="/local-seo">SEO Schema</a><a rel="" href="/analytics">Analytics</a><a rel="" href="/reports">Reports</a><a rel="" href="/sitemaps">Sitemaps</a><a rel="" href="/settings">Settings</a><a rel="" href="/subscription">Subscription Plans</a><a rel="" href="/credit-bundles">AI Content Optimizer</a></ui-nav-menu><div style="display: flex; flex-direction: column; min-height: 100vh;"><div style="flex: 1 1 0%;"><div class="Polaris-Page"><div class="Polaris-Page__Content"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-block-align: center; --pc-inline-stack-wrap: wrap; --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-100);"><h3 class="Polaris-Text--root Polaris-Text--headingMd">Welcome to StoreSEO</h3><p class="Polaris-Text--root Polaris-Text--subdued">Complete the onboarding process to get started</p></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="onboard-header-icon"><button class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantTertiary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--iconOnly Polaris-Button--disabled" aria-disabled="true" type="button" tabindex="-1"><span class="Polaris-Button__Icon"><span class="Polaris-Icon"><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path d="M6 10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"></path><path d="M11.5 10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"></path><path d="M17 10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"></path></svg></span></span></button></div></div></div><div class="onboardin-header-progress"><div class="title"><p class="Polaris-Text--root">0 out of 3 steps are completed</p></div><div class="onboarding-progress"><div class="Polaris-ProgressBar Polaris-ProgressBar--sizeSmall Polaris-ProgressBar--tonePrimary"><progress class="Polaris-ProgressBar__Progress" value="0" max="100"></progress><div class="Polaris-ProgressBar__Indicator Polaris-ProgressBar__IndicatorAppearDone" style="--pc-progress-bar-duration: 500ms; --pc-progress-bar-percent: 0;"><span class="Polaris-ProgressBar__Label">0%</span></div></div></div></div></div><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-400); --pc-bleed-margin-inline-end-xs: var(--p-space-400);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-InlineStack" style="--pc-inline-stack-block-align: center; --pc-inline-stack-wrap: wrap; --pc-inline-stack-flex-direction-xs: row;"><span class="check-circle"></span><div class="skeleton-wrapper" style="line-height: 0px;"><div class="skeleton-box" style="width: 135px; height: 6px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-block-align: center; --pc-inline-stack-wrap: wrap; --pc-inline-stack-flex-direction-xs: row;"><span class="check-circle"></span><div class="skeleton-wrapper" style="line-height: 0px;"><div class="skeleton-box" style="width: 185px; height: 6px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-block-align: center; --pc-inline-stack-wrap: wrap; --pc-inline-stack-flex-direction-xs: row;"><span class="check-circle"></span><div class="skeleton-wrapper" style="line-height: 0px;"><div class="skeleton-box" style="width: 110px; height: 6px;"></div></div></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip;"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: nowrap; --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper" style="line-height: 0px;"><div class="skeleton-box" style="height: 184px; width: 313px;"></div></div><div class="Polaris-Box" style="--pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-flex-direction-xs: row;"><h4 class="Polaris-Text--root Polaris-Text--headingSm">Want to schedule a call?</h4><div class="Polaris-Box"><button class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantTertiary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--iconOnly Polaris-Button--disabled" aria-disabled="true" type="button" tabindex="-1"><span class="Polaris-Button__Icon"><span class="Polaris-Icon"><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path d="M6 10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"></path><path d="M11.5 10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"></path><path d="M17 10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"></path></svg></span></span></button></div></div><p class="Polaris-Text--root Polaris-Text--subdued">We have an in-house SEO expert who can audit your store and guide you in enhancing its SEO. Would you like to book a one-on-one session with them? It’s completely FREE!</p><div class="Polaris-Box"><button class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--iconWithText Polaris-Button--disabled" aria-disabled="true" type="button" tabindex="-1"><span class="Polaris-Button__Icon"><span class="Polaris-Icon"><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="M6.75 4.5c-1.283 0-2.213 1.025-2.044 2.127.384 2.498 1.296 4.459 2.707 5.89 1.41 1.43 3.373 2.389 5.96 2.786 1.101.17 2.126-.76 2.126-2.044v-.727a.25.25 0 0 0-.187-.242l-1.9-.498a.25.25 0 0 0-.182.022l-1.067.576c-.69.373-1.638.492-2.422-.056a8.678 8.678 0 0 1-2.071-2.09c-.542-.787-.423-1.735-.045-2.428l.57-1.047a.252.252 0 0 0 .022-.182l-.498-1.9a.25.25 0 0 0-.242-.187h-.726Zm-3.526 2.355c-.334-2.174 1.497-3.856 3.527-3.855h.726a1.75 1.75 0 0 1 1.693 1.306l.498 1.9c.113.43.058.885-.153 1.276l-.001.002-.572 1.05c-.191.351-.169.668-.036.86a7.184 7.184 0 0 0 1.694 1.71c.187.13.498.156.85-.034l1.067-.576a1.75 1.75 0 0 1 1.276-.153l1.9.498a1.75 1.75 0 0 1 1.306 1.693v.727c0 2.03-1.68 3.86-3.854 3.527-2.838-.436-5.12-1.511-6.8-3.216-1.68-1.703-2.701-3.978-3.121-6.715Z"></path></svg></span></span><span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Get Free SEO Consultation</span></button></div></div></div></div></div></div></div></div></div></div><footer class="footer"><a data-polaris-unstyled="true" class="Polaris-Link" url="/" href="/"><svg width="100" height="34" viewBox="0 0 100 34" fill="none" xmlns="http://www.w3.org/2000/svg"><mask id="mask0_44407_102018" maskUnits="userSpaceOnUse" x="3" y="3" width="94" height="28"><path d="M96.6795 3.42969H3.32007V30.5721H96.6795V3.42969Z" fill="white"></path></mask><g mask="url(#mask0_44407_102018)"><path d="M38.8228 23.2729C37.67 23.2729 36.6819 22.9723 36.1549 22.7217V21.1349C36.9125 21.4857 37.7524 21.6862 38.444 21.6862C39.6298 21.6862 40.1732 21.185 40.1732 20.0993C40.1732 18.9635 39.745 18.6462 38.3782 17.8612C36.9125 17.0093 36.0232 16.3746 36.0232 14.5206C36.0232 12.416 37.3077 11.2969 39.7121 11.2969C40.5355 11.2969 41.3754 11.4305 42.1 11.6643L41.8859 13.1342C41.3919 12.9671 40.7167 12.867 40.0909 12.867C39.2345 12.867 38.3617 13.034 38.3617 14.2366C38.3617 15.005 38.6746 15.2722 40.0415 16.1241C41.6719 17.1262 42.5117 17.7442 42.5117 19.7486C42.5282 22.1371 41.326 23.2729 38.8228 23.2729Z" fill="#AFAFAF"></path><path d="M47.2023 23.2723C45.5061 23.2723 44.7485 22.4872 44.7485 20.7334V15.8728H43.6287V14.3528H44.8144L44.9626 12.2148H46.9388V14.3528H48.8492L48.7832 15.8728H46.9388V20.6499C46.9388 21.4683 47.1365 21.9026 48.1575 21.9026C48.3551 21.9026 48.5527 21.8692 48.7174 21.8024L48.6515 23.0217C48.2892 23.1888 47.7622 23.2723 47.2023 23.2723Z" fill="#AFAFAF"></path><path d="M53.6599 23.2744C50.5803 23.2744 50.1521 21.5206 50.1521 19.7835V17.6121C50.1521 15.2736 51.3049 14.1211 53.6599 14.1211C56.0313 14.1211 57.1841 15.2569 57.1841 17.6121V19.7835C57.1841 22.2388 56.1302 23.2744 53.6599 23.2744ZM53.6599 15.5074C52.4742 15.5074 52.3424 16.4261 52.3424 17.1109V20.3346C52.3424 21.0028 52.4742 21.9047 53.6599 21.9047C54.8456 21.9047 54.9774 21.0028 54.9774 20.3346V17.1109C54.9938 16.0252 54.5657 15.5074 53.6599 15.5074Z" fill="#AFAFAF"></path><path d="M58.7341 23.0718V14.3528H60.7433V15.9062C61.1715 14.6534 62.0772 14.1523 63.1642 14.1523C63.1971 14.1523 63.23 14.1523 63.2465 14.1523V16.1734C63.2136 16.1734 63.1971 16.1734 63.1642 16.1734C62.3242 16.1734 61.4515 16.4908 60.9574 16.9919L60.9245 17.0253V23.0884H58.7341V23.0718Z" fill="#AFAFAF"></path><path d="M68.2853 23.2722C65.8479 23.2722 64.4646 22.0028 64.4646 19.798V17.5932C64.4646 15.3048 65.6009 14.1523 67.8242 14.1523C70.0806 14.1523 71.1341 15.1712 71.1341 17.3761V19.1132H66.6219V19.8815C66.6219 21.1509 67.4621 21.8691 68.9111 21.8691C69.5041 21.8691 70.1298 21.7522 70.8874 21.4683L70.8212 22.8379C70.2282 23.1052 69.224 23.2722 68.2853 23.2722ZM67.8406 15.4385C66.7701 15.4385 66.6219 16.2235 66.6219 17.0086V18.0609H69.0429V17.0086C69.0429 16.2235 68.8947 15.4385 67.8406 15.4385Z" fill="#AFAFAF"></path><path d="M75.3422 23.2729C74.1898 23.2729 73.2019 22.9723 72.6746 22.7217V21.1349C73.4322 21.4857 74.2724 21.6862 74.9638 21.6862C76.1497 21.6862 76.6928 21.185 76.6928 20.0993C76.6928 18.9635 76.2645 18.6462 74.8982 17.8612C73.4322 17.0093 72.5427 16.3746 72.5427 14.5206C72.5427 12.416 73.8277 11.2969 76.2317 11.2969C77.0555 11.2969 77.8951 11.4305 78.6199 11.6643L78.4054 13.1342C77.9115 12.9671 77.2366 12.867 76.6108 12.867C75.7542 12.867 74.8811 13.034 74.8811 14.2366C74.8811 15.005 75.1946 15.2722 76.561 16.1241C78.1916 17.1262 79.0312 17.7442 79.0312 19.7486C79.0482 22.1371 77.8295 23.2729 75.3422 23.2729Z" fill="#AFAFAF"></path><path d="M80.6365 23.0719V11.5469H86.8783V13.0669H82.9749V16.5076H86.2355V18.0276H82.9749V21.5186H87.0259V23.0719H80.6365Z" fill="#AFAFAF"></path><path d="M92.4622 23.2719C89.7446 23.2719 88.2458 21.7018 88.2458 18.8623V15.7221C88.2458 12.916 89.7774 11.3125 92.4622 11.3125C95.2289 11.3125 96.6778 12.8325 96.6778 15.7221V18.8957C96.6778 21.6851 95.1298 23.2719 92.4622 23.2719ZM92.4622 12.8826C91.2106 12.8826 90.6833 13.6008 90.6833 15.2878V19.4135C90.6833 20.9836 91.227 21.7018 92.4622 21.7018C94.043 21.7018 94.2404 20.566 94.2404 19.4636V15.2878C94.2404 13.6008 93.7137 12.8826 92.4622 12.8826Z" fill="#AFAFAF"></path><path d="M31.8226 4.98307C31.7073 4.46528 31.3779 4.0143 30.9332 3.73034C30.6204 3.52991 30.258 3.42969 29.8958 3.42969C29.2041 3.42969 28.5618 3.78045 28.1995 4.38176L17.4621 22.3041C16.8857 23.2563 17.1822 24.4923 18.1209 25.0602C18.4337 25.2606 18.796 25.3608 19.1584 25.3608C19.85 25.3608 20.4923 25.0101 20.8546 24.4088L31.6085 6.51976C31.872 6.05207 31.9543 5.51757 31.8226 4.98307Z" fill="#AFAFAF"></path><path d="M20.6274 8.33854C20.5121 7.82074 20.1828 7.36976 19.7381 7.08581C19.4252 6.88537 19.0629 6.78516 18.7006 6.78516C18.0089 6.78516 17.3667 7.13592 17.0043 7.73722L8.70428 21.3669C8.12789 22.319 8.42433 23.555 9.36302 24.1229C9.67593 24.3234 10.0382 24.4235 10.4005 24.4235C11.0922 24.4235 11.7344 24.0728 12.0968 23.4715L20.3969 9.84181C20.6768 9.39083 20.7591 8.85633 20.6274 8.33854Z" fill="#AFAFAF"></path><path d="M11.2862 9.59244C11.1709 9.07465 10.8415 8.62367 10.3969 8.33971C10.084 8.13927 9.72166 8.03906 9.35939 8.03906C8.66772 8.03906 8.02545 8.38982 7.66315 8.99113L3.61192 15.3717C3.33196 15.8227 3.24962 16.3739 3.38136 16.8917C3.49664 17.4095 3.80954 17.8604 4.25419 18.1444C4.56709 18.3448 4.92939 18.4451 5.2917 18.4451C5.98337 18.4451 6.62564 18.0943 6.98794 17.493L11.0556 11.1124C11.3356 10.6614 11.418 10.1269 11.2862 9.59244Z" fill="#AFAFAF"></path><path d="M9.10021 28.3024C8.96846 27.317 8.12857 26.582 7.15694 26.582C7.05813 26.582 6.97579 26.582 6.87697 26.5987C5.80653 26.7491 5.04899 27.7679 5.21367 28.8703C5.34542 29.8558 6.1853 30.5907 7.15694 30.5907C7.25574 30.5907 7.33809 30.5907 7.4369 30.5741C8.49087 30.4237 9.24842 29.4048 9.10021 28.3024Z" fill="#AFAFAF"></path><path d="M17.8895 28.3024C17.7578 27.317 16.9179 26.582 15.9462 26.582C15.8475 26.582 15.7651 26.582 15.6663 26.5987C14.5958 26.7491 13.8383 27.7679 14.003 28.8703C14.1348 29.8558 14.9746 30.5907 15.9462 30.5907C16.0451 30.5907 16.1274 30.5907 16.2262 30.5741C16.7532 30.4905 17.2143 30.2233 17.5272 29.789C17.8401 29.3714 17.9719 28.8369 17.8895 28.3024Z" fill="#AFAFAF"></path><path d="M23.8231 11.7832C23.2302 12.635 22.0939 12.9023 21.2046 12.3511C20.3153 11.7999 20.0024 10.6307 20.4635 9.69531L20.2659 10.0127L13.9255 20.4354C13.9421 20.4186 13.9421 20.4186 13.9585 20.402C13.975 20.3852 13.9915 20.3519 14.0079 20.3351C14.0244 20.3184 14.0408 20.3017 14.0573 20.285C14.4196 19.8841 14.9466 19.6503 15.5065 19.6503C16.6099 19.6503 17.5157 20.5523 17.5157 21.6881C17.5157 21.9553 17.4663 22.2226 17.3675 22.4564L21.1388 16.2095L23.8231 11.7832Z" fill="#E7E7E7"></path><path d="M13.9529 20.4183C13.9693 20.4016 13.9858 20.3682 14.0023 20.3516C14.0023 20.3682 13.9693 20.385 13.9529 20.4183Z" fill="#E54141"></path></g></svg></a><div class="Polaris-InlineStack" style="--pc-inline-stack-align: center; --pc-inline-stack-block-align: center; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><span class="Polaris-Text--root Polaris-Text--subdued">Copyright © 2025</span><strong class="Polaris-Text--root Polaris-Text--subdued">|</strong><a data-polaris-unstyled="true" class="Polaris-Link Polaris-Link--monochrome Polaris-Link--removeUnderline" url="https://storeware.io/" href="https://storeware.io/" target="_blank"><strong class="Polaris-Text--root Polaris-Text--subdued">Storeware</strong></a><strong class="Polaris-Text--root Polaris-Text--subdued">|</strong><strong class="Polaris-Text--root Polaris-Text--subdued">Version: <a data-polaris-unstyled="true" class="Polaris-Link Polaris-Link--monochrome Polaris-Link--removeUnderline" url="https://storeseo.com/changelog/" href="https://storeseo.com/changelog/" target="_blank">3.4.6</a></strong>|<div><div class="Polaris-Box" style="display: flex; align-items: center;"><button class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantMonochromePlain Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--disclosure Polaris-Button--iconWithText" type="button" tabindex="0" aria-controls=":r2:" aria-owns=":r2:" aria-expanded="false" data-state="closed"><span class="Polaris-Button__Icon"><span class="Polaris-Icon"><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="M3 10a7 7 0 1 1 14 0 7 7 0 0 1-14 0Zm7-5.5a5.497 5.497 0 0 0-4.737 2.703l2 1.999c.472.472.737 1.113.737 1.78v.518a.5.5 0 0 0 .5.5 2 2 0 0 1 2 2v1.478a5.504 5.504 0 0 0 4.52-3.228h-1.02a.75.75 0 0 1-.75-.75v-.5a.75.75 0 0 0-.75-.75h-2.5a1.755 1.755 0 0 1-1.07-3.144l.463-.356a.393.393 0 0 0 .152-.312v-.04c0-.885.62-1.624 1.449-1.808a5.531 5.531 0 0 0-.994-.09Zm2.875.81a1.85 1.85 0 0 1-1.477.735.352.352 0 0 0-.353.353v.04c0 .587-.271 1.14-.736 1.499l-.462.356a.256.256 0 0 0 .153.457h2.5a2.25 2.25 0 0 1 2.236 2h.713a5.497 5.497 0 0 0-2.574-5.44Zm-8.375 4.69c0-.443.052-.875.152-1.288l1.55 1.55c.19.191.298.45.298.72v.518a2 2 0 0 0 2 2 .5.5 0 0 1 .5.5v1.41a5.502 5.502 0 0 1-4.5-5.41Z"></path></svg></span></span><span class="Polaris-Text--root Polaris-Text--bodyMd Polaris-Text--regular">English (EN)</span><span class="Polaris-Button__Icon"><span class="Polaris-Icon"><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="M14.53 12.28a.75.75 0 0 1-1.06 0l-3.47-3.47-3.47 3.47a.75.75 0 0 1-1.06-1.06l4-4a.75.75 0 0 1 1.06 0l4 4a.75.75 0 0 1 0 1.06Z"></path></svg></span></span></button></div></div></div><div></div></footer></div><div><div class="notification-icon"><span class="" tabindex="0" aria-describedby=":r5:" data-polaris-tooltip-activator="true" aria-controls=":r4:" aria-owns=":r4:" aria-expanded="false" data-state="closed" aria-haspopup="false"><span style="position: relative; top: 7px;"><span class="Polaris-Icon"><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="m7.252 14.424-2.446-.281c-1.855-.213-2.38-2.659-.778-3.616l.065-.038a2.887 2.887 0 0 0 1.407-2.48v-.509a4.5 4.5 0 0 1 9 0v.51c0 1.016.535 1.958 1.408 2.479l.065.038c1.602.957 1.076 3.403-.778 3.616l-2.543.292v.365a2.7 2.7 0 0 1-5.4 0v-.376Zm3.9.076h-2.4v.3a1.2 1.2 0 0 0 2.4 0v-.3Zm-3.152-1.5h4l3.024-.348a.452.452 0 0 0 .18-.837l-.065-.038a4.414 4.414 0 0 1-.747-.562 4.387 4.387 0 0 1-1.392-3.205v-.51a3 3 0 0 0-6 0v.51a4.387 4.387 0 0 1-2.138 3.767l-.065.038a.452.452 0 0 0 .18.838l3.023.347Z"></path></svg></span></span></span><span style="width: 8px; height: 8px; background: var(--p-color-bg-fill); border-radius: var(--p-border-radius-full); position: absolute; top: 8px; right: 8px;"></span></div></div><aside class="ReactQueryDevtools" aria-label="React Query Devtools"><div class="ReactQueryDevtoolsPanel" aria-label="React Query Devtools Panel" id="ReactQueryDevtoolsPanel" style="font-size: clamp(12px, 1.5vw, 14px); font-family: sans-serif; display: flex; background-color: rgb(11, 21, 33); color: white; position: fixed; bottom: 0px; right: 0px; z-index: 99999; width: 100%; height: 500px; max-height: 90%; box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 20px; border-top: 1px solid rgb(63, 78, 96); transform-origin: center top 0px; visibility: hidden; transition: 0.2s; opacity: 0; pointer-events: none; transform: translateY(15px) scale(1.02);"><style>
            .ReactQueryDevtoolsPanel * {
              scrollbar-color: #132337 #3f4e60;
            }

            .ReactQueryDevtoolsPanel *::-webkit-scrollbar, .ReactQueryDevtoolsPanel scrollbar {
              width: 1em;
              height: 1em;
            }

            .ReactQueryDevtoolsPanel *::-webkit-scrollbar-track, .ReactQueryDevtoolsPanel scrollbar-track {
              background: #132337;
            }

            .ReactQueryDevtoolsPanel *::-webkit-scrollbar-thumb, .ReactQueryDevtoolsPanel scrollbar-thumb {
              background: #3f4e60;
              border-radius: .5em;
              border: 3px solid #132337;
            }
          </style><div style="position: absolute; left: 0px; top: 0px; width: 100%; height: 4px; margin-bottom: -4px; cursor: row-resize; z-index: 100000;"></div><div style="flex: 1 1 500px; min-height: 40%; max-height: 100%; overflow: auto; border-right: 1px solid rgb(34, 46, 62); display: none; flex-direction: column;"><div style="padding: 0.5em; background: rgb(19, 35, 55); display: flex; justify-content: space-between; align-items: center;"><button type="button" aria-label="Close React Query Devtools" aria-controls="ReactQueryDevtoolsPanel" aria-haspopup="true" aria-expanded="true" style="display: inline-flex; background: none; border: 0px; padding: 0px; margin-right: 0.5em; cursor: pointer;"><svg width="40px" height="40px" viewBox="0 0 190 190" version="1.1" aria-hidden="true"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g transform="translate(-33.000000, 0.000000)"><path d="M72.7239712,61.3436237 C69.631224,46.362877 68.9675112,34.8727722 70.9666331,26.5293551 C72.1555965,21.5671678 74.3293088,17.5190846 77.6346064,14.5984631 C81.1241394,11.5150478 85.5360327,10.0020122 90.493257,10.0020122 C98.6712013,10.0020122 107.26826,13.7273214 116.455725,20.8044264 C120.20312,23.6910458 124.092437,27.170411 128.131651,31.2444746 C128.45314,30.8310265 128.816542,30.4410453 129.22143,30.0806152 C140.64098,19.9149716 150.255245,13.5989272 158.478408,11.1636507 C163.367899,9.715636 167.958526,9.57768202 172.138936,10.983031 C176.551631,12.4664684 180.06766,15.5329489 182.548314,19.8281091 C186.642288,26.9166735 187.721918,36.2310983 186.195595,47.7320243 C185.573451,52.4199112 184.50985,57.5263831 183.007094,63.0593153 C183.574045,63.1277086 184.142416,63.2532808 184.705041,63.4395297 C199.193932,68.2358678 209.453582,73.3937462 215.665021,79.2882839 C219.360669,82.7953831 221.773972,86.6998434 222.646365,91.0218204 C223.567176,95.5836746 222.669313,100.159332 220.191548,104.451297 C216.105211,111.529614 208.591643,117.11221 197.887587,121.534031 C193.589552,123.309539 188.726579,124.917559 183.293259,126.363748 C183.541176,126.92292 183.733521,127.516759 183.862138,128.139758 C186.954886,143.120505 187.618598,154.61061 185.619477,162.954027 C184.430513,167.916214 182.256801,171.964297 178.951503,174.884919 C175.46197,177.968334 171.050077,179.48137 166.092853,179.48137 C157.914908,179.48137 149.31785,175.756061 140.130385,168.678956 C136.343104,165.761613 132.410866,162.238839 128.325434,158.108619 C127.905075,158.765474 127.388968,159.376011 126.77857,159.919385 C115.35902,170.085028 105.744755,176.401073 97.5215915,178.836349 C92.6321009,180.284364 88.0414736,180.422318 83.8610636,179.016969 C79.4483686,177.533532 75.9323404,174.467051 73.4516862,170.171891 C69.3577116,163.083327 68.2780823,153.768902 69.8044053,142.267976 C70.449038,137.410634 71.56762,132.103898 73.1575891,126.339009 C72.5361041,126.276104 71.9120754,126.144816 71.2949591,125.940529 C56.8060684,121.144191 46.5464184,115.986312 40.3349789,110.091775 C36.6393312,106.584675 34.2260275,102.680215 33.3536352,98.3582381 C32.4328237,93.7963839 33.3306866,89.2207269 35.8084524,84.9287618 C39.8947886,77.8504443 47.4083565,72.2678481 58.1124133,67.8460273 C62.5385143,66.0176154 67.5637208,64.366822 73.1939394,62.8874674 C72.9933393,62.3969171 72.8349374,61.8811235 72.7239712,61.3436237 Z" fill="#002C4B" fill-rule="nonzero" transform="translate(128.000000, 95.000000) scale(-1, 1) translate(-128.000000, -95.000000) "></path><path d="M113.396882,64 L142.608177,64 C144.399254,64 146.053521,64.958025 146.944933,66.5115174 L161.577138,92.0115174 C162.461464,93.5526583 162.461464,95.4473417 161.577138,96.9884826 L146.944933,122.488483 C146.053521,124.041975 144.399254,125 142.608177,125 L113.396882,125 C111.605806,125 109.951539,124.041975 109.060126,122.488483 L94.4279211,96.9884826 C93.543596,95.4473417 93.543596,93.5526583 94.4279211,92.0115174 L109.060126,66.5115174 C109.951539,64.958025 111.605806,64 113.396882,64 Z M138.987827,70.2765273 C140.779849,70.2765273 142.434839,71.2355558 143.325899,72.7903404 L154.343038,92.0138131 C155.225607,93.5537825 155.225607,95.4462175 154.343038,96.9861869 L143.325899,116.20966 C142.434839,117.764444 140.779849,118.723473 138.987827,118.723473 L117.017233,118.723473 C115.225211,118.723473 113.570221,117.764444 112.67916,116.20966 L101.662022,96.9861869 C100.779452,95.4462175 100.779452,93.5537825 101.662022,92.0138131 L112.67916,72.7903404 C113.570221,71.2355558 115.225211,70.2765273 117.017233,70.2765273 L138.987827,70.2765273 Z M135.080648,77.1414791 L120.924411,77.1414791 C119.134228,77.1414791 117.480644,78.0985567 116.5889,79.6508285 L116.5889,79.6508285 L109.489217,92.0093494 C108.603232,93.5515958 108.603232,95.4484042 109.489217,96.9906506 L109.489217,96.9906506 L116.5889,109.349172 C117.480644,110.901443 119.134228,111.858521 120.924411,111.858521 L120.924411,111.858521 L135.080648,111.858521 C136.870831,111.858521 138.524416,110.901443 139.41616,109.349172 L139.41616,109.349172 L146.515843,96.9906506 C147.401828,95.4484042 147.401828,93.5515958 146.515843,92.0093494 L146.515843,92.0093494 L139.41616,79.6508285 C138.524416,78.0985567 136.870831,77.1414791 135.080648,77.1414791 L135.080648,77.1414791 Z M131.319186,83.7122186 C133.108028,83.7122186 134.760587,84.6678753 135.652827,86.2183156 L138.983552,92.0060969 C139.87203,93.5500005 139.87203,95.4499995 138.983552,96.9939031 L135.652827,102.781684 C134.760587,104.332125 133.108028,105.287781 131.319186,105.287781 L124.685874,105.287781 C122.897032,105.287781 121.244473,104.332125 120.352233,102.781684 L117.021508,96.9939031 C116.13303,95.4499995 116.13303,93.5500005 117.021508,92.0060969 L120.352233,86.2183156 C121.244473,84.6678753 122.897032,83.7122186 124.685874,83.7122186 L131.319186,83.7122186 Z M128.003794,90.1848875 C126.459294,90.1848875 125.034382,91.0072828 124.263005,92.3424437 C123.491732,93.6774232 123.491732,95.3225768 124.263005,96.6575563 C125.034382,97.9927172 126.459294,98.8151125 128.001266,98.8151125 L128.001266,98.8151125 C129.545766,98.8151125 130.970678,97.9927172 131.742055,96.6575563 C132.513327,95.3225768 132.513327,93.6774232 131.742055,92.3424437 C130.970678,91.0072828 129.545766,90.1848875 128.003794,90.1848875 L128.003794,90.1848875 Z M93,94.5009646 L100.767764,94.5009646" fill="#FFD94C"></path><path d="M87.8601729,108.357758 C89.1715224,107.608286 90.8360246,108.074601 91.5779424,109.399303 L91.5779424,109.399303 L92.0525843,110.24352 C95.8563392,116.982993 99.8190116,123.380176 103.940602,129.435068 C108.807881,136.585427 114.28184,143.82411 120.362479,151.151115 C121.316878,152.30114 121.184944,154.011176 120.065686,154.997937 L120.065686,154.997937 L119.454208,155.534625 C99.3465389,173.103314 86.2778188,176.612552 80.2480482,166.062341 C74.3500652,155.742717 76.4844915,136.982888 86.6513274,109.782853 C86.876818,109.179582 87.3045861,108.675291 87.8601729,108.357758 Z M173.534177,129.041504 C174.986131,128.785177 176.375496,129.742138 176.65963,131.194242 L176.65963,131.194242 L176.812815,131.986376 C181.782365,157.995459 178.283348,171 166.315764,171 C154.609745,171 139.708724,159.909007 121.612702,137.727022 C121.211349,137.235047 120.994572,136.617371 121,135.981509 C121.013158,134.480686 122.235785,133.274651 123.730918,133.287756 L123.730918,133.287756 L124.684654,133.294531 C132.305698,133.335994 139.714387,133.071591 146.910723,132.501323 C155.409039,131.82788 164.283523,130.674607 173.534177,129.041504 Z M180.408726,73.8119663 C180.932139,72.4026903 182.508386,71.6634537 183.954581,72.149012 L183.954581,72.149012 L184.742552,72.4154854 C210.583763,81.217922 220.402356,90.8916805 214.198332,101.436761 C208.129904,111.751366 190.484347,119.260339 161.26166,123.963678 C160.613529,124.067994 159.948643,123.945969 159.382735,123.618843 C158.047025,122.846729 157.602046,121.158214 158.388848,119.847438 L158.388848,119.847438 L158.889328,119.0105 C162.877183,112.31633 166.481358,105.654262 169.701854,99.0242957 C173.50501,91.1948179 177.073967,82.7907081 180.408726,73.8119663 Z M94.7383398,66.0363218 C95.3864708,65.9320063 96.0513565,66.0540315 96.6172646,66.3811573 C97.9529754,67.153271 98.3979538,68.8417862 97.6111517,70.1525615 L97.6111517,70.1525615 L97.1106718,70.9895001 C93.1228168,77.6836699 89.5186416,84.3457379 86.2981462,90.9757043 C82.49499,98.8051821 78.9260328,107.209292 75.5912744,116.188034 C75.0678608,117.59731 73.4916142,118.336546 72.045419,117.850988 L72.045419,117.850988 L71.2574475,117.584515 C45.4162372,108.782078 35.597644,99.1083195 41.8016679,88.5632391 C47.8700957,78.2486335 65.515653,70.7396611 94.7383398,66.0363218 Z M136.545792,34.4653746 C156.653461,16.8966864 169.722181,13.3874478 175.751952,23.9376587 C181.649935,34.2572826 179.515508,53.0171122 169.348673,80.2171474 C169.123182,80.8204179 168.695414,81.324709 168.139827,81.6422422 C166.828478,82.3917144 165.163975,81.9253986 164.422058,80.6006966 L164.422058,80.6006966 L163.947416,79.7564798 C160.143661,73.0170065 156.180988,66.6198239 152.059398,60.564932 C147.192119,53.4145727 141.71816,46.1758903 135.637521,38.8488847 C134.683122,37.6988602 134.815056,35.9888243 135.934314,35.0020629 L135.934314,35.0020629 Z M90.6842361,18 C102.390255,18 117.291276,29.0909926 135.387298,51.2729777 C135.788651,51.7649527 136.005428,52.3826288 136,53.0184911 C135.986842,54.5193144 134.764215,55.7253489 133.269082,55.7122445 L133.269082,55.7122445 L132.315346,55.7054689 C124.694302,55.6640063 117.285613,55.9284091 110.089277,56.4986773 C101.590961,57.17212 92.7164767,58.325393 83.4658235,59.9584962 C82.0138691,60.2148231 80.6245044,59.2578618 80.3403697,57.805758 L80.3403697,57.805758 L80.1871846,57.0136235 C75.2176347,31.0045412 78.7166519,18 90.6842361,18 Z" fill="#FF4154"></path></g></g></svg></button><div style="display: flex; flex-direction: column;"><span style="display: inline-block; font-size: 0.9em; margin-bottom: 0.5em;"><span style="display: inline-flex; align-items: center; padding: 0.2em 0.4em; font-weight: bold; text-shadow: black 0px 0px 10px; border-radius: 0.2em; background: rgb(0, 171, 82); opacity: 0.3;">fresh <code style="font-size: 0.9em; color: inherit; background: inherit;">(0)</code></span> <span style="display: inline-flex; align-items: center; padding: 0.2em 0.4em; font-weight: bold; text-shadow: black 0px 0px 10px; border-radius: 0.2em; background: rgb(0, 107, 255); opacity: 1;">fetching <code style="font-size: 0.9em; color: inherit; background: inherit;">(2)</code></span> <span style="display: inline-flex; align-items: center; padding: 0.2em 0.4em; font-weight: bold; border-radius: 0.2em; background: rgb(255, 178, 0); color: black; opacity: 1;">stale <code style="font-size: 0.9em; color: inherit; background: inherit;">(1)</code></span> <span style="display: inline-flex; align-items: center; padding: 0.2em 0.4em; font-weight: bold; text-shadow: black 0px 0px 10px; border-radius: 0.2em; background: rgb(63, 78, 96); opacity: 0.3;">inactive <code style="font-size: 0.9em; color: inherit; background: inherit;">(0)</code></span></span><div style="display: flex; align-items: center;"><input placeholder="Filter" aria-label="Filter by queryhash" style="background-color: rgb(255, 255, 255); border: 0px; border-radius: 0.2em; color: rgb(0, 0, 0); font-size: 0.9em; line-height: 1.3; padding: 0.3em 0.4em; flex: 1 1 0%; margin-right: 0.5em; width: 100%;" value=""><select aria-label="Sort queries" style="display: inline-block; font-size: 0.9em; font-family: sans-serif; font-weight: normal; line-height: 1.3; padding: 0.3em 1.5em 0.3em 0.5em; height: auto; border: 0px; border-radius: 0.2em; appearance: none; background-color: rgb(255, 255, 255); background-image: url(&quot;data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='%23444444'><polygon points='0,25 100,25 50,75'/></svg>&quot;); background-repeat: no-repeat; background-position: right 0.55em center; background-size: 0.65em, 100%; color: rgb(0, 0, 0); flex: 1 1 0%; min-width: 75px; margin-right: 0.5em;"><option value="Status > Last Updated">Sort by Status &gt; Last Updated</option><option value="Query Hash">Sort by Query Hash</option><option value="Last Updated">Sort by Last Updated</option></select><button type="button" style="appearance: none; font-size: 0.9em; font-weight: bold; background: rgb(63, 78, 96); border: 0px; border-radius: 0.3em; color: white; padding: 0.3em 0.4em; cursor: pointer;">⬆ Asc</button></div></div></div><div style="overflow-y: auto; flex: 1 1 0%;"><div role="button" aria-label="Open query details for [&quot;AUTH_USER&quot;]" style="display: flex; border-bottom: 1px solid rgb(34, 46, 62); cursor: pointer;"><div style="flex: 0 0 auto; width: 2em; height: 2em; background: rgb(0, 107, 255); display: flex; align-items: center; justify-content: center; font-weight: bold; text-shadow: black 0px 0px 10px; color: white;">1</div><code style="font-size: 0.9em; color: inherit; background: inherit; padding: 0.5em;">["AUTH_USER"]</code></div><div role="button" aria-label="Open query details for [&quot;backup_restore_status&quot;]" style="display: flex; border-bottom: 1px solid rgb(34, 46, 62); cursor: pointer;"><div style="flex: 0 0 auto; width: 2em; height: 2em; background: rgb(0, 107, 255); display: flex; align-items: center; justify-content: center; font-weight: bold; text-shadow: black 0px 0px 10px; color: white;">1</div><code style="font-size: 0.9em; color: inherit; background: inherit; padding: 0.5em;">["backup_restore_status"]</code></div><div role="button" aria-label="Open query details for [&quot;BETTERDOCS_INSTALL_STATUS&quot;]" style="display: flex; border-bottom: 1px solid rgb(34, 46, 62); cursor: pointer;"><div style="flex: 0 0 auto; width: 2em; height: 2em; background: rgb(255, 178, 0); display: flex; align-items: center; justify-content: center; font-weight: bold; color: black;">1</div><div style="flex: 0 0 auto; height: 2em; background: rgb(63, 78, 96); display: flex; align-items: center; font-weight: bold; padding: 0px 0.5em;">disabled</div><code style="font-size: 0.9em; color: inherit; background: inherit; padding: 0.5em;">["BETTERDOCS_INSTALL_STATUS"]</code></div></div></div></div><button type="button" aria-label="Open React Query Devtools" aria-controls="ReactQueryDevtoolsPanel" aria-haspopup="true" aria-expanded="false" style="background: none; border: 0px; padding: 0px; position: fixed; z-index: 99999; display: inline-flex; font-size: 1.5em; margin: 0.5em; cursor: pointer; width: fit-content; bottom: 0px; left: 0px;"><svg width="40px" height="40px" viewBox="0 0 190 190" version="1.1" aria-hidden="true"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g transform="translate(-33.000000, 0.000000)"><path d="M72.7239712,61.3436237 C69.631224,46.362877 68.9675112,34.8727722 70.9666331,26.5293551 C72.1555965,21.5671678 74.3293088,17.5190846 77.6346064,14.5984631 C81.1241394,11.5150478 85.5360327,10.0020122 90.493257,10.0020122 C98.6712013,10.0020122 107.26826,13.7273214 116.455725,20.8044264 C120.20312,23.6910458 124.092437,27.170411 128.131651,31.2444746 C128.45314,30.8310265 128.816542,30.4410453 129.22143,30.0806152 C140.64098,19.9149716 150.255245,13.5989272 158.478408,11.1636507 C163.367899,9.715636 167.958526,9.57768202 172.138936,10.983031 C176.551631,12.4664684 180.06766,15.5329489 182.548314,19.8281091 C186.642288,26.9166735 187.721918,36.2310983 186.195595,47.7320243 C185.573451,52.4199112 184.50985,57.5263831 183.007094,63.0593153 C183.574045,63.1277086 184.142416,63.2532808 184.705041,63.4395297 C199.193932,68.2358678 209.453582,73.3937462 215.665021,79.2882839 C219.360669,82.7953831 221.773972,86.6998434 222.646365,91.0218204 C223.567176,95.5836746 222.669313,100.159332 220.191548,104.451297 C216.105211,111.529614 208.591643,117.11221 197.887587,121.534031 C193.589552,123.309539 188.726579,124.917559 183.293259,126.363748 C183.541176,126.92292 183.733521,127.516759 183.862138,128.139758 C186.954886,143.120505 187.618598,154.61061 185.619477,162.954027 C184.430513,167.916214 182.256801,171.964297 178.951503,174.884919 C175.46197,177.968334 171.050077,179.48137 166.092853,179.48137 C157.914908,179.48137 149.31785,175.756061 140.130385,168.678956 C136.343104,165.761613 132.410866,162.238839 128.325434,158.108619 C127.905075,158.765474 127.388968,159.376011 126.77857,159.919385 C115.35902,170.085028 105.744755,176.401073 97.5215915,178.836349 C92.6321009,180.284364 88.0414736,180.422318 83.8610636,179.016969 C79.4483686,177.533532 75.9323404,174.467051 73.4516862,170.171891 C69.3577116,163.083327 68.2780823,153.768902 69.8044053,142.267976 C70.449038,137.410634 71.56762,132.103898 73.1575891,126.339009 C72.5361041,126.276104 71.9120754,126.144816 71.2949591,125.940529 C56.8060684,121.144191 46.5464184,115.986312 40.3349789,110.091775 C36.6393312,106.584675 34.2260275,102.680215 33.3536352,98.3582381 C32.4328237,93.7963839 33.3306866,89.2207269 35.8084524,84.9287618 C39.8947886,77.8504443 47.4083565,72.2678481 58.1124133,67.8460273 C62.5385143,66.0176154 67.5637208,64.366822 73.1939394,62.8874674 C72.9933393,62.3969171 72.8349374,61.8811235 72.7239712,61.3436237 Z" fill="#002C4B" fill-rule="nonzero" transform="translate(128.000000, 95.000000) scale(-1, 1) translate(-128.000000, -95.000000) "></path><path d="M113.396882,64 L142.608177,64 C144.399254,64 146.053521,64.958025 146.944933,66.5115174 L161.577138,92.0115174 C162.461464,93.5526583 162.461464,95.4473417 161.577138,96.9884826 L146.944933,122.488483 C146.053521,124.041975 144.399254,125 142.608177,125 L113.396882,125 C111.605806,125 109.951539,124.041975 109.060126,122.488483 L94.4279211,96.9884826 C93.543596,95.4473417 93.543596,93.5526583 94.4279211,92.0115174 L109.060126,66.5115174 C109.951539,64.958025 111.605806,64 113.396882,64 Z M138.987827,70.2765273 C140.779849,70.2765273 142.434839,71.2355558 143.325899,72.7903404 L154.343038,92.0138131 C155.225607,93.5537825 155.225607,95.4462175 154.343038,96.9861869 L143.325899,116.20966 C142.434839,117.764444 140.779849,118.723473 138.987827,118.723473 L117.017233,118.723473 C115.225211,118.723473 113.570221,117.764444 112.67916,116.20966 L101.662022,96.9861869 C100.779452,95.4462175 100.779452,93.5537825 101.662022,92.0138131 L112.67916,72.7903404 C113.570221,71.2355558 115.225211,70.2765273 117.017233,70.2765273 L138.987827,70.2765273 Z M135.080648,77.1414791 L120.924411,77.1414791 C119.134228,77.1414791 117.480644,78.0985567 116.5889,79.6508285 L116.5889,79.6508285 L109.489217,92.0093494 C108.603232,93.5515958 108.603232,95.4484042 109.489217,96.9906506 L109.489217,96.9906506 L116.5889,109.349172 C117.480644,110.901443 119.134228,111.858521 120.924411,111.858521 L120.924411,111.858521 L135.080648,111.858521 C136.870831,111.858521 138.524416,110.901443 139.41616,109.349172 L139.41616,109.349172 L146.515843,96.9906506 C147.401828,95.4484042 147.401828,93.5515958 146.515843,92.0093494 L146.515843,92.0093494 L139.41616,79.6508285 C138.524416,78.0985567 136.870831,77.1414791 135.080648,77.1414791 L135.080648,77.1414791 Z M131.319186,83.7122186 C133.108028,83.7122186 134.760587,84.6678753 135.652827,86.2183156 L138.983552,92.0060969 C139.87203,93.5500005 139.87203,95.4499995 138.983552,96.9939031 L135.652827,102.781684 C134.760587,104.332125 133.108028,105.287781 131.319186,105.287781 L124.685874,105.287781 C122.897032,105.287781 121.244473,104.332125 120.352233,102.781684 L117.021508,96.9939031 C116.13303,95.4499995 116.13303,93.5500005 117.021508,92.0060969 L120.352233,86.2183156 C121.244473,84.6678753 122.897032,83.7122186 124.685874,83.7122186 L131.319186,83.7122186 Z M128.003794,90.1848875 C126.459294,90.1848875 125.034382,91.0072828 124.263005,92.3424437 C123.491732,93.6774232 123.491732,95.3225768 124.263005,96.6575563 C125.034382,97.9927172 126.459294,98.8151125 128.001266,98.8151125 L128.001266,98.8151125 C129.545766,98.8151125 130.970678,97.9927172 131.742055,96.6575563 C132.513327,95.3225768 132.513327,93.6774232 131.742055,92.3424437 C130.970678,91.0072828 129.545766,90.1848875 128.003794,90.1848875 L128.003794,90.1848875 Z M93,94.5009646 L100.767764,94.5009646" fill="#FFD94C"></path><path d="M87.8601729,108.357758 C89.1715224,107.608286 90.8360246,108.074601 91.5779424,109.399303 L91.5779424,109.399303 L92.0525843,110.24352 C95.8563392,116.982993 99.8190116,123.380176 103.940602,129.435068 C108.807881,136.585427 114.28184,143.82411 120.362479,151.151115 C121.316878,152.30114 121.184944,154.011176 120.065686,154.997937 L120.065686,154.997937 L119.454208,155.534625 C99.3465389,173.103314 86.2778188,176.612552 80.2480482,166.062341 C74.3500652,155.742717 76.4844915,136.982888 86.6513274,109.782853 C86.876818,109.179582 87.3045861,108.675291 87.8601729,108.357758 Z M173.534177,129.041504 C174.986131,128.785177 176.375496,129.742138 176.65963,131.194242 L176.65963,131.194242 L176.812815,131.986376 C181.782365,157.995459 178.283348,171 166.315764,171 C154.609745,171 139.708724,159.909007 121.612702,137.727022 C121.211349,137.235047 120.994572,136.617371 121,135.981509 C121.013158,134.480686 122.235785,133.274651 123.730918,133.287756 L123.730918,133.287756 L124.684654,133.294531 C132.305698,133.335994 139.714387,133.071591 146.910723,132.501323 C155.409039,131.82788 164.283523,130.674607 173.534177,129.041504 Z M180.408726,73.8119663 C180.932139,72.4026903 182.508386,71.6634537 183.954581,72.149012 L183.954581,72.149012 L184.742552,72.4154854 C210.583763,81.217922 220.402356,90.8916805 214.198332,101.436761 C208.129904,111.751366 190.484347,119.260339 161.26166,123.963678 C160.613529,124.067994 159.948643,123.945969 159.382735,123.618843 C158.047025,122.846729 157.602046,121.158214 158.388848,119.847438 L158.388848,119.847438 L158.889328,119.0105 C162.877183,112.31633 166.481358,105.654262 169.701854,99.0242957 C173.50501,91.1948179 177.073967,82.7907081 180.408726,73.8119663 Z M94.7383398,66.0363218 C95.3864708,65.9320063 96.0513565,66.0540315 96.6172646,66.3811573 C97.9529754,67.153271 98.3979538,68.8417862 97.6111517,70.1525615 L97.6111517,70.1525615 L97.1106718,70.9895001 C93.1228168,77.6836699 89.5186416,84.3457379 86.2981462,90.9757043 C82.49499,98.8051821 78.9260328,107.209292 75.5912744,116.188034 C75.0678608,117.59731 73.4916142,118.336546 72.045419,117.850988 L72.045419,117.850988 L71.2574475,117.584515 C45.4162372,108.782078 35.597644,99.1083195 41.8016679,88.5632391 C47.8700957,78.2486335 65.515653,70.7396611 94.7383398,66.0363218 Z M136.545792,34.4653746 C156.653461,16.8966864 169.722181,13.3874478 175.751952,23.9376587 C181.649935,34.2572826 179.515508,53.0171122 169.348673,80.2171474 C169.123182,80.8204179 168.695414,81.324709 168.139827,81.6422422 C166.828478,82.3917144 165.163975,81.9253986 164.422058,80.6006966 L164.422058,80.6006966 L163.947416,79.7564798 C160.143661,73.0170065 156.180988,66.6198239 152.059398,60.564932 C147.192119,53.4145727 141.71816,46.1758903 135.637521,38.8488847 C134.683122,37.6988602 134.815056,35.9888243 135.934314,35.0020629 L135.934314,35.0020629 Z M90.6842361,18 C102.390255,18 117.291276,29.0909926 135.387298,51.2729777 C135.788651,51.7649527 136.005428,52.3826288 136,53.0184911 C135.986842,54.5193144 134.764215,55.7253489 133.269082,55.7122445 L133.269082,55.7122445 L132.315346,55.7054689 C124.694302,55.6640063 117.285613,55.9284091 110.089277,56.4986773 C101.590961,57.17212 92.7164767,58.325393 83.4658235,59.9584962 C82.0138691,60.2148231 80.6245044,59.2578618 80.3403697,57.805758 L80.3403697,57.805758 L80.1871846,57.0136235 C75.2176347,31.0045412 78.7166519,18 90.6842361,18 Z" fill="#FF4154"></path></g></g></svg></button></aside></div></main></div>`;

const ONBOARDING_SKELETON = `<div class="Polaris-Frame" data-polaris-layer="true"><div class="Polaris-Frame__Skip"><a href="#AppFrameMain"><span class="Polaris-Text--root Polaris-Text--bodyLg Polaris-Text--medium">Skip to content</span></a></div><div class="Polaris-Frame__ContextualSaveBar Polaris-Frame-CSSAnimation--startFade"></div><main class="Polaris-Frame__Main" id="AppFrameMain" data-has-global-ribbon="false"><div class="Polaris-Frame__Content"><ui-nav-menu><a rel="home" href="/">Home</a><a rel="" href="/optimize-seo">Optimize SEO</a><a rel="" href="/image-optimizer">Image Optimizer</a><a rel="" href="/image-alt-text">Image Alt Text Generator</a><a rel="" href="/local-seo">SEO Schema</a><a rel="" href="/analytics">Analytics</a><a rel="" href="/reports">Reports</a><a rel="" href="/sitemaps">Sitemaps</a><a rel="" href="/settings">Settings</a><a rel="" href="/subscription">Subscription Plans</a><a rel="" href="/credit-bundles">AI Content Optimizer</a></ui-nav-menu><div style="display: flex; flex-direction: column; min-height: 100vh;"><div style="flex: 1 1 0%;"><div class="Polaris-Page"><div class="Polaris-Page__Content"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper" style="line-height: 0px;"><div class="skeleton-box" style="width: 135px; height: 32px;"></div></div><div class="skeleton-wrapper" style="line-height: 0px;"><div class="skeleton-box" style="width: 50%; height: 13px;"></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-block-align: center; --pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-400); --pc-inline-stack-flex-direction-xs: row;"><p class="Polaris-Text--root">1 out of 4 steps</p><div style="flex: 1 1 0%;"><div class="Polaris-ProgressBar Polaris-ProgressBar--sizeSmall Polaris-ProgressBar--toneHighlight"><progress class="Polaris-ProgressBar__Progress" value="0" max="100"></progress><div class="Polaris-ProgressBar__Indicator Polaris-ProgressBar__IndicatorAppearDone" style="--pc-progress-bar-duration: 500ms; --pc-progress-bar-percent: 0;"><span class="Polaris-ProgressBar__Label">0%</span></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="skeleton-wrapper" style="line-height: 1px;"><div class="skeleton-box" style="width: 220px; height: 24px;"></div></div><div class="Polaris-Grid" style="--pc-grid-columns-xs: 1; --pc-grid-columns-sm: 2; --pc-grid-columns-md: 2; --pc-grid-columns-lg: 2; --pc-grid-columns-xl: 2;"><div class="Polaris-Grid-Cell"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: start; --pc-inline-stack-block-align: start; --pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-Box" style="--pc-box-width: 70%;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="skeleton-wrapper" style="line-height: 10px;"><div class="skeleton-box" style="width: 50%; height: 20px;"></div></div><div class="skeleton-wrapper" style="line-height: 0px;"><div class="skeleton-box" style="width: 80%; height: 13px;"></div></div></div></div></div></div><div class="Polaris-Grid-Cell"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: start; --pc-inline-stack-block-align: start; --pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-Box" style="--pc-box-width: 70%;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="skeleton-wrapper" style="line-height: 10px;"><div class="skeleton-box" style="width: 50%; height: 20px;"></div></div><div class="skeleton-wrapper" style="line-height: 0px;"><div class="skeleton-box" style="width: 80%; height: 13px;"></div></div></div></div></div></div><div class="Polaris-Grid-Cell"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: start; --pc-inline-stack-block-align: start; --pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-Box" style="--pc-box-width: 70%;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="skeleton-wrapper" style="line-height: 10px;"><div class="skeleton-box" style="width: 50%; height: 20px;"></div></div><div class="skeleton-wrapper" style="line-height: 0px;"><div class="skeleton-box" style="width: 80%; height: 13px;"></div></div></div></div></div></div><div class="Polaris-Grid-Cell"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: start; --pc-inline-stack-block-align: start; --pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-Box" style="--pc-box-width: 70%;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="skeleton-wrapper" style="line-height: 10px;"><div class="skeleton-box" style="width: 50%; height: 20px;"></div></div><div class="skeleton-wrapper" style="line-height: 0px;"><div class="skeleton-box" style="width: 80%; height: 13px;"></div></div></div></div></div></div><div class="Polaris-Grid-Cell"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: start; --pc-inline-stack-block-align: start; --pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-Box" style="--pc-box-width: 70%;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="skeleton-wrapper" style="line-height: 10px;"><div class="skeleton-box" style="width: 50%; height: 20px;"></div></div><div class="skeleton-wrapper" style="line-height: 0px;"><div class="skeleton-box" style="width: 80%; height: 13px;"></div></div></div></div></div></div><div class="Polaris-Grid-Cell"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: start; --pc-inline-stack-block-align: start; --pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-Box" style="--pc-box-width: 70%;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="skeleton-wrapper" style="line-height: 10px;"><div class="skeleton-box" style="width: 50%; height: 20px;"></div></div><div class="skeleton-wrapper" style="line-height: 0px;"><div class="skeleton-box" style="width: 80%; height: 13px;"></div></div></div></div></div></div><div class="Polaris-Grid-Cell"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: start; --pc-inline-stack-block-align: start; --pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-Box" style="--pc-box-width: 70%;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="skeleton-wrapper" style="line-height: 10px;"><div class="skeleton-box" style="width: 50%; height: 20px;"></div></div><div class="skeleton-wrapper" style="line-height: 0px;"><div class="skeleton-box" style="width: 80%; height: 13px;"></div></div></div></div></div></div><div class="Polaris-Grid-Cell"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: start; --pc-inline-stack-block-align: start; --pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-Box" style="--pc-box-width: 70%;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="skeleton-wrapper" style="line-height: 10px;"><div class="skeleton-box" style="width: 50%; height: 20px;"></div></div><div class="skeleton-wrapper" style="line-height: 0px;"><div class="skeleton-box" style="width: 80%; height: 13px;"></div></div></div></div></div></div><div class="Polaris-Grid-Cell"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: start; --pc-inline-stack-block-align: start; --pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-Box" style="--pc-box-width: 70%;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="skeleton-wrapper" style="line-height: 10px;"><div class="skeleton-box" style="width: 50%; height: 20px;"></div></div><div class="skeleton-wrapper" style="line-height: 0px;"><div class="skeleton-box" style="width: 80%; height: 13px;"></div></div></div></div></div></div><div class="Polaris-Grid-Cell"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: start; --pc-inline-stack-block-align: start; --pc-inline-stack-wrap: nowrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-Box" style="--pc-box-width: 70%;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="skeleton-wrapper" style="line-height: 10px;"><div class="skeleton-box" style="width: 50%; height: 20px;"></div></div><div class="skeleton-wrapper" style="line-height: 0px;"><div class="skeleton-box" style="width: 80%; height: 13px;"></div></div></div></div></div></div></div></div></div></div></div></div></div></div><footer class="footer"><a data-polaris-unstyled="true" class="Polaris-Link" url="/" href="/"><svg width="100" height="34" viewBox="0 0 100 34" fill="none" xmlns="http://www.w3.org/2000/svg"><mask id="mask0_44407_102018" maskUnits="userSpaceOnUse" x="3" y="3" width="94" height="28"><path d="M96.6795 3.42969H3.32007V30.5721H96.6795V3.42969Z" fill="white"></path></mask><g mask="url(#mask0_44407_102018)"><path d="M38.8228 23.2729C37.67 23.2729 36.6819 22.9723 36.1549 22.7217V21.1349C36.9125 21.4857 37.7524 21.6862 38.444 21.6862C39.6298 21.6862 40.1732 21.185 40.1732 20.0993C40.1732 18.9635 39.745 18.6462 38.3782 17.8612C36.9125 17.0093 36.0232 16.3746 36.0232 14.5206C36.0232 12.416 37.3077 11.2969 39.7121 11.2969C40.5355 11.2969 41.3754 11.4305 42.1 11.6643L41.8859 13.1342C41.3919 12.9671 40.7167 12.867 40.0909 12.867C39.2345 12.867 38.3617 13.034 38.3617 14.2366C38.3617 15.005 38.6746 15.2722 40.0415 16.1241C41.6719 17.1262 42.5117 17.7442 42.5117 19.7486C42.5282 22.1371 41.326 23.2729 38.8228 23.2729Z" fill="#AFAFAF"></path><path d="M47.2023 23.2723C45.5061 23.2723 44.7485 22.4872 44.7485 20.7334V15.8728H43.6287V14.3528H44.8144L44.9626 12.2148H46.9388V14.3528H48.8492L48.7832 15.8728H46.9388V20.6499C46.9388 21.4683 47.1365 21.9026 48.1575 21.9026C48.3551 21.9026 48.5527 21.8692 48.7174 21.8024L48.6515 23.0217C48.2892 23.1888 47.7622 23.2723 47.2023 23.2723Z" fill="#AFAFAF"></path><path d="M53.6599 23.2744C50.5803 23.2744 50.1521 21.5206 50.1521 19.7835V17.6121C50.1521 15.2736 51.3049 14.1211 53.6599 14.1211C56.0313 14.1211 57.1841 15.2569 57.1841 17.6121V19.7835C57.1841 22.2388 56.1302 23.2744 53.6599 23.2744ZM53.6599 15.5074C52.4742 15.5074 52.3424 16.4261 52.3424 17.1109V20.3346C52.3424 21.0028 52.4742 21.9047 53.6599 21.9047C54.8456 21.9047 54.9774 21.0028 54.9774 20.3346V17.1109C54.9938 16.0252 54.5657 15.5074 53.6599 15.5074Z" fill="#AFAFAF"></path><path d="M58.7341 23.0718V14.3528H60.7433V15.9062C61.1715 14.6534 62.0772 14.1523 63.1642 14.1523C63.1971 14.1523 63.23 14.1523 63.2465 14.1523V16.1734C63.2136 16.1734 63.1971 16.1734 63.1642 16.1734C62.3242 16.1734 61.4515 16.4908 60.9574 16.9919L60.9245 17.0253V23.0884H58.7341V23.0718Z" fill="#AFAFAF"></path><path d="M68.2853 23.2722C65.8479 23.2722 64.4646 22.0028 64.4646 19.798V17.5932C64.4646 15.3048 65.6009 14.1523 67.8242 14.1523C70.0806 14.1523 71.1341 15.1712 71.1341 17.3761V19.1132H66.6219V19.8815C66.6219 21.1509 67.4621 21.8691 68.9111 21.8691C69.5041 21.8691 70.1298 21.7522 70.8874 21.4683L70.8212 22.8379C70.2282 23.1052 69.224 23.2722 68.2853 23.2722ZM67.8406 15.4385C66.7701 15.4385 66.6219 16.2235 66.6219 17.0086V18.0609H69.0429V17.0086C69.0429 16.2235 68.8947 15.4385 67.8406 15.4385Z" fill="#AFAFAF"></path><path d="M75.3422 23.2729C74.1898 23.2729 73.2019 22.9723 72.6746 22.7217V21.1349C73.4322 21.4857 74.2724 21.6862 74.9638 21.6862C76.1497 21.6862 76.6928 21.185 76.6928 20.0993C76.6928 18.9635 76.2645 18.6462 74.8982 17.8612C73.4322 17.0093 72.5427 16.3746 72.5427 14.5206C72.5427 12.416 73.8277 11.2969 76.2317 11.2969C77.0555 11.2969 77.8951 11.4305 78.6199 11.6643L78.4054 13.1342C77.9115 12.9671 77.2366 12.867 76.6108 12.867C75.7542 12.867 74.8811 13.034 74.8811 14.2366C74.8811 15.005 75.1946 15.2722 76.561 16.1241C78.1916 17.1262 79.0312 17.7442 79.0312 19.7486C79.0482 22.1371 77.8295 23.2729 75.3422 23.2729Z" fill="#AFAFAF"></path><path d="M80.6365 23.0719V11.5469H86.8783V13.0669H82.9749V16.5076H86.2355V18.0276H82.9749V21.5186H87.0259V23.0719H80.6365Z" fill="#AFAFAF"></path><path d="M92.4622 23.2719C89.7446 23.2719 88.2458 21.7018 88.2458 18.8623V15.7221C88.2458 12.916 89.7774 11.3125 92.4622 11.3125C95.2289 11.3125 96.6778 12.8325 96.6778 15.7221V18.8957C96.6778 21.6851 95.1298 23.2719 92.4622 23.2719ZM92.4622 12.8826C91.2106 12.8826 90.6833 13.6008 90.6833 15.2878V19.4135C90.6833 20.9836 91.227 21.7018 92.4622 21.7018C94.043 21.7018 94.2404 20.566 94.2404 19.4636V15.2878C94.2404 13.6008 93.7137 12.8826 92.4622 12.8826Z" fill="#AFAFAF"></path><path d="M31.8226 4.98307C31.7073 4.46528 31.3779 4.0143 30.9332 3.73034C30.6204 3.52991 30.258 3.42969 29.8958 3.42969C29.2041 3.42969 28.5618 3.78045 28.1995 4.38176L17.4621 22.3041C16.8857 23.2563 17.1822 24.4923 18.1209 25.0602C18.4337 25.2606 18.796 25.3608 19.1584 25.3608C19.85 25.3608 20.4923 25.0101 20.8546 24.4088L31.6085 6.51976C31.872 6.05207 31.9543 5.51757 31.8226 4.98307Z" fill="#AFAFAF"></path><path d="M20.6274 8.33854C20.5121 7.82074 20.1828 7.36976 19.7381 7.08581C19.4252 6.88537 19.0629 6.78516 18.7006 6.78516C18.0089 6.78516 17.3667 7.13592 17.0043 7.73722L8.70428 21.3669C8.12789 22.319 8.42433 23.555 9.36302 24.1229C9.67593 24.3234 10.0382 24.4235 10.4005 24.4235C11.0922 24.4235 11.7344 24.0728 12.0968 23.4715L20.3969 9.84181C20.6768 9.39083 20.7591 8.85633 20.6274 8.33854Z" fill="#AFAFAF"></path><path d="M11.2862 9.59244C11.1709 9.07465 10.8415 8.62367 10.3969 8.33971C10.084 8.13927 9.72166 8.03906 9.35939 8.03906C8.66772 8.03906 8.02545 8.38982 7.66315 8.99113L3.61192 15.3717C3.33196 15.8227 3.24962 16.3739 3.38136 16.8917C3.49664 17.4095 3.80954 17.8604 4.25419 18.1444C4.56709 18.3448 4.92939 18.4451 5.2917 18.4451C5.98337 18.4451 6.62564 18.0943 6.98794 17.493L11.0556 11.1124C11.3356 10.6614 11.418 10.1269 11.2862 9.59244Z" fill="#AFAFAF"></path><path d="M9.10021 28.3024C8.96846 27.317 8.12857 26.582 7.15694 26.582C7.05813 26.582 6.97579 26.582 6.87697 26.5987C5.80653 26.7491 5.04899 27.7679 5.21367 28.8703C5.34542 29.8558 6.1853 30.5907 7.15694 30.5907C7.25574 30.5907 7.33809 30.5907 7.4369 30.5741C8.49087 30.4237 9.24842 29.4048 9.10021 28.3024Z" fill="#AFAFAF"></path><path d="M17.8895 28.3024C17.7578 27.317 16.9179 26.582 15.9462 26.582C15.8475 26.582 15.7651 26.582 15.6663 26.5987C14.5958 26.7491 13.8383 27.7679 14.003 28.8703C14.1348 29.8558 14.9746 30.5907 15.9462 30.5907C16.0451 30.5907 16.1274 30.5907 16.2262 30.5741C16.7532 30.4905 17.2143 30.2233 17.5272 29.789C17.8401 29.3714 17.9719 28.8369 17.8895 28.3024Z" fill="#AFAFAF"></path><path d="M23.8231 11.7832C23.2302 12.635 22.0939 12.9023 21.2046 12.3511C20.3153 11.7999 20.0024 10.6307 20.4635 9.69531L20.2659 10.0127L13.9255 20.4354C13.9421 20.4186 13.9421 20.4186 13.9585 20.402C13.975 20.3852 13.9915 20.3519 14.0079 20.3351C14.0244 20.3184 14.0408 20.3017 14.0573 20.285C14.4196 19.8841 14.9466 19.6503 15.5065 19.6503C16.6099 19.6503 17.5157 20.5523 17.5157 21.6881C17.5157 21.9553 17.4663 22.2226 17.3675 22.4564L21.1388 16.2095L23.8231 11.7832Z" fill="#E7E7E7"></path><path d="M13.9529 20.4183C13.9693 20.4016 13.9858 20.3682 14.0023 20.3516C14.0023 20.3682 13.9693 20.385 13.9529 20.4183Z" fill="#E54141"></path></g></svg></a><div class="Polaris-InlineStack" style="--pc-inline-stack-align: center; --pc-inline-stack-block-align: center; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><span class="Polaris-Text--root Polaris-Text--subdued">Copyright © 2025</span><strong class="Polaris-Text--root Polaris-Text--subdued">|</strong><a data-polaris-unstyled="true" class="Polaris-Link Polaris-Link--monochrome Polaris-Link--removeUnderline" url="https://storeware.io/" href="https://storeware.io/" target="_blank"><strong class="Polaris-Text--root Polaris-Text--subdued">Storeware</strong></a><strong class="Polaris-Text--root Polaris-Text--subdued">|</strong><strong class="Polaris-Text--root Polaris-Text--subdued">Version: <a data-polaris-unstyled="true" class="Polaris-Link Polaris-Link--monochrome Polaris-Link--removeUnderline" url="https://storeseo.com/changelog/" href="https://storeseo.com/changelog/" target="_blank">3.4.6</a></strong>|<div><div class="Polaris-Box" style="display: flex; align-items: center;"><button class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantMonochromePlain Polaris-Button--sizeMedium Polaris-Button--textAlignCenter Polaris-Button--disclosure Polaris-Button--iconOnly" type="button" tabindex="0" aria-controls=":r2:" aria-owns=":r2:" aria-expanded="false" data-state="closed"><span class="Polaris-Button__Icon"><span class="Polaris-Icon"><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="M3 10a7 7 0 1 1 14 0 7 7 0 0 1-14 0Zm7-5.5a5.497 5.497 0 0 0-4.737 2.703l2 1.999c.472.472.737 1.113.737 1.78v.518a.5.5 0 0 0 .5.5 2 2 0 0 1 2 2v1.478a5.504 5.504 0 0 0 4.52-3.228h-1.02a.75.75 0 0 1-.75-.75v-.5a.75.75 0 0 0-.75-.75h-2.5a1.755 1.755 0 0 1-1.07-3.144l.463-.356a.393.393 0 0 0 .152-.312v-.04c0-.885.62-1.624 1.449-1.808a5.531 5.531 0 0 0-.994-.09Zm2.875.81a1.85 1.85 0 0 1-1.477.735.352.352 0 0 0-.353.353v.04c0 .587-.271 1.14-.736 1.499l-.462.356a.256.256 0 0 0 .153.457h2.5a2.25 2.25 0 0 1 2.236 2h.713a5.497 5.497 0 0 0-2.574-5.44Zm-8.375 4.69c0-.443.052-.875.152-1.288l1.55 1.55c.19.191.298.45.298.72v.518a2 2 0 0 0 2 2 .5.5 0 0 1 .5.5v1.41a5.502 5.502 0 0 1-4.5-5.41Z"></path></svg></span></span><span class="Polaris-Button__Icon"><span class="Polaris-Icon"><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="M14.53 12.28a.75.75 0 0 1-1.06 0l-3.47-3.47-3.47 3.47a.75.75 0 0 1-1.06-1.06l4-4a.75.75 0 0 1 1.06 0l4 4a.75.75 0 0 1 0 1.06Z"></path></svg></span></span></button></div></div></div><div></div></footer></div><div><div class="notification-icon"><span class="" tabindex="0" aria-describedby=":r5:" data-polaris-tooltip-activator="true" aria-controls=":r4:" aria-owns=":r4:" aria-expanded="false" data-state="closed" aria-haspopup="false"><span style="position: relative; top: 7px;"><span class="Polaris-Icon"><svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path fill-rule="evenodd" d="m7.252 14.424-2.446-.281c-1.855-.213-2.38-2.659-.778-3.616l.065-.038a2.887 2.887 0 0 0 1.407-2.48v-.509a4.5 4.5 0 0 1 9 0v.51c0 1.016.535 1.958 1.408 2.479l.065.038c1.602.957 1.076 3.403-.778 3.616l-2.543.292v.365a2.7 2.7 0 0 1-5.4 0v-.376Zm3.9.076h-2.4v.3a1.2 1.2 0 0 0 2.4 0v-.3Zm-3.152-1.5h4l3.024-.348a.452.452 0 0 0 .18-.837l-.065-.038a4.414 4.414 0 0 1-.747-.562 4.387 4.387 0 0 1-1.392-3.205v-.51a3 3 0 0 0-6 0v.51a4.387 4.387 0 0 1-2.138 3.767l-.065.038a.452.452 0 0 0 .18.838l3.023.347Z"></path></svg></span></span></span><span style="width: 8px; height: 8px; background: var(--p-color-bg-fill); border-radius: var(--p-border-radius-full); position: absolute; top: 8px; right: 8px;"></span></div></div><aside class="ReactQueryDevtools" aria-label="React Query Devtools"><div class="ReactQueryDevtoolsPanel" aria-label="React Query Devtools Panel" id="ReactQueryDevtoolsPanel" style="font-size: clamp(12px, 1.5vw, 14px); font-family: sans-serif; display: flex; background-color: rgb(11, 21, 33); color: white; position: fixed; bottom: 0px; right: 0px; z-index: 99999; width: 100%; height: 500px; max-height: 90%; box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 20px; border-top: 1px solid rgb(63, 78, 96); transform-origin: center top; visibility: hidden; transition: 0.2s; opacity: 0; pointer-events: none; transform: translateY(15px) scale(1.02);"><style>
            .ReactQueryDevtoolsPanel * {
              scrollbar-color: #132337 #3f4e60;
            }

            .ReactQueryDevtoolsPanel *::-webkit-scrollbar, .ReactQueryDevtoolsPanel scrollbar {
              width: 1em;
              height: 1em;
            }

            .ReactQueryDevtoolsPanel *::-webkit-scrollbar-track, .ReactQueryDevtoolsPanel scrollbar-track {
              background: #132337;
            }

            .ReactQueryDevtoolsPanel *::-webkit-scrollbar-thumb, .ReactQueryDevtoolsPanel scrollbar-thumb {
              background: #3f4e60;
              border-radius: .5em;
              border: 3px solid #132337;
            }
          </style><div style="position: absolute; left: 0px; top: 0px; width: 100%; height: 4px; margin-bottom: -4px; cursor: row-resize; z-index: 100000;"></div><div style="flex: 1 1 500px; min-height: 40%; max-height: 100%; overflow: auto; border-right: 1px solid rgb(34, 46, 62); display: none; flex-direction: column;"><div style="padding: 0.5em; background: rgb(19, 35, 55); display: flex; justify-content: space-between; align-items: center;"><button type="button" aria-label="Close React Query Devtools" aria-controls="ReactQueryDevtoolsPanel" aria-haspopup="true" aria-expanded="true" style="display: inline-flex; background: none; border: 0px; padding: 0px; margin-right: 0.5em; cursor: pointer;"><svg width="40px" height="40px" viewBox="0 0 190 190" version="1.1" aria-hidden="true"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g transform="translate(-33.000000, 0.000000)"><path d="M72.7239712,61.3436237 C69.631224,46.362877 68.9675112,34.8727722 70.9666331,26.5293551 C72.1555965,21.5671678 74.3293088,17.5190846 77.6346064,14.5984631 C81.1241394,11.5150478 85.5360327,10.0020122 90.493257,10.0020122 C98.6712013,10.0020122 107.26826,13.7273214 116.455725,20.8044264 C120.20312,23.6910458 124.092437,27.170411 128.131651,31.2444746 C128.45314,30.8310265 128.816542,30.4410453 129.22143,30.0806152 C140.64098,19.9149716 150.255245,13.5989272 158.478408,11.1636507 C163.367899,9.715636 167.958526,9.57768202 172.138936,10.983031 C176.551631,12.4664684 180.06766,15.5329489 182.548314,19.8281091 C186.642288,26.9166735 187.721918,36.2310983 186.195595,47.7320243 C185.573451,52.4199112 184.50985,57.5263831 183.007094,63.0593153 C183.574045,63.1277086 184.142416,63.2532808 184.705041,63.4395297 C199.193932,68.2358678 209.453582,73.3937462 215.665021,79.2882839 C219.360669,82.7953831 221.773972,86.6998434 222.646365,91.0218204 C223.567176,95.5836746 222.669313,100.159332 220.191548,104.451297 C216.105211,111.529614 208.591643,117.11221 197.887587,121.534031 C193.589552,123.309539 188.726579,124.917559 183.293259,126.363748 C183.541176,126.92292 183.733521,127.516759 183.862138,128.139758 C186.954886,143.120505 187.618598,154.61061 185.619477,162.954027 C184.430513,167.916214 182.256801,171.964297 178.951503,174.884919 C175.46197,177.968334 171.050077,179.48137 166.092853,179.48137 C157.914908,179.48137 149.31785,175.756061 140.130385,168.678956 C136.343104,165.761613 132.410866,162.238839 128.325434,158.108619 C127.905075,158.765474 127.388968,159.376011 126.77857,159.919385 C115.35902,170.085028 105.744755,176.401073 97.5215915,178.836349 C92.6321009,180.284364 88.0414736,180.422318 83.8610636,179.016969 C79.4483686,177.533532 75.9323404,174.467051 73.4516862,170.171891 C69.3577116,163.083327 68.2780823,153.768902 69.8044053,142.267976 C70.449038,137.410634 71.56762,132.103898 73.1575891,126.339009 C72.5361041,126.276104 71.9120754,126.144816 71.2949591,125.940529 C56.8060684,121.144191 46.5464184,115.986312 40.3349789,110.091775 C36.6393312,106.584675 34.2260275,102.680215 33.3536352,98.3582381 C32.4328237,93.7963839 33.3306866,89.2207269 35.8084524,84.9287618 C39.8947886,77.8504443 47.4083565,72.2678481 58.1124133,67.8460273 C62.5385143,66.0176154 67.5637208,64.366822 73.1939394,62.8874674 C72.9933393,62.3969171 72.8349374,61.8811235 72.7239712,61.3436237 Z" fill="#002C4B" fill-rule="nonzero" transform="translate(128.000000, 95.000000) scale(-1, 1) translate(-128.000000, -95.000000) "></path><path d="M113.396882,64 L142.608177,64 C144.399254,64 146.053521,64.958025 146.944933,66.5115174 L161.577138,92.0115174 C162.461464,93.5526583 162.461464,95.4473417 161.577138,96.9884826 L146.944933,122.488483 C146.053521,124.041975 144.399254,125 142.608177,125 L113.396882,125 C111.605806,125 109.951539,124.041975 109.060126,122.488483 L94.4279211,96.9884826 C93.543596,95.4473417 93.543596,93.5526583 94.4279211,92.0115174 L109.060126,66.5115174 C109.951539,64.958025 111.605806,64 113.396882,64 Z M138.987827,70.2765273 C140.779849,70.2765273 142.434839,71.2355558 143.325899,72.7903404 L154.343038,92.0138131 C155.225607,93.5537825 155.225607,95.4462175 154.343038,96.9861869 L143.325899,116.20966 C142.434839,117.764444 140.779849,118.723473 138.987827,118.723473 L117.017233,118.723473 C115.225211,118.723473 113.570221,117.764444 112.67916,116.20966 L101.662022,96.9861869 C100.779452,95.4462175 100.779452,93.5537825 101.662022,92.0138131 L112.67916,72.7903404 C113.570221,71.2355558 115.225211,70.2765273 117.017233,70.2765273 L138.987827,70.2765273 Z M135.080648,77.1414791 L120.924411,77.1414791 C119.134228,77.1414791 117.480644,78.0985567 116.5889,79.6508285 L116.5889,79.6508285 L109.489217,92.0093494 C108.603232,93.5515958 108.603232,95.4484042 109.489217,96.9906506 L109.489217,96.9906506 L116.5889,109.349172 C117.480644,110.901443 119.134228,111.858521 120.924411,111.858521 L120.924411,111.858521 L135.080648,111.858521 C136.870831,111.858521 138.524416,110.901443 139.41616,109.349172 L139.41616,109.349172 L146.515843,96.9906506 C147.401828,95.4484042 147.401828,93.5515958 146.515843,92.0093494 L146.515843,92.0093494 L139.41616,79.6508285 C138.524416,78.0985567 136.870831,77.1414791 135.080648,77.1414791 L135.080648,77.1414791 Z M131.319186,83.7122186 C133.108028,83.7122186 134.760587,84.6678753 135.652827,86.2183156 L138.983552,92.0060969 C139.87203,93.5500005 139.87203,95.4499995 138.983552,96.9939031 L135.652827,102.781684 C134.760587,104.332125 133.108028,105.287781 131.319186,105.287781 L124.685874,105.287781 C122.897032,105.287781 121.244473,104.332125 120.352233,102.781684 L117.021508,96.9939031 C116.13303,95.4499995 116.13303,93.5500005 117.021508,92.0060969 L120.352233,86.2183156 C121.244473,84.6678753 122.897032,83.7122186 124.685874,83.7122186 L131.319186,83.7122186 Z M128.003794,90.1848875 C126.459294,90.1848875 125.034382,91.0072828 124.263005,92.3424437 C123.491732,93.6774232 123.491732,95.3225768 124.263005,96.6575563 C125.034382,97.9927172 126.459294,98.8151125 128.001266,98.8151125 L128.001266,98.8151125 C129.545766,98.8151125 130.970678,97.9927172 131.742055,96.6575563 C132.513327,95.3225768 132.513327,93.6774232 131.742055,92.3424437 C130.970678,91.0072828 129.545766,90.1848875 128.003794,90.1848875 L128.003794,90.1848875 Z M93,94.5009646 L100.767764,94.5009646" fill="#FFD94C"></path><path d="M87.8601729,108.357758 C89.1715224,107.608286 90.8360246,108.074601 91.5779424,109.399303 L91.5779424,109.399303 L92.0525843,110.24352 C95.8563392,116.982993 99.8190116,123.380176 103.940602,129.435068 C108.807881,136.585427 114.28184,143.82411 120.362479,151.151115 C121.316878,152.30114 121.184944,154.011176 120.065686,154.997937 L120.065686,154.997937 L119.454208,155.534625 C99.3465389,173.103314 86.2778188,176.612552 80.2480482,166.062341 C74.3500652,155.742717 76.4844915,136.982888 86.6513274,109.782853 C86.876818,109.179582 87.3045861,108.675291 87.8601729,108.357758 Z M173.534177,129.041504 C174.986131,128.785177 176.375496,129.742138 176.65963,131.194242 L176.65963,131.194242 L176.812815,131.986376 C181.782365,157.995459 178.283348,171 166.315764,171 C154.609745,171 139.708724,159.909007 121.612702,137.727022 C121.211349,137.235047 120.994572,136.617371 121,135.981509 C121.013158,134.480686 122.235785,133.274651 123.730918,133.287756 L123.730918,133.287756 L124.684654,133.294531 C132.305698,133.335994 139.714387,133.071591 146.910723,132.501323 C155.409039,131.82788 164.283523,130.674607 173.534177,129.041504 Z M180.408726,73.8119663 C180.932139,72.4026903 182.508386,71.6634537 183.954581,72.149012 L183.954581,72.149012 L184.742552,72.4154854 C210.583763,81.217922 220.402356,90.8916805 214.198332,101.436761 C208.129904,111.751366 190.484347,119.260339 161.26166,123.963678 C160.613529,124.067994 159.948643,123.945969 159.382735,123.618843 C158.047025,122.846729 157.602046,121.158214 158.388848,119.847438 L158.388848,119.847438 L158.889328,119.0105 C162.877183,112.31633 166.481358,105.654262 169.701854,99.0242957 C173.50501,91.1948179 177.073967,82.7907081 180.408726,73.8119663 Z M94.7383398,66.0363218 C95.3864708,65.9320063 96.0513565,66.0540315 96.6172646,66.3811573 C97.9529754,67.153271 98.3979538,68.8417862 97.6111517,70.1525615 L97.6111517,70.1525615 L97.1106718,70.9895001 C93.1228168,77.6836699 89.5186416,84.3457379 86.2981462,90.9757043 C82.49499,98.8051821 78.9260328,107.209292 75.5912744,116.188034 C75.0678608,117.59731 73.4916142,118.336546 72.045419,117.850988 L72.045419,117.850988 L71.2574475,117.584515 C45.4162372,108.782078 35.597644,99.1083195 41.8016679,88.5632391 C47.8700957,78.2486335 65.515653,70.7396611 94.7383398,66.0363218 Z M136.545792,34.4653746 C156.653461,16.8966864 169.722181,13.3874478 175.751952,23.9376587 C181.649935,34.2572826 179.515508,53.0171122 169.348673,80.2171474 C169.123182,80.8204179 168.695414,81.324709 168.139827,81.6422422 C166.828478,82.3917144 165.163975,81.9253986 164.422058,80.6006966 L164.422058,80.6006966 L163.947416,79.7564798 C160.143661,73.0170065 156.180988,66.6198239 152.059398,60.564932 C147.192119,53.4145727 141.71816,46.1758903 135.637521,38.8488847 C134.683122,37.6988602 134.815056,35.9888243 135.934314,35.0020629 L135.934314,35.0020629 Z M90.6842361,18 C102.390255,18 117.291276,29.0909926 135.387298,51.2729777 C135.788651,51.7649527 136.005428,52.3826288 136,53.0184911 C135.986842,54.5193144 134.764215,55.7253489 133.269082,55.7122445 L133.269082,55.7122445 L132.315346,55.7054689 C124.694302,55.6640063 117.285613,55.9284091 110.089277,56.4986773 C101.590961,57.17212 92.7164767,58.325393 83.4658235,59.9584962 C82.0138691,60.2148231 80.6245044,59.2578618 80.3403697,57.805758 L80.3403697,57.805758 L80.1871846,57.0136235 C75.2176347,31.0045412 78.7166519,18 90.6842361,18 Z" fill="#FF4154"></path></g></g></svg></button><div style="display: flex; flex-direction: column;"><span style="display: inline-block; font-size: 0.9em; margin-bottom: 0.5em;"><span style="display: inline-flex; align-items: center; padding: 0.2em 0.4em; font-weight: bold; text-shadow: black 0px 0px 10px; border-radius: 0.2em; background: rgb(0, 171, 82); opacity: 0.3;">fresh <code style="font-size: 0.9em; color: inherit; background: inherit;">(0)</code></span> <span style="display: inline-flex; align-items: center; padding: 0.2em 0.4em; font-weight: bold; text-shadow: black 0px 0px 10px; border-radius: 0.2em; background: rgb(0, 107, 255); opacity: 1;">fetching <code style="font-size: 0.9em; color: inherit; background: inherit;">(2)</code></span> <span style="display: inline-flex; align-items: center; padding: 0.2em 0.4em; font-weight: bold; border-radius: 0.2em; background: rgb(255, 178, 0); color: black; opacity: 1;">stale <code style="font-size: 0.9em; color: inherit; background: inherit;">(1)</code></span> <span style="display: inline-flex; align-items: center; padding: 0.2em 0.4em; font-weight: bold; text-shadow: black 0px 0px 10px; border-radius: 0.2em; background: rgb(63, 78, 96); opacity: 0.3;">inactive <code style="font-size: 0.9em; color: inherit; background: inherit;">(0)</code></span></span><div style="display: flex; align-items: center;"><input placeholder="Filter" aria-label="Filter by queryhash" value="" style="background-color: rgb(255, 255, 255); border: 0px; border-radius: 0.2em; color: rgb(0, 0, 0); font-size: 0.9em; line-height: 1.3; padding: 0.3em 0.4em; flex: 1 1 0%; margin-right: 0.5em; width: 100%;"><select aria-label="Sort queries" style="display: inline-block; font-size: 0.9em; font-family: sans-serif; font-weight: normal; line-height: 1.3; padding: 0.3em 1.5em 0.3em 0.5em; height: auto; border: 0px; border-radius: 0.2em; appearance: none; background-color: rgb(255, 255, 255); background-image: url(&quot;data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='%23444444'><polygon points='0,25 100,25 50,75'/></svg>&quot;); background-repeat: no-repeat; background-position: right 0.55em center; background-size: 0.65em, 100%; color: rgb(0, 0, 0); flex: 1 1 0%; min-width: 75px; margin-right: 0.5em;"><option value="Status > Last Updated">Sort by Status &gt; Last Updated</option><option value="Query Hash">Sort by Query Hash</option><option value="Last Updated">Sort by Last Updated</option></select><button type="button" style="appearance: none; font-size: 0.9em; font-weight: bold; background: rgb(63, 78, 96); border: 0px; border-radius: 0.3em; color: white; padding: 0.3em 0.4em; cursor: pointer;">⬆ Asc</button></div></div></div><div style="overflow-y: auto; flex: 1 1 0%;"><div role="button" aria-label="Open query details for [&quot;backup_restore_status&quot;]" style="display: flex; border-bottom: 1px solid rgb(34, 46, 62); cursor: pointer;"><div style="flex: 0 0 auto; width: 2em; height: 2em; background: rgb(0, 107, 255); display: flex; align-items: center; justify-content: center; font-weight: bold; text-shadow: black 0px 0px 10px; color: white;">1</div><code style="font-size: 0.9em; color: inherit; background: inherit; padding: 0.5em;">["backup_restore_status"]</code></div><div role="button" aria-label="Open query details for [&quot;AUTH_USER&quot;]" style="display: flex; border-bottom: 1px solid rgb(34, 46, 62); cursor: pointer;"><div style="flex: 0 0 auto; width: 2em; height: 2em; background: rgb(0, 107, 255); display: flex; align-items: center; justify-content: center; font-weight: bold; text-shadow: black 0px 0px 10px; color: white;">1</div><code style="font-size: 0.9em; color: inherit; background: inherit; padding: 0.5em;">["AUTH_USER"]</code></div><div role="button" aria-label="Open query details for [&quot;BETTERDOCS_INSTALL_STATUS&quot;]" style="display: flex; border-bottom: 1px solid rgb(34, 46, 62); cursor: pointer;"><div style="flex: 0 0 auto; width: 2em; height: 2em; background: rgb(255, 178, 0); display: flex; align-items: center; justify-content: center; font-weight: bold; color: black;">1</div><div style="flex: 0 0 auto; height: 2em; background: rgb(63, 78, 96); display: flex; align-items: center; font-weight: bold; padding: 0px 0.5em;">disabled</div><code style="font-size: 0.9em; color: inherit; background: inherit; padding: 0.5em;">["BETTERDOCS_INSTALL_STATUS"]</code></div></div></div></div><button type="button" aria-label="Open React Query Devtools" aria-controls="ReactQueryDevtoolsPanel" aria-haspopup="true" aria-expanded="false" style="background: none; border: 0px; padding: 0px; position: fixed; z-index: 99999; display: inline-flex; font-size: 1.5em; margin: 0.5em; cursor: pointer; width: fit-content; bottom: 0px; left: 0px;"><svg width="40px" height="40px" viewBox="0 0 190 190" version="1.1" aria-hidden="true"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g transform="translate(-33.000000, 0.000000)"><path d="M72.7239712,61.3436237 C69.631224,46.362877 68.9675112,34.8727722 70.9666331,26.5293551 C72.1555965,21.5671678 74.3293088,17.5190846 77.6346064,14.5984631 C81.1241394,11.5150478 85.5360327,10.0020122 90.493257,10.0020122 C98.6712013,10.0020122 107.26826,13.7273214 116.455725,20.8044264 C120.20312,23.6910458 124.092437,27.170411 128.131651,31.2444746 C128.45314,30.8310265 128.816542,30.4410453 129.22143,30.0806152 C140.64098,19.9149716 150.255245,13.5989272 158.478408,11.1636507 C163.367899,9.715636 167.958526,9.57768202 172.138936,10.983031 C176.551631,12.4664684 180.06766,15.5329489 182.548314,19.8281091 C186.642288,26.9166735 187.721918,36.2310983 186.195595,47.7320243 C185.573451,52.4199112 184.50985,57.5263831 183.007094,63.0593153 C183.574045,63.1277086 184.142416,63.2532808 184.705041,63.4395297 C199.193932,68.2358678 209.453582,73.3937462 215.665021,79.2882839 C219.360669,82.7953831 221.773972,86.6998434 222.646365,91.0218204 C223.567176,95.5836746 222.669313,100.159332 220.191548,104.451297 C216.105211,111.529614 208.591643,117.11221 197.887587,121.534031 C193.589552,123.309539 188.726579,124.917559 183.293259,126.363748 C183.541176,126.92292 183.733521,127.516759 183.862138,128.139758 C186.954886,143.120505 187.618598,154.61061 185.619477,162.954027 C184.430513,167.916214 182.256801,171.964297 178.951503,174.884919 C175.46197,177.968334 171.050077,179.48137 166.092853,179.48137 C157.914908,179.48137 149.31785,175.756061 140.130385,168.678956 C136.343104,165.761613 132.410866,162.238839 128.325434,158.108619 C127.905075,158.765474 127.388968,159.376011 126.77857,159.919385 C115.35902,170.085028 105.744755,176.401073 97.5215915,178.836349 C92.6321009,180.284364 88.0414736,180.422318 83.8610636,179.016969 C79.4483686,177.533532 75.9323404,174.467051 73.4516862,170.171891 C69.3577116,163.083327 68.2780823,153.768902 69.8044053,142.267976 C70.449038,137.410634 71.56762,132.103898 73.1575891,126.339009 C72.5361041,126.276104 71.9120754,126.144816 71.2949591,125.940529 C56.8060684,121.144191 46.5464184,115.986312 40.3349789,110.091775 C36.6393312,106.584675 34.2260275,102.680215 33.3536352,98.3582381 C32.4328237,93.7963839 33.3306866,89.2207269 35.8084524,84.9287618 C39.8947886,77.8504443 47.4083565,72.2678481 58.1124133,67.8460273 C62.5385143,66.0176154 67.5637208,64.366822 73.1939394,62.8874674 C72.9933393,62.3969171 72.8349374,61.8811235 72.7239712,61.3436237 Z" fill="#002C4B" fill-rule="nonzero" transform="translate(128.000000, 95.000000) scale(-1, 1) translate(-128.000000, -95.000000) "></path><path d="M113.396882,64 L142.608177,64 C144.399254,64 146.053521,64.958025 146.944933,66.5115174 L161.577138,92.0115174 C162.461464,93.5526583 162.461464,95.4473417 161.577138,96.9884826 L146.944933,122.488483 C146.053521,124.041975 144.399254,125 142.608177,125 L113.396882,125 C111.605806,125 109.951539,124.041975 109.060126,122.488483 L94.4279211,96.9884826 C93.543596,95.4473417 93.543596,93.5526583 94.4279211,92.0115174 L109.060126,66.5115174 C109.951539,64.958025 111.605806,64 113.396882,64 Z M138.987827,70.2765273 C140.779849,70.2765273 142.434839,71.2355558 143.325899,72.7903404 L154.343038,92.0138131 C155.225607,93.5537825 155.225607,95.4462175 154.343038,96.9861869 L143.325899,116.20966 C142.434839,117.764444 140.779849,118.723473 138.987827,118.723473 L117.017233,118.723473 C115.225211,118.723473 113.570221,117.764444 112.67916,116.20966 L101.662022,96.9861869 C100.779452,95.4462175 100.779452,93.5537825 101.662022,92.0138131 L112.67916,72.7903404 C113.570221,71.2355558 115.225211,70.2765273 117.017233,70.2765273 L138.987827,70.2765273 Z M135.080648,77.1414791 L120.924411,77.1414791 C119.134228,77.1414791 117.480644,78.0985567 116.5889,79.6508285 L116.5889,79.6508285 L109.489217,92.0093494 C108.603232,93.5515958 108.603232,95.4484042 109.489217,96.9906506 L109.489217,96.9906506 L116.5889,109.349172 C117.480644,110.901443 119.134228,111.858521 120.924411,111.858521 L120.924411,111.858521 L135.080648,111.858521 C136.870831,111.858521 138.524416,110.901443 139.41616,109.349172 L139.41616,109.349172 L146.515843,96.9906506 C147.401828,95.4484042 147.401828,93.5515958 146.515843,92.0093494 L146.515843,92.0093494 L139.41616,79.6508285 C138.524416,78.0985567 136.870831,77.1414791 135.080648,77.1414791 L135.080648,77.1414791 Z M131.319186,83.7122186 C133.108028,83.7122186 134.760587,84.6678753 135.652827,86.2183156 L138.983552,92.0060969 C139.87203,93.5500005 139.87203,95.4499995 138.983552,96.9939031 L135.652827,102.781684 C134.760587,104.332125 133.108028,105.287781 131.319186,105.287781 L124.685874,105.287781 C122.897032,105.287781 121.244473,104.332125 120.352233,102.781684 L117.021508,96.9939031 C116.13303,95.4499995 116.13303,93.5500005 117.021508,92.0060969 L120.352233,86.2183156 C121.244473,84.6678753 122.897032,83.7122186 124.685874,83.7122186 L131.319186,83.7122186 Z M128.003794,90.1848875 C126.459294,90.1848875 125.034382,91.0072828 124.263005,92.3424437 C123.491732,93.6774232 123.491732,95.3225768 124.263005,96.6575563 C125.034382,97.9927172 126.459294,98.8151125 128.001266,98.8151125 L128.001266,98.8151125 C129.545766,98.8151125 130.970678,97.9927172 131.742055,96.6575563 C132.513327,95.3225768 132.513327,93.6774232 131.742055,92.3424437 C130.970678,91.0072828 129.545766,90.1848875 128.003794,90.1848875 L128.003794,90.1848875 Z M93,94.5009646 L100.767764,94.5009646" fill="#FFD94C"></path><path d="M87.8601729,108.357758 C89.1715224,107.608286 90.8360246,108.074601 91.5779424,109.399303 L91.5779424,109.399303 L92.0525843,110.24352 C95.8563392,116.982993 99.8190116,123.380176 103.940602,129.435068 C108.807881,136.585427 114.28184,143.82411 120.362479,151.151115 C121.316878,152.30114 121.184944,154.011176 120.065686,154.997937 L120.065686,154.997937 L119.454208,155.534625 C99.3465389,173.103314 86.2778188,176.612552 80.2480482,166.062341 C74.3500652,155.742717 76.4844915,136.982888 86.6513274,109.782853 C86.876818,109.179582 87.3045861,108.675291 87.8601729,108.357758 Z M173.534177,129.041504 C174.986131,128.785177 176.375496,129.742138 176.65963,131.194242 L176.65963,131.194242 L176.812815,131.986376 C181.782365,157.995459 178.283348,171 166.315764,171 C154.609745,171 139.708724,159.909007 121.612702,137.727022 C121.211349,137.235047 120.994572,136.617371 121,135.981509 C121.013158,134.480686 122.235785,133.274651 123.730918,133.287756 L123.730918,133.287756 L124.684654,133.294531 C132.305698,133.335994 139.714387,133.071591 146.910723,132.501323 C155.409039,131.82788 164.283523,130.674607 173.534177,129.041504 Z M180.408726,73.8119663 C180.932139,72.4026903 182.508386,71.6634537 183.954581,72.149012 L183.954581,72.149012 L184.742552,72.4154854 C210.583763,81.217922 220.402356,90.8916805 214.198332,101.436761 C208.129904,111.751366 190.484347,119.260339 161.26166,123.963678 C160.613529,124.067994 159.948643,123.945969 159.382735,123.618843 C158.047025,122.846729 157.602046,121.158214 158.388848,119.847438 L158.388848,119.847438 L158.889328,119.0105 C162.877183,112.31633 166.481358,105.654262 169.701854,99.0242957 C173.50501,91.1948179 177.073967,82.7907081 180.408726,73.8119663 Z M94.7383398,66.0363218 C95.3864708,65.9320063 96.0513565,66.0540315 96.6172646,66.3811573 C97.9529754,67.153271 98.3979538,68.8417862 97.6111517,70.1525615 L97.6111517,70.1525615 L97.1106718,70.9895001 C93.1228168,77.6836699 89.5186416,84.3457379 86.2981462,90.9757043 C82.49499,98.8051821 78.9260328,107.209292 75.5912744,116.188034 C75.0678608,117.59731 73.4916142,118.336546 72.045419,117.850988 L72.045419,117.850988 L71.2574475,117.584515 C45.4162372,108.782078 35.597644,99.1083195 41.8016679,88.5632391 C47.8700957,78.2486335 65.515653,70.7396611 94.7383398,66.0363218 Z M136.545792,34.4653746 C156.653461,16.8966864 169.722181,13.3874478 175.751952,23.9376587 C181.649935,34.2572826 179.515508,53.0171122 169.348673,80.2171474 C169.123182,80.8204179 168.695414,81.324709 168.139827,81.6422422 C166.828478,82.3917144 165.163975,81.9253986 164.422058,80.6006966 L164.422058,80.6006966 L163.947416,79.7564798 C160.143661,73.0170065 156.180988,66.6198239 152.059398,60.564932 C147.192119,53.4145727 141.71816,46.1758903 135.637521,38.8488847 C134.683122,37.6988602 134.815056,35.9888243 135.934314,35.0020629 L135.934314,35.0020629 Z M90.6842361,18 C102.390255,18 117.291276,29.0909926 135.387298,51.2729777 C135.788651,51.7649527 136.005428,52.3826288 136,53.0184911 C135.986842,54.5193144 134.764215,55.7253489 133.269082,55.7122445 L133.269082,55.7122445 L132.315346,55.7054689 C124.694302,55.6640063 117.285613,55.9284091 110.089277,56.4986773 C101.590961,57.17212 92.7164767,58.325393 83.4658235,59.9584962 C82.0138691,60.2148231 80.6245044,59.2578618 80.3403697,57.805758 L80.3403697,57.805758 L80.1871846,57.0136235 C75.2176347,31.0045412 78.7166519,18 90.6842361,18 Z" fill="#FF4154"></path></g></g></svg></button></aside></div></main></div>`;

const SUBSCRIPTION_SKELETON = readFileSync(path.resolve(process.cwd(), `html_skeletons/subscription.html`), {
  encoding: "utf-8",
});

/**
 *
 * @param {import("express").Request} req
 */
const generateHtml = async (req) => {
  let skeleton = readFileSync(path.resolve(process.cwd(), `html_skeletons/dashboard.html`), {
    encoding: "utf-8",
  });

  if (req.originalUrl.startsWith("/settings")) skeleton = SETTINGS_SKELETON;
  else if (req.originalUrl.startsWith("/subscription")) skeleton = SUBSCRIPTION_SKELETON;
  // else if (req.originalUrl.startsWith("/onboard")) skeleton = ONBOARD_SKELETON;
  else if (req.originalUrl.startsWith("/onboarding")) skeleton = ONBOARDING_SKELETON;
  else if (req.originalUrl.match(/(?:(?:products)|(?:pages)|(?:articles))\/[0-9].*/gim)) {
    skeleton = ITEM_FIX_SKELETON;
  } else if (req.originalUrl.match(/\/[a-z]+/gim)) {
    skeleton = PAGE_SKELETON;
  }

  return `${INDEX_HTML_FIRST_PART}${skeleton}${INDEX_HTML_SECOND_PART}`;
};

module.exports = {
  generateHtml,
};
