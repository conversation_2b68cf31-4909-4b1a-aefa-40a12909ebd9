//@ts-check
const yup = require("yup");
const { PRODUCT, PAGE, ARTICLE } = require("storeseo-enums/analysisEntityTypes");

const getGeneralSEOUpdateValidationSchema = () =>
  yup.object().shape({
    metaTitle: yup.string().trim().required("Please input valid meta title").min(1, "Please input valid meta title"),
    metaDescription: yup.string().trim().required("Please input valid meta description"),
    focusKeyword: yup.string().trim().required("Please input a focus keyword"),
    handle: yup.string().trim().required("Please input a URL handle"),
    tags: yup.array(),
    images: yup.array(),
    image: yup.mixed().nullable(),
    createRedirectUrl: yup.boolean(),
  });

/**
 * @param {yup.ObjectSchema} schema
 * @param {Object} body
 * @returns {Promise<{error: {details: {path: string, message: string}[]} | undefined}>}
 */
const validate = async (schema, body) => {
  let error;
  try {
    await schema.validate(body, { abortEarly: false });
  } catch (error) {
    error = { details: error.inner.map((item) => ({ path: item.path, message: item.message })) };
    return { error };
  }
  return { error };
};

module.exports = {
  validateGeneralSEOUpdateData: async (reqBody) => {
    const schema = getGeneralSEOUpdateValidationSchema();
    const { error } = await validate(schema, reqBody);
    return { error };
  },

  validateSubscriptionCoupon: async (reqBody) => {
    const schema = yup.object().shape({
      slug: yup.string().trim().required("Invalid plan parameter"),
      coupon: yup.string().trim().required("Please input coupon code"),
    });
    const { error } = await validate(schema, reqBody);
    return { error };
  },

  validateAiOptimizerInput: async (reqBody, resourceType = PRODUCT) => {
    const contentOrDescription = resourceType !== PAGE && resourceType !== ARTICLE ? "description" : "content";

    const titleErrorMessage = `${resourceType.toLowerCase()} title is required to generate ai content`;
    const descriptionErrorMessage = `${resourceType.toLowerCase()} ${contentOrDescription} is required to generate ai content`;
    const focusKeywordErrorMessage = "focus keyword is required to generate ai content";

    const schema = yup.object().shape({
      title: yup.string().trim().required(titleErrorMessage),
      description: yup.string().trim().required(descriptionErrorMessage),
      focusKeyword: yup.string().trim().required(focusKeywordErrorMessage),
    });

    const { error } = await validate(schema, reqBody);
    return { error };
  },

  validateImageAltTextGenerationInput: async (reqBody) => {
    const schema = yup.object().shape({
      image: yup.object().shape({
        src: yup.string().url("Please input a valid image URL").required("Please input a valid image URL"),
        id: yup.number().required("Please input a valid image id"),
        product: yup.object().required("Please input a valid product"),
      }),
    });

    const { error } = await validate(schema, reqBody);
    return { error };
  },
  validate,
};
