const { default: axios } = require("axios");

/**
 *
 * @param {string} percentageOptimized example: `20.5%`, `0%`, `-12.33%`
 */
const imageSizeHasDecreased = (percentageOptimized) => !new RegExp(/^(0|-)/gim).test(percentageOptimized);

async function isValidImageUrl(imageUrl) {
  try {
    await axios.get(imageUrl);
    return true;
  } catch (err) {
    return false;
  }
}

module.exports = {
  imageSizeHasDecreased,

  isValidImageUrl,
};
