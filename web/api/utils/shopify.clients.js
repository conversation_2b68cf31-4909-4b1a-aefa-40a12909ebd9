require("graphql-import-node/register");
// const logger = require("storeseo-logger");
const shopify = require("../../shopify");

/**
 * @typedef {Object} GraphQLClientParams
 * @property {string|DocumentNode} query - The GraphQL query string
 * @property {Object} [variables] - Optional variables for the query
 * @property {Array<string|metaFragment>} [fragments] - Optional GraphQL fragments
 */

/**
 *
 * @param {string} shop  - The shop myshopify domain
 * @param {GraphQLClientParams} param1
 * @returns {Promise<any>}
 */
async function graphQLClient(shop, { query, variables = undefined, fragments = [] }) {
  try {
    const sessionId = shopify.api.session.getOfflineId(shop);
    let session = await shopify.config.sessionStorage.loadSession(sessionId);

    if (!session) {
      console.warn(
        `Session not found for Shop: ${shop}, Session id: ${sessionId} from session storage. looking into DB...`
      );
      const ShopService = require("../services/ShopService");

      const shopData = await ShopService.getShop(shop, ["id", "domain", "access_token"]);
      session = {
        id: sessionId,
        shop,
        state: "",
        isOnline: false,
        accessToken: shopData.access_token,
      };
    }

    const client = new shopify.api.clients.Graphql({ session });
    const queryString = prepareQueryString(query, fragments);
    const res = await client.request(queryString, { variables });

    return res;
  } catch (error) {
    // logger.error(error, { domain: shop, transaction: "graphQLClient" });
    throw error;
  }
}


/**
 *
 * @param {object} session  - The shop session in database
 * @param {string} session.shop  - The shop myshopify domain
 * @param {string} session.accessToken  - The shop myshopify access token
 * @param {GraphQLClientParams} param1
 * @returns {Promise<any>}
 */
async function graphQLClientWithDBSession(session, { query, variables = undefined, fragments = [] }) {
  try {

    if (!session) throw new Error("Session not found");

    const client = new shopify.api.clients.Graphql({ session });
    const queryString = prepareQueryString(query, fragments);
    const res = await client.request(queryString, { variables });

    return res;
  } catch (error) {
    // logger.error(error, { domain: shop, transaction: "graphQLClient" });
    throw error;
  }
}

function prepareQueryString(query, fragments = []) {
  let string = typeof query === "string" ? query : query.loc.source.body;

  if (fragments.length > 0) {
    string += "\n" + fragments.map((f) => `${f.loc.source.body}`).join("\n");
  }

  return string;
}

module.exports = {
  graphQLClient,
  graphQLClientWithDBSession,
  prepareQueryString,
};
