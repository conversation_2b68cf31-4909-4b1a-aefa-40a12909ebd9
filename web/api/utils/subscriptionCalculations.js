const discountType = require("storeseo-enums/discountType");
const { pick, isEmpty } = require("lodash");
const planInterval = require("storeseo-enums/planInterval");
const { PERCENT, AMOUNT } = require("storeseo-enums/discountType");
const appSubscriptionInterval = require("storeseo-enums/appSubscriptionInterval");
const subscriptionAddonInterval = require("storeseo-enums/subscriptionAddonInterval");

/**
 *
 * @param {object} plan
 * @param {object} activePlan
 * @param beforeSubscription
 * @returns {{[p: string]: *, subtotal: (number|number), isDiscountApplicable: boolean, discount: (*|number)}|{[p: string]: *, subtotal, discount: number}|{}|{[p: string]: *, subtotal: (number|number), isDiscountApplicable: boolean, discount}}
 */
const calculateCouponDiscount = (plan, activePlan = null, beforeSubscription = true) => {
  const { serializeCouponData } = require("../serializers/SubscriptionSerializer");
  const price = parseFloat(plan.price.toFixed(2));
  const duration = plan.meta?.duration || 1;
  const totalPrice = parseFloat((price * duration).toFixed(2));
  const discount = parseFloat(plan.discount.toFixed(2));
  const subtotal = totalPrice - discount;

  const data = {
    price,
    duration,
    totalPrice,
    discount,
    subtotal,
    isHundredPercentCoupon: plan?.coupon?.discount_type === discountType.PERCENT && plan?.coupon?.amount === 100,
    isDiscountApplicable: false,
    coupon: serializeCouponData(plan?.coupon),
    interval: plan.interval,
  };

  if (!plan.coupon) {
    return data;
  }

  if (!calculatePlanIsUpgradable(plan, activePlan) && (plan.id !== activePlan?.id || price === 0)) {
    return {
      ...data,
      discount: 0.0,
      subtotal,
    };
  }

  const couponAmount = parseFloat(plan?.coupon?.amount?.toFixed(2) || 0);
  if (plan?.coupon?.discount_type === discountType.AMOUNT) {
    return {
      ...data,
      discount: couponAmount,
      subtotal: totalPrice >= couponAmount ? totalPrice - couponAmount : 0,
      isDiscountApplicable: true,
    };
  }

  const discountAmount = parseFloat((couponAmount >= 100 ? totalPrice : (totalPrice * couponAmount) / 100).toFixed(2));

  return {
    ...data,
    discount: discountAmount,
    subtotal: couponAmount <= 100 ? totalPrice - discountAmount : 0,
    isDiscountApplicable: true,
  };
};

const checkIsMonthlySubscription = (name) => {
  let isMonthly = true;
  const yearlySeparator = "-yearly";
  if (name && name.includes(yearlySeparator)) {
    isMonthly = false;
    name = name.replace("-yearly", "");
  }
  return { name, isMonthly };
};

const calculateDiscountDataForShopify = (plan, coupon) => {
  if (coupon) {
    return {
      value: {
        percentage: coupon.discount_type === PERCENT ? coupon.amount / 100 : undefined,
        amount: coupon.discount_type === AMOUNT ? coupon.amount : undefined,
      },
      durationLimitInIntervals: coupon.discount_duration || undefined,
    };
  }

  if (plan.discount > 0) {
    return {
      value: {
        amount: plan.discount,
      },
      durationLimitInIntervals: undefined,
    };
  }

  return undefined;
};

const calculateShopifyPlanInterval = (interval) => {
  switch (interval) {
    case planInterval.MONTHLY:
      return appSubscriptionInterval.EVERY_30_DAYS;
    case planInterval.ANNUALLY:
      return appSubscriptionInterval.ANNUAL;
    default:
      return appSubscriptionInterval.EVERY_30_DAYS;
  }
};
//
// const calculatePlanIsUpgradable = (plan, activePlan) => {
//   if (plan.id === activePlan.id || plan.price === 0) {
//     return false;
//   }
//
//   if (plan.interval === activePlan.interval && plan.price <= activePlan.price) {
//     return false;
//   }
//
//   if (plan.name === activePlan.name && plan.price <= activePlan.price) {
//     return false;
//   }
//
//   return true;
// };

const calculatePlanIsUpgradable = (plan, activePlan) => {
  if (isEmpty(activePlan)) {
    return true;
  }
  if (plan.id === activePlan.id || plan.price === 0) {
    return false;
  }

  return plan.order > activePlan.order;
};

const calculateRecurringPrice = (plan, addons = []) => {
  if (addons.length === 0) {
    return plan.meta?.isSpecial ? 0 : plan.price;
  }
  const planPrice = plan.interval === planInterval.USAGE || plan.meta?.isSpecial ? 0 : plan.price;
  return addons.reduce((price, addon) => {
    let interval = plan.interval === planInterval.ANNUALLY ? 12 : 1;
    return price + addon.price * interval;
  }, planPrice);
};

const calculateRecurringPriceDiscount = (plan, addons = []) => {
  let discount = addons.reduce((discount, addon) => {
    let interval = plan.interval === planInterval.ANNUALLY ? 12 : 1;
    return discount + addon.discount * interval;
  }, plan.discount);

  if (discount === 0) {
    return undefined;
  }

  return {
    value: {
      amount: discount.toFixed(2),
    },
    durationLimitInIntervals: plan?.coupon?.discount_duration || undefined,
  };
};

const calculateCappedAmount = (plan, addons = []) => {
  if (addons.length === 0 && plan.interval === planInterval.USAGE) {
    return parseFloat(plan.meta?.cappedAmount || 0).toFixed(2);
  }

  const planPrice = plan.interval === planInterval.USAGE ? plan.meta?.cappedAmount : 0;
  return addons.reduce((price, addon) => {
    return price + (addon.meta?.cappedAmount || 0);
  }, planPrice);
};

const calculateUsagePrice = (plan, addons = []) => {
  const planPrice = plan.interval === planInterval.USAGE ? plan.subtotal : 0;
  return addons.reduce((price, addon) => {
    return price + addon?.subtotal;
  }, planPrice);
};

const calculateAddonPrice = (plan, addons = []) => {
  const { planPrice, addonPrice } = addons
    .filter((addon) => addon.interval === subscriptionAddonInterval.MONTHLY)
    .reduce(
      ({ planPrice, addonPrice }, addon) => {
        let interval = plan.interval === planInterval.ANNUALLY ? 12 : 1;

        addonPrice = addon.price * interval;
        return {
          planPrice: planPrice + addonPrice,
          addonPrice,
        };
      },
      { planPrice: plan.subtotal, addonPrice: 0 }
    );

  return {
    ...plan,
    subtotal: plan.interval === planInterval.USAGE ? plan.subtotal : planPrice,
    addonPrice,
  };
};

const calculateIsFreeSubcription = (plan, addons) => {
  const price = addons.reduce((price, addon) => {
    let interval = plan.interval === planInterval.ANNUALLY ? 12 : 1;
    return price + addon.subtotal * interval;
  }, plan.subtotal);

  return price === 0;
};

const calculateCreditAddonPrice = (addons = []) => {
  const price = addons
    .filter((addon) => addon.interval === subscriptionAddonInterval.CREDIT)
    .reduce((price, addon) => price + addon.price, 0);

  return price;
};

const isOnlyFreeAddonSubscriptionWithVisionary = (shop, plan, addons) => {
  const addonPrice = addons.reduce((price, addon) => price + addon.subtotal, 0);
  if (shop.plan_info?.slug === "visionary" && plan.slug === "visionary" && addonPrice === 0) {
    return true;
  }

  return false;
};

module.exports = {
  calculateCouponDiscount,
  calculatePlanIsUpgradable,
  checkIsMonthlySubscription,
  calculateShopifyPlanInterval,
  calculateDiscountDataForShopify,
  calculateRecurringPrice,
  calculateRecurringPriceDiscount,
  calculateUsagePrice,
  calculateCappedAmount,
  calculateAddonPrice,
  calculateIsFreeSubcription,
  calculateCreditAddonPrice,
  isOnlyFreeAddonSubscriptionWithVisionary,
};
