const { runQueues } = require("./index");

const {
  seperate: {
    CollectionSyncQueue,
    WebhookCollectionCreateQueue,
    WebhookCollectionUpdateQueue,
    WebhookCollectionDeleteQueue,
    AutoCollectionAiOptimizationQueue,
  },
} = require("./kernel");

(async () => {
  const queues = [
    CollectionSyncQueue,
    WebhookCollectionDeleteQueue,
    WebhookCollectionUpdateQueue,
    WebhookCollectionCreateQueue,
    AutoCollectionAiOptimizationQueue,
  ];

  await runQueues(queues);
})();
