require("../config");
const { createClient } = require("redis");
const { host, port, tls, username, password } = require("../config/redis");

const { USAGE_COUNT, IMAGE_OPTIMIZER, AI_OPTIMIZER } = require("storeseo-enums/cacheKeys");
const { dispatchQueue } = require("./queueDispatcher");
const { QUEUE_NAMES } = require("./config");

const redisClient = createClient({
  socket: { host, port, tls, connectTimeout: 10000 },
  username,
  password,
});

const addons = [IMAGE_OPTIMIZER, AI_OPTIMIZER];

(async () => {
  redisClient.on("ready", async () => {
    console.log(`> Redis client listening => ${host}:${port}`);
  });

  await redisClient.connect();

  for (let addon of addons) {
    const key = `*:${addon}:${USAGE_COUNT}`;
    const keySpaceChannel = `__key*__:${key}`;
    await redisClient.pSubscribe(keySpaceChannel, async (event, channel) => {
      const shopDomain = channel.split(":")[1];
      console.log(`dispatching usage notification queue for addon: ${addon} for shop: ${shopDomain}`);
      dispatchQueue({
        queueName: QUEUE_NAMES.ADDON_USAGE_NOTIFICATION_QUEUE,
        message: {
          shopDomain,
          addonGroup: addon,
        },
      });
    });

    console.log("Listening for keyspace notifications on key:", key);
  }
})();
