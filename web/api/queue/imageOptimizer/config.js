const { <PERSON>XCH<PERSON><PERSON>_NAMES, EXCHAN<PERSON>_TYPES, MAX_RETRY_FOR_FAILED_MESSAGES, KEYS } = require("../config");

const NUMBER_OF_QUEUES = Number(process.env.NUMBER_OF_IMAGE_OPTIMIZER_QUEUES) || 3;
const QUEUE_BASE_NAME = "image.optimizer.queue";
const MAX_MESSAGE_PER_QUEUE = 3;

const PREFETCH_LIMIT = 2;

module.exports = {
  NUMBER_OF_QUEUES,
  QUEUE_BASE_NAME,
  MAX_MESSAGE_PER_QUEUE,

  PREFETCH_LIMIT,

  EXCHANGE_NAMES,
  <PERSON>X<PERSON>AN<PERSON>_TYPES,

  MAX_RETRY_FOR_FAILED_MESSAGES,

  KEYS,
};
