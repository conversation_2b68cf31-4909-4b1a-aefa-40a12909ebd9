const { isAxiosError } = require("axios");
const { RABBIT_MQ_CONNECTION } = require("..");
const logger = require("storeseo-logger");
const ImageService = require("../../services/ImageService");
const ProductImageService = require("../../services/ProductImageService");
const { EXCHANGE_NAMES, EXCHANGE_TYPES, NUMBER_OF_QUEUES } = require("./config");
const { getQueueName } = require("./helper");
const imageOptimization = require("storeseo-enums/imageOptimization");
const cache = require("../../cache");
const { isEmpty } = require("lodash");
const { HOST } = require("storeseo-enums/cacheKeys");
const redisClient = require("../../cache/client");
const { isValidImageUrl } = require("../../utils/imageOptimizer");
const { url } = require("../../config/app");
const ShopService = require("../../services/ShopService");
const { InactiveShopError } = require("../../../errors");
const shopStatus = require("storeseo-enums/shopStatus");
const ShopifyService = require("../../services/ShopifyService");

/**
 * @type {import("amqplib").ConfirmChannel | null}
 */
let consumerChannel = null;

/**
 * Assert exchanges into existance for use in queue consume & dispatch
 * @param {import("amqplib").Channel} channel
 * @returns {Promise<import("amqplib").Channel} same channel provided in the input
 */
const assertExchanges = async (channel) => {
  await channel.assertExchange(EXCHANGE_NAMES.DEFAULT, EXCHANGE_TYPES.DIRECT);
  await channel.assertExchange(EXCHANGE_NAMES.EX_DELAYED, EXCHANGE_TYPES.DELAYED, {
    arguments: { "x-delayed-type": "direct" },
  });
  await channel.assertExchange(EXCHANGE_NAMES.MESSAGE_DUMP, EXCHANGE_TYPES.DIRECT);

  return channel;
};

/**
 * Create a new channel & perapre it for message dispatch
 */
const prepareConsumerChannel = async () => {
  console.log("creating consumer channel...");
  const connection = await RABBIT_MQ_CONNECTION;
  const channel = await connection.createChannel();
  await assertExchanges(channel);

  consumerChannel = channel;
  consumerChannel.prefetch(1);
  console.log("Consumer channel created!");
};

async function bindQueues() {
  console.log(`Queues to bind: ${NUMBER_OF_QUEUES}`);

  for (let i = 1; i <= NUMBER_OF_QUEUES; i++) {
    const queueName = getQueueName(i);
    await consumerChannel.bindQueue(queueName, EXCHANGE_NAMES.DEFAULT, queueName);
    await consumerChannel.bindQueue(queueName, EXCHANGE_NAMES.EX_DELAYED, queueName);
  }

  console.log("Queues binded!");
}

/**
 *
 * @param {string} queue
 * @param {import("amqplib").ConsumeMessage} message
 */
async function handleMessage(queue, message) {
  const { image, shop } = JSON.parse(message.content.toString());

  try {
    await ShopifyService.hasValidSession(shop);

    const { id, optimization_setting, src } = image;

    if (!(await isValidImageUrl(src))) {
      await handleInvalidImage(image, shop);
      console.log(`Image id: ${id}, shop: ${shop} - Image url is invalid`);
      consumerChannel.ack(message);
      return;
    }

    if (isEmpty(optimization_setting)) {
      await ProductImageService.updateImage(id, { optimization_status: imageOptimization.NOT_OPTIMIZED });
      await cache.updateImageOptimizerUsageCount(shop, false);
      console.log(`Image reset: ${id}, shop: ${shop}, due to no optimization settings`);
      consumerChannel.ack(message);
      return;
    }

    const host = (await redisClient.get(HOST)) || url;

    const payload = {
      ...optimization_setting,
      shop_domain: shop,
      application_hook: `${host}/image-service/queue-optimize-webhook`,

      image_details: [
        {
          image_id: String(id),
          image_url: src,
        },
      ],
    };

    // console.log("payload =>", payload);
    await ImageService.optimizeImagesViaQueue(payload);
    await cache.imageOptimizer.incrementImagesQueuedByDate(1);

    await ProductImageService.updateImage(id, { optimization_status: imageOptimization.PROCESSING });

    consumerChannel.ack(message);
    console.log(`Optimization started for image: ${id}, shop: ${shop}`);
  } catch (err) {
    console.log(`Error for image:`, image, `error:`, err);
    if (err instanceof InactiveShopError) {
      logger.info(err, {
        queue,
        dispatchedMessage: JSON.parse(message.content.toString()),
        transaction: "ImageOptimizer",
        domain: shop,
      });
      await cache.updateImageOptimizerUsageCount(shop, false);
      consumerChannel.ack(message);
      return;
    } else if (isAxiosError(err)) {
      logger.info(err, {
        description: `Failed to process image: ${image.id} for shop: ${shop} in queue: ${queue}`,
        queue,
        dispatchedMessage: message.content.toString(),
        details: err.response.data,
        transaction: "ImageOptimizer",
      });
    } else
      logger.error(err, {
        description: `Failed to process image: ${image.id} for shop: ${shop} in queue: ${queue}`,
        queue,
        dispatchedMessage: message.content.toString(),
        transaction: "ImageOptimizer",
      });

    consumerChannel.nack(message);
  }
}

/**
 *
 * @param {*} image
 * @param {string} shop
 */
const handleInvalidImage = async (image, shop) => {
  try {
    await cache.updateImageOptimizerUsageCount(shop, false);
    await cache.incrementImageOptimizerInvalidImageCount(shop);

    await ProductImageService.deleteImage(image.id);
  } catch (err) {
    console.log(`Error handling invalid image - image id: ${image.id}, shop: ${shop}`, err);
  }
};

/**
 * @callback messageHandler
 * @param {import("amqplib").ConsumeMessage} message
 */

/**
 * @param {string} queue name of the queue
 * @return {messageHandler}
 */
function createHandler(queue) {
  return (message) => handleMessage(queue, message);
}

async function startConsumingMessages() {
  for (let i = 1; i <= NUMBER_OF_QUEUES; i++) {
    const queueName = getQueueName(i);
    const handler = createHandler(queueName);
    consumerChannel.consume(queueName, handler, {
      noAck: false,
    });
  }
}

async function bootConsumer() {
  try {
    await prepareConsumerChannel();
    await bindQueues();

    await startConsumingMessages();
  } catch (err) {
    console.log(err);
  }
}

module.exports = {
  bootConsumer,
};
