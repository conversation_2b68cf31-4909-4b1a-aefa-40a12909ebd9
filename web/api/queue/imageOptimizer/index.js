require("../../config");

const logger = require("storeseo-logger");
const { RABBIT_MQ_CONNECTION } = require("../index");
const { bootConsumer } = require("./consumer");
const { bootDispatcher } = require("./dispatcher");
const { manageQueuesAndPendingImages } = require("./manager");
const { resetState } = require("./state");
const { QueueStats } = require("../../../sequelize");
const cron = require("node-cron");
const { QUEUE_BASE_NAME } = require("./config");
const cache = require("../../cache");
const moment = require("moment");
const SlackService = require("../../services/SlackService");

cron.schedule("0 0 */6 * * *", async function checkWebhookReturnsAndTriggerAlert() {
  console.log("Checking webhooks returned vs images queued...");

  const date = moment().format("YYYY-MM-DD");

  const imagesQueued = await cache.imageOptimizer.imagesQueuedByDate(date);
  const webhooksReceived = await cache.imageOptimizer.imageOptimizeWebhooksCountByDate(date);

  const imagesMissing = imagesQueued - webhooksReceived;
  const missingPercentage = (imagesMissing * 100) / imagesQueued;

  if (missingPercentage < 10 || imagesQueued === 0) {
    return console.log("service is almost stable!");
  }

  await SlackService.sendImageOptimizerMissingWebhooksAlert({
    date,
    imagesQueued,
    webhooksReceived,
    imagesMissing,
  });
});

cron.schedule("0 0 * * * *", async function syncStatsIntoDb() {
  try {
    logger.info("syncing stats data from redis to database...");

    const date = await cache.imageOptimizer.popOneDateFromPendingSyncStatisticsDates();
    if (!date) return console.log("No stats to sync!");

    const { max: maxQueueUsage, min: minQueueUsage } = await cache.imageOptimizer.getMaxMinQueueUsageCount(date);
    const { max: maxStoreCount, min: minStoreCount } = await cache.imageOptimizer.getMaxMinStoreCount(date);

    const imagesQueued = await cache.imageOptimizer.imagesQueuedByDate(date);
    const imagesProcessed = await cache.imageOptimizer.imagesProcessedByDate(date);
    const imagesFailedToProcess = await cache.imageOptimizer.processingFailedByDate(date);
    const imagesFailed = await cache.imageOptimizer.imagesFailedByDate(date);

    const webhooksCount = await cache.imageOptimizer.imageOptimizeWebhooksCountByDate(date);

    const invalidImages = await cache.invalidImagesCountByDate(date);

    await QueueStats.upsert({
      queue_name: QUEUE_BASE_NAME,
      date,

      statistics: {
        maxQueueUsage,
        minQueueUsage,

        maxStoreCount,
        minStoreCount,

        imagesQueued,
        imagesProcessed,
        imagesFailedToProcess,
        imagesFailed,

        webhooksCount,

        invalidImages,
      },
    });

    console.log("stats data synced in database");
  } catch (err) {
    logger.error(err, {
      description: "Failed to sync stats data from redis to database",
      transaction: "ImageOptimizer",
    });
  }
});

async function startTheProcess() {
  try {
    await RABBIT_MQ_CONNECTION;
    console.log("RabbitMQ connection is ready!");

    resetState();
    await bootDispatcher();
    await bootConsumer();

    manageQueuesAndPendingImages();
  } catch (err) {
    logger.error(err);
  }
}

startTheProcess();

module.exports = {};
