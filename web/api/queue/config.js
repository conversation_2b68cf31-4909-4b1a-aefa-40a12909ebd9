// // @ts-check
require("dotenv").config();

const QUEUE_NAMES = {
  DEFAULT: "default",
  PRODUCT_SYNC: "product.sync",
  PRODUCT_SYNC_SINGLE: "product.sync.single",
  PRODUCT_SYNC_WITH_DATA_MIGRATION: "product.sync.data.migration",
  PRODUCT_OPTIMIZE: "product.optimize",
  PRODUCT_OPTIMIZE_BULK_OPERATION: "product.optimize.bulk.operation",
  WEBHOOK_PRODUCT_CREATE: "webhook.product.create",
  WEBHOOK_PRODUCT_UPDATE: "webhook.product.update",
  WEBHOOK_PRODUCT_DELETE: "webhook.product.delete",
  SUBSCRIPTION_DOWNGRADE: "subscription.downgrade",
  MAILCHIMP_ADD_MEMBER: "mailchimp.add.member",
  FLUENT_CRM_ADD_CONTACT: "fluentcrm.add.contact",
  PRODUCT_INDEXING_GOOGLE: "product.indexing.google",
  DELETE_SHOP_DATA_QUEUE: "delete.shop.data",
  PAGE_SYNC: "page.sync",
  ARTICLE_SYNC: "article.sync",
  PAGE_METAFIELD_SYNC: "page.metafield.sync",
  BLOG_ARTICLES_SYNC: "blog.articles.sync",
  ARTICLE_METAFIELD_SYNC: "blog.articles.metafield.sync",
  // TEST: "test",
  // FOO: "foo",
  FAILED_MESSAGES: "failed.messages",
  MIGRATE_PRODUCT_PREVIEW_IMAGES: "migrate.product.preview.images",
  RE_ANALYSE_PRODUCT: "product.re-analyse",
  DATA_MIGRATION_QUEUE: "data.migration.seo.app",
  MESSAGE_DUMP_QUEUE: "message.dump.queue",
  UPDATE_WEBHOOKS_REGISTRATION_QUEUE: "update.webhooks.registration.queue",
  PUBSUB_PRODUCT_CREATE: "pubsub.product.create",
  PUBSUB_PRODUCT_UPDATE: "pubsub.product.update",
  PUBSUB_PRODUCT_DELETE: "pubsub.product.delete",
  WEBHOOK_IMAGE_OPTIMIZE: "webhook.image.optimize",
  AUTO_IMAGE_OPTIMIZER_QUEUE: "auto.image.optimizer",
  WEBHOOK_APP_SUBSCRIPTION_UPDATE_QUEUE: "webhook.app.subscription.update",
  WEBHOOK_THEME_PUBLISH_QUEUE: "webhook.theme.publish",
  WEBHOOK_SHOP_UPDATE_QUEUE: "webhook.shop.update",
  WEBHOOK_APP_UNINSTALL_QUEUE: "webhook.app.uninstall",
  WEBHOOK_LOCATION_CREATE_QUEUE: "webhook.location.create",
  WEBHOOK_LOCATION_UPDATE_QUEUE: "webhook.location.update",
  WEBHOOK_LOCATION_DELETE_QUEUE: "webhook.location.delete",
  WEBHOOK_BULK_OPERATION_FINISH_QUEUE: "webhook.bulk.operation.finish",
  SETUP_NEW_SHOP: "setup.new.shop",
  // Collection related queues name
  COLLECTION_SYNC_QUEUE: "collection.sync",
  COLLECTION_PRODUCTS_SYNC_QUEUE: "collection.products.sync",
  WEBHOOK_COLLECTION_CREATE: "webhook.collection.create",
  WEBHOOK_COLLECTION_UPDATE: "webhook.collection.update",
  WEBHOOK_COLLECTION_DELETE: "webhook.collection.delete",

  // Docs related queues
  DOC_SYNC_QUEUE: "doc.sync",
  WEBHOOK_DOC_CREATE: "webhook.doc.create",
  WEBHOOK_DOC_UPDATE: "webhook.doc.update",
  WEBHOOK_DOC_DELETE: "webhook.doc.delete",
  WEBHOOK_BETTERDOCS_UNINSTALLED: "webhook.betterdocs.uninstalled",

  // email notification trigger
  EMAIL_NOTIFICATION_TRIGGER_QUEUE: "email.notification.trigger.queue",
  GENERATE_EMAIL_QUEUE: "generate.email.queue",

  // Event queues
  EVENT_WEEKLY_STORE_REPORT: "event.weekly.store.report",
  GOOGLE_ANALYTICS_INTEGRATION_NOTIFICATION_QUEUE: "google.analytics.integration.notification.queue",
  GOOGLE_SEARCH_CONSOLE_INTEGRATION_NOTIFICATION_QUEUE: "google.search.console.integration.notification.queue",
  HTML_SITEMAP_INTEGRATION_NOTIFICATION_QUEUE: "html.sitemap.integration.notification.queue",
  AUTO_IMAGE_OPTIMIZER_NOTIFICATION_QUEUE: "auto.image.optimizer.notification.queue",

  // Email send queues
  SEND_EMAIL: "send.email",

  // Onetime credit purchase
  WEBHOOK_APP_PURCHASE_ONETIME: "webhook.app.purchase.onetime",

  // Localaizations
  SYNC_SHOP_LOCALES_LIST: "sync.shop.locales.list",

  // Multi-language
  MULTI_LANGUAGE_PRODUCT_SYNC_QUEUE: "multi.language.product.sync",

  // Partners
  PARTNER_SAVE_APP_EVENTS: "partners.save.app.events",
  PARTNER_SAVE_TRANSACTIONS: "partners.save.transactions",

  // AI content
  AUTO_AI_OPTIMIZATION_QUEUE: "auto.ai.optimization",
  AUTO_IMAGE_ALT_TEXT_OPTIMIZATION_QUEUE: "auto.image.alt.text.optimization",

  AUTO_COLLECTION_AI_OPTIMIZATION_QUEUE: "auto.collection.ai.optimization",

  IMAGE_FILE_SIZE_SYNC: "image.file.size.sync",

  BACKUP_QUEUE: "backup.queue",
  RESTORE_QUEUE: "restore.queue",
  RESTORE_MANUALLY_QUEUE: "restore.manually.queue",

  FOCUS_KEYWORD_GENERATOR: "focus.keyword.generator",
  DEFINE_INDUSTRY: "define.industry",

  BULK_OPERATION_TRACKER: "bulk.operation.tracker",

  ADDON_USAGE_NOTIFICATION_QUEUE: "addon.usage.notification.queue",
  REMOVE_DELETED_PRODUCTS_QUEUE: "remove.deleted.products.queue",
};

const PREFETCH_LIMITS = {
  [QUEUE_NAMES.DEFAULT]: 1,
  [QUEUE_NAMES.PRODUCT_SYNC]: 3,
  [QUEUE_NAMES.PRODUCT_SYNC_SINGLE]: 1,
  [QUEUE_NAMES.PRODUCT_SYNC_WITH_DATA_MIGRATION]: 1,
  [QUEUE_NAMES.PRODUCT_OPTIMIZE]: 2,
  [QUEUE_NAMES.PRODUCT_OPTIMIZE_BULK_OPERATION]: 2,
  [QUEUE_NAMES.WEBHOOK_PRODUCT_CREATE]: 2,
  [QUEUE_NAMES.WEBHOOK_PRODUCT_UPDATE]: 5,
  [QUEUE_NAMES.WEBHOOK_PRODUCT_DELETE]: 2,
  [QUEUE_NAMES.SUBSCRIPTION_DOWNGRADE]: 2,
  [QUEUE_NAMES.MAILCHIMP_ADD_MEMBER]: 1,
  [QUEUE_NAMES.DELETE_SHOP_DATA_QUEUE]: 1,
  [QUEUE_NAMES.PRODUCT_INDEXING_GOOGLE]: 1,
  [QUEUE_NAMES.PAGE_SYNC]: 2,
  [QUEUE_NAMES.ARTICLE_SYNC]: 2,
  [QUEUE_NAMES.PAGE_METAFIELD_SYNC]: 1,
  [QUEUE_NAMES.BLOG_ARTICLES_SYNC]: 1,
  [QUEUE_NAMES.ARTICLE_METAFIELD_SYNC]: 1,
  [QUEUE_NAMES.RE_ANALYSE_PRODUCT]: 10,
  [QUEUE_NAMES.DATA_MIGRATION_QUEUE]: 1,
  [QUEUE_NAMES.UPDATE_WEBHOOKS_REGISTRATION_QUEUE]: 5,
  [QUEUE_NAMES.PUBSUB_PRODUCT_CREATE]: 2,
  [QUEUE_NAMES.PUBSUB_PRODUCT_UPDATE]: 5,
  [QUEUE_NAMES.PUBSUB_PRODUCT_DELETE]: 2,
  [QUEUE_NAMES.WEBHOOK_IMAGE_OPTIMIZE]: 2,
  [QUEUE_NAMES.AUTO_IMAGE_OPTIMIZER_QUEUE]: 5,
  [QUEUE_NAMES.WEBHOOK_APP_SUBSCRIPTION_UPDATE_QUEUE]: 3,
  [QUEUE_NAMES.WEBHOOK_THEME_PUBLISH_QUEUE]: 1,
  [QUEUE_NAMES.WEBHOOK_SHOP_UPDATE_QUEUE]: 2,
  [QUEUE_NAMES.WEBHOOK_APP_UNINSTALL_QUEUE]: 1,
  [QUEUE_NAMES.WEBHOOK_LOCATION_CREATE_QUEUE]: 1,
  [QUEUE_NAMES.WEBHOOK_LOCATION_UPDATE_QUEUE]: 1,
  [QUEUE_NAMES.WEBHOOK_LOCATION_DELETE_QUEUE]: 1,
  [QUEUE_NAMES.WEBHOOK_BULK_OPERATION_FINISH_QUEUE]: 1,
  [QUEUE_NAMES.SETUP_NEW_SHOP]: 1,
  [QUEUE_NAMES.FLUENT_CRM_ADD_CONTACT]: 1,
  // Collection related queues prefetch limit
  [QUEUE_NAMES.COLLECTION_SYNC_QUEUE]: 2,
  [QUEUE_NAMES.COLLECTION_PRODUCTS_SYNC_QUEUE]: 1,
  [QUEUE_NAMES.WEBHOOK_COLLECTION_CREATE]: 2,
  [QUEUE_NAMES.WEBHOOK_COLLECTION_UPDATE]: 5,
  [QUEUE_NAMES.WEBHOOK_COLLECTION_DELETE]: 2,

  // [QUEUE_NAMES.EVENT_WEEKLY_STORE_REPORT]: 5,

  [QUEUE_NAMES.SEND_EMAIL]: 10,

  [QUEUE_NAMES.WEBHOOK_APP_PURCHASE_ONETIME]: 1,
  // [QUEUE_NAMES.TEST]: 1,
  // [QUEUE_NAMES.FOO]: 1,

  // Localaizations
  [QUEUE_NAMES.SYNC_SHOP_LOCALES_LIST]: 2,

  // Multi-language
  [QUEUE_NAMES.MULTI_LANGUAGE_PRODUCT_SYNC_QUEUE]: 3,

  // Partners
  [QUEUE_NAMES.PARTNER_SAVE_APP_EVENTS]: 1,
  [QUEUE_NAMES.PARTNER_SAVE_TRANSACTIONS]: 1,

  // AI content
  [QUEUE_NAMES.AUTO_AI_OPTIMIZATION_QUEUE]: 5,
  [QUEUE_NAMES.AUTO_IMAGE_ALT_TEXT_OPTIMIZATION_QUEUE]: 5,

  [QUEUE_NAMES.AUTO_COLLECTION_AI_OPTIMIZATION_QUEUE]: 5,

  [QUEUE_NAMES.IMAGE_FILE_SIZE_SYNC]: 1,

  [QUEUE_NAMES.BACKUP_QUEUE]: 1,
  [QUEUE_NAMES.RESTORE_QUEUE]: 2,
  [QUEUE_NAMES.RESTORE_MANUALLY_QUEUE]: 5,

  // email notification
  [QUEUE_NAMES.EMAIL_NOTIFICATION_TRIGGER_QUEUE]: 3,
  [QUEUE_NAMES.GENERATE_EMAIL_QUEUE]: 5,

  [QUEUE_NAMES.FOCUS_KEYWORD_GENERATOR]: 20,
  [QUEUE_NAMES.DEFINE_INDUSTRY]: 1,

  [QUEUE_NAMES.BULK_OPERATION_TRACKER]: 1,

  [QUEUE_NAMES.ADDON_USAGE_NOTIFICATION_QUEUE]: 1,
  [QUEUE_NAMES.REMOVE_DELETED_PRODUCTS_QUEUE]: 1,
};

const EXCHANGE_TYPES = {
  DIRECT: "direct",
  DELAYED: "x-delayed-message",
};

const EXCHANGE_NAMES = {
  DEFAULT: "default",
  EX_DELAYED: "delayed",
  MESSAGE_DUMP: "message-dump",
};

const KEYS = {
  STORESEO: "storeseo",
};

const CONNECTION_CONFIG = {
  host: process.env.AMPQ_HOST || "localhost",
  port: process.env.AMPQ_PORT || 5672,
  username: process.env.AMPQ_USERNAME || "test",
  password: process.env.AMPQ_PASSWORD || "123456",
};

const MAX_RETRY_FOR_FAILED_MESSAGES = 3;

module.exports = {
  QUEUE_NAMES,
  PREFETCH_LIMITS,
  EXCHANGE_TYPES,
  EXCHANGE_NAMES,
  KEYS,
  CONNECTION_CONFIG,
  MAX_RETRY_FOR_FAILED_MESSAGES,
};
