// // @ts-check
const { isAxiosError } = require("axios");
const { RABBIT_MQ_CONNECTION } = require("..");
const logger = require("storeseo-logger");
const { EXCHANGE_NAMES, EXCHANGE_TYPES, NUMBER_OF_QUEUES } = require("./config");
const { getQueueName } = require("./helper");
const AiOptimizationStatus = require("storeseo-enums/aiOptimization");
const AiService = require("../../services/AiService");
const ResourceOptimizationService = require("../../services/resource/ResourceOptimizationService");
const resourceType = require("storeseo-enums/resourceType");
const ShopService = require("../../services/ShopService");
const cache = require("../../cache");
const subscriptionAddonGroup = require("storeseo-enums/subscriptionAddonGroup");
const { METAFIELD_KEYS } = require("storeseo-enums/metafields");
const resourceOPType = require("storeseo-enums/resourceOPType");
const { isEmpty } = require("lodash");
const { InactiveShopError } = require("../../../errors");
const ShopifyService = require("../../services/ShopifyService");
const OpenAiService = require("../../services/openai/OpenAiService");
const CollectionService = require("../../services/collections/CollectionService");
const CollectionAnalysisService = require("../../services/collections/CollectionAnalysisService");
const { dispatchQueue } = require("../queueDispatcher");
const { QUEUE_NAMES } = require("../config");

/**
 * @typedef {{meta:boolean,tags:boolean,imageAltText:"allImages" | "featuredImage"}} AiOptimizationSettings
 */

/**
 * @type {import("amqplib").ConfirmChannel | null}
 */
let consumerChannel = null;

/**
 * Assert exchanges into existance for use in queue consume & dispatch
 * @param {import("amqplib").Channel} channel
 * @returns {Promise<import("amqplib").Channel} same channel provided in the input
 */
const assertExchanges = async (channel) => {
  await channel.assertExchange(EXCHANGE_NAMES.DEFAULT, EXCHANGE_TYPES.DIRECT);
  await channel.assertExchange(EXCHANGE_NAMES.EX_DELAYED, EXCHANGE_TYPES.DELAYED, {
    arguments: { "x-delayed-type": "direct" },
  });
  await channel.assertExchange(EXCHANGE_NAMES.MESSAGE_DUMP, EXCHANGE_TYPES.DIRECT);

  return channel;
};

/**
 * Create a new channel & perapre it for message dispatch
 */
const prepareConsumerChannel = async () => {
  console.log("creating consumer channel...");
  const connection = await RABBIT_MQ_CONNECTION;
  const channel = await connection.createChannel();
  await assertExchanges(channel);

  consumerChannel = channel;
  consumerChannel.prefetch(1);
  console.log("Consumer channel created!");
};

async function bindQueues() {
  console.log(`Queues to bind: ${NUMBER_OF_QUEUES}`);

  for (let i = 1; i <= NUMBER_OF_QUEUES; i++) {
    const queueName = getQueueName(i);
    await consumerChannel.bindQueue(queueName, EXCHANGE_NAMES.DEFAULT, queueName);
    await consumerChannel.bindQueue(queueName, EXCHANGE_NAMES.EX_DELAYED, queueName);
  }

  console.log("Queues binded!");
}

/**
 *
 * @param {string} queue
 * @param {import("amqplib").ConsumeMessage} message
 */
async function handleMessage(queue, message) {
  const { collection, shop } = JSON.parse(message.content.toString());
  let approximate_credit_usage = 0;
  const { title, description, focus_keyword } = collection;
  const shopDetails = await ShopService.getShop(shop);

  try {
    await ShopifyService.hasValidSession(shop);

    let countTotalCreditUsages = 0; // Update this value as per the successful AI content generation

    const resourceOptimizationSetting = await ResourceOptimizationService.getByCondition({
      shop_id: shopDetails?.id,
      resource_id: collection.id,
      resource_type: resourceType.COLLECTION,
      resource_op_type: resourceOPType.AI_OPTIMIZATION,
    });

    approximate_credit_usage = resourceOptimizationSetting?.approximate_credit_usage ?? 0;

    let generatedAiContent = null;

    // Generate ai content
    const aiContentGeneratorLanguage = await cache.shop.aiContentGeneratorLanguage(shop);
    const data = await OpenAiService.generateCollectionContent({
      title,
      description: description ?? "",
      focusKeyword: focus_keyword ?? "",
      language: aiContentGeneratorLanguage.name,
    });

    if (data?.output) {
      const { output, usage } = data;
      generatedAiContent = output;
      const aiContentCreditUsage = AiService.calculateCreditUsage(usage);
      countTotalCreditUsages += aiContentCreditUsage; // Update this value as per the successful AI content generation
    }

    const remainingCreditUsage = Number((approximate_credit_usage - countTotalCreditUsages).toFixed(2));

    // Sync the credit usage after AI content generation
    if (remainingCreditUsage < 0) {
      incrementRelatedUsageCount(shop, Math.abs(remainingCreditUsage));
    } else {
      decrementRelatedUsageCount(shop, Math.abs(remainingCreditUsage));
    }

    await handleSuccessfulCollectionDataGeneration(
      shopDetails,
      collection,
      generatedAiContent,
      resourceOptimizationSetting
    );
    console.log(`[${queue}] shop: ${shop} - collection id: ${collection.id} ai data generation successful!`);
    consumerChannel.ack(message);
  } catch (err) {
    if (err instanceof InactiveShopError) {
      logger.info(err, {
        queue,
        dispatchedMessage: JSON.parse(message.content.toString()),
        transaction: "AiOptimizer",
        domain: shop,
      });
      await handleFailedCollectionDataGeneration(
        shopDetails,
        collection,
        approximate_credit_usage,
        JSON.stringify(err?.message || "Unknown error")
      );
      consumerChannel.ack(message);
    } else if (isAxiosError(err) && err.code !== "ECONNABORTED") {
      console.error("Failed due to axios error", err);
      logger.error("API call to generate content using AI failed", {
        description: `Failed to process collection: ${collection.id} for shop: ${shop} in queue: ${queue}`,
        queue,
        dispatchedMessage: JSON.parse(message.content.toString()),
        details: JSON.stringify(err.response.data),
        transaction: "AiOptimizer",
        domain: shop,
      });
      await handleFailedCollectionDataGeneration(
        shopDetails,
        collection,
        approximate_credit_usage,
        JSON.stringify(err?.message || "Unknown error")
      );
      consumerChannel.ack(message);
    } else {
      console.error("Failed due to unknown error", err);
      logger.error(err, {
        description: `Failed to process collection: ${collection.id} for shop: ${shop} in queue: ${queue}`,
        queue,
        dispatchedMessage: JSON.parse(message.content.toString()),
        transaction: "AiOptimizer",
        domain: shop,
      });
      consumerChannel.nack(message);
    }
  }
}

// Extend this method as you see fit
/**
 *
 * @param {object} shop
 * @param {*} collection
 * @param {*} generatedAiContent
 * @param {ResourceOptimizationService.ResourceOptimizationType} resourceOptimizationConfigs
 */
const handleSuccessfulCollectionDataGeneration = async (
  shop,
  collection,
  generatedAiContent,
  resourceOptimizationConfigs
) => {
  const session = await ShopService.getSession({ domain: shop?.domain });
  const { optimization_setting, optimization_stats } = resourceOptimizationConfigs;

  dispatchQueue({
    queueName: QUEUE_NAMES.BULK_OPERATION_TRACKER,
    message: {
      shop_id: shop.id,
      shopDomain: shop.domain,
      resourceType: resourceType.COLLECTION,
      opType: resourceOPType.AI_OPTIMIZATION,
      resourceId: collection.collection_id,
      isSuccess: true,
    },
  });

  const returnedCollection = await CollectionService.update(shop.id, collection.id, {
    ai_optimized_at: new Date(),
    ai_optimization_status: AiOptimizationStatus.OPTIMIZED,
  });

  if (isEmpty(returnedCollection)) return;

  const findCollectionMetaTitle = collection.meta.find((item) => item.key === METAFIELD_KEYS.TITLE_TAG);
  const findCollectionMetaDescription = collection.meta.find((item) => item.key === METAFIELD_KEYS.DESCRIPTION_TAG);
  const metaData = {
    ...(optimization_setting?.meta
      ? { metaTitle: generatedAiContent?.meta_title, metaDescription: generatedAiContent?.meta_description }
      : { metaTitle: findCollectionMetaTitle?.value, metaDescription: findCollectionMetaDescription?.value }),
  };

  const updatedCollection = await CollectionService.updateAndSyncWithShopify(
    shop.id,
    collection.collection_id,
    {
      ...metaData,
      focusKeyword: collection?.focus_keyword,
    },
    session
  );

  const update_optimization_stats = {
    ...optimization_stats,
    last_queue_process_completed_date: new Date(),
  };

  await ResourceOptimizationService.upsert({
    shop_id: shop.id,
    resource_id: collection.id,
    resource_type: resourceType.COLLECTION,
    resource_op_type: resourceOPType.AI_OPTIMIZATION,
    optimization_stats: update_optimization_stats,
  });

  await CollectionAnalysisService.analysis({
    shopId: shop.id,
    collection: updatedCollection,
    oldCollection: collection,
  });
};

// Extend this method as you see fit
/**
 *
 * @param {object} shop
 * @param {object} collection
 * @param {number} usagesCreditsToRestore
 * @param {string} error
 */
const handleFailedCollectionDataGeneration = async (shop, collection, usagesCreditsToRestore, error) => {
  dispatchQueue({
    queueName: QUEUE_NAMES.BULK_OPERATION_TRACKER,
    message: {
      shop_id: shop.id,
      shopDomain: shop.domain,
      resourceType: resourceType.COLLECTION,
      opType: resourceOPType.AI_OPTIMIZATION,
      resourceId: collection.collection_id,
      isSuccess: false,
      reason: error,
    },
  });

  await CollectionService.update(shop.id, collection.id, {
    ai_optimization_status: AiOptimizationStatus.NOT_OPTIMIZED,
  });

  decrementRelatedUsageCount(shop.domain, usagesCreditsToRestore);
};

/**
 *
 * @param {string} shopDomain
 * @param {string | number} value
 * @returns {Promise<void>}
 */
const incrementRelatedUsageCount = async (shopDomain, value = "4.2") => {
  await cache.addons.incrementUsageCount(shopDomain, {
    addon: subscriptionAddonGroup.AI_OPTIMIZER,
    incrementBy: value,
  });
  await cache.addons.incrementTotalUsageCount(shopDomain, {
    addon: subscriptionAddonGroup.AI_OPTIMIZER,
    incrementBy: value,
  });
  await cache.addons.incrementTotalAppUsageCount(subscriptionAddonGroup.AI_OPTIMIZER, value);
};

/**
 *
 * @param {string} shopDomain
 * @param {string | number} value
 * @returns {Promise<void>}
 */
const decrementRelatedUsageCount = async (shopDomain, value = "4.2") => {
  await cache.addons.decrementUsageCount(shopDomain, {
    addon: subscriptionAddonGroup.AI_OPTIMIZER,
    decrementBy: value, // Approximate value. This will sync after AI content generation
  });
  await cache.addons.decrementTotalUsageCount(shopDomain, {
    addon: subscriptionAddonGroup.AI_OPTIMIZER,
    decrementBy: value, // Approximate value. This will sync after AI content generation
  });
  await cache.addons.decrementTotalAppUsageCount(subscriptionAddonGroup.AI_OPTIMIZER, value); // Approximate value. This will sync after AI content generation
};

/**
 * @callback messageHandler
 * @param {import("amqplib").ConsumeMessage} message
 */

/**
 * @param {string} queue name of the queue
 * @return {messageHandler}
 */
function createHandler(queue) {
  return (message) => handleMessage(queue, message);
}

async function startConsumingMessages() {
  for (let i = 1; i <= NUMBER_OF_QUEUES; i++) {
    const queueName = getQueueName(i);
    const handler = createHandler(queueName);
    consumerChannel.consume(queueName, handler, {
      noAck: false,
    });
  }
}

async function bootConsumer() {
  try {
    await prepareConsumerChannel();
    await bindQueues();

    await startConsumingMessages();
  } catch (err) {
    console.log(err);
  }
}

module.exports = {
  bootConsumer,
};
