const { NUMBER_OF_QUEUES } = require("./config");
const { getQueueName } = require("./helper");

const EMPTY_QUEUES = new Set();

const PENDING_MESSAGE_COUNT_BY_QUEUE = {};

const resetState = () => {
  for (let i = 1; i <= NUMBER_OF_QUEUES; i++) {
    const queue = getQueueName(i);

    EMPTY_QUEUES.add(queue);
    PENDING_MESSAGE_COUNT_BY_QUEUE[queue] = 0;
  }
};

/**
 * Get name of the queues that are currently empty
 * @returns {string[]}
 */
const listOfEmptyQueues = () => Array.from(EMPTY_QUEUES);

module.exports = {
  resetState,
  listOfEmptyQueues,
};
