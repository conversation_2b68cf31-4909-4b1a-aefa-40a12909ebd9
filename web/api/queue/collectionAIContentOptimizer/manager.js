// @ts-check
const cache = require("../../cache");
const logger = require("storeseo-logger");
const ShopService = require("../../services/ShopService");
const { sleep } = require("../../utils/helper");
const { dispatchQueue } = require("../queueDispatcher");
const { MAX_MESSAGE_PER_QUEUE } = require("./config");
const { listOfEmptyQueues } = require("./state");
const AiOptimizationStatus = require("storeseo-enums/aiOptimization");
const moment = require("moment");
const CollectionService = require("../../services/collections/CollectionService");

/**
 *
 * @param {string} queue
 * @param {string} shop
 * @param {any[]} collections
 */
function dispatchCollections(queue, shop, collections) {
  for (let collection of collections) {
    dispatchQueue({
      queueName: queue,
      message: { collection, shop },
    });
  }

  return true;
}

/**
 * @param {number} shopId
 * @param {any[]} collections
 */
async function markDispatchedCollections(shopId, collections) {
  for (let collection of collections)
    await CollectionService.update(shopId, collection.id, {
      ai_optimization_status: AiOptimizationStatus.DISPATCHED,
    });

  return true;
}

/**
 *
 * @param {string} queue name of the queue
 * @param {string} shop myshopify domain of the shop
 */
async function allocatePendingCollectionsToQueue(queue, shop) {
  console.log(`Dispatching '${shop}' to queue '${queue}'`);
  const shopDetails = await ShopService.getShop(shop);

  const collections = await CollectionService.getFullCollectionsByCondition(
    shopDetails.id,
    {
      ai_optimization_status: AiOptimizationStatus.PENDING,
    },
    {
      limit: MAX_MESSAGE_PER_QUEUE,
    }
  );

  if (collections.length) {
    const filteredCollections = collections?.filter((collection) => collection.focus_keyword);
    dispatchCollections(queue, shop, filteredCollections);
    await markDispatchedCollections(shopDetails.id, filteredCollections);
    await cache.collectionAiOptimizer.addStoreToPendingOptimizationQueue(shop);
  }
}

async function manageQueuesAndPendingCollections() {
  try {
    const queues = listOfEmptyQueues();
    const pendingStoresCount = await cache.collectionAiOptimizer.numberOfStoresPendingOptimization();

    if (queues.length === 0) console.log("All queues are busy now...");
    // else console.log(`\nFree queues: ${JSON.stringify(queues, null, 2)}`);

    let queueUsageCount = 0;
    for (let queue of queues) {
      const shop = await cache.collectionAiOptimizer.getNextStoreFromPendingOptimizationQueue();
      if (shop) {
        await allocatePendingCollectionsToQueue(queue, shop);
        queueUsageCount++;
      } else {
        console.log("No shops are in 'pending auto collection ai optimization' list!");
        break;
      }
    }

    await cache.collectionAiOptimizer.updateMaxMinQueueUsageStats({
      pendingStoresCount,
      queueUsageCount,
    });
    await cache.collectionAiOptimizer.addToPendingSyncStatisticDates(moment().format("YYYY-MM-DD"));
  } catch (err) {
    logger.error(err, {
      description: "Error in queue manager",
      transaction: "CollectionAIContentOptimizer",
    });
    console.log(err);
  } finally {
    await sleep(3000);
    await manageQueuesAndPendingCollections();
  }
}

module.exports = {
  manageQueuesAndPendingCollections,
};
