const emailStatus = require("storeseo-enums/emailStatus");
const { RABBIT_MQ_CONNECTION } = require("../..");
const EmailTrackerService = require("../../../services/email/EmailTrackerService");
const ShopService = require("../../../services/ShopService");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../../config");
const BaseQueue = require("../BaseQueue");
const EmailGenerateProcessor = require("./EmailGenerateProcessor");

class GenerateEmailQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);

    try {
      const { shopDomain, topic, deliveryDate } = decodedMessage;

      const shop = await ShopService.getShopByCondition({
        domain: shopDomain,
      });

      console.log(
        `---- ${this.config.queueName} generating email on ${topic} for ${shopDomain} scheduled at ${deliveryDate} -----`
      );

      const emailData = await EmailGenerateProcessor[topic](shop);

      this.storeEmail({ shop, topic, deliveryDate, emailData });

      console.log(`---- ${this.config.queueName} generated email ---- `);

      channel.ack(message);
    } catch (err) {
      console.error(err);
      console.log(`${this.config.queueName} failed. \n Message: ${err.message}`, err);
      channel.ack(message);
      await this.handleError(err, message);
    }
  }

  async storeEmail({ shop, topic, deliveryDate, emailData }) {
    const trackResult = await EmailTrackerService.insertEmail({
      shop_id: shop.id,
      topic: topic,
      send_to: shop.email,
      data: emailData,
      scheduled_delivery_date: deliveryDate,
      status: emailStatus.READY_FOR_DELIVERY,
    });
    return trackResult;
  }
}

module.exports = new GenerateEmailQueue(
  {
    queueName: QUEUE_NAMES.GENERATE_EMAIL_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.GENERATE_EMAIL_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
