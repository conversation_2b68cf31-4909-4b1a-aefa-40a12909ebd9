const eventTopics = require("storeseo-enums/eventTopics");
const { RABBIT_MQ_CONNECTION } = require("../..");
const EmailTrackerService = require("../../../services/email/EmailTrackerService");
const ShopService = require("../../../services/ShopService");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../../config");
const { dispatchQueue } = require("../../queueDispatcher");
const BaseQueue = require("../BaseQueue");
const moment = require("moment-timezone");
const { defaultEmailNotificationSettings } = require("../../../config/email-notification");
const { GOOGLE_INTEGRATION_INFO, HTML_SITEMAP, IMAGE_OPTIMIZER } = require("storeseo-enums/settingKeys");
const { EMAIL_NOTIFICATION } = require("storeseo-enums/settings/email-notification");
const AddonUsageService = require("../../../services/AddonUsageService");
const subscriptionAddonGroup = require("storeseo-enums/subscriptionAddonGroup");

const haveFiveDaysPassed = (date) => {
  const timeAfterFiveDays = moment(date).add(5, "days").startOf("day");
  return moment().isSameOrAfter(timeAfterFiveDays);
};

const formatDeliveryDate = (time, day, timeZone) => {
  let momentInstance = moment({ minute: 0 }).tz(timeZone);
  if (day !== null || day !== "") momentInstance = momentInstance.day(day);
  return momentInstance.hour(time).utc().format();
};

const isAWeekPassed = (fromDate, day, timezone) => {
  const previous = moment(fromDate);
  const now = moment();
  const diff = now.diff(previous, "days") + 1;

  const dayMatch = moment().tz(timezone).format("dddd")?.toLowerCase() === day?.toLowerCase();
  return diff >= 7 && dayMatch;
};

class EmailNotificationTriggerQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);

    try {
      const { shopDomain } = decodedMessage;
      const shop = await ShopService.getShopByCondition({ domain: shopDomain });

      let emailNotificationSettings = await ShopService.getShopSetting(shop.id, EMAIL_NOTIFICATION);
      if (!emailNotificationSettings) {
        emailNotificationSettings = await ShopService.updateShopSetting(shop.id, defaultEmailNotificationSettings);
      }

      if (shop) {
        // Weekly Store Report Trigger
        await this.triggerWeeklyStoreReportQueue(shop, emailNotificationSettings);
        // Google Analytics Trigger
        await this.triggerGoogleAnalyticsQueue(shop, emailNotificationSettings);
        // Google Search Console Trigger
        await this.triggerGoogleSearchConsoleQueue(shop, emailNotificationSettings);
        // Html Sitemap Trigger
        await this.triggerHtmlSitemapQueue(shop, emailNotificationSettings);
        // Auto Image Optimizer Trigger
        await this.triggerAutoImageOptimizerQueue(shop, emailNotificationSettings);
      }
      channel.ack(message);
    } catch (err) {
      console.error(err);
      console.log(`${this.config.queueName} failed. \n Message: ${err.message}`, err);
      channel.ack(message);
      await this.handleError(err, message);
    }
  }

  async triggerWeeklyStoreReportQueue(shop, emailSetting) {
    const settingsItems = emailSetting.value?.items ?? {};
    const weeklyStoreReportNotificationSettings = settingsItems[eventTopics.WEEKLY_STORE_REPORT];

    if (!weeklyStoreReportNotificationSettings) return null;

    const { time, day, instantNotification } = weeklyStoreReportNotificationSettings;

    const previousEmail = await EmailTrackerService.getEmailByTopic(shop.id, eventTopics.WEEKLY_STORE_REPORT);
    if (
      (!previousEmail && isAWeekPassed(shop.created_at, day, shop.ianaTimezone)) ||
      (previousEmail && isAWeekPassed(previousEmail.created_at, day, shop.ianaTimezone))
    ) {
      dispatchQueue({
        queueName: QUEUE_NAMES.GENERATE_EMAIL_QUEUE,
        message: {
          shopDomain: shop.domain,
          topic: eventTopics.WEEKLY_STORE_REPORT,
          deliveryDate:
            instantNotification && weeklyStoreReportNotificationSettings?.enabled
              ? new Date()
              : weeklyStoreReportNotificationSettings?.enabled
                ? formatDeliveryDate(time, day, shop.ianaTimezone)
                : null,
        },
      });
    } else {
      console.log(`Weekly email report already stored or it's not a week passed for ${shop.domain}. Skipping...`);
    }
  }

  async triggerGoogleAnalyticsQueue(shop, emailSetting) {
    const settingsItems = emailSetting.value?.items ?? {};
    const googleAnalyticsNotificationSettings = settingsItems[eventTopics.GOOGLE_ANALYTICS_INTEGRATION];

    if (!googleAnalyticsNotificationSettings) return null;

    const { time, day, instantNotification } = googleAnalyticsNotificationSettings;

    if (googleAnalyticsNotificationSettings?.enabled) {
      const googleIntegrationSettings = await ShopService.getShopSetting(shop.id, GOOGLE_INTEGRATION_INFO);

      if (
        googleIntegrationSettings &&
        googleIntegrationSettings?.value?.googleUserEmail &&
        googleIntegrationSettings?.value?.steps?.authenticated
      )
        return null;

      const previousEmail = await EmailTrackerService.getEmailByTopic(
        shop.id,
        eventTopics.GOOGLE_ANALYTICS_INTEGRATION
      );

      if (!previousEmail && haveFiveDaysPassed(shop.created_at)) {
        dispatchQueue({
          queueName: QUEUE_NAMES.GENERATE_EMAIL_QUEUE,
          message: {
            shopDomain: shop.domain,
            topic: eventTopics.GOOGLE_ANALYTICS_INTEGRATION,
            deliveryDate: instantNotification ? new Date() : formatDeliveryDate(time, day, shop.ianaTimezone),
          },
        });
      } else {
        console.log(
          `Google Analytics Integration Email already stored or five days have't passed for ${shop.domain}. Skipping...`
        );
      }
    } else {
      console.log(`----------${eventTopics.GOOGLE_ANALYTICS_INTEGRATION} Notification Settings Disabled-------------`);
    }
  }

  async triggerGoogleSearchConsoleQueue(shop, emailSetting) {
    const settingsItems = emailSetting.value?.items ?? {};
    const googleSearchConsoleNotificationSettings = settingsItems[eventTopics.GOOGLE_SEARCH_CONSOLE_INTEGRATION];

    if (!googleSearchConsoleNotificationSettings) return null;

    const { time, day, instantNotification } = googleSearchConsoleNotificationSettings;

    if (googleSearchConsoleNotificationSettings?.enabled) {
      const googleIntegrationSettings = await ShopService.getShopSetting(shop.id, GOOGLE_INTEGRATION_INFO);

      if (googleIntegrationSettings && googleIntegrationSettings?.value?.analyticsPropertyId) return null;

      const previousEmail = await EmailTrackerService.getEmailByTopic(
        shop.id,
        eventTopics.GOOGLE_SEARCH_CONSOLE_INTEGRATION
      );

      if (!previousEmail && haveFiveDaysPassed(shop.created_at)) {
        dispatchQueue({
          queueName: QUEUE_NAMES.GENERATE_EMAIL_QUEUE,
          message: {
            shopDomain: shop.domain,
            topic: eventTopics.GOOGLE_SEARCH_CONSOLE_INTEGRATION,
            deliveryDate: instantNotification ? new Date() : formatDeliveryDate(time, day, shop.ianaTimezone),
          },
        });
      } else {
        console.log(
          `Google Search Console Email already stored or five days have't passed for ${shop.domain}. Skipping...`
        );
      }
    } else {
      console.log(
        `----------${eventTopics.GOOGLE_SEARCH_CONSOLE_INTEGRATION} Notification Settings Disabled-------------`
      );
    }
  }

  async triggerHtmlSitemapQueue(shop, emailSetting) {
    const settingsItems = emailSetting.value?.items ?? {};
    const htmlSitemapNotificationSettings = settingsItems[eventTopics.HTML_SITEMAP_INTEGRATION];

    if (!htmlSitemapNotificationSettings) return null;

    const { time, day, instantNotification } = htmlSitemapNotificationSettings;

    if (htmlSitemapNotificationSettings?.enabled) {
      const htmlSitemapSettings = await ShopService.getShopSetting(shop.id, HTML_SITEMAP);

      if (htmlSitemapSettings && htmlSitemapSettings?.value?.setupStep === 3) return null;

      const previousEmail = await EmailTrackerService.getEmailByTopic(shop.id, eventTopics.HTML_SITEMAP_INTEGRATION);

      if (!previousEmail && haveFiveDaysPassed(shop.created_at)) {
        dispatchQueue({
          queueName: QUEUE_NAMES.GENERATE_EMAIL_QUEUE,
          message: {
            shopDomain: shop.domain,
            topic: eventTopics.HTML_SITEMAP_INTEGRATION,
            deliveryDate: instantNotification ? new Date() : formatDeliveryDate(time, day, shop.ianaTimezone),
          },
        });
      } else {
        console.log(`Html Sitemap Email already stored or five days have't passed for ${shop.domain}. Skipping...`);
      }
    } else {
      console.log(`----------${eventTopics.HTML_SITEMAP_INTEGRATION} Notification Settings Disabled-------------`);
    }
  }

  async triggerAutoImageOptimizerQueue(shop, emailSetting) {
    const settingsItems = emailSetting.value?.items ?? {};
    const autoImageOptimizerNotificationSettings = settingsItems[eventTopics.AUTO_IMAGE_OPTIMIZER_REMINDER];

    if (!autoImageOptimizerNotificationSettings) return null;

    const { time, day, instantNotification } = autoImageOptimizerNotificationSettings;

    if (autoImageOptimizerNotificationSettings?.enabled) {
      const addon = await AddonUsageService.getActiveRecord(shop.id, subscriptionAddonGroup.IMAGE_OPTIMIZER);
      const imageOptimizerSettings = await ShopService.getShopSetting(shop.id, IMAGE_OPTIMIZER);
      const isAutoImageOptimizerEnabled = imageOptimizerSettings?.value?.autoOptimization;

      if (!addon || isAutoImageOptimizerEnabled) return null;

      const previousEmail = await EmailTrackerService.getEmailByTopic(
        shop.id,
        eventTopics.AUTO_IMAGE_OPTIMIZER_REMINDER
      );

      if (!previousEmail && haveFiveDaysPassed(addon?.purchase_date)) {
        dispatchQueue({
          queueName: QUEUE_NAMES.GENERATE_EMAIL_QUEUE,
          message: {
            shopDomain: shop.domain,
            topic: eventTopics.AUTO_IMAGE_OPTIMIZER_REMINDER,
            deliveryDate: instantNotification ? new Date() : formatDeliveryDate(time, day, shop.ianaTimezone),
          },
        });
      } else {
        console.log(
          `Auto Image Optimizer Email already stored or five days have't passed for ${shop.domain}. Skipping...`
        );
      }
    } else {
      console.log(`----------${eventTopics.AUTO_IMAGE_OPTIMIZER_REMINDER} Notification Settings Disabled-------------`);
    }
  }
}

module.exports = new EmailNotificationTriggerQueue(
  {
    queueName: QUEUE_NAMES.EMAIL_NOTIFICATION_TRIGGER_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.EMAIL_NOTIFICATION_TRIGGER_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
