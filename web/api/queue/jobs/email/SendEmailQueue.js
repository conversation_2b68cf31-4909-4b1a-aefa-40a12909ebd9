const emailStatus = require("storeseo-enums/emailStatus");
const OPTIONS = require("storeseo-enums/settings/email-notification").OPTIONS;
const { RABBIT_MQ_CONNECTION } = require("../..");
const EmailTrackerService = require("../../../services/email/EmailTrackerService");
const mailService = require("../../../services/MailService");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../../config");
const BaseQueue = require("../BaseQueue");
const { emailReportTemplate } = require("../../../utils/emailTemplateBuilder");
const ShopService = require("../../../services/ShopService");
const ShopifyService = require("../../../services/ShopifyService");
const eventTopics = require("storeseo-enums/eventTopics");
const moment = require("moment");
const { blockedEmailDomain } = require("../../../config/email-notification");
const { HttpResponseError } = require("@shopify/shopify-api");

class SendEmailQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const email = decodedMessage;
    const { id, shop_id, topic, send_to, data, scheduled_delivery_date } = email;
    try {
      const shop = await ShopService.getShopById(shop_id);
      if (shop) {
        const appName = await ShopifyService.getAppHandle(shop.domain);

        const emailTemplateData = {
          // ...data,
          ...data.data,
          date: moment(scheduled_delivery_date).format("ll"),
        };

        const isEnableSendEmail = process.env?.ENABLE_EMAIL?.toLowerCase() === "true" || false;
        const isBlockedEmail = blockedEmailDomain.some((item) => send_to.includes(item));

        if (!isBlockedEmail && isEnableSendEmail) {
          const result = await mailService.send({
            to: send_to,
            // subject: OPTIONS[topic].emailSubject,
            subject: data?.emailSubject ? data.emailSubject : OPTIONS[topic]?.emailSubject,
            html: await emailReportTemplate(appName, topic, emailTemplateData),
          });

          if (result.status === 200) {
            EmailTrackerService.updateEmailById(id, {
              delivered_at: new Date(),
              status: emailStatus.DELIVERED,
              failed_reason: null,
            });
          }
        } else {
          EmailTrackerService.updateEmailById(id, {
            status: emailStatus.INTERNAL_EMAIL_SKIPPED_DELIVERY,
          });
        }

        console.log(`${this.config.queueName} - processed successfully`);
        channel.ack(message);
      } else {
        EmailTrackerService.updateEmailById(id, {
          status: emailStatus.DELIVERY_FAILED,
          failed_reason: "Shop not found",
        });
        channel.ack(message);
        console.error(`${this.config.queueName} failed. for topic: ${topic}, \n Message: Shop not found`);
      }
    } catch (err) {
      EmailTrackerService.updateEmailById(id, {
        status: emailStatus.DELIVERY_FAILED,
        failed_reason: err?.details || "Something went wrong",
      });

      channel.ack(message);
      console.error(`${this.config.queueName} failed. for topic: ${topic}, \n Message: ${err.details}`, err?.message);
      if ((!err) instanceof HttpResponseError) await this.handleError(err, message);
    }
  }
}

module.exports = new SendEmailQueue(
  {
    queueName: QUEUE_NAMES.SEND_EMAIL,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.SEND_EMAIL],
  },
  RABBIT_MQ_CONNECTION
);
