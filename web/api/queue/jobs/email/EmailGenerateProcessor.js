const eventTopics = require("storeseo-enums/eventTopics");
const ShopifyService = require("../../../services/ShopifyService");
const ReportService = require("../../../services/ReportService");
const { generateShopifyDomainHandle } = require("../../../utils/helper");
const { OPTIONS } = require("storeseo-enums/settings/email-notification");
const cache = require("../../../cache");
const { IMAGE_OPTIMIZER, AI_OPTIMIZER } = require("storeseo-enums/cacheKeys");

async function generateEmailDataForWeeklyStoreReport(shop) {
  const data = await ReportService.getWeeklyEmailNotificationProductReport(shop);
  const emailSubject = OPTIONS[eventTopics.WEEKLY_STORE_REPORT].emailSubject;
  return { data, emailSubject };
}

async function generateEmailDataForProductSyncComplete(shop) {
  const appHandle = await ShopifyService.getAppHandle(shop.domain);
  const shopifyDomainHandle = generateShopifyDomainHandle(shop.domain);
  const path = `admin.shopify.com/store/${shopifyDomainHandle}/apps/${appHandle}/optimize-seo`;

  const data = {
    store: {
      name: shop.name,
      domain: shop.domain,
      shopifyDomainHandle: shopifyDomainHandle,
      path: path,
    },
  };

  const emailSubject = OPTIONS[eventTopics.PRODUCT_SYNC_COMPLETE].emailSubject;
  return { data, emailSubject };
}

async function generateEmailDataForGoogleAnalytics(shop) {
  const shopifyDomainHandle = generateShopifyDomainHandle(shop.domain);

  const data = { store: { name: shop.name, shopifyDomainHandle: shopifyDomainHandle, domain: shop.domain } };
  const emailSubject = OPTIONS[eventTopics.GOOGLE_ANALYTICS_INTEGRATION].emailSubject;

  return { data, emailSubject };
}

async function generateEmailDataForGoogleSearchConsole(shop) {
  const shopifyDomainHandle = generateShopifyDomainHandle(shop.domain);

  const data = { store: { name: shop.name, shopifyDomainHandle: shopifyDomainHandle, domain: shop.domain } };
  const emailSubject = OPTIONS[eventTopics.GOOGLE_SEARCH_CONSOLE_INTEGRATION].emailSubject;
  return { data, emailSubject };
}

async function generateEmailDataForAutoImageOptimizer(shop) {
  const shopifyDomainHandle = generateShopifyDomainHandle(shop.domain);

  const data = { store: { name: shop.name, shopifyDomainHandle: shopifyDomainHandle, domain: shop.domain } };
  const emailSubject = OPTIONS[eventTopics.AUTO_IMAGE_OPTIMIZER_REMINDER].emailSubject;
  return { data, emailSubject };
}

async function generateEmailDataForHtmlSitemap(shop) {
  const shopifyDomainHandle = generateShopifyDomainHandle(shop.domain);

  const data = { store: { name: shop.name, shopifyDomainHandle: shopifyDomainHandle, domain: shop.domain } };
  const emailSubject = OPTIONS[eventTopics.HTML_SITEMAP_INTEGRATION].emailSubject;
  return { data, emailSubject };
}

async function generateEmailDataForImageOptimizerUsage(shop) {
  const shopifyDomainHandle = generateShopifyDomainHandle(shop.domain);
  const lastSentEmailUsagePercentage = await cache.addons.lastEmailSentForUsagePercentage(shop.domain, {
    addon: IMAGE_OPTIMIZER,
  });

  const data = {
    store: {
      name: shop.name,
      shopifyDomainHandle: shopifyDomainHandle,
      domain: shop.domain,
      usagePercentage: lastSentEmailUsagePercentage,
    },
  };

  const emailSubject = `You Have Used ${lastSentEmailUsagePercentage}% of Your Image Optimizer Plan`;
  return { data, emailSubject };
}

async function generateEmailDataForAIContentOptimizerUsage(shop) {
  const shopifyDomainHandle = generateShopifyDomainHandle(shop.domain);
  const lastSentEmailUsagePercentage = await cache.addons.lastEmailSentForUsagePercentage(shop.domain, {
    addon: AI_OPTIMIZER,
  });

  const data = {
    store: {
      name: shop.name,
      shopifyDomainHandle: shopifyDomainHandle,
      domain: shop.domain,
      usagePercentage: lastSentEmailUsagePercentage,
    },
  };

  const emailSubject = `You Have Used ${lastSentEmailUsagePercentage}% of Your AI Content Optimizer Credits`;
  return { data, emailSubject };
}

module.exports = {
  [eventTopics.WEEKLY_STORE_REPORT]: generateEmailDataForWeeklyStoreReport,
  [eventTopics.PRODUCT_SYNC_COMPLETE]: generateEmailDataForProductSyncComplete,
  [eventTopics.GOOGLE_ANALYTICS_INTEGRATION]: generateEmailDataForGoogleAnalytics,
  [eventTopics.GOOGLE_SEARCH_CONSOLE_INTEGRATION]: generateEmailDataForGoogleSearchConsole,
  [eventTopics.AUTO_IMAGE_OPTIMIZER_REMINDER]: generateEmailDataForAutoImageOptimizer,
  [eventTopics.HTML_SITEMAP_INTEGRATION]: generateEmailDataForHtmlSitemap,
  [eventTopics.IMAGE_OPTIMIZER_USAGE_NOTIFICATION]: generateEmailDataForImageOptimizerUsage,
  [eventTopics.AI_CONTENT_OPTIMIZER_USAGE_NOTIFICATION]: generateEmailDataForAIContentOptimizerUsage,
};
