const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const logger = require("storeseo-logger");
const ProductService = require("../../services/ProductService");
const analysisService = require("../../services/AnalysisService");
const { RABBIT_MQ_CONNECTION } = require("../index");
const { isEmpty } = require("lodash");
const FailedMessagesQueue = require("./FailedMessagesQueue");
const { dispatchQueue } = require("../queueDispatcher");
const ShopService = require("../../services/ShopService");

class WebhookProductDeleteQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    // const { productGqlId, shopId } = decodeToJSON(message);
    const decodedMessage = decodeToJSON(message);
    const { headers, body } = decodedMessage;
    const shopDomain = headers["X-Shopify-Shop-Domain"] || headers["x-shopify-shop-domain"];
    const { id: productGqlId } = body;
    try {
      const shop = await ShopService.getShop(shopDomain);

      if (isEmpty(shop)) {
        channel.ack(message);
        console.log("Shop not found for domain = " + shopDomain);
        return;
      }

      const product = await ProductService.deleteWebhookProduct(shop.id, productGqlId);
      channel.ack(message);
      console.log(`[${this.config.queueName}](${shopDomain}) processed successfully`);
      return true;
    } catch (e) {
      channel.ack(message);
      if (this.isThrottled(e)) {
        // re-dispatch
        dispatchQueue({ queueName: this.config.queueName, message: decodeToJSON(message), ttl: this.throttledDelay });

        console.log(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(`[${shopDomain}] ${this.config.queueName} failed.`, e);
        await this.handleError(e, message);
      }
      return false;
    }
  }
}

module.exports = new WebhookProductDeleteQueue(
  {
    queueName: QUEUE_NAMES.WEBHOOK_PRODUCT_DELETE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.WEBHOOK_PRODUCT_DELETE],
  },
  RABBIT_MQ_CONNECTION
);
