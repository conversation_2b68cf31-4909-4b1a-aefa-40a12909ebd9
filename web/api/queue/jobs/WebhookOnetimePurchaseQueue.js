const subscriptionAddonGroup = require("storeseo-enums/subscriptionAddonGroup");
const { RABBIT_MQ_CONNECTION } = require("..");
const ShopService = require("../../services/ShopService");
const SubscriptionService = require("../../services/SubscriptionService");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const { dispatchQueue } = require("../queueDispatcher");
const BaseQueue = require("./BaseQueue");
const cache = require("../../cache");

class WebhookOnetimePurchaseQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const {
      headers,
      body: { app_purchase_one_time },
    } = decodedMessage;
    const shopDomain = headers["X-Shopify-Shop-Domain"] || headers["x-shopify-shop-domain"];

    try {
      const { access_token } = await ShopService.getShop(shopDomain);
      const session = {
        shop: shopDomain,
        accessToken: access_token,
      };

      await SubscriptionService.onetimePurchaseUpdate(session, app_purchase_one_time);

      //  resetting the last sent email usage to null
      await cache.addons.lastEmailSentForUsagePercentage(shopDomain, {
        addon: subscriptionAddonGroup.AI_OPTIMIZER,
        usagePerecentage: "",
      });

      console.info(
        `[${this.config.queueName}](${shopDomain}) processed successfully: [plan name: ${app_purchase_one_time.name}, plan status: ${app_purchase_one_time.status}]`
      );
      this.consumerChannel.ack(message);
    } catch (e) {
      this.consumerChannel.ack(message);

      if (this.isThrottled(e)) {
        // re-dispatch
        // await ShopService.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.info(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(
          `[${shopDomain}] ${this.config.queueName} failed. [plan name: ${app_purchase_one_time.name}, plan status: ${app_purchase_one_time.status}] Message: ${e.message}`
        );

        await this.handleError(e, message);
      }
    }
  }
}

module.exports = new WebhookOnetimePurchaseQueue(
  {
    queueName: QUEUE_NAMES.WEBHOOK_APP_PURCHASE_ONETIME,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.WEBHOOK_APP_PURCHASE_ONETIME],
  },
  RABBIT_MQ_CONNECTION
);
