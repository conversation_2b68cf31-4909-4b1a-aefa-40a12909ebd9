const { RABBIT_MQ_CONNECTION } = require("..");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const { dispatchQueue } = require("../queueDispatcher");
const logger = require("storeseo-logger");
const BaseQueue = require("./BaseQueue");
const ShopService = require("../../services/ShopService");
const LocationService = require("../../services/LocationService");
const { serializeShopifyLocationData } = require("../../serializers/LocationSerializer");

class WebhookLocationCreateQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { headers, body } = decodedMessage;
    const shopDomain = headers["X-Shopify-Shop-Domain"] || headers["x-shopify-shop-domain"];

    try {
      const shop = await ShopService.getShop(shopDomain);
      const data = await serializeShopifyLocationData(body, shop.id);
      await LocationService.saveLocation(data);

      console.info(`[${this.config.queueName}](${shopDomain}) processed successfully`);
      this.consumerChannel.ack(message);
    } catch (e) {
      this.consumerChannel.ack(message);

      if (this.isThrottled(e)) {
        // re-dispatch
        // await ShopService.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.info(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(`[${shopDomain}] ${this.config.queueName} failed for shop [${shopDomain}] Message: ${e.message}`);
        await this.handleError(e, message);
      }

      return;
    }
  }
}

module.exports = new WebhookLocationCreateQueue(
  {
    queueName: QUEUE_NAMES.WEBHOOK_LOCATION_CREATE_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.WEBHOOK_LOCATION_CREATE_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
