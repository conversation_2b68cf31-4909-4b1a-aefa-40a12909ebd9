// //@ts-check
const { RABBIT_MQ_CONNECTION } = require("..");
const ShopService = require("../../services/ShopService");
const BackupService = require("../../services/BackupService");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const BaseQueue = require("./BaseQueue");
const logger = require("storeseo-logger");

class RestoreQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    /**
     * @type {{shopId: number, backupId: number}}
     */
    const { shopId, backupId } = decodedMessage;

    const shop = await ShopService.getShopById(shopId);
    if (!shop) {
      channel.ack(message);
      console.error(`Shop with id ${shopId} not found.`);
      return;
    }

    const shopDomain = shop.domain;

    try {
      const backup = await BackupService.getManualBackupByCondition(shopId, {
        id: backupId,
      });

      if (!backup) {
        channel.ack(message);
        console.error(`Backup with id ${backupId} not found.`);
        return;
      }

      const fileName = backup.backup_file_src;

      // Download and extract backup
      await BackupService.downloadAndExtractManualBackupFile(shopDomain, fileName);

      // Create input files for bulk operation
      await BackupService.createInputFilesForRestoreBulkOperation(shop, fileName);

      // Restore data to database
      await BackupService.restoreManualBackupData(shop, backupId);

      channel.ack(message);
      console.log(`[${shopDomain}] ${this.config.queueName} successful.`);
    } catch (err) {
      channel.ack(message);
      console.error(`[${shopDomain}] ${this.config.queueName} failed.`, err);
      logger.error(err);
      await this.handleError(err, message);
    }
  }
}

module.exports = new RestoreQueue(
  {
    queueName: QUEUE_NAMES.RESTORE_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.RESTORE_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
