const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const ProductService = require("../../services/ProductService");
const { RABBIT_MQ_CONNECTION } = require("../index");
const ShopService = require("../../services/ShopService");
const { dispatchQueue } = require("../queueDispatcher");
const logger = require("storeseo-logger");
const FailedMessagesQueue = require("./FailedMessagesQueue");

class PubSubProductDeleteQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { id } = decodedMessage.body;
    const shopDomain = decodedMessage.headers["X-Shopify-Shop-Domain"];

    try {
      const shop = await ShopService.getShop(shopDomain);
      const shopId = shop.id;
      const product = await ProductService.deleteWebhookProduct(shopId, id);
      channel.ack(message);
      console.info(`[${this.config.queueName}](${shopId}) --- "${product.title}" deleted. ID: ${product.product_id}`);
      return true;
    } catch (e) {
      channel.ack(message);
      if (this.isThrottled(e)) {
        // re-dispatch
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.info(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(`[${shopDomain}] ${this.config.queueName} failed.`, e);
        await this.handleError(e, message);
      }
      return false;
    }
  }
}

module.exports = new PubSubProductDeleteQueue(
  {
    queueName: QUEUE_NAMES.PUBSUB_PRODUCT_DELETE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.PUBSUB_PRODUCT_DELETE],
  },
  RABBIT_MQ_CONNECTION
);
