const settingKeys = require("storeseo-enums/settingKeys");
const { RABBIT_MQ_CONNECTION } = require("..");
const ShopService = require("../../services/ShopService");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const BaseQueue = require("./BaseQueue");
const cache = require("../../cache");
const ProductImageService = require("../../services/ProductImageService");
const imageOptimization = require("storeseo-enums/imageOptimization");

class AutoImageOptimizer extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { shop: shopDomain, itemId, type } = decodedMessage;

    try {
      const status = await this.handleAutoOptimization({ shopDomain, itemId, type });
      channel.ack(message);

      console.info(
        `[${this.config.queueName}](${shopDomain}) processed successfully for item id: ${itemId}, type: ${type}. Status: ${status}`
      );
    } catch (err) {
      channel.ack(message);
      console.error(
        `[${shopDomain}] ${this.config.queueName} failed. for item id: ${itemId}, item type: ${type} Message: ${err.message}`,
        err
      );
      await this.handleError(err, message);
    }
  }

  /**
   *
   * @param {{ shopDomain: string, itemId: string | number, type: string}} param0
   * @returns {Promise<"USAGE_LIMIT_EXCEEDED" | "QUEUED" | "SETTINGS_NOT_FOUND" | "INVALID_SETTINGS" | "AUTO_OPTIMIZER_TURNED_OFF" | "IMAGE_OPTIMIZER_ADDON_DISABLED">}
   */
  async handleAutoOptimization({ shopDomain, itemId, type }) {
    const shop = await ShopService.getShop(shopDomain);

    if (!this.imageOptimizerAddOnEnabled(shop)) return "IMAGE_OPTIMIZER_ADDON_DISABLED";
    if (await this.imageOptimizerUsageLimitExceeded(shopDomain)) return "USAGE_LIMIT_EXCEEDED";

    const imageOptimizerSetting = await this.getImageOptimizerSettings(shop.id);

    if (!imageOptimizerSetting) return "SETTINGS_NOT_FOUND";
    if (!imageOptimizerSetting.autoOptimization) return "AUTO_OPTIMIZER_TURNED_OFF";
    if (!this.isValidSetting(imageOptimizerSetting)) return "INVALID_SETTINGS";

    await this.queueItemImagesForOptimization({ itemId, type, shopDomain, optimizationSetting: imageOptimizerSetting });

    return "QUEUED";
  }

  /**
   *
   * @param {*} shop
   * @return {boolean}
   */
  imageOptimizerAddOnEnabled(shop) {
    return !!shop.plan_rules?.image_optimizer;
  }

  /**
   *
   * @param {string} shopDomain
   * @returns {Promise<boolean>}
   */
  async imageOptimizerUsageLimitExceeded(shopDomain) {
    const imageOptimizerUsageLimit = await cache.imageOptimizerUsageLimit(shopDomain);
    const imageOptimizerUsageCount = await cache.imageOptimizerUsageCount(shopDomain);

    return imageOptimizerUsageCount >= imageOptimizerUsageLimit;
  }

  /**
   *
   * @param {number} shopId
   * @returns {Promise<{ autoOptimization: boolean, compressionType: string, format: string,resize: string,}>}
   */
  async getImageOptimizerSettings(shopId) {
    try {
      const setting = await ShopService.getShopSetting(shopId, settingKeys.IMAGE_OPTIMIZER);
      return setting.value;
    } catch (err) {
      console.error(`[${this.config.queueName}]`, err);
      return null;
    }
  }

  /**
   *
   * @param {*} setting
   * @returns {boolean}
   */
  isValidSetting(setting) {
    const { compressionType, compressionFormat, resize } = setting;

    return !(compressionType === "none" && compressionFormat === "none" && resize === "none");
  }

  /**
   *
   * @param {{ itemId: string | number, type: string, shopDomain: string, optimizationSetting: *}} param0
   */
  async queueItemImagesForOptimization({ itemId, type, shopDomain, optimizationSetting }) {
    const unOptimizedImages = await ProductImageService.getUnoptimizedImages(itemId);

    const formattedSetting = {
      compression_type:
        optimizationSetting.compressionType === "none" || !optimizationSetting.compressionType
          ? undefined
          : optimizationSetting.compressionType,
      target_width:
        optimizationSetting.resize === "none" || !optimizationSetting.resize ? undefined : optimizationSetting.resize,
      target_format: undefined,
      // target_format:
      //   optimizationSetting.format === "none" || !optimizationSetting.format ? undefined : optimizationSetting.format,
    };

    for (let image of unOptimizedImages) {
      if (await this.imageOptimizerUsageLimitExceeded(shopDomain)) break;

      if (this.isSvgImage(image.src)) continue;

      const updatedImage = await ProductImageService.updateImage(image.id, {
        optimization_status: imageOptimization.PENDING,
        optimization_meta: {},
        optimization_setting: formattedSetting,
      });

      await cache.updateImageOptimizerUsageCount(shopDomain);
    }

    await cache.addStoreToPendingImageOptimizationQueue(shopDomain);
  }

  /**
   *
   * @param {string} url shopify image url
   */
  isSvgImage = (url) => {
    return url.split(".").reverse()[0].toLowerCase().startsWith("svg");
  };
}

module.exports = new AutoImageOptimizer(
  {
    queueName: QUEUE_NAMES.AUTO_IMAGE_OPTIMIZER_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.AUTO_IMAGE_OPTIMIZER_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
