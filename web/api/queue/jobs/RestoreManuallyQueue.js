// //@ts-check
const fs = require("fs");
const readline = require("readline");
const { RABBIT_MQ_CONNECTION } = require("..");
const ShopService = require("../../services/ShopService");
const BackupService = require("../../services/BackupService");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const BaseQueue = require("./BaseQueue");
const logger = require("storeseo-logger");
const ShopifyService = require("../../services/ShopifyService");
const resourceTypes = require("storeseo-enums/resourceType");
const { sleep } = require("../../utils/helper");
const ProductAnalysisService = require("../../services/ProductAnalysisService");
const ProductService = require("../../services/ProductService");

class RestoreManuallyQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    /**
     * @type {{shopId: number, backupId: number, data: Array<object> | object, filePath: string, resourceType: string}}
     */
    const { shopId, backupId, data, filePath, resourceType, isLastItem } = decodedMessage;

    const shop = await ShopService.getShopById(shopId);
    if (!shop) {
      channel.ack(message);
      console.error(`Shop with id ${shopId} not found.`);
      return;
    }

    const shopDomain = shop.domain;

    // If API rate limit is exceed flag is on, requeue the message instead of further processing
    if (await ShopService.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API")) {
      channel.ack(message);
      return dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
    }

    try {
      const backup = await BackupService.getManualBackupByCondition(shopId, {
        id: backupId,
      });

      if (!backup) {
        channel.ack(message);
        console.error(`Backup with id ${backupId} not found.`);
        return;
      }

      const session = { shop: shop.domain, accessToken: shop.access_token };

      if (resourceType === resourceTypes.PRODUCT_IMAGE) {
        for (const item of data) {
          console.log(`${item.image.id}: image file restored`);

          const owner = item.owner;
          const oldProduct = await ProductService.getProductDetails(shopId, owner.id);

          const res = await ShopifyService.updateImageFiles(session.shop, item.image);
          if (res.fileUpdate.userErrors.length > 0) {
            const errors = res.fileUpdate.userErrors.map((error) => error.code).join(", ");
            console.error(`${item.id}: image file restore failed. Errors: ${errors}`);
          } else {
            const product = await ProductService.getProductDetails(shopId, owner.id);
            // Re-analyse product after image alt text optimization
            await ProductAnalysisService.analyseEachProduct({ product, shopId, oldProduct });
          }

          await sleep(750);
        }
      }

      if (isLastItem) {
        const restoreManualSteps = backup.restore_steps.manual_mutations.map((step) => {
          if (step.filePath === filePath) {
            return {
              ...step,
              complete: true,
              completed_at: new Date(),
            };
          }
          return step;
        });

        await BackupService.updateManualBackup(backupId, {
          restore_steps: {
            ...backup.restore_steps,
            manual_mutations: restoreManualSteps,
          },
        });

        await BackupService.markedManualRestoreAsComplete(shopId, shopDomain, backupId);
      }

      channel.ack(message);
      console.log(`[${shopDomain}] ${this.config.queueName} successful.`);
    } catch (err) {
      channel.ack(message);
      if (this.isThrottled(err)) {
        // re-dispatch
        await ShopService.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.log(`[${shopDomain}] ${this.config.queueName} redispatched. Wait ${this.throttledDelay / 1000}s.`);
      } else {
        console.error(`[${shopDomain}] ${this.config.queueName} failed.`, err);
        logger.error(err);
        await this.handleError(err, message);
      }
    }
  }
}

module.exports = new RestoreManuallyQueue(
  {
    queueName: QUEUE_NAMES.RESTORE_MANUALLY_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.RESTORE_MANUALLY_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
