const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const logger = require("storeseo-logger");
const PageService = require("../../services/PageService");
const ShopService = require("../../services/ShopService");
const analysisService = require("../../services/AnalysisService");
const { RABBIT_MQ_CONNECTION } = require("../index");
const { dispatchQueue } = require("../queueDispatcher");
const SitemapService = require("../../services/SitemapService");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const { sleep } = require("../../utils/helper");

class PageMetafieldSyncQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { user, shopId } = decodedMessage;

    try {
      // If API rate limit is exceed flag is on, requeue the message instead of further processing
      if (await ShopService.apiRateLimitExceeded(user.shop, "SHOPIFY_HTTP_API")) {
        channel.ack(message);
        return dispatchQueue({
          queueName: this.config.queueName,
          message: decodedMessage,
          ttl: this.throttledDelay,
        });
      }

      const pages = await PageService.getAllPageIds(shopId);
      const { url } = await ShopService.getShopById(shopId);

      if (pages.length > 0) {
        let hasLimit = true;
        for (let page of pages) {
          if (hasLimit) {
            const { limitRemaining, updatedPage } = await PageService.updatePageMetafieldsFromShopify(
              user,
              shopId,
              page.id
            );

            await sleep(2000);

            if (updatedPage) {
              await SitemapService.storeSitemapData(updatedPage, analysisEntityTypes.PAGE);
              await analysisService.analyseEachPage({ shopId, page: updatedPage, url });
            }

            if (limitRemaining === 0) {
              hasLimit = false;
              await ShopService.apiRateLimitExceeded(user.shop, "SHOPIFY_HTTP_API", true, 90);
              dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
            }
          }
        }
      }
      console.info(`[${user.shop}] ${this.config.queueName} success.`);
      channel.ack(message);
      return true;
    } catch (e) {
      channel.ack(message);
      if (this.isThrottled(e)) {
        // re-dispatch

        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });

        console.info(`[${user.shop}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(`[${user.shop}] ${this.config.queueName} failed.`, e);
        await this.handleError(e, message);
      }
      return false;
    }
  }
}

module.exports = new PageMetafieldSyncQueue(
  {
    queueName: QUEUE_NAMES.PAGE_METAFIELD_SYNC,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.PAGE_METAFIELD_SYNC],
  },
  RABBIT_MQ_CONNECTION
);
