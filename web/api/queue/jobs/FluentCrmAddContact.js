const shopStatus = require("storeseo-enums/shopStatus");
const { RABBIT_MQ_CONNECTION } = require("..");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const BaseQueue = require("./BaseQueue");
const FluentCrmService = require("../../services/FluentCrmService");
const { sleep } = require("../../utils/helper");
const { Shop, ShopSetting, SubscriptionPlan } = require("../../../sequelize");
const fluentCrmCustomFields = require("storeseo-enums/fluentCrmCustomFields");
const ProductService = require("../../services/ProductService");
const ReportService = require("../../services/ReportService");
const { difference } = require("lodash");
const customLogger = require("../../../customLogger");
const settingKeys = require("storeseo-enums/settingKeys");

class FluentCrmAddContact extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const { domain } = decodeToJSON(message);
    try {
      const shop = await Shop.findOne({
        where: { domain },
        attributes: ["id", "email", "status", "domain", "name", "url"],
        include: [
          {
            model: ShopSetting,
            as: "billing_address_data",
            where: { key: settingKeys.BILLING_ADDRESS },
            attributes: ["value"],
            required: false,
          },
          {
            model: SubscriptionPlan,
            as: "plan",
            attributes: ["id", "name", "slug", "interval"],
          },
        ],
      });

      const {
        email,
        billing_address_data: { value: billing_address },
        plan,
        name,
        url,
      } = shop;

      const plan_slug =
        shop.status === shopStatus.UNINSTALLED ? "app-uninstalled" : plan?.slug ? plan.slug : "installed";
      const tag = await FluentCrmService.getTagBySlugFromDb(plan_slug);

      const productsCount = await ProductService.countProducts(shop.id);
      const { overallScore } = await ReportService.getProductStats(shop.id);

      /**
       * @type {import("../../services/FluentCrmService").FluentCrmContact}
       */
      const contact = {
        email,
        address_line_1: billing_address.address1,
        address_line_2: billing_address.address2,
        city: billing_address.city,
        country: billing_address.country,
        phone: billing_address.phone,
        postal_code: billing_address.zip,
        tags: [tag.tag_id],
        custom_values: {
          [fluentCrmCustomFields.DOMAIN]: domain,
          [fluentCrmCustomFields.SHOP_NAME]: name,
          [fluentCrmCustomFields.URL]: url,
          [fluentCrmCustomFields.TOTAL_PRODUCTS]: productsCount,
          [fluentCrmCustomFields.OVERALL_SCORE]: overallScore,
        },
        status: "subscribed",
      };

      let existingContact = null;
      try {
        existingContact = await FluentCrmService.getContact(email);
      } catch (err) {}

      if (!existingContact) {
        await FluentCrmService.createContact(contact);
      } else {
        contact.attach_tags = contact.tags;
        contact.detach_tags = difference(
          existingContact.tags.map((t) => t.id),
          contact.attach_tags
        );

        delete contact.tags;

        await FluentCrmService.updateContact(existingContact.id, { subscriber: contact });
      }

      await sleep(500);
      this.consumerChannel.ack(message);
      console.log(`[${domain}] [${this.config.queueName}] successful.`);
      customLogger.log(`[${domain}] [${this.config.queueName}] successful.`);
    } catch (err) {
      this.consumerChannel.ack(message);
      // customLogger.error(`[${domain}] [${this.config.queueName}] failed. ${err.message}`);
      console.error(`[${domain}] [${this.config.queueName}] failed.`, err.message);
      // this.handleError(err, message);
    }
  }
}

module.exports = new FluentCrmAddContact(
  {
    queueName: QUEUE_NAMES.FLUENT_CRM_ADD_CONTACT,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.FLUENT_CRM_ADD_CONTACT],
  },
  RABBIT_MQ_CONNECTION
);
