const { RABBIT_MQ_CONNECTION } = require("..");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const { dispatchQueue } = require("../queueDispatcher");
const BaseQueue = require("./BaseQueue");
const ShopService = require("../../services/ShopService");
const AppLogService = require("../../services/AppLogService");
const WebhookService = require("../../services/WebhookService");
const BackupService = require("../../services/BackupService");
const cache = require("../../cache");
const shopify = require("../../../shopify");

class WebhookAppUninstallQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { headers, body } = decodedMessage;
    const shopDomain = headers["X-Shopify-Shop-Domain"] || headers["x-shopify-shop-domain"];

    try {
      // Delete session
      const sessionId = shopify.api.session.getOfflineId(shopDomain);
      await shopify.config.sessionStorage.deleteSession(sessionId);
      console.log("Session deleted.", shopDomain);

      // Log app uninstall
      const shop = await ShopService.getShop(shopDomain);
      await AppLogService.logAppUninstalled(shop);
      console.log("App uninstalled log created.", shopDomain);

      // Mark shop as uninstalled
      await ShopService.markShopAsUninstalled(shopDomain);
      console.log("Shop marked as uninstalled.", shopDomain);

      // Delete webhooks
      await WebhookService.deleteWebhookByConditions({ shop_id: shop.id });
      console.log("Webhooks deleted.", shopDomain);

      // Clear cache
      await cache.deleteAllCache(shopDomain);
      console.log("Cache cleared.", shopDomain);

      // Store backup
      await BackupService.storeBackup(shopDomain);
      console.log("Backup stored.", shopDomain);

      // Dispatch fluent CRM contact tag update
      dispatchQueue({
        queueName: QUEUE_NAMES.FLUENT_CRM_ADD_CONTACT,
        message: { domain: shopDomain },
      });

      console.info(`[${this.config.queueName}](${shopDomain}) processed successfully`);
      this.consumerChannel.ack(message);
    } catch (e) {
      this.consumerChannel.ack(message);

      if (this.isThrottled(e)) {
        // re-dispatch
        // await ShopService.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.info(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(`[${shopDomain}] ${this.config.queueName} failed for shop [${shopDomain}] Message: ${e.message}`);
        await this.handleError(e, message);
      }
    }
  }
}

module.exports = new WebhookAppUninstallQueue(
  {
    queueName: QUEUE_NAMES.WEBHOOK_APP_UNINSTALL_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.WEBHOOK_APP_UNINSTALL_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
