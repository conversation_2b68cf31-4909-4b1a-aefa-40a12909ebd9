const { RABBIT_MQ_CONNECTION } = require("..");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const { dispatchQueue } = require("../queueDispatcher");
const BaseQueue = require("./BaseQueue");
const ShopService = require("../../services/ShopService");
const { isSameAddress } = require("../../utils/helper");
const { serializeWebhookShopData } = require("../../serializers/ShopSerializer");
const WebhookService = require("../../services/WebhookService");

class WebhookShopUpdateQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { headers, body } = decodedMessage;
    const shopDomain = headers["X-Shopify-Shop-Domain"] || headers["x-shopify-shop-domain"];

    try {
      const shop = await ShopService.getShopIncludingAssociations({ where: { domain: shopDomain } });

      await this.#updateJsonldData(shop, body);
      await this.#updateBillingAddress(shop, body);

      const shopUpdateData = serializeWebhookShopData(body);
      await ShopService.updateShop(shop.id, shopUpdateData);

      if (body.plan_display_name?.toLowerCase() != shop?.shopify_plan_name?.toLowerCase()) {
        WebhookService.removeMandatoryWebhooks({
          session: { shop: shopDomain, accessToken: shop.access_token },
        });
      }
      console.info(`[${this.config.queueName}](${shopDomain}) processed successfully`);
      this.consumerChannel.ack(message);
    } catch (e) {
      this.consumerChannel.ack(message);

      if (this.isThrottled(e)) {
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.info(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(`[${shopDomain}] ${this.config.queueName} failed for shop [${shopDomain}] Message: ${e.message}`);
        await this.handleError(e, message);
      }

      return;
    }
  }

  /**
   *
   * @param {*} shop
   * @param {*} webhookData
   */
  async #updateJsonldData(shop, webhookData) {
    if (!shop?.jsonld_data) return;

    const {
      name,
      domain,
      phone: telephone,

      address1,
      address2,
      city,
      zip,
      country,

      latitude,
      longitude,
    } = webhookData;

    const data = {
      ...shop.jsonld_data,

      name,
      telephone,
      url: `https://${domain}`,

      address: {
        address1,
        address2,
        city,
        zip,
        country,
      },

      geo: {
        latitude,
        longitude,
      },
    };

    await ShopService.upsertJsonldSetting(shop.id, data);
  }

  /**
   *
   * @param {*} shop
   * @param {*} webhookData
   */
  async #updateBillingAddress(shop, webhookData) {
    if (!shop?.jsonld_data) return;

    const { address1, address2, city, zip, country } = webhookData;
    const latestAddress = { address1, address2, city, zip, country };

    if (!isSameAddress(latestAddress, shop.billing_address)) {
      const updatedAddress = {
        ...shop.billing_address,
        ...latestAddress,
      };
      await ShopService.upsertBillingAddress(shop.id, updatedAddress);
    }
  }
}

module.exports = new WebhookShopUpdateQueue(
  {
    queueName: QUEUE_NAMES.WEBHOOK_SHOP_UPDATE_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.WEBHOOK_SHOP_UPDATE_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
