const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const { RABBIT_MQ_CONNECTION } = require("../..");
const DocAnalysisService = require("../../../services/docs/DocAnalysisService");
const DocService = require("../../../services/docs/DocService");
const EventService = require("../../../services/EventService");
const ShopService = require("../../../services/ShopService");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../../config");
const { dispatchQueue } = require("../../queueDispatcher");
const BaseQueue = require("../BaseQueue");
const SitemapService = require("../../../services/SitemapService");
const BetterDocsService = require("../../../services/betterdocs/BetterDocsService");
const resourceType = require("storeseo-enums/resourceType");

class DocSyncQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { user, page } = decodedMessage;
    const { shopId, shop: shopDomain } = user || {};
    const limit = 10;

    try {
      const docsData = await BetterDocsService.getDocsByPageAndLimit(shopDomain, page, limit);

      const { docs, pagination } = docsData;

      // doc count from local DB
      const dbDocCount = await DocService.count(shopId);
      // doc count from API

      const apiDocCount = pagination?.total;

      console.info(`[${shopDomain}] Syncing docs ${dbDocCount}/${apiDocCount}.`);

      // return if all docs are synced to the local db
      if (Number(dbDocCount) === Number(apiDocCount)) {
        this.handleSyncComplete(shopDomain, apiDocCount);
        return channel.ack(message);
      }

      if (docs?.length) {
        let syncedCount = 0;
        for (let doc of docs) {
          console.info(`[${shopDomain}] Syncing docs: ${doc.title}, ID: ${doc.id}`);
          const savedDoc = await DocService.upsertRelatedData(shopId, doc);

          if (!savedDoc) {
            console.warn(`[${shopDomain}] Failed to save doc: ${doc.title}, ID: ${doc.id}`);
          }

          if (savedDoc && !savedDoc.focus_keyword) {
            dispatchQueue({
              queueName: QUEUE_NAMES.FOCUS_KEYWORD_GENERATOR,
              message: {
                shopId,
                dbResourceId: savedDoc.id,
                resourceType: resourceType.DOC,
              },
            });
          }

          if (savedDoc) {
            await SitemapService.storeSitemapData(savedDoc, analysisEntityTypes.DOC);
            syncedCount++;
            try {
              const _analysedDoc = await DocAnalysisService.analysis({
                shopId,
                doc: savedDoc,
              });
            } catch (error) {
              console.error("Doc analysis failed", error);
            }
          }
        }

        EventService.handleDocSyncUpdate({
          shopDomain,
          synced: dbDocCount + syncedCount,
          total: apiDocCount,
        });
      }

      if (pagination?.has_next_page) {
        const nextPage = page + 1;

        dispatchQueue({
          queueName: this.config.queueName,
          message: { ...decodedMessage, page: nextPage },
          ttl: 3000,
        });

        console.log(`[${shopDomain}] ${this.config.queueName} redispatch successfull.`);
      } else {
        this.handleSyncComplete(shopDomain, apiDocCount);
      }

      channel.ack(message);
    } catch (error) {
      channel.ack(message);
      if (this.isThrottled(error)) {
        // re-dispatch
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.log(`[${shopDomain}] ${this.config.queueName} redispatched. Wait ${this.throttledDelay / 1000}s.`);
      } else {
        console.error(`[${shopDomain}] ${this.config.queueName} failed.`, error);
        await this.handleError(error, message);
      }
    }
  }

  handleSyncComplete(shop, docCount) {
    console.info(`[${shop}] All doc synced successfully.`);
    EventService.handleDocSyncComplete({
      shop,
      total: docCount,
    });
  }
}

module.exports = new DocSyncQueue(
  {
    queueName: QUEUE_NAMES.DOC_SYNC_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.DOC_SYNC_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
