const { isEmpty } = require("lodash");
const { RABBIT_MQ_CONNECTION } = require("../..");
const ShopService = require("../../../services/ShopService");
const { QUEUE_NAMES } = require("../../config");
const BaseQueue = require("../BaseQueue");
const DocService = require("../../../services/docs/DocService");
const SitemapService = require("../../../services/SitemapService");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const DocAnalysisService = require("../../../services/docs/DocAnalysisService");
const { execTimeTracker } = require("../../../utils/helper");
const resourceType = require("storeseo-enums/resourceType");
const { dispatchQueue } = require("../../queueDispatcher");

class DocCreateQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const execTime = execTimeTracker();
    const decodedMessage = decodeToJSON(message);
    const { headers, body, user } = decodedMessage;

    const shopDomain = headers["X-Shopify-Shop-Domain"] || headers["x-shopify-shop-domain"];

    // const { doc: docData } = data;

    try {
      const shop = await ShopService.getShop(shopDomain);

      if (isEmpty(shop)) {
        channel.ack(message);
        console.log("Shop not found for domain = " + shopDomain);
        return;
      }

      let doc;

      doc = await DocService.upsertRelatedData(shop.id, body);

      if (doc && !doc.focus_keyword) {
        dispatchQueue({
          queueName: QUEUE_NAMES.FOCUS_KEYWORD_GENERATOR,
          message: {
            shopId: shop.id,
            dbResourceId: doc.id,
            resourceType: resourceType.DOC,
          },
        });
      }

      await SitemapService.storeSitemapData(doc, analysisEntityTypes.DOC);
      await DocAnalysisService.analysis({
        shopId: shop.id,
        doc,
      });

      console.info(`[${this.config.queueName}](${shopDomain}) processed successfully : ${doc.id} -- ${execTime.get()}`);
      channel.ack(message);
    } catch (error) {
      channel.ack(message);
      if (this.isThrottled(error)) {
        // re-dispatch
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.info(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(
          `[${shopDomain}] ${this.config.queueName} failed. Doc ID: ${body.id}, Message: ${error.message}`,
          user,
          error
        );
        await this.handleError(error, message);
      }
      return false;
    }
  }
}

module.exports = new DocCreateQueue(
  {
    queueName: QUEUE_NAMES.WEBHOOK_DOC_CREATE,
    // prefetch: PREFETCH_LIMITS[QUEUE_NAMES.WEBHOOK_COLLECTION_CREATE],
  },
  RABBIT_MQ_CONNECTION
);
