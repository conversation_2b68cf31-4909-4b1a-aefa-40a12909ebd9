const { isEmpty } = require("lodash");
const { RABBIT_MQ_CONNECTION } = require("../..");
const DocService = require("../../../services/docs/DocService");
const ShopService = require("../../../services/ShopService");
const { QUEUE_NAMES } = require("../../config");
const BaseQueue = require("../BaseQueue");
const cache = require("../../../cache");

class BetterDocsUninstallQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { headers, body } = decodedMessage;

    const shopDomain = headers["X-Shopify-Shop-Domain"] || headers["x-shopify-shop-domain"];

    try {
      const shop = await ShopService.getShop(shopDomain);

      if (isEmpty(shop)) {
        channel.ack(message);
        console.log("Shop not found for domain = " + shopDomain);
        return;
      }
      const result = await DocService.deleteByShopId(shop.id);
      await DocService.deletePageByPageId();
      await cache.removeBetterDocsInstallationStatus(shopDomain);

      channel.ack(message);
      console.log(`[${this.config.queueName}](${shopDomain}) processed successfully`);
      return true;
    } catch (error) {
      console.log("Error", error);
      channel.ack(message);
      if (this.isThrottled(error)) {
        // re-dispatch
        dispatchQueue({ queueName: this.config.queueName, message: decodeToJSON(message), ttl: this.throttledDelay });
        console.log(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(`[${shopDomain}] ${this.config.queueName} failed.`, error);
        await this.handleError(error, message);
      }
      return false;
    }
  }
}

module.exports = new BetterDocsUninstallQueue(
  {
    queueName: QUEUE_NAMES.WEBHOOK_BETTERDOCS_UNINSTALLED,
    // prefetch: PREFETCH_LIMITS[QUEUE_NAMES.WEBHOOK_COLLECTION_CREATE],
  },
  RABBIT_MQ_CONNECTION
);
