// // @ts-check

/**
 * Note:
 * We are not using this collection product sync queue for now as the collection SEO analysis is not depending on the collection products anymore.
 * We can remove this queue and its related code if we are not going to use it in the future.
 */

const { RABBIT_MQ_CONNECTION } = require("../..");
const ShopService = require("../../../services/ShopService");
const ShopifyService = require("../../../services/ShopifyService");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../../config");
const { dispatchQueue } = require("../../queueDispatcher");
const BaseQueue = require("../BaseQueue");
const EventService = require("../../../services/EventService");
const CollectionProductsService = require("../../../services/collections/CollectionProductsService");
const ProductService = require("../../../services/ProductService");
const CollectionAnalysisService = require("../../../services/collections/CollectionAnalysisService");

class CollectionProductsSyncQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { user, collection } = decodedMessage;
    const { shopId, shop } = user || {};
    const { id: collectionId, collection_id: collectionGqlId, title: collectionTitle } = collection;

    try {
      // If API rate limit is exceed flag is on, requeue the message instead of further processing
      const APIRateLimitExceeded = await ShopService.apiRateLimitExceeded(user.shop, "SHOPIFY_GRAPHQL_API");
      if (APIRateLimitExceeded) {
        channel.ack(message);
        return dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
      }

      // Note: Look into this when we decide to set the limit based on subscription plan
      let itemsToGet = 20;

      // collection products count from shopify
      const shopifyCollectionProductsCount = await ShopifyService.getShopifyCollectionProductsCount(
        user.shop,
        collectionGqlId
      );
      // collection count from local db
      const collectionProductsCount = await CollectionProductsService.count(shopId, collectionId);
      console.info(
        `[${shop}] Syncing collection products ${collectionProductsCount}/${shopifyCollectionProductsCount} for ${collectionTitle}.`
      );

      // return if all collections are synced to the local db
      if (Number(shopifyCollectionProductsCount) === Number(collectionProductsCount)) {
        this.handleSyncComplete(shop, collectionProductsCount, collection);
        return channel.ack(message);
      }

      const lastCursor = await CollectionProductsService.getSyncCursor(shop, collectionId);
      const collectionProducts = await ShopifyService.getCollectionProducts(
        user.shop,
        collectionGqlId,
        itemsToGet,
        lastCursor || null
      );
      const collectionProductsLength = collectionProducts?.products?.nodes?.length || 0;
      const collectionProductsNodes = collectionProducts?.products?.nodes || [];
      console.info("Collection products get =", collectionProductsLength);

      let syncedCount = 0;
      if (collectionProductsLength) {
        for (let collectionProduct of collectionProductsNodes) {
          // Get BD product by shopify id
          const product = await ProductService.getProductByCondition(shopId, { product_id: collectionProduct.id });

          if (!product) continue;
          try {
            const savedCollectionProducts = await CollectionProductsService.upsert(shopId, product.id, collectionId);
            if (savedCollectionProducts) {
              syncedCount++;
            }
          } catch (error) {
            console.error("Collection products upsert failed", error);
          }
        }
      }

      if (collectionProducts?.products?.pageInfo?.hasNextPage) {
        await CollectionProductsService.setSyncCursor(
          shop,
          collectionId,
          collectionProducts.products.pageInfo.endCursor
        );
        dispatchQueue({
          queueName: this.config.queueName,
          message: decodedMessage,
          ttl: 3000,
        });
        console.log(
          `[${shop}] ${this.config.queueName} - Sync ${syncedCount} products for collection - ${collectionTitle} successful.`
        );
      } else {
        this.handleSyncComplete(shop, collectionProductsCount, collection);
      }

      channel.ack(message);
    } catch (error) {
      channel.ack(message);
      if (this.isThrottled(error)) {
        // re-dispatch
        await ShopService.apiRateLimitExceeded(user.shop, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.log(`[${user.shop}] ${this.config.queueName} redispatched. Wait ${this.throttledDelay / 1000}s.`);
      } else {
        console.error(`[${user.shop}] ${this.config.queueName} failed.`, error);
        await this.handleError(error, message);
      }
    }
  }

  async handleSyncComplete(shop, collectionProductsCount, collection) {
    const { title } = collection;
    console.info(`[${shop}] All products for collection - ${title} synced successfully.`);
    try {
      const _analysedProduct = await CollectionAnalysisService.analysis({
        shopId: collection.shop_id,
        collection,
      });
    } catch (error) {
      console.error("Collection analysis failed", error);
    }
    EventService.handleCollectionProductsSyncComplete({
      shop,
      total: collectionProductsCount,
      deleteSyncCursor: true,
      collection,
    });
  }
}

module.exports = new CollectionProductsSyncQueue(
  {
    queueName: QUEUE_NAMES.COLLECTION_PRODUCTS_SYNC_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.COLLECTION_PRODUCTS_SYNC_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
