// // @ts-check
const BaseQueue = require("../BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../../config");
const ShopService = require("../../../services/ShopService");
const { RABBIT_MQ_CONNECTION } = require("../../index");
const { execTimeTracker } = require("../../../utils/helper");
const { dispatchQueue } = require("../../queueDispatcher");
const { isEmpty } = require("lodash");
const CollectionService = require("../../../services/collections/CollectionService");
const ShopifyService = require("../../../services/ShopifyService");
const CollectionAnalysisService = require("../../../services/collections/CollectionAnalysisService");
const SitemapService = require("../../../services/SitemapService");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const resourceType = require("storeseo-enums/resourceType");

class WebhookCollectionCreateQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const execTime = execTimeTracker();
    const decodedMessage = decodeToJSON(message);
    const { headers, body, user } = decodedMessage;
    const shopDomain = headers["X-Shopify-Shop-Domain"] || headers["x-shopify-shop-domain"];
    const { admin_graphql_api_id: collectionGQLId } = body;

    try {
      const shop = await ShopService.getShop(shopDomain);

      if (isEmpty(shop)) {
        channel.ack(message);
        console.log("Shop not found for domain = " + shopDomain);
        return;
      }

      // If API rate limit is exceed flag is on, requeue the message instead of further processing
      const APIRateLimitExceeded = await ShopService.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API");
      if (APIRateLimitExceeded) {
        channel.ack(message);
        return dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
      }

      const shopifyCollection = await ShopifyService.getCollectionById(user.shop, collectionGQLId);

      if (!shopifyCollection) {
        channel.ack(message);
        console.info(`[${this.config.queueName}](${shopDomain})  collecion not found : ${collectionGQLId}`);
        return true;
      }

      let collection;

      collection = await CollectionService.upsertRelatedData(shop.id, shopifyCollection);
      console.info(`[${this.config.queueName}](${shopDomain}) collection : ${collectionGQLId} has created...`);

      if (!collection.focus_keyword) {
        dispatchQueue({
          queueName: QUEUE_NAMES.FOCUS_KEYWORD_GENERATOR,
          message: {
            shopId: shop.id,
            dbResourceId: collection.id,
            resourceType: resourceType.COLLECTION,
          },
        });
      }

      await SitemapService.storeSitemapData(collection, analysisEntityTypes.COLLECTION);
      await CollectionAnalysisService.analysis({
        shopId: shop.id,
        collection,
      });

      dispatchQueue({
        queueName: QUEUE_NAMES.AUTO_COLLECTION_AI_OPTIMIZATION_QUEUE,
        message: {
          shop: shopDomain,
          itemId: collection.id,
        },
      });

      console.info(
        `[${this.config.queueName}](${shopDomain}) processed successfully : ${collectionGQLId} -- ${execTime.get()}`
      );

      // Collection products sync is not required for now
      // Sync collection products
      // dispatchQueue({
      //   queueName: QUEUE_NAMES.COLLECTION_PRODUCTS_SYNC_QUEUE,
      //   message: { user, collection: collection },
      // });

      channel.ack(message);
      // return true;
    } catch (error) {
      channel.ack(message);
      if (this.isThrottled(error)) {
        // re-dispatch
        await ShopService.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.info(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(
          `[${shopDomain}] ${this.config.queueName} failed. Collection ID: ${collectionGQLId}, Message: ${error.message}`,
          user,
          error
        );
        await this.handleError(error, message);
      }
      return false;
    }
  }
}

module.exports = new WebhookCollectionCreateQueue(
  {
    queueName: QUEUE_NAMES.WEBHOOK_COLLECTION_CREATE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.WEBHOOK_COLLECTION_CREATE],
  },
  RABBIT_MQ_CONNECTION
);
