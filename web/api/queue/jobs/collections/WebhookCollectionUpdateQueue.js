// // @ts-check
const { isEmpty } = require("lodash");
const BaseQueue = require("../BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../../config");
const ShopService = require("../../../services/ShopService");
const { RABBIT_MQ_CONNECTION } = require("../../index");
const { execTimeTracker } = require("../../../utils/helper");
const { dispatchQueue } = require("../../queueDispatcher");
const CollectionService = require("../../../services/collections/CollectionService");
const CollectionAnalysisService = require("../../../services/collections/CollectionAnalysisService");
const ShopifyService = require("../../../services/ShopifyService");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const SitemapService = require("../../../services/SitemapService");

class WebhookCollectionUpdateQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const execTime = execTimeTracker();
    const decodedMessage = decodeToJSON(message);

    const { headers, body: webhookPayload, user } = decodedMessage;

    const shopDomain = headers["X-Shopify-Shop-Domain"] || headers["x-shopify-shop-domain"];
    const { admin_graphql_api_id: collectionGQLId } = webhookPayload;

    try {
      // If API rate limit is exceed flag is on, requeue the message instead of further processing
      const APIRateLimitExceeded = await ShopService.apiRateLimitExceeded(user.shop, "SHOPIFY_GRAPHQL_API");
      if (APIRateLimitExceeded) {
        channel.ack(message);
        return dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
      }

      const shop = await ShopService.getShop(shopDomain);

      if (isEmpty(shop)) {
        channel.ack(message);
        console.log("Shop not found for domain = " + shopDomain);
        return;
      }

      let oldCollection;

      try {
        oldCollection = await CollectionService.getByCondition(shop.id, { collection_id: collectionGQLId });
      } catch (error) {
        console.info(
          `Collection doesn't exist in DB! Dispatching collection create queue for (${shopDomain}), collection id ${collectionGQLId}`
        );
        dispatchQueue({
          queueName: QUEUE_NAMES.WEBHOOK_COLLECTION_CREATE,
          message: decodedMessage,
        });

        channel.ack(message);
        return;
      }

      const shopifyCollection = await ShopifyService.getCollectionById(user.shop, collectionGQLId);

      const collection = await CollectionService.upsertRelatedData(shop.id, shopifyCollection);
      console.info(`[${this.config.queueName}](${shopDomain}) updated successfully : ${collectionGQLId}`);

      await SitemapService.storeSitemapData(collection, analysisEntityTypes.COLLECTION);
      await CollectionAnalysisService.analysis({
        shopId: shop.id,
        collection,
        oldCollection,
      });
      console.info(
        `[${this.config.queueName}](${shopDomain}) processed successfully : ${collectionGQLId} -- ${execTime.get()}`
      );

      // Destroy collection products that are not in the collection anymore
      // console.log("Destroying deleted products for collection id = " + collectionGQLId);
      // const _destroyDeletedProducts = await CollectionProductsService.destroyDeletedProducts(shop.id, collection.id);

      // Note: Collection products sync is not required for now
      // Sync collection products
      // dispatchQueue({
      //   queueName: QUEUE_NAMES.COLLECTION_PRODUCTS_SYNC_QUEUE,
      //   message: { user, collection: collection },
      // });

      channel.ack(message);
      // return true;
    } catch (error) {
      channel.ack(message);
      if (this.isThrottled(error)) {
        // re-dispatch
        await ShopService.apiRateLimitExceeded(user.shop, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({
          queueName: this.config.queueName,
          message: decodedMessage,
          ttl: this.throttledDelay,
        });
        console.info(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(
          `[${shopDomain}] ${this.config.queueName} failed. Collection ID: ${collectionGQLId}, Message: ${error.message}`
        );
        await this.handleError(error, message);
      }
      return false;
    }
  }
}

module.exports = new WebhookCollectionUpdateQueue(
  {
    queueName: QUEUE_NAMES.WEBHOOK_COLLECTION_UPDATE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.WEBHOOK_COLLECTION_UPDATE],
  },
  RABBIT_MQ_CONNECTION
);
