// // @ts-check
const BaseQueue = require("../BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../../config");
const { RABBIT_MQ_CONNECTION } = require("../../index");
const { isEmpty } = require("lodash");
const { dispatchQueue } = require("../../queueDispatcher");
const ShopService = require("../../../services/ShopService");
const CollectionService = require("../../../services/collections/CollectionService");

class WebhookCollectionDeleteQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { headers, body } = decodedMessage;
    const shopDomain = headers["X-Shopify-Shop-Domain"] || headers["x-shopify-shop-domain"];
    const { id } = body;
    try {
      const shop = await ShopService.getShop(shopDomain);

      if (isEmpty(shop)) {
        channel.ack(message);
        console.log("Shop not found for domain = " + shopDomain);
        return;
      }

      const collectionGID = `gid://shopify/Collection/${id}`;
      await CollectionService.deleteByGID(shop.id, collectionGID);
      channel.ack(message);
      console.log(`[${this.config.queueName}](${shopDomain}) processed successfully`);
      return true;
    } catch (error) {
      channel.ack(message);
      if (this.isThrottled(error)) {
        // re-dispatch
        dispatchQueue({ queueName: this.config.queueName, message: decodeToJSON(message), ttl: this.throttledDelay });

        console.log(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(`[${shopDomain}] ${this.config.queueName} failed.`, error);
        await this.handleError(error, message);
      }
      return false;
    }
  }
}

module.exports = new WebhookCollectionDeleteQueue(
  {
    queueName: QUEUE_NAMES.WEBHOOK_COLLECTION_DELETE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.WEBHOOK_COLLECTION_DELETE],
  },
  RABBIT_MQ_CONNECTION
);
