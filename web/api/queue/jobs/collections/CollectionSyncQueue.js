// // @ts-check
const { RABBIT_MQ_CONNECTION } = require("../..");
const ShopService = require("../../../services/ShopService");
const ShopifyService = require("../../../services/ShopifyService");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../../config");
const { dispatchQueue } = require("../../queueDispatcher");
const BaseQueue = require("../BaseQueue");
const EventService = require("../../../services/EventService");
const CollectionService = require("../../../services/collections/CollectionService");
const CollectionAnalysisService = require("../../../services/collections/CollectionAnalysisService");
const SitemapService = require("../../../services/SitemapService");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const resourceType = require("storeseo-enums/resourceType");

class CollectionSyncQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { user } = decodedMessage;
    const { shopId, shop } = user || {};

    try {
      // If API rate limit is exceed flag is on, requeue the message instead of further processing
      const APIRateLimitExceeded = await ShopService.apiRateLimitExceeded(user.shop, "SHOPIFY_GRAPHQL_API");
      if (APIRateLimitExceeded) {
        channel.ack(message);
        return dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
      }

      let itemsToGet = 25;

      // collection count from shopify
      const shopifyCollectionCount = await ShopifyService.getShopifyCollectionCount(user.shop);
      // collection count from local db
      const collectionCount = await CollectionService.count(shopId);
      console.info(`[${shop}] Syncing collections ${collectionCount}/${shopifyCollectionCount}.`);

      // return if all collections are synced to the local db
      if (Number(collectionCount) >= Number(shopifyCollectionCount)) {
        this.handleSyncComplete(shop, shopifyCollectionCount);
        return channel.ack(message);
      }

      const lastCursor = await CollectionService.getSyncCursor(shop);
      const collections = await ShopifyService.getCollections(user.shop, itemsToGet, lastCursor || null);
      const collectionsLength = collections?.edges?.length || 0;
      console.info("Collections get =", collectionsLength);

      if (collectionsLength) {
        let syncedCount = 0;

        for (let collection of collections.edges) {
          console.info(`[${shop}] Syncing collection: ${collection.node.title}, ID: ${collection.node.id}`);

          const savedCollection = await CollectionService.upsertRelatedData(shopId, collection.node);

          if (savedCollection) {
            syncedCount++;

            if (!savedCollection.focus_keyword) {
              dispatchQueue({
                queueName: QUEUE_NAMES.FOCUS_KEYWORD_GENERATOR,
                message: {
                  shopId,
                  dbResourceId: savedCollection.id,
                  resourceType: resourceType.COLLECTION,
                },
              });
            }
            await SitemapService.storeSitemapData(savedCollection, analysisEntityTypes.COLLECTION);
            try {
              await CollectionAnalysisService.analysis({
                shopId,
                collection: savedCollection,
              });

              dispatchQueue({
                queueName: QUEUE_NAMES.AUTO_COLLECTION_AI_OPTIMIZATION_QUEUE,
                message: {
                  shop,
                  itemId: savedCollection.id,
                },
              });
            } catch (error) {
              console.error("Collection analysis failed", error);
            }

            // Note: Collection products sync is not required for now
            // Sync collection products
            // dispatchQueue({
            //   queueName: QUEUE_NAMES.COLLECTION_PRODUCTS_SYNC_QUEUE,
            //   message: { user, collection: savedCollection },
            // });
          }
        }

        EventService.handleCollectionSyncUpdate({
          shop,
          synced: collectionCount + syncedCount,
          total: shopifyCollectionCount,
        });
      }

      if (collections?.pageInfo?.hasNextPage) {
        await CollectionService.setSyncCursor(shop, collections.pageInfo.endCursor);
        dispatchQueue({
          queueName: this.config.queueName,
          message: decodedMessage,
          ttl: 3000,
        });
        console.log(`[${shop}] ${this.config.queueName} successful.`);
      } else {
        this.handleSyncComplete(shop, shopifyCollectionCount);
      }

      channel.ack(message);
    } catch (error) {
      channel.ack(message);
      if (this.isThrottled(error)) {
        // re-dispatch
        await ShopService.apiRateLimitExceeded(user.shop, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.log(`[${user.shop}] ${this.config.queueName} redispatched. Wait ${this.throttledDelay / 1000}s.`);
      } else {
        console.error(`[${user.shop}] ${this.config.queueName} failed.`, error);
        await this.handleError(error, message);
      }
    }
  }

  handleSyncComplete(shop, collectionCount) {
    console.info(`[${shop}] All collection synced successfully.`);
    EventService.handleCollectionSyncComplete({
      shop,
      total: collectionCount,
      deleteSyncCursor: true,
    });
  }
}

module.exports = new CollectionSyncQueue(
  {
    queueName: QUEUE_NAMES.COLLECTION_SYNC_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.COLLECTION_SYNC_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
