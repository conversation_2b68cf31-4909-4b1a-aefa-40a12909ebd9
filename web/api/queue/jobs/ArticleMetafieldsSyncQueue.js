const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const BaseQueue = require("./BaseQueue");
const logger = require("storeseo-logger");
const { RABBIT_MQ_CONNECTION } = require("..");
const { dispatchQueue } = require("../queueDispatcher");
const { NAMESPACE } = require("storeseo-enums/metafields");

class ArticleMetafieldsSyncQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { user, shopId, articleId, namespace = NAMESPACE.GLOBAL } = decodedMessage;

    try {
      const ShopService = require("../../services/ShopService");
      // If API rate limit is exceed flag is on, requeue the message instead of further processing
      if (await ShopService.apiRateLimitExceeded(user.shop, "SHOPIFY_HTTP_API")) {
        channel.ack(message);
        return dispatchQueue({
          queueName: this.config.queueName,
          message: decodedMessage,
          ttl: this.throttledDelay,
        });
      }

      const ArticleService = require("../../services/ArticleService");
      const { limitRemaining, article } = await ArticleService.updateMetafieldsFromShopify(
        user,
        shopId,
        articleId,
        namespace
      );

      // re-dispatch if Shopify API rate limit is exceeded & the req failed.
      if (limitRemaining == undefined) {
        channel.ack(message);
        await ShopService.apiRateLimitExceeded(user.shop, "SHOPIFY_HTTP_API", true, 90);
        return dispatchQueue({
          queueName: this.config.queueName,
          message: decodedMessage,
          ttl: this.throttledDelay,
        });
      }

      const shop = await ShopService.getShopById(shopId);

      if (article) {
        const AnalysisService = require("../../services/AnalysisService");
        await AnalysisService.analyseEachArticle({ shopId, article, shopURL: shop?.url });
      }

      // console.log(
      //   `[${user.shop}] ${this.config.queueName} successful for article id ${articleId}, namespace: ${namespace}.`
      // );

      // re-dispatch to sync 'store_seo' namespace metafields
      if (namespace === NAMESPACE.GLOBAL) {
        channel.ack(message);
        return dispatchQueue({
          queueName: this.config.queueName,
          message: { ...decodedMessage, namespace: NAMESPACE.STORE_SEO },
        });
      }
      channel.ack(message);
      console.log(`[${this.config.queueName}](${shop.domain}) processed successfully`);
      return true;
    } catch (e) {
      channel.ack(message);
      if (this.isThrottled(e)) {
        // re-dispatch
        dispatchQueue({
          queueName: this.config.queueName,
          message: decodedMessage,
          ttl: this.throttledDelay,
        });
        console.log(`[${user.shop}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(`[${user.shop}] ${this.config.queueName} failed.`, e);
        await this.handleError(e, message);
      }
      return false;
    }
  }
}

module.exports = new ArticleMetafieldsSyncQueue(
  {
    queueName: QUEUE_NAMES.ARTICLE_METAFIELD_SYNC,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.ARTICLE_METAFIELD_SYNC],
  },
  RABBIT_MQ_CONNECTION
);
