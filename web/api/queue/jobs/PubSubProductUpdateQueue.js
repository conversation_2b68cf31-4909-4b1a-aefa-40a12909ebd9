const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
// const ShopifyService = require("../../services/ShopifyService");
const ProductService = require("../../services/ProductService");
const ProductAnalysisService = require("../../services/ProductAnalysisService");
const ShopService = require("../../services/ShopService");
const SitemapService = require("../../services/SitemapService");
const { isNull } = require("lodash/lang");
const { RABBIT_MQ_CONNECTION } = require("../index");
const { execTimeTracker } = require("../../utils/helper");
const { compareShopifyProductWithDbProduct } = require("../../utils/comparer");
const { dispatchQueue } = require("../queueDispatcher");
const logger = require("storeseo-logger");
// const FailedMessagesQueue = require("./FailedMessagesQueue");
const { serializeWebhookPayloadToShopifyProduct } = require("../../serializers/ProductSerializer");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");

class PubSubProductUpdateQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const execTime = execTimeTracker();
    const decodedMessage = decodeToJSON(message);
    const webhookPayload = decodedMessage.body;
    const { admin_graphql_api_id: productGqlId } = decodedMessage.body;
    const shopDomain = decodedMessage.headers["X-Shopify-Shop-Domain"];

    try {
      const shop = await ShopService.getShop(shopDomain);
      const shopId = shop.id;
      const user = {
        shop: shopDomain,
        accessToken: shop.access_token,
      };
      const oldProduct = await ProductService.getProductByCondition(shopId, { product_id: productGqlId });

      if (!oldProduct || !webhookPayload) {
        const limit = !isNull(shop?.plan_rules?.products) ? shop.plan_rules.products : Number.POSITIVE_INFINITY;

        // Stop product insert if more than limit
        const productCount = await ProductService.countProducts(user.shopId);

        if (productCount >= limit) {
          console.info(`[${this.config.queueName}](${user.shop}) - Limit exceeded. Product cannot be saved.`, {
            productCount,
            productLimit: limit,
          });
        } else {
          console.info(
            `Product doesn't exist in DB! Dispatching product create queue for (${user.shop}), product id ${productGqlId}`
          );
          dispatchQueue({
            queueName: QUEUE_NAMES.WEBHOOK_PRODUCT_CREATE,
            message: { productGqlId, user },
          });
        }

        channel.ack(message);
        return;
      }

      // If API rate limit is exceed flag is on, requeue the message instead of further processing
      // if (await ShopService.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API")) {
      //   channel.ack(message);
      //   return dispatchQueue(this.config.queueName, decodedMessage, this.throttledDelay);
      // }

      // const shopifyProduct = await ShopifyService.getProductFromShopify(user.shop, productGqlId);
      const shopifyProduct = serializeWebhookPayloadToShopifyProduct(webhookPayload);

      let product;
      if (!compareShopifyProductWithDbProduct(shopifyProduct, oldProduct)) {
        product = await ProductService.saveOrUpdateProduct(shopId, shopifyProduct);
        console.info(`[${this.config.queueName}](${shopDomain}) "${product.title}" updated. ID: ${productGqlId}`);

        await SitemapService.storeSitemapData(product, analysisEntityTypes.PRODUCT);
        await ProductAnalysisService.analyseEachProduct({
          shopId,
          product,
          oldProduct,
        });
      } else {
        console.info(`[${this.config.queueName}](${user.shop}) product : ${productGqlId} not changed!`);
      }

      console.info(
        `[${this.config.queueName}](${shopDomain}) processed successfully : ${productGqlId} -- ${execTime.get()}`
      );

      channel.ack(message);
      return true;
    } catch (e) {
      channel.ack(message);
      if (this.isThrottled(e)) {
        // re-dispatch
        // await ShopService.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API", true, 90);
        // dispatchQueue(this.config.queueName, decodedMessage, this.throttledDelay);
        // console.info(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(
          `[${shopDomain}] ${this.config.queueName} failed. Product ID: ${productGqlId}, Message: ${e.message}`
        );
        await this.handleError(e, message);
      }
      return false;
    }
  }
}

module.exports = new PubSubProductUpdateQueue(
  {
    queueName: QUEUE_NAMES.PUBSUB_PRODUCT_UPDATE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.PUBSUB_PRODUCT_UPDATE],
  },
  RABBIT_MQ_CONNECTION
);
