const { RABBIT_MQ_CONNECTION } = require("../..");
const { Op } = require("../../../../sequelize");
const cache = require("../../../cache");
const ProductService = require("../../../services/ProductService");
const ShopifyService = require("../../../services/ShopifyService");
const ShopService = require("../../../services/ShopService");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../../config");
const { dispatchQueue } = require("../../queueDispatcher");
const BaseQueue = require("../BaseQueue");
const productSerializer = require("../../../serializers/ProductSerializer");
const ProductAnalysisService = require("../../../services/ProductAnalysisService");
const EventService = require("../../../services/EventService");
const settingKeys = require("storeseo-enums/settingKeys");
const resourceType = require("storeseo-enums/resourceType");

class MultiLanguageProductSyncQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { shopId, shopDomain, languageCode, session } = decodedMessage;

    try {
      const exited = await this.exitAndRequeueIfRateLimitExceeded(shopDomain, message, decodedMessage);
      if (exited) return;

      const lastSyncedProductId = await cache.product.multiLanugageSyncCursor(shopDomain, languageCode);
      const productsToSync = await this.getNextProductsToSync(shopId, lastSyncedProductId, 7);

      if (productsToSync.length === 0) this.handleSyncComplete(shopId, shopDomain, languageCode);
      else {
        await this.syncLanguageDataFromShopify(session, languageCode, productsToSync);
        await this.handleSyncUpdate({
          shopId,
          shopDomain,
          languageCode,
          session,
          productsToSync,
        });
      }

      console.log(`[${this.config.queueName}] [${shopDomain}] successful.`);
      channel.ack(message);
    } catch (err) {
      channel.ack(message);
      if (this.isThrottled(err)) {
        // re-dispatch
        await ShopService.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.info(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(`[${shopDomain}] ${this.config.queueName} failed. Message: ${err.message}`, err);
        await this.handleError(err, message);
      }
      return false;
    }
  }

  async exitAndRequeueIfRateLimitExceeded(shopDomain, message, decodedMessage) {
    // If API rate limit is exceed flag is on, requeue the message instead of further processing
    if (await ShopService.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API")) {
      channel.ack(message);
      console.log("-----", shopDomain, "Api rate limit exceeded.");
      dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
      return true;
    }

    return false;
  }

  async handleSyncComplete(shopId, shopDomain, languageCode) {
    await cache.product.mulitLanguageSyncOngoing(shopDomain, languageCode, false);
    await cache.product.multiLanugageSyncCursor(shopDomain, languageCode, 0);

    const syncedLanguageName = await this.getSyncedLanguageName(shopId, languageCode);
    await EventService.handleMultiLanguageProductSyncComplete({
      shopId,
      shop: shopDomain,
      languageCode,
      languageName: syncedLanguageName,
    });
    console.log(`[${this.config.queueName}] [${shopDomain}] sync complete.`);
  }

  async getSyncedLanguageName(shopId, languageCode) {
    const shopSetting = await ShopService.getShopSetting(shopId, settingKeys.MULTI_LANGUAGE_SETTING);
    const setting = shopSetting?.value || { enabled: false, shopLocales: [] };
    const syncedLanguage = setting.shopLocales.find((locale) => locale.locale === languageCode);
    return syncedLanguage?.name;
  }

  /**
   *
   * @param {{ shopId: number, shopDomain: string, languageCode: string, session: import("../../../../../types").Session, productsToSync: {id: number, product_id: string, shop_id: number}[]}} param0
   */
  async handleSyncUpdate({ shopId, shopDomain, languageCode, session, productsToSync }) {
    const latestSyncedProductId = productsToSync[productsToSync.length - 1].id;

    await cache.product.multiLanugageSyncCursor(shopDomain, languageCode, latestSyncedProductId);
    dispatchQueue({
      queueName: this.config.queueName,
      message: {
        shopId,
        shopDomain,
        languageCode,
        session,
      },
    });
  }

  /**
   *
   * @param {number} shopId
   * @param {number} lastSyncedProductId
   * @param {number} [limit]
   * @returns {Promise<{id: number, product_id: string, shop_id: number}[]>}
   */
  async getNextProductsToSync(shopId, lastSyncedProductId, limit = 7) {
    return await ProductService.getFullProductsByCondition(
      shopId,
      {
        id: {
          [Op.gt]: lastSyncedProductId,
        },
      },
      {
        limit,
        order: [["id", "asc"]],
      }
    );
  }

  async syncLanguageDataFromShopify(session, languageCode, products) {
    const shopId = products[0].shop_id;
    const productMapByShopifyId = products.reduce(
      (mapData, product) => ({ ...mapData, [product.product_id]: product }),
      {}
    );
    const shopifyProductIds = Object.keys(productMapByShopifyId);

    const languageData = await ShopifyService.localaizations.getTranslationsByResourceIds(session.shop, {
      locale: languageCode,
      resourceIds: shopifyProductIds,
    });

    const productsWithTranslations = languageData.edges.map((node) => node.node);

    for (let mulitLangShopifyProductData of productsWithTranslations) {
      const dbProduct = productMapByShopifyId[mulitLangShopifyProductData.resourceId];

      const multiLangProduct = productSerializer.serializeMulitLanguageShopifyProductData({
        shopId,
        dbProductId: dbProduct.id,
        languageCode,
        defaultFocusKeyword: dbProduct.focus_keyword,
        mulitLangShopifyProductData,
      });
      const savedMultiLanguageProduct = await ProductService.upsertMulitLanguageProduct(multiLangProduct);
      await ProductAnalysisService.analyseMulitLanguageProduct({
        shopId,
        product: savedMultiLanguageProduct,
      });

      if (!mulitLangShopifyProductData.translations.length && !savedMultiLanguageProduct.focus_keyword) {
        dispatchQueue({
          queueName: QUEUE_NAMES.FOCUS_KEYWORD_GENERATOR,
          message: {
            shopId,
            resourceType: resourceType.MULTI_LANGUAGE_PRODUCT,
            dbResourceId: savedMultiLanguageProduct.id,
          },
        });
      }
    }
  }
}

module.exports = new MultiLanguageProductSyncQueue(
  {
    queueName: QUEUE_NAMES.MULTI_LANGUAGE_PRODUCT_SYNC_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.MULTI_LANGUAGE_PRODUCT_SYNC_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
