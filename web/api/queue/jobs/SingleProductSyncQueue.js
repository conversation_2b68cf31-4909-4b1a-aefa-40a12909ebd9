const { isNull } = require("lodash/lang");
const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const logger = require("storeseo-logger");
const ShopifyService = require("../../services/ShopifyService");
const ProductAnalysisService = require("../../services/ProductAnalysisService");
const SitemapService = require("../../services/SitemapService");
const { RABBIT_MQ_CONNECTION } = require("../index");
const FailedMessagesQueue = require("./FailedMessagesQueue");
const { dispatchQueue } = require("../queueDispatcher");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");

class SingleProductSyncQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { user, id, shopifyProductCount } = decodedMessage;

    const { shopId, shop, permission } = user || {};

    try {
      const ProductService = require("../../services/ProductService");
      const limit = !isNull(permission.products) ? permission.products : Number.POSITIVE_INFINITY;

      // Stop product insert if more then
      let productCount = await ProductService.countProducts(shopId);
      const productLimit = limit - productCount;

      if (productLimit <= 0) {
        console.info(`[${shop}] Limit exceeded. Product cannot be saved.`, {
          productLimit,
          freeProductLimit: limit,
        });
        channel.ack(message);
        return false;
      }

      productCount++;
      const shopifyProduct = await ShopifyService.getProductFromShopify(user.shop, id);
      console.info(
        `[${shop}] Syncing Product(${productCount}/${shopifyProductCount}): ${shopifyProduct.title}, ID: ${shopifyProduct.id}`
      );
      const savedProduct = await ProductService.saveOrUpdateProduct(shopId, shopifyProduct);
      await SitemapService.storeSitemapData(savedProduct, analysisEntityTypes.PRODUCT);
      await ProductAnalysisService.analyseEachProduct({ shopId, product: savedProduct });
      console.info(`[${shop}] ${this.config.queueName} successful.`);
      channel.ack(message);
    } catch (e) {
      channel.ack(message);
      if (this.isThrottled(e)) {
        // re-dispatch
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.info(`[${user.shop}] ${this.config.queueName} redispatched. Wait ${this.throttledDelay / 1000}s.`);
      } else {
        console.error(`[${user.shop}] ${this.config.queueName} failed.`, e);
        await this.handleError(e, message);
      }
    }
  }
}

module.exports = new SingleProductSyncQueue(
  {
    queueName: QUEUE_NAMES.PRODUCT_SYNC_SINGLE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.PRODUCT_SYNC_SINGLE],
  },
  RABBIT_MQ_CONNECTION
);
