const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const logger = require("storeseo-logger");
const ShopifyService = require("../../services/ShopifyService");
const ProductService = require("../../services/ProductService");
const ProductAnalysisService = require("../../services/ProductAnalysisService");
const ShopService = require("../../services/ShopService");
const SitemapService = require("../../services/SitemapService");
const { isNull } = require("lodash/lang");
const { RABBIT_MQ_CONNECTION } = require("../index");
const FailedMessagesQueue = require("./FailedMessagesQueue");
const { execTimeTracker } = require("../../utils/helper");
const { compareShopifyProductWithDbProduct } = require("../../utils/comparer");
const { dispatchQueue } = require("../queueDispatcher");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");

class PubSubProductCreateQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const execTime = execTimeTracker();
    const decodedMessage = decodeToJSON(message);
    const webhookPayload = decodedMessage.body;
    const { admin_graphql_api_id: productGqlId } = decodedMessage.body;
    const shopDomain = decodedMessage.headers["X-Shopify-Shop-Domain"];

    try {
      const shop = await ShopService.getShop(shopDomain);
      const shopId = shop.id;
      const user = {
        shop: shopDomain,
        accessToken: shop.access_token,
      };

      const oldProduct = await ProductService.getProductByCondition(shopId, { product_id: productGqlId });

      const limit = !isNull(shop.plan_rules.products) ? shop.plan_rules.products : Number.POSITIVE_INFINITY;
      const productCount = await ProductService.countProducts(shopId);

      // Stop product insert if more than limit
      if (!oldProduct && productCount >= limit) {
        channel.ack(message);
        console.info(`[${this.config.queueName}](${shopDomain}) - Limit exceeded. Product cannot be saved.`, {
          productCount,
          productLimit: limit,
        });
        return true;
      }

      // If API rate limit is exceed flag is on, requeue the message instead of further processing
      if (await ShopService.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API")) {
        channel.ack(message);
        return dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
      }

      const shopifyProduct = await ShopifyService.getProductFromShopify(user.shop, productGqlId);

      let product;
      if (oldProduct && !compareShopifyProductWithDbProduct(shopifyProduct, oldProduct)) {
        product = await ProductService.saveOrUpdateProduct(shopId, shopifyProduct);
        console.info(`[${this.config.queueName}](${shopDomain}) updated successfully : ${productGqlId}`);
      } else if (!oldProduct) {
        product = await ProductService.saveOrUpdateProduct(shopId, shopifyProduct);
        console.info(`[${this.config.queueName}](${shopDomain}) created successfully : ${productGqlId}`);
      }

      if (!product) {
        channel.ack(message);
        console.info(
          `[${this.config.queueName}](${shopDomain}) product not found: ${productGqlId} -- ${execTime.get()}`
        );
        return true;
      }

      await SitemapService.storeSitemapData(product, analysisEntityTypes.PRODUCT);
      await ProductAnalysisService.analyseEachProduct({
        shopId: shopId,
        product,
        oldProduct,
      });

      console.info(
        `[${this.config.queueName}](${shopDomain}) processed successfully : ${productGqlId} -- ${execTime.get()}`
      );

      channel.ack(message);
      // return true;
    } catch (e) {
      channel.ack(message);
      if (this.isThrottled(e)) {
        // re-dispatch
        await ShopService.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.info(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(
          `[${shopDomain}] ${this.config.queueName} failed. Product ID: ${productGqlId}, Message: ${e.message}`
        );
        await this.handleError(e, message);
      }
      return false;
    }
  }
}

module.exports = new PubSubProductCreateQueue(
  {
    queueName: QUEUE_NAMES.PUBSUB_PRODUCT_CREATE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.PUBSUB_PRODUCT_CREATE],
  },
  RABBIT_MQ_CONNECTION
);
