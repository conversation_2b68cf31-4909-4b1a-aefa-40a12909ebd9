const { RABBIT_MQ_CONNECTION } = require("../..");
const logger = require("storeseo-logger");
const TransactionService = require("../../../services/partners/TransactionService");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../../config");
const { dispatchQueue } = require("../../queueDispatcher");
const BaseQueue = require("../BaseQueue");

/**
 * @typedef {Object} Message
 * @property {string} startDate
 * @property {string} endDate
 * @property {string} cursor
 */

class SaveTransactionData extends BaseQueue {
  /**
   * @param {Message} message
   * @param channel
   * @param decodeToJSON
   * @returns {Promise<boolean>}
   */
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { startDate, endDate, cursor = "" } = decodedMessage;

    try {
      const { hasNextPage, lastCursor } = await TransactionService.saveData(startDate, endDate, cursor);

      if (hasNextPage && lastCursor) {
        dispatchQueue({
          queueName: this.config.queueName,
          message: { ...decodedMessage, cursor: lastCursor },
          ttl: 5000,
        });
      }

      channel.ack(message);

      console.info(`✅ - [${this.config.queueName}] processed successfully`, {
        hasNextPage,
        lastCursor,
        startDate,
        endDate,
      });
    } catch (e) {
      channel.ack(message);
      logger.error(e, { transaction: "SaveTransactionData", ...decodedMessage });
    }
  }
}

module.exports = new SaveTransactionData(
  {
    queueName: QUEUE_NAMES.PARTNER_SAVE_TRANSACTIONS,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.PARTNER_SAVE_TRANSACTIONS],
  },
  RABBIT_MQ_CONNECTION
);
