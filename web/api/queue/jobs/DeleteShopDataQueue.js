const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const AuthService = require("../../services/AuthService");
const appLogService = require("../../services/AppLogService");
const ShopService = require("../../services/ShopService");
const reportService = require("../../services/ReportService");
const { RABBIT_MQ_CONNECTION } = require("../index");
const { dispatchQueue } = require("../queueDispatcher");
const logger = require("storeseo-logger");

class DeleteShopDataQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const { shopDomain } = decodeToJSON(message);
    try {
      const shop = await ShopService.getShop(shopDomain);
      const result = await Promise.allSettled([
        reportService.getBlogArticlesReportData(shop.id),
        reportService.getProductsReportData(shop.id),
        reportService.getPagesReportData(shop.id),
      ]);
      const data = result.map((r) => r.value);

      AuthService.removeShopDataPermanently(shopDomain).then(() => {
        logger.debug(`${shopDomain} removed permanently`, { data, shopDomain });
      });
      appLogService.updateLog({ data, data_deleted_at: new Date() }, { shop: shopDomain }).then(() => {
        console.log(`${shopDomain} app uninstalled log updated`);
      });
      channel.ack(message);
      console.log(`[${this.config.queueName}](${shopDomain}) processed successfully`);
      return true;
    } catch (e) {
      channel.ack(message);
      if (this.isThrottled(e)) {
        // re-dispatch
        dispatchQueue({
          queueName: this.config.queueName,
          message: JSON.parse(message?.content.toString()),
          ttl: this.throttledDelay,
        });
        console.log(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(`[${shopDomain}] ${this.config.queueName} failed.`, e);
        await this.handleError(e, message);
      }
      return false;
    }
  }
}

module.exports = new DeleteShopDataQueue(
  {
    queueName: QUEUE_NAMES.DELETE_SHOP_DATA_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.DELETE_SHOP_DATA_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
