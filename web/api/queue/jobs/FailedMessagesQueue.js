const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const { RABBIT_MQ_CONNECTION } = require("..");

class FailedMessagesQueue extends BaseQueue {
  async handle(message, channel) {
    console.info("[x] %s Received %s", this.config.queueName, message.content.toString());
    channel.ack(message);
  }
}

module.exports = new FailedMessagesQueue(
  {
    queueName: QUEUE_NAMES.FAILED_MESSAGES,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.DEFAULT],
  },
  RABBIT_MQ_CONNECTION
);
