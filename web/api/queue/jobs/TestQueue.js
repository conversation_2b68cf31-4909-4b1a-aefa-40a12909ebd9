const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
// const { dispatchQueue, RABBIT_MQ_CONNECTION } = require("..");
const { RABBIT_MQ_CONNECTION } = require("..");
const { dispatchQueue } = require("../queueDispatcher");
const customLogger = require("../../../customLogger");

let counter = 1;

class TestQueue extends BaseQueue {
  /**
   *
   * @param {import("amqplib").Message} message
   * @param {*} channel
   */
  async handle(message, channel) {
    // console.info(message);
    console.info("headers: ", message.properties.headers);
    console.info("[x] %s Received %s", this.config.queueName, message.content.toString());

    this.consumerChannel.ack(message);

    try {
      throw new Error("Test error!");
    } catch (e) {
      console.log(`[${this.config.queueName}] failed..-`);
      await this.handleError(e, message);
    }

    //   setTimeout(() => {
    //     // console.log(`[-] ${message.content.toString()} processing done!`);
    //     channel.ack(message);

    //     // if (this.config.queueName === "test") {
    //     const FooQueue = require("./FooQueue");
    //     dispatchQueue(
    //       FooQueue.config.queueName,
    //       `foo from hello -> ${message.content.toString()}`,
    //       counter % 50 === 0 ? 2500 : null
    //     );
    //     counter++;
    //   }, 500);
  }
}

module.exports = new TestQueue(
  {
    queueName: QUEUE_NAMES.TEST,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.TEST],
  },
  RABBIT_MQ_CONNECTION
);
