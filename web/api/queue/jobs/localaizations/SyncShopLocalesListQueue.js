const { RABBIT_MQ_CONNECTION } = require("../..");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../../config");
const BaseQueue = require("../BaseQueue");
const ShopService = require("../../../services/ShopService");
const { dispatchQueue } = require("../../queueDispatcher");
const ShopifyService = require("../../../services/ShopifyService");
const settingKeys = require("storeseo-enums/settingKeys");
const { MultiLanguageProducts, ProductAnalysis } = require("../../../../sequelize");
const cache = require("../../../cache");
const { pick } = require("lodash");

class SyncShopLocalesListQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const { headers } = decodeToJSON(message);
    const shopDomain = headers["X-Shopify-Shop-Domain"] || headers["x-shopify-shop-domain"];

    try {
      const shop = await ShopService.getShop(shopDomain);

      const shopifyLocales = await ShopifyService.localaizations.getShopLocales(shopDomain);
      
      const defaultLanguage = pick(
        shopifyLocales.find((l) => l.primary),
        ["locale", "name"]
      );
      await cache.shop.defaultLanguage(shopDomain, defaultLanguage);
      await cache.shop.aiContentGeneratorLanguage(shopDomain, defaultLanguage);
      await cache.shop.hasMultipleLanguages(shopDomain, shopifyLocales.length > 1);

      const savedMultiLangSetting = (await ShopService.getShopSetting(shop.id, settingKeys.MULTI_LANGUAGE_SETTING))
        ?.value;
      const currentlySavedLocales = savedMultiLangSetting?.shopLocales || [];

      const { updatedLocales, deletedLocales } = this.analyseLocaleList(currentlySavedLocales, shopifyLocales);

      await this.updateLocaleListInDb(shop.id, updatedLocales, savedMultiLangSetting);
      await this.removeDeletedLocaleData(shop.id, deletedLocales);

      console.log(`[${this.config.queueName}](${shopDomain}) processed successfully`);

      channel.ack(message);
    } catch (err) {
      channel.ack(message);
      if (this.isThrottled(err)) {
        // re-dispatch
        await ShopService.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.info(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(`[${shopDomain}] ${this.config.queueName} failed. Message: ${err.message}`);
        // await this.handleError(err, message);
      }
      return false;
    }
  }

  /**
   *
   * @param {(import("../../../services/shopify/LocalaizationService").ShopLocale & { synced: boolean })[]} currentLocales
   * @param {import("../../../services/shopify/LocalaizationService").ShopLocale[]} shopifyLocales
   *
   * @returns {{ updatedLocales: (import("../../../services/shopify/LocalaizationService").ShopLocale & { synced: boolean })[], deletedLocales: import("../../../services/shopify/LocalaizationService").ShopLocale}
   */
  analyseLocaleList(currentLocales, shopifyLocales) {
    /** @type {{ [x: string]: import("../../../services/shopify/LocalaizationService").ShopLocale & { synced: boolean } }}*/
    const updatedLocalesMap = {};
    const deletedLocales = [];

    for (let locale of shopifyLocales) {
      updatedLocalesMap[locale.locale] = {
        ...locale,
        synced: false,
      };
    }

    for (let locale of currentLocales) {
      if (updatedLocalesMap[locale.locale])
        updatedLocalesMap[locale.locale] = {
          ...updatedLocalesMap[locale.locale],
          ...locale,
        };
      else deletedLocales.push(locale);
    }

    return {
      updatedLocales: Object.values(updatedLocalesMap),
      deletedLocales,
    };
  }

  /**
   * @param {number} shopId
   * @param {import("../../../services/shopify/LocalaizationService").ShopLocale[]} updatedLocales
   * @param {*} prevMultiLangSetting
   */
  async updateLocaleListInDb(shopId, updatedLocales, prevMultiLangSetting = {}) {
    const updatedSetting = {
      enabled: false,
      shopLocales: [],
      ...prevMultiLangSetting,
      shopLocales: updatedLocales,
    };

    const data = {
      key: settingKeys.MULTI_LANGUAGE_SETTING,
      value: JSON.stringify(updatedSetting),
      value_type: "json",
    };

    await ShopService.updateShopSetting(shopId, data);
  }

  /**
   *
   * @param {import("../../../services/shopify/LocalaizationService").ShopLocale[]} deletedLocales
   */
  async removeDeletedLocaleData(shopId, deletedLocales) {
    for (let locale of deletedLocales) {
      await MultiLanguageProducts.destroy({
        where: {
          shop_id: shopId,
          language_code: locale.locale,
        },
      });
      await ProductAnalysis.destroy({
        where: {
          shop_id: shopId,
          language_code: locale.locale,
        },
      });
    }
  }
}

module.exports = new SyncShopLocalesListQueue(
  {
    queueName: QUEUE_NAMES.SYNC_SHOP_LOCALES_LIST,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.SYNC_SHOP_LOCALES_LIST],
  },
  RABBIT_MQ_CONNECTION
);
