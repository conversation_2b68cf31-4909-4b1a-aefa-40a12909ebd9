const { RABBIT_MQ_CONNECTION } = require("..");
const ShopService = require("../../services/ShopService");
const SubscriptionService = require("../../services/SubscriptionService");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const { dispatchQueue } = require("../queueDispatcher");
const BaseQueue = require("./BaseQueue");
const BackupService = require("../../services/BackupService");
const appSubscriptionStatus = require("storeseo-enums/appSubscriptionStatus");
const logger = require("storeseo-logger");
const cache = require("../../cache");
const subscriptionAddonGroup = require("storeseo-enums/subscriptionAddonGroup");

class WebhookAppSubscriptionUpdateQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { headers, body } = decodedMessage;
    const shopDomain = headers["X-Shopify-Shop-Domain"] || headers["x-shopify-shop-domain"];

    try {
      const { access_token, id } = await ShopService.getShop(shopDomain);
      const session = {
        shop: shopDomain,
        accessToken: access_token,
      };

      const { app_subscription } = body;

      if (app_subscription.status === appSubscriptionStatus.CANCELLED) {
        await BackupService.storeBackup(shopDomain, true);
      }

      await SubscriptionService.subscriptionUpdate(session, app_subscription);

      if (app_subscription.status === appSubscriptionStatus.ACTIVE) {
        await cache.webhooks.addShopToPendingWebhookRegistrationList(shopDomain);
      }

      //  resetting the last sent email usage to null
      await cache.addons.lastEmailSentForUsagePercentage(shopDomain, {
        addon: subscriptionAddonGroup.IMAGE_OPTIMIZER,
        usagePerecentage: "",
      });

      console.info(
        `[${this.config.queueName}](${shopDomain}) processed successfully: [plan name: ${app_subscription.name}, plan status: ${app_subscription.status}]`
      );
      this.consumerChannel.ack(message);
    } catch (e) {
      this.consumerChannel.ack(message);
      logger.error(e, { domain: shopDomain, ...body });

      // if (this.isThrottled(e)) {
      //   // re-dispatch
      //   // await ShopService.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API", true, 90);
      //   dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
      //   console.info(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      // } else {
      //   console.error(
      //     `[${shopDomain}] ${this.config.queueName} failed. [plan name: ${body.app_subscription.name}, plan status: ${body.app_subscription.status}] Message: ${e.message}`
      //   );

      //   await this.handleError(e, message);
      // }
    }
  }
}

module.exports = new WebhookAppSubscriptionUpdateQueue(
  {
    queueName: QUEUE_NAMES.WEBHOOK_APP_SUBSCRIPTION_UPDATE_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.WEBHOOK_APP_SUBSCRIPTION_UPDATE_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
