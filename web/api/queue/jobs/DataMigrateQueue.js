const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const logger = require("storeseo-logger");
const ShopService = require("../../services/ShopService");
const { RABBIT_MQ_CONNECTION } = require("../index");
const { dispatchQueue } = require("../queueDispatcher");
const cache = require("../../cache");
const ProductSyncService = require("../../services/products/ProductSyncService");
const BulkOperationService = require("../../services/BulkOperationService");
const bulkOperationStatus = require("storeseo-enums/bulkOperationStatus");
const { Op } = require("../../../sequelize");
const bulkOperationTypes = require("storeseo-enums/bulkOperationTypes");

class DataMigrateQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const ProductService = require("../../services/ProductService");
    const decodedMessage = decodeToJSON(message);
    const { shopDomain } = decodedMessage;

    try {
      const migrateDataFromApp = await cache.product.migrateDataFromApp(shopDomain);

      const productSyncService = new ProductSyncService(shopDomain, migrateDataFromApp);
      const { syncedProducts, dbProductCount, planLimit } = await productSyncService.syncNextProducts();
      console.info(
        `[${shopDomain}] ${this.config.queueName} Migrated products ${
          dbProductCount - syncedProducts.length
        }-${dbProductCount} from app ${migrateDataFromApp}, Limit: ${planLimit}.`
      );

      await this.#handleMigrationProgress({ shopDomain, syncedProducts, dbProductCount, migrateDataFromApp });

      console.info(`[${shopDomain}] ${this.config.queueName} successful.`);
      channel.ack(message);
    } catch (e) {
      channel.ack(message);
      console.error(`[${shopDomain}] ${this.config.queueName} failed.`, e);
      logger.error(e);
      await this.handleError(e, message);
    }
  }

  /**
   *
   * @param {{ shopDomain: string, syncedProducts: *[], dbProductCount: number, migrateDataFromApp: string}} param0
   */
  async #handleMigrationProgress({ shopDomain, syncedProducts, dbProductCount, migrateDataFromApp }) {
    if (syncedProducts.length === 0) {
      console.info(`[${shopDomain}] All products data migrated successfully from ${migrateDataFromApp}.`);
    } else {
      dispatchQueue({
        queueName: this.config.queueName,
        message: {
          shopDomain,
        },
        ttl: 1500,
      });
    }

    const shop = await ShopService.getShop(shopDomain);
    await BulkOperationService.updateBulkOperationByCondition(
      {
        shop_id: shop.id,
        op_status: {
          [Op.in]: [bulkOperationStatus.PENDING, bulkOperationStatus.PROCESSING],
        },
        op_type: bulkOperationTypes.DATA_MIGRATE,
      },
      {
        synced: dbProductCount,
        op_status: syncedProducts.length ? bulkOperationStatus.PROCESSING : bulkOperationStatus.COMPLETED,
      }
    );
  }
}

module.exports = new DataMigrateQueue(
  {
    queueName: QUEUE_NAMES.DATA_MIGRATION_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.DATA_MIGRATION_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
