const { RABBIT_MQ_CONNECTION } = require("../..");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../../config");
const BaseQueue = require("../BaseQueue");
const logger = require("storeseo-logger");
const cache = require("../../../cache");
const { dispatchQueue } = require("../../queueDispatcher");
const ShopifyService = require("../../../services/ShopifyService");
const BlogSerializer = require("../../../serializers/BlogSerializer");
const ArticleSerializer = require("../../../serializers/ArticleSerializer");
const { metafieldKeysFilterArray } = require("storeseo-enums/metafields");
const ArticleService = require("../../../services/ArticleService");
const AnalysisService = require("../../../services/AnalysisService");
const resourceType = require("storeseo-enums/resourceType");
const EventService = require("../../../services/EventService");
const SitemapService = require("../../../services/SitemapService");
const BlogService = require("../../../services/BlogService");

class PageSyncQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { shopId, shopDomain, shopUrl, session, cursor = null } = decodedMessage;

    try {
      if (await cache.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API")) {
        dispatchQueue({
          queueName: this.config.queueName,
          message: decodedMessage,
          ttl: this.throttledDelay,
        });
      }

      console.log(`[${this.config.queueName}] - Syncing articles for shop: ${shopDomain}. Cursor: ${cursor}`);
      const { nextCursor } = await this.#syncArticlesAndBlogs({ shopId, shopDomain, shopUrl, session, cursor });

      if (nextCursor)
        dispatchQueue({
          queueName: this.config.queueName,
          message: {
            ...decodedMessage,
            cursor: nextCursor,
          },
        });
      else {
        await ArticleService.deleteNotSyncedArticles(shopId);
        await BlogService.deleteNotSyncedBlogs(shopId);

        const total = await ArticleService.count(shopId);
        await EventService.handleBlogArticleSyncComplete({
          shop: shopDomain,
          total,
        });
        await cache.blogSyncOngoing(shopDomain, false);
        console.log(
          `[${this.config.queueName}] - Article sync complete for shop ${shopDomain}. Total synced: ${total} articles.`
        );
      }
    } catch (err) {
      if (this.isThrottled(err)) {
        // re-dispatch
        await cache.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.log(`[${shopDomain}] ${this.config.queueName} redispatched. Wait ${this.throttledDelay / 1000}s.`);
        return;
      }

      console.error(`[${this.config.queueName}] Error syncing articles for shop ${shopDomain}`, err);
      await this.handleError(err, message);
      logger.error(err, {
        description: `Failed to sync articles for shop: ${shopDomain} in queue: ${this.config.queueName}`,
        dispatchedMessage: decodedMessage,
        transaction: "Articlesync",
        domain: shopDomain,
      });
    } finally {
      channel.ack(message);
    }
  }

  /**
   *
   * @returns { Promise<{nextCursor: string | null}> }
   */
  async #syncArticlesAndBlogs({ shopId, shopDomain, shopUrl, session, cursor }) {
    const { articles: shopifyArticles, pageInfo } = await ShopifyService.onlineStore.getArticles(session.shop, {
      after: cursor,
      metafieldKeys: metafieldKeysFilterArray,
    });

    for (let shopifyArticle of shopifyArticles) {
      const serializedShopifyBlog = BlogSerializer.serializeShopifyBlogData(shopId, shopifyArticle.blog);
      const savedBlog = await BlogService.saveOrUpdateBlog(serializedShopifyBlog);
      
      const serializedShopifyArticle = ArticleSerializer.serializeShopifyArticleData(shopId, savedBlog.id, shopifyArticle);
      const savedArticle = await ArticleService.saveOrUpdateArticle(serializedShopifyArticle);

    
      await AnalysisService.analyseEachArticle({
        shopId,
        article: savedArticle,
        shopUrl,
      });
      await SitemapService.storeSitemapData(savedArticle, resourceType.ARTICLE);

      if (!savedArticle.focus_keyword) {
        dispatchQueue({
          queueName: QUEUE_NAMES.FOCUS_KEYWORD_GENERATOR,
          message: {
            shopDomain,
            shopId,
            resourceType: resourceType.ARTICLE,
            dbResourceId: savedArticle.id,
          },
        });
      }
    }

    return { nextCursor: pageInfo.hasNextPage ? pageInfo.endCursor : null };
  }
}

module.exports = new PageSyncQueue(
  {
    queueName: QUEUE_NAMES.ARTICLE_SYNC,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.ARTICLE_SYNC],
  },
  RABBIT_MQ_CONNECTION
);
