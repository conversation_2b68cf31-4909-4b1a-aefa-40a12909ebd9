const { isNull } = require("lodash");
const { RABBIT_MQ_CONNECTION } = require("..");
const ProductService = require("../../services/ProductService");
const { graphQLClient } = require("../../utils/shopify.clients");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const BaseQueue = require("./BaseQueue");
const { sleep } = require("../../utils/helper");

class RemoveDeletedProductsQueue extends BaseQueue {
  async handle(message, channel, decodedToJSON) {
    const decodedMessage = decodedToJSON(message);

    const { domain, products } = decodedMessage;

    try {
      const query = `query ($shopifyProductId: ID!){
            product(id: $shopifyProductId){
              id
              status
            }
          }`;

      for (let i = 0; i < products.length; i++) {
        const shopifyProductId = products[i].product_id;
        const productId = products[i].id;

        const {
          data: { product },
        } = await graphQLClient(domain, { query, variables: { shopifyProductId } });

        if (isNull(product) || product?.status !== "ACTIVE") {
          await ProductService.deleteInactiveProducts(shopifyProductId, productId);
          sleep(3000);
        }
      }

      channel.ack(message);
    } catch (err) {
      console.error(`${this.config.queueName} failed. \n Message: ${err.message}`, err);
      await this.handleError(err, message);
      channel.ack(message);
    }
  }
}

module.exports = new RemoveDeletedProductsQueue(
  {
    queueName: QUEUE_NAMES.REMOVE_DELETED_PRODUCTS_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.REMOVE_DELETED_PRODUCTS_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
