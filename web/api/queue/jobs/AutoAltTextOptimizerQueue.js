const { RABBIT_MQ_CONNECTION } = require("../index");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const BaseQueue = require("./BaseQueue");
const ShopService = require("../../services/ShopService");
const AiService = require("../../services/AiService");
const productService = require("../../services/ProductService");
const cache = require("../../cache");
const settingKeys = require("storeseo-enums/settingKeys");
const { AI_OPTIMIZER } = require("storeseo-enums/cacheKeys");
const aiContentTypes = require("storeseo-enums/aiContentTypes");
const altTextOptimizationStatus = require("storeseo-enums/altTextOptimization");
const { generateImageAltTextObjectByMediaId } = require("../../serializers/ProductImageSerializer");
const ResourceDataBackupService = require("../../services/resource/ResourceDataBackupService");
const resourceType = require("storeseo-enums/resourceType");
const resourceOPType = require("storeseo-enums/resourceOPType");
const ProductImageService = require("../../services/ProductImageService");

/**
 * @typedef {object} TAutoAltTextOptimierQueueMessage
 * @property {string} shop
 * @property {number} resourceId
 * @property {string} resourceType
 *
 */

/**
 * @typedef {{meta:boolean,tags:boolean,imageAltText:"allImages" | "featuredImage"}} AiOptimizationSettings
 */

class AutoAltTextOptimizerQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    /** @type {TAutoAltTextOptimierQueueMessage} */
    const decodedMessage = decodeToJSON(message);
    const { shop: shopDomain, resourceId, resourceType } = decodedMessage;

    try {
      const status = await this.handleAutoOptimization({ shopDomain, resourceId, resourceType });
      channel.ack(message);

      console.info(
        `[${this.config.queueName}](${shopDomain}) processed successfully for resource type: ${resourceType}, resource id: ${resourceId}, Status: ${status}`
      );
    } catch (err) {
      channel.ack(message);
      console.error(
        `[${shopDomain}] ${this.config.queueName} failed for resource type: ${resourceType}, resource id: ${resourceId}, item Message: ${err.message}`,
        err
      );
      await this.handleError(err, message);
    }
  }

  /**
   *
   * @param {{ shopDomain: string, resourceId: number, resourceType: string}} param0
   * @returns {Promise<"USAGE_LIMIT_EXCEEDED" | "QUEUED" | "SETTINGS_NOT_FOUND" | "AUTO_OPTIMIZER_TURNED_OFF" | "AI_OPTIMIZER_ADDON_DISABLED">}
   */
  async handleAutoOptimization({ shopDomain, resourceId, resourceType }) {
    const shop = await ShopService.getShop(shopDomain);

    if (!!shop?.plan_rules?.ai_optimizer === false) return "AI_OPTIMIZER_ADDON_DISABLED";
    if (await this.aiOptimizerUsageLimitExceeded(shopDomain)) return "USAGE_LIMIT_EXCEEDED";

    const optimizationSetting = await this.getAiOptimizerSettings(shop.id);

    if (!optimizationSetting) return "SETTINGS_NOT_FOUND";
    if (!optimizationSetting?.status) return "AUTO_OPTIMIZER_TURNED_OFF";

    await this.queueImagesForOptimization({
      resourceId,
      shop,
      optimizationSetting: optimizationSetting?.settings,
    });

    return "QUEUED";
  }

  /**
   *
   * @param {string} shopDomain
   * @returns {Promise<boolean>}
   */
  async aiOptimizerUsageLimitExceeded(shopDomain) {
    const aiOptimizerUsageLimit = await cache.addons.usageLimit(shopDomain, { addon: AI_OPTIMIZER });
    const aiOptimizerUsageCount = await cache.addons.usageCount(shopDomain, { addon: AI_OPTIMIZER });

    return aiOptimizerUsageCount >= aiOptimizerUsageLimit;
  }

  /**
   *
   * @param {number} shopId
   * @returns {Promise<{ status: boolean, settings?: AiOptimizationSettings} | null>}
   */
  async getAiOptimizerSettings(shopId) {
    try {
      const setting = await ShopService.getShopSetting(shopId, settingKeys.AI_CONTENT_SETTINGS);
      return setting?.value?.[resourceType.PRODUCT] ?? null;
    } catch (err) {
      console.error(`[${this.config.queueName}]`, err);
      return null;
    }
  }

  /**
   *
   * @param {{ resourceId: number, shop: any, optimizationSetting: AiOptimizationSettings | undefined}} param0
   */
  async queueImagesForOptimization({ resourceId, shop, optimizationSetting }) {
    const product = await productService.getProductByCondition(shop.id, {
      id: resourceId,
    });

    let imagesWithoutAltText = product.images?.filter((img) => !img.altText) || [];
    if (optimizationSetting?.imageAltText === "featuredImage") {
      imagesWithoutAltText = imagesWithoutAltText?.filter((img) => img.media_id === product.featuredImage?.media_id);
    }

    const currentCreditUsage = await cache.addons.usageCount(shop.domain, { addon: AI_OPTIMIZER });
    const currentLimit = await cache.addons.usageLimit(shop.domain, { addon: AI_OPTIMIZER });

    const perImageCreditUsage = AiService.calculateCreditUsage({}, aiContentTypes.IMAGE);
    let estimatedCreditUsage = perImageCreditUsage * imagesWithoutAltText.length;
    const hasSufficientCredit = currentCreditUsage + estimatedCreditUsage <= currentLimit;

    if (!hasSufficientCredit) {
      return;
    }

    for (let img of imagesWithoutAltText) {
      // take backup of the image
      const imagesObjectByMediaId = generateImageAltTextObjectByMediaId(img);

      await ResourceDataBackupService.upsert({
        shop_id: shop.id,
        resource_id: img.id,
        resource_type: resourceType.PRODUCT_IMAGE,
        resource_op_type: resourceOPType.AI_OPTIMIZATION,
        data: imagesObjectByMediaId,
      });

      await ProductImageService.updateImage(img.id, {
        alt_text_optimization_status: altTextOptimizationStatus.PENDING,
      });
    }

    await cache.addons.incrementUsageCount(shop, {
      addon: AI_OPTIMIZER,
      incrementBy: estimatedCreditUsage,
    });
    await cache.addons.incrementTotalUsageCount(shop, {
      addon: AI_OPTIMIZER,
      incrementBy: estimatedCreditUsage,
    });
    await cache.addons.incrementTotalAppUsageCount(AI_OPTIMIZER, estimatedCreditUsage);

    await cache.altTextOptimizer.addStoreToPendingOptimizationQueue(shop.domain);
  }
}

module.exports = new AutoAltTextOptimizerQueue(
  {
    queueName: QUEUE_NAMES.AUTO_IMAGE_ALT_TEXT_OPTIMIZATION_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.AUTO_IMAGE_ALT_TEXT_OPTIMIZATION_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
