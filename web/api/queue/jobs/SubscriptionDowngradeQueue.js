const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const logger = require("storeseo-logger");
const ProductService = require("../../services/ProductService");
const ShopService = require("../../services/ShopService");
const ProductAnalysisService = require("../../services/ProductAnalysisService");
const { isNull } = require("lodash/lang");
const { RABBIT_MQ_CONNECTION } = require("../index");
const FailedMessagesQueue = require("./FailedMessagesQueue");
const { dispatchQueue } = require("../queueDispatcher");

class SubscriptionDowngradeQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { shopId, plan } = decodedMessage;

    try {
      const shop = await ShopService.getShopById(shopId);
      const { products, tags } = shop.plan_rules;
      const productCount = await ProductService.countProducts(shopId);

      const planProductCount = isNull(products) ? Number.POSITIVE_INFINITY : products;

      if (productCount > planProductCount) {
        await ProductService.deleteProductRelatedData(shopId, planProductCount);
      }
      console.info(`\n---\nsubscripton downgrade...\nshop: ${shop.domain}\ndeleting sync cursor...\n`);
      await ProductService.deleteSyncCursor(shop.domain);
      console.info(`\n---\nsubscripton downgrade...\nshop: ${shop.domain}\ndeleting sync cursor done!\n`);

      // todo: check for need of this method call
      // await ProductAnalysisService.analyseShopAllProducts(shopId);

      dispatchQueue({
        queueName: QUEUE_NAMES.FLUENT_CRM_ADD_CONTACT,
        message: { domain: shop.domain },
      });

      channel.ack(message);
      console.info(`${this.config.queueName} successful.`);
      return true;
    } catch (e) {
      channel.ack(message);
      if (this.isThrottled(e)) {
        // re-dispatch
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.info(`[${shopId}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(`[${shopId}] ${this.config.queueName} failed.`, e);
        await this.handleError(e, message);
      }
      return false;
    }
  }
}

module.exports = new SubscriptionDowngradeQueue(
  {
    queueName: QUEUE_NAMES.SUBSCRIPTION_DOWNGRADE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.SUBSCRIPTION_DOWNGRADE],
  },
  RABBIT_MQ_CONNECTION
);
