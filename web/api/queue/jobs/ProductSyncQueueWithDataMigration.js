const { isNull } = require("lodash/lang");
const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const logger = require("storeseo-logger");
const ShopifyService = require("../../services/ShopifyService");
const SitemapService = require("../../services/SitemapService");
const ProductAnalysisService = require("../../services/ProductAnalysisService");
const { RABBIT_MQ_CONNECTION } = require("../index");
const FailedMessagesQueue = require("./FailedMessagesQueue");
const { NAMESPACE, METAFIELD_KEYS } = require("storeseo-enums/metafields");
const appDataMigrateService = require("../../services/AppDataMigrateService");
const { dispatchQueue } = require("../queueDispatcher");
const EventService = require("../../services/EventService");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const { omit } = require("lodash");

class ProductSyncQueueWithDataMigration extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const ShopService = require("../../services/ShopService");

    const decodedMessage = decodeToJSON(message);
    const { user, migrateDataFromApp } = decodedMessage;
    const { shopId, shop, permission } = user || {};

    try {
      // If API rate limit is exceed flag is on, requeue the message instead of further processing
      if (await ShopService.apiRateLimitExceeded(user.shop, "SHOPIFY_GRAPHQL_API")) {
        channel.ack(message);
        return dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
      }

      const ProductService = require("../../services/ProductService");
      const limit = !isNull(permission.products) ? permission.products : Number.POSITIVE_INFINITY;
      let itemsToGet = 5;

      const productCount = await ProductService.countProducts(shopId);
      const productLimit = limit - productCount;

      console.info(`[${shop}] Syncing products ${productCount}-${productCount + itemsToGet}}, Limit: ${productLimit}.`);

      if (limit === parseInt(productCount)) {
        this.#handleSyncComplete(shop, limit, productCount);

        channel.ack(message);
        return false;
      }

      if (productLimit < itemsToGet) {
        itemsToGet = productLimit;
      }

      if (productLimit <= 0) {
        console.info(`[${shop}] Limit exceeded. Product cannot be saved.`, {
          productLimit,
          freeProductLimit: limit,
        });

        EventService.handleProductSyncComplete({
          shop,
          total: Math.min(productCount, limit),
        });

        // dispatch FluentCRM contact update
        dispatchQueue({
          queueName: QUEUE_NAMES.FLUENT_CRM_ADD_CONTACT,
          message: {
            domain: shop,
          },
        });

        channel.ack(message);
        return false;
      }

      const lastCursor = await ProductService.getSyncCursor(shop);
      console.info("LastCursor =", lastCursor);
      const products = await ShopifyService.getProductsFromShopify(user.shop, itemsToGet, lastCursor);
      const productsLength = products?.edges?.length || 0;
      console.info("Products get =", productsLength);

      if (products?.edges?.length > 0) {
        let syncedCount = 0;

        for (let product of products.edges) {
          console.info(`[${shop}] Syncing Product: ${product.node.title}, ID: ${product.node.id}`);

          let shopifyProduct = this.#serializeShopifyProduct(product.node);
          if (migrateDataFromApp) {
            shopifyProduct = await appDataMigrateService.collectAndMergeOtherAppData(
              migrateDataFromApp,
              shopifyProduct,
              shop,
              user
            );
          }

          const savedProduct = await ProductService.saveOrUpdateProduct(shopId, shopifyProduct);
          if (savedProduct) {
            syncedCount++;
            await ProductService.saveSyncCursor(shop, savedProduct.cursor);
            await SitemapService.storeSitemapData(savedProduct, analysisEntityTypes.PRODUCT);
            const analysedProduct = await ProductAnalysisService.analyseEachProduct({
              shopId,
              product: savedProduct,
              isLiveUpdate: false,
            });

            dispatchQueue({
              queueName: QUEUE_NAMES.AUTO_IMAGE_OPTIMIZER_QUEUE,
              message: {
                shop,
                itemId: savedProduct.id,
                type: analysisEntityTypes.PRODUCT,
              },
            });

            dispatchQueue({
              queueName: QUEUE_NAMES.AUTO_AI_OPTIMIZATION_QUEUE,
              message: {
                shop,
                itemId: product.id,
              },
            });
          }
        }

        EventService.handleProductSyncUpdate({
          shop,
          synced: productCount + syncedCount,
          total: Math.min(limit),
        });

        // dispatch FluentCRM contact update
        dispatchQueue({
          queueName: QUEUE_NAMES.FLUENT_CRM_ADD_CONTACT,
          message: {
            domain: shop,
          },
        });

        // re-dispatch
        if (productLimit > 0) {
          dispatchQueue({
            queueName: this.config.queueName,
            message: { ...decodedMessage },
            ttl: 3000,
          });
        }

        if (permission.google_console) {
          // const { google_indexing_enabled } = await ShopService.getShopById(shopId);
          // await ProductService.indexAllProducts(user, google_indexing_enabled);
        }
      } else this.#handleSyncComplete(shop, limit, productCount);
      console.log(`[${shop}] ${this.config.queueName} successful.`);
      channel.ack(message);
    } catch (e) {
      channel.ack(message);
      if (this.isThrottled(e)) {
        // re-dispatch
        await ShopService.apiRateLimitExceeded(user.shop, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.log(`[${user.shop}] ${this.config.queueName} redispatched. Wait ${this.throttledDelay / 1000}s.`);
      } else {
        console.error(`[${user.shop}] ${this.config.queueName} failed.`, e);
        await this.handleError(e, message);
      }
    }
  }

  #serializeShopifyProduct(shopifyProduct) {
    // console.log("shopify: ", JSON.stringify(shopifyProduct.mediaImages, null, 2));
    const serializedProduct = omit(shopifyProduct, ["meta", "mediaImages"]);
    serializedProduct.descriptionHtml = serializedProduct.description || serializedProduct.descriptionHtml || "";

    if (!serializedProduct.featuredMedia) {
      serializedProduct.featuredMedia = {
        id: shopifyProduct.mediaImages[0]?.id,
      };
    }

    serializedProduct.metafields = {
      edges: shopifyProduct.metafields.edges,
      //   .filter(
      //   (m) =>
      //     (m.node.namespace === NAMESPACE.GLOBAL && m.node.key == METAFIELD_KEYS.TITLE_TAG) ||
      //     m.node.key == METAFIELD_KEYS.DESCRIPTION_TAG
      // ),
      // .map((m) => ({ node: { ...omit(m, ["__parentId"]) } })),
    };

    // serializedProduct.storeSEOMetaFields = {
    //   edges: shopifyProduct.meta
    //     .filter((m) => m.namespace === NAMESPACE.STORE_SEO)
    //     .map((m) => ({ node: { ...omit(m, ["__parentId"]) } })),
    // };

    serializedProduct.mediaImages = {
      edges: shopifyProduct.mediaImages.edges
        .filter(({ node }) => node.image)
        .map(({ node: media }) => ({
          node: {
            id: media.id,
            url: media.image.url,
            altText: media.image.altText,
            fileSize: media.originalSource?.fileSize,
          },
        })),
    };

    return serializedProduct;
  }

  #handleSyncComplete(shop, productLimit, productCount) {
    console.info(`[${shop}] All product synced successfully.`);

    EventService.handleProductSyncComplete({
      shop,
      total: Math.min(productLimit, productCount),
      deleteSyncCursor: true,
    });

    // dispatch FluentCRM contact update
    dispatchQueue({
      queueName: QUEUE_NAMES.FLUENT_CRM_ADD_CONTACT,
      message: {
        domain: shop,
      },
    });
  }
}

module.exports = new ProductSyncQueueWithDataMigration(
  {
    queueName: QUEUE_NAMES.PRODUCT_SYNC_WITH_DATA_MIGRATION,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.PRODUCT_SYNC_WITH_DATA_MIGRATION],
  },
  RABBIT_MQ_CONNECTION
);
