const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const ShopService = require("../../services/ShopService");
const { RABBIT_MQ_CONNECTION } = require("../index");
const OpenAiService = require("../../services/openai/OpenAiService");
const settingKeys = require("storeseo-enums/settingKeys");

class DeleteShopDataQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const { domain } = decodeToJSON(message);
    try {
      const { shopId, url } = await ShopService.getShop(domain, [["id", "shopId"], "url"]);
      const hasIndustry = await ShopService.hasShopSetting(shopId, settingKeys.SHOP_INDUSTRIES);

      // If shop already has industry, skip
      if (hasIndustry) {
        channel.ack(message);
        console.log(`[${this.config.queueName}](${domain}) already processed`);
        return true;
      }

      // Define industry
      const { industries } = await OpenAiService.defineIndustry({ url });
      await ShopService.updateShopSetting(shopId, {
        key: settingKeys.SHOP_INDUSTRIES,
        value: JSON.stringify(industries),
        value_type: "json",
      });
      channel.ack(message);
      console.log(`[${this.config.queueName}](${domain}) processed successfully`);
      return true;
    } catch (e) {
      channel.ack(message);
      console.error(`[${this.config.queueName}] ${domain} failed.`, e.message);
      return false;
    }
  }
}

module.exports = new DeleteShopDataQueue(
  {
    queueName: QUEUE_NAMES.DEFINE_INDUSTRY,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.DEFINE_INDUSTRY],
  },
  RABBIT_MQ_CONNECTION
);
