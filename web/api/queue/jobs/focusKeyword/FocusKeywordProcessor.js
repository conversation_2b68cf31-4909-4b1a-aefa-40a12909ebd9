const RESOURCE_TYPE = require("storeseo-enums/resourceType");
const focusKeywordGenerationStatus = require("storeseo-enums/focusKeywordGenerationStatus");

const ProductService = require("../../../services/ProductService");
const ProductAnalysisService = require("../../../services/ProductAnalysisService");

const CollectionService = require("../../../services/collections/CollectionService");
const CollectionAnalysisService = require("../../../services/collections/CollectionAnalysisService");

const DocService = require("../../../services/docs/DocService");
const DocAnalysisService = require("../../../services/docs/DocAnalysisService");

const PageService = require("../../../services/PageService");
const ArticleService = require("../../../services/ArticleService");
const AnalysisService = require("../../../services/AnalysisService");
const ShopService = require("../../../services/ShopService");

const OpenAiService = require("../../../services/openai/OpenAiService");

class FocusKeywordProcessor {
  async generateFocusKeyword(shopId, dbResourceId) {}
  async handleFocusKeywordGenerationFailure(shopId, dbResourceId) {}

  async recalculateSeoAnalysis(shopId, dbResourceId) {}

  async getFocusKeywordSuggestions({ title, description, resourceType }) {
    return OpenAiService.extractKeywords({ title, description, resourceType });
  }
}

class ProductFocusKeywordProcessor extends FocusKeywordProcessor {
  #resourceType = RESOURCE_TYPE.PRODUCT;

  async generateFocusKeyword(shopId, dbResourceId) {
    const product = await ProductService.getProductDetails(shopId, dbResourceId);

    console.log("Generating focus keyword for product", dbResourceId, "in shop", shopId);
    const { keywords } = await this.getFocusKeywordSuggestions({
      title: product.title,
      description: product.description,
      resourceType: this.#resourceType,
    });

    return await ProductService.updateProduct(shopId, dbResourceId, {
      focus_keyword: keywords[0] || product.title?.split(" ").slice(0, 2).join(" "),
      focus_keyword_suggestions: keywords,
      focus_keyword_generation_status: focusKeywordGenerationStatus.GENERATED,
    });
  }

  async recalculateSeoAnalysis(shopId, product) {
    console.log("Recalculating SEO analysis for product", product.id, "in shop", shopId);
    await ProductAnalysisService.analyseEachProduct({
      shopId,
      product,
    });
  }

  async handleFocusKeywordGenerationFailure(shopId, dbResourceId) {
    await ProductService.updateProduct(shopId, dbResourceId, {
      focus_keyword_generation_status: focusKeywordGenerationStatus.FAILED,
    });
  }
}

class MultiLanguageProductFocusKeywordProcessor extends FocusKeywordProcessor {
  #resourceType = RESOURCE_TYPE.MULTI_LANGUAGE_PRODUCT;

  async generateFocusKeyword(shopId, dbResourceId) {
    const product = await ProductService.getMultiLanguageProductDetails(shopId, dbResourceId);

    console.log("Generating focus keyword for multi-language product", dbResourceId, "in shop", shopId);
    const { keywords } = await this.getFocusKeywordSuggestions({
      title: product.title,
      description: product.description,
      resourceType: this.#resourceType,
    });

    return await ProductService.updateMulitLanguageProduct(shopId, dbResourceId, {
      focus_keyword:
        product.title === product.originalVersion.title
          ? keywords[0] || product.title?.split(" ").slice(0, 2).join(" ")
          : originalVersion.focus_keyword,
      focus_keyword_suggestions: keywords,
      focus_keyword_generation_status: focusKeywordGenerationStatus.GENERATED,
    });
  }

  async recalculateSeoAnalysis(shopId, product) {
    console.log("Recalculating SEO analysis for mulit-language product", product.id, "in shop", shopId);
    await ProductAnalysisService.analyseMulitLanguageProduct({
      shopId,
      product,
    });
  }

  async handleFocusKeywordGenerationFailure(shopId, dbResourceId) {
    await ProductService.updateMulitLanguageProduct(shopId, dbResourceId, {
      focus_keyword_generation_status: focusKeywordGenerationStatus.FAILED,
    });
  }
}

class CollectionFocusKeywordProcessor extends FocusKeywordProcessor {
  #resourceType = RESOURCE_TYPE.COLLECTION;

  async generateFocusKeyword(shopId, dbResourceId) {
    const collection = await CollectionService.getDetails(shopId, dbResourceId);
    console.log("Generating focus keyword for collection", dbResourceId, "in shop", shopId);
    const { keywords } = await this.getFocusKeywordSuggestions({
      title: collection.title,
      description: collection.description,
      resourceType: this.#resourceType,
    });

    return await CollectionService.update(shopId, dbResourceId, {
      focus_keyword: keywords[0] || collection.title?.split(" ").slice(0, 2).join(" "),
      focus_keyword_suggestions: keywords,
      focus_keyword_generation_status: focusKeywordGenerationStatus.GENERATED,
    });
  }

  async recalculateSeoAnalysis(shopId, collection) {
    console.log("Recalculating SEO analysis for collection", collection.id, "in shop", shopId);
    await CollectionAnalysisService.analysis({
      shopId,
      collection,
    });
  }

  async handleFocusKeywordGenerationFailure(shopId, dbResourceId) {
    await CollectionService.update(shopId, dbResourceId, {
      focus_keyword_generation_status: focusKeywordGenerationStatus.FAILED,
    });
  }
}

class PageFocusKeywordProcessor extends FocusKeywordProcessor {
  #resourceType = RESOURCE_TYPE.PAGE;

  async generateFocusKeyword(shopId, dbResourceId) {
    console.log("Generating focus keyword for page", dbResourceId, "in shop", shopId);
    const page = await PageService.getPage(shopId, dbResourceId);
    const { keywords } = await this.getFocusKeywordSuggestions({
      title: page.title,
      description: page.body_html,
      resourceType: this.#resourceType,
    });

    return await PageService.updatePage(dbResourceId, {
      focus_keyword: keywords[0] || page.title?.split(" ").slice(0, 2).join(" "),
      focus_keyword_suggestions: keywords,
      focus_keyword_generation_status: focusKeywordGenerationStatus.GENERATED,
    });
  }

  async recalculateSeoAnalysis(shopId, page) {
    console.log("Recalculating SEO analysis for page", page.id, "in shop", shopId);
    const shop = await ShopService.getShopById(shopId);

    await AnalysisService.analyseEachPage({
      shopId,
      page,
      url: shop.url,
    });
  }

  async handleFocusKeywordGenerationFailure(shopId, dbResourceId) {
    await PageService.updatePage(dbResourceId, {
      focus_keyword_generation_status: focusKeywordGenerationStatus.FAILED,
    });
  }
}

class ArticleFocusKeywordProcessor extends FocusKeywordProcessor {
  #resourceType = RESOURCE_TYPE.ARTICLE;

  async generateFocusKeyword(shopId, dbResourceId) {
    const article = await ArticleService.getArticle(shopId, dbResourceId);

    console.log("Generating focus keyword for article", dbResourceId, "in shop", shopId);
    const { keywords } = await this.getFocusKeywordSuggestions({
      title: article.title,
      description: article.body_html,
      resourceType: this.#resourceType,
    });
    await ArticleService.updateArticle(dbResourceId, {
      focus_keyword: keywords[0] || article.title?.split(" ").slice(0, 2).join(" "),
      focus_keyword_suggestions: keywords,
      focus_keyword_generation_status: focusKeywordGenerationStatus.GENERATED,
    });

    return { ...article, focus_keyword: keywords[0] || article.title };
  }

  async recalculateSeoAnalysis(shopId, article) {
    console.log("Recalculating SEO analysis for article", article.id, "in shop", shopId);
    const shop = await ShopService.getShopById(shopId);
    await AnalysisService.analyseEachArticle({
      shopId,
      article,
      shopURL: shop.url,
    });
  }

  async handleFocusKeywordGenerationFailure(shopId, dbResourceId) {
    await ArticleService.updateArticle(dbResourceId, {
      focus_keyword_generation_status: focusKeywordGenerationStatus.FAILED,
    });
  }
}

class DocFocusKeywordProcessor extends FocusKeywordProcessor {
  #resourceType = RESOURCE_TYPE.DOC;

  async generateFocusKeyword(shopId, dbResourceId) {
    console.log("Generating focus keyword for doc", dbResourceId, "in shop", shopId);
    const doc = await DocService.getDetails(shopId, dbResourceId);

    const { keywords } = await this.getFocusKeywordSuggestions({
      title: doc.title,
      description: doc.description,
      resourceType: this.#resourceType,
    });

    return await DocService.update(shopId, dbResourceId, {
      focus_keyword: keywords[0] || doc.title?.split(" ").slice(0, 2).join(" "),
      focus_keyword_suggestions: keywords,
      focus_keyword_generation_status: focusKeywordGenerationStatus.GENERATED,
    });
  }

  async recalculateSeoAnalysis(shopId, doc) {
    console.log("Recalculating SEO analysis for doc", doc.id, "in shop", shopId);
    await DocAnalysisService.analysis({
      shopId,
      doc,
    });
  }

  async handleFocusKeywordGenerationFailure(shopId, dbResourceId) {
    await DocService.update(dbResourceId, {
      focus_keyword_generation_status: focusKeywordGenerationStatus.FAILED,
    });
  }
}

/**
 * @type {Record<string, FocusKeywordProcessor>}
 */
const FOCUS_KEYWORD_PROCESSOR_MAP = {
  [RESOURCE_TYPE.PRODUCT]: new ProductFocusKeywordProcessor(),
  [RESOURCE_TYPE.MULTI_LANGUAGE_PRODUCT]: new MultiLanguageProductFocusKeywordProcessor(),
  [RESOURCE_TYPE.COLLECTION]: new CollectionFocusKeywordProcessor(),
  [RESOURCE_TYPE.PAGE]: new PageFocusKeywordProcessor(),
  [RESOURCE_TYPE.ARTICLE]: new ArticleFocusKeywordProcessor(),
  [RESOURCE_TYPE.DOC]: new DocFocusKeywordProcessor(),
};

const getFocusKeywordProcessor = (resourceType) => {
  return FOCUS_KEYWORD_PROCESSOR_MAP[resourceType];
};

module.exports = { getFocusKeywordProcessor };
