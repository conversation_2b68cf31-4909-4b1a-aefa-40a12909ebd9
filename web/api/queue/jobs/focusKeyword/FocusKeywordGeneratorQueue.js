const { RABBIT_MQ_CONNECTION } = require("../..");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../../config");
const BaseQueue = require("../BaseQueue");
const logger = require("storeseo-logger");
const { getFocusKeywordProcessor } = require("./FocusKeywordProcessor");

/**
 * @typedef {{shopDomain: string, shopId: number, resourceType: string, dbResourceId: number }} TFocusKeywordGeneratorQueueMessage
 */

class FocusKeywordGeneratorQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    /** @type {TFocusKeywordGeneratorQueueMessage} */
    const decodedMessage = decodeToJSON(message);

    const { shopId, resourceType, dbResourceId } = decodedMessage;
    const focusKeywordProcessor = getFocusKeywordProcessor(resourceType);

    try {
      const updatedResource = await focusKeywordProcessor.generateFocusKeyword(shopId, dbResourceId);
      await focusKeywordProcessor.recalculateSeoAnalysis(shopId, updatedResource);
      console.log(
        `[${this.config.queueName}] - shop id: ${shopId}, resource type: ${resourceType}, DB resource id: ${dbResourceId} processed successfully`
      );
    } catch (error) {
      await focusKeywordProcessor?.handleFocusKeywordGenerationFailure(shopId, dbResourceId);
      console.error(
        `[${this.config.queueName}] error generating focus keyword for shop id: ${shopId}, resource type: ${resourceType}, resource id: ${dbResourceId}`,
        error
      );
      logger.error(error, { shopId, resourceType, dbResourceId, message: "Error generating focus keyword" });
      await this.handleError(error, message);
    } finally {
      this.consumerChannel.ack(message);
    }
  }
}

module.exports = new FocusKeywordGeneratorQueue(
  {
    queueName: QUEUE_NAMES.FOCUS_KEYWORD_GENERATOR,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.FOCUS_KEYWORD_GENERATOR],
  },
  RABBIT_MQ_CONNECTION
);
