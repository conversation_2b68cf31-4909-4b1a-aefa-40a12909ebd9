const { RABBIT_MQ_CONNECTION } = require("../..");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../../config");
const BaseQueue = require("../BaseQueue");
const logger = require("storeseo-logger");
const cache = require("../../../cache");
const { dispatchQueue } = require("../../queueDispatcher");
const ShopifyService = require("../../../services/ShopifyService");
const PageSerializer = require("../../../serializers/PageSerializer");
const { metafieldKeysFilterArray } = require("storeseo-enums/metafields");
const PageService = require("../../../services/PageService");
const AnalysisService = require("../../../services/AnalysisService");
const resourceType = require("storeseo-enums/resourceType");
const EventService = require("../../../services/EventService");
const SitemapService = require("../../../services/SitemapService");

class PageSyncQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { shopId, shopDomain, shopUrl, session, cursor = null } = decodedMessage;

    try {
      if (await cache.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API")) {
        dispatchQueue({
          queueName: this.config.queueName,
          message: decodedMessage,
          ttl: this.throttledDelay,
        });
      }

      console.log(`[${this.config.queueName}] - Syncing pages for shop: ${shopDomain}. Cursor: ${cursor}`);
      const { nextCursor } = await this.#syncPages({ shopId, shopDomain, shopUrl, session, cursor });

      if (nextCursor)
        dispatchQueue({
          queueName: this.config.queueName,
          message: {
            ...decodedMessage,
            cursor: nextCursor,
          },
        });
      else {
        await PageService.deleteNotSyncedPages(shopId);

        const total = await PageService.count(shopId);
        await EventService.handlePageSyncComplete({
          shop: shopDomain,
          total,
        });
        await cache.pageSyncOngoing(shopDomain, false);
        console.log(
          `[${this.config.queueName}] - Page sync complete for shop ${shopDomain}. Total synced: ${total} pages.`
        );
      }
    } catch (err) {
      if (this.isThrottled(err)) {
        // re-dispatch
        await cache.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.log(`[${shopDomain}] ${this.config.queueName} redispatched. Wait ${this.throttledDelay / 1000}s.`);
        return;
      }

      console.error(`[${this.config.queueName}] Error syncing pages for shop ${shopDomain}`, err);
      await this.handleError(err, message);
      logger.error(err, {
        description: `Failed to sync pages for shop: ${shopDomain} in queue: ${this.config.queueName}`,
        dispatchedMessage: decodedMessage,
        transaction: "PageSync",
        domain: shopDomain,
      });
    } finally {
      channel.ack(message);
    }
  }

  /**
   *
   * @returns { Promise<{nextCursor: string | null}> }
   */
  async #syncPages({ shopId, shopDomain, shopUrl, session, cursor }) {
    const { pages: shopifyPages, pageInfo } = await ShopifyService.onlineStore.getPages(session.shop, {
      after: cursor,
      metafieldKeys: metafieldKeysFilterArray,
    });

    for (let shopifyPage of shopifyPages) {
      const serializedShopifyPage = PageSerializer.serializeShopifyPageData(shopId, shopifyPage);
      const savedPage = await PageService.saveOrUpdatePage(shopId, serializedShopifyPage);
      await AnalysisService.analyseEachPage({
        shopId,
        page: savedPage,
        url: shopUrl,
      });
      await SitemapService.storeSitemapData(savedPage, resourceType.PAGE);

      if (!savedPage.focus_keyword) {
        dispatchQueue({
          queueName: QUEUE_NAMES.FOCUS_KEYWORD_GENERATOR,
          message: {
            shopDomain,
            shopId,
            resourceType: resourceType.PAGE,
            dbResourceId: savedPage.id,
          },
        });
      }
    }

    return { nextCursor: pageInfo.hasNextPage ? pageInfo.endCursor : null };
  }
}

module.exports = new PageSyncQueue(
  {
    queueName: QUEUE_NAMES.PAGE_SYNC,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.PAGE_SYNC],
  },
  RABBIT_MQ_CONNECTION
);
