const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const ProductAnalysisService = require("../../services/ProductAnalysisService");
const ProductService = require("../../services/ProductService");
const { RABBIT_MQ_CONNECTION } = require("../index");

class ReAnalyseProduct extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { shopId, productId } = decodedMessage;

    try {
      const product = await ProductService.getProductDetails(shopId, productId);
      const updatedProduct = await ProductAnalysisService.analyseEachProduct({ shopId, product });
      console.info(
        "Re-analysing product",
        updatedProduct.title,
        updatedProduct.product_id,
        "Score =",
        updatedProduct.score
      );
      channel.ack(message);
      console.info(`[${this.config.queueName}](shop id: ${shopId}) (product id: ${productId}) processed successfully`);
    } catch (e) {
      channel.ack(message);
      console.error(`Error in ${this.config.queueName}: `, e);
      await this.handleError(e, message);
    }
  }
}

module.exports = new ReAnalyseProduct(
  {
    queueName: QUEUE_NAMES.RE_ANALYSE_PRODUCT,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.RE_ANALYSE_PRODUCT],
  },
  RABBIT_MQ_CONNECTION
);
