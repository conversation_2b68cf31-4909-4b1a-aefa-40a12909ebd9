const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const ShopService = require("../../services/ShopService");
const ProductService = require("../../services/ProductService");
const ShopifyService = require("../../services/ShopifyService");
const GoogleBucketService = require("../../services/GoogleBucketService");
const { RABBIT_MQ_CONNECTION } = require("..");
const { isEmpty } = require("lodash");
const { updateOrInsertMetaInfoInArray } = require("../../utils/helper");
const { NAMESPACE, METAFIELD_KEYS } = require("storeseo-enums/metafields");

const getMetaInfo = (namespace, key, metaArr) => {
  const r = metaArr.find((m) => m.namespace === namespace && m.key === key);
  return r;
};

const getOriginalFilePath = (currentPath) => {
  const regexp = new RegExp(
    `${process.env.HOST}\/?|https://storage.googleapis.com/${process.env.GOOGLE_BUCKET_NAME}/shops\/?`,
    "i"
  );
  const filePath = decodeURIComponent(decodeURI(currentPath)).replace(regexp, "");
  return `uploads/${filePath}`;
};

class MigrateProductPreviewImages extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    try {
      const product = decodedMessage;

      const shop = await ShopService.getShopById(product.shop_id);

      if (isEmpty(shop)) {
        channel.ack(message);
        return;
      }

      let meta = product.meta || [];

      const namespace = NAMESPACE.STORE_SEO;
      const facebookMetaKey = METAFIELD_KEYS.FACEBOOK_PREVIEW_IMAGE_URL;
      const twitterMetaKey = METAFIELD_KEYS.TWITTER_PREVIEW_IMAGE_URL;

      const facebookPreviewMeta = getMetaInfo(namespace, facebookMetaKey, meta);
      if (facebookPreviewMeta) {
        const filePath = getOriginalFilePath(facebookPreviewMeta.value);
        const { publicUrl } = await GoogleBucketService.uploadMedia({
          folder: `shops/${shop.domain}`,
          originalFilePath: filePath,
          makePublic: true,
        });

        meta = updateOrInsertMetaInfoInArray({
          metaArr: meta,
          namespace,
          key: facebookMetaKey,
          metaInfo: {
            namespace,
            key: facebookMetaKey,
            value: publicUrl,
            type: facebookPreviewMeta.type,
          },
        });
      }

      const twitterPreviewMeta = getMetaInfo(namespace, twitterMetaKey, meta);
      if (twitterPreviewMeta) {
        const filePath = getOriginalFilePath(twitterPreviewMeta.value);
        const { publicUrl } = await GoogleBucketService.uploadMedia({
          folder: `shops/${shop.domain}`,
          originalFilePath: filePath,
          makePublic: true,
        });

        meta = updateOrInsertMetaInfoInArray({
          metaArr: meta,
          namespace,
          key: twitterMetaKey,
          metaInfo: {
            namespace,
            key: twitterMetaKey,
            value: publicUrl,
            type: twitterPreviewMeta.type,
          },
        });
      }

      const user = { shop: shop.domain, accessToken: shop.access_token };

      const {
        productUpdate: { product: shopifyProduct, userErrors },
      } = await ShopifyService.updateProductMetaFields(user.shop, {
        productId: product.product_id,
        metaFieldDefinitions: meta,
      });
      if (userErrors.length > 0) {
        throw new Error(userErrors[0]?.message);
      }

      await ProductService.upsertProductMetadata(shopId, product.id, shopifyProduct);
      channel.ack(message);
      console.info(`[${shop.domain}] ${this.config.queueName} successful for product id ${product.product_id}.`);
    } catch (e) {
      console.error("err: ", e);
      channel.ack(message);
      console.error(`${this.config.queueName} failed.`, e);
      await this.handleError(e, message);
    }
  }
}

module.exports = new MigrateProductPreviewImages(
  {
    queueName: QUEUE_NAMES.MIGRATE_PRODUCT_PREVIEW_IMAGES,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.DEFAULT],
  },
  RABBIT_MQ_CONNECTION
);
