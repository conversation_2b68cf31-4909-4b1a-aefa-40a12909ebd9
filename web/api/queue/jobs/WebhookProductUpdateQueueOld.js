const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const logger = require("storeseo-logger");
const ShopifyService = require("../../services/ShopifyService");
const ProductService = require("../../services/ProductService");
const ProductAnalysisService = require("../../services/ProductAnalysisService");
const ShopService = require("../../services/ShopService");
const SitemapService = require("../../services/SitemapService");
const { isNull } = require("lodash/lang");
const { RABBIT_MQ_CONNECTION } = require("../index");
const FailedMessagesQueue = require("./FailedMessagesQueue");
const { execTimeTracker } = require("../../utils/helper");
const { compareShopifyProductWithDbProduct } = require("../../utils/comparer");
const { dispatchQueue } = require("../queueDispatcher");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const { freeProductLimit } = require("../../config/app");

class WebhookProductUpdateQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const execTime = execTimeTracker();
    const decodedMessage = decodeToJSON(message);
    const { productGqlId, user } = decodedMessage;

    try {
      // If API rate limit is exceed flag is on, requeue the message instead of further processing
      if (await ShopService.apiRateLimitExceeded(user.shop, "SHOPIFY_GRAPHQL_API")) {
        channel.ack(message);
        return dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
      }

      const shopifyProduct = await ShopifyService.getProductFromShopify(user.shop, productGqlId);

      const oldProduct = await ProductService.getProductByCondition(user.shopId, { product_id: productGqlId });

      let product;
      if (oldProduct && !compareShopifyProductWithDbProduct(shopifyProduct, oldProduct)) {
        product = await ProductService.saveOrUpdateProduct(user.shopId, shopifyProduct);
        console.info(`[${this.config.queueName}](${user.shop}) updated successfully : ${productGqlId}`);
      } else if (!oldProduct) {
        const shop = await ShopService.getShopById(user.shopId);
        const limit = !isNull(shop.plan_rules.products) ? shop.plan_rules.products : Number.POSITIVE_INFINITY;

        // Stop product insert if more than limit
        const productCount = await ProductService.countProducts(user.shopId);

        if (productCount >= limit) {
          channel.ack(message);
          console.info(`[${this.config.queueName}](${user.shop}) - Limit exceeded. Product cannot be saved.`, {
            productCount,
            freeProductLimit,
          });
          return false;
        }

        product = await ProductService.saveOrUpdateProduct(user.shopId, shopifyProduct);
        console.info(`[${this.config.queueName}](${user.shop}) created successfully : ${productGqlId}`);
      }

      if (!product) {
        channel.ack(message);
        console.info(
          `[${this.config.queueName}](${user.shop}) product not found: ${productGqlId} -- ${execTime.get()}`
        );
        return true;
      }

      await SitemapService.storeSitemapData(product, analysisEntityTypes.PRODUCT);
      await ProductAnalysisService.analyseEachProduct({
        shopId: user.shopId,
        product,
        oldProduct,
      });

      console.info(
        `[${this.config.queueName}](${user.shop}) processed successfully : ${productGqlId} -- ${execTime.get()}`
      );

      channel.ack(message);
      // return true;
    } catch (e) {
      channel.ack(message);
      if (this.isThrottled(e)) {
        // re-dispatch
        await ShopService.apiRateLimitExceeded(user.shop, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.info(`[${user.shop}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(
          `[${user.shop}] ${this.config.queueName} failed. Product ID: ${productGqlId}, Message: ${e.message}`
        );
        await this.handleError(e, message);
      }
      return false;
    }
  }
}

module.exports = new WebhookProductUpdateQueue(
  {
    queueName: QUEUE_NAMES.WEBHOOK_PRODUCT_UPDATE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.WEBHOOK_PRODUCT_UPDATE],
  },
  RABBIT_MQ_CONNECTION
);
