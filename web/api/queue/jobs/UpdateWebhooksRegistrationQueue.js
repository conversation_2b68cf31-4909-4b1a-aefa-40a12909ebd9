const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const { RABBIT_MQ_CONNECTION } = require("..");
const WebhookService = require("../../services/WebhookService");
const ShopService = require("../../services/ShopService");

class UpdateWebhooksRegistrationQueue extends BaseQueue {
  /**
   *
   * @param {{shopDomain: string}} message
   * @param {*} channel
   * @param {() => {}} decodeToJSON
   */
  async handle(message, channel, decodeToJSON) {
    try {
      const { shopDomain } = decodeToJSON(message);

      const { id, plan_id, domain, access_token: accessToken } = await ShopService.getShop(shopDomain);

      const session = {
        shop: domain,
        accessToken,
      };

      console.log(`[${this.config.queueName}] - updating webhooks for shop (${domain})`);
      await WebhookService.registerAllWebhooks(session, id, plan_id);
      console.log(`[${this.config.queueName}] - webhook registration for shop (${domain}) done!`);

      channel.ack(message);
    } catch (e) {
      console.error(`Error in [${this.config.queueName}]: `, e);
      channel.ack(message);
      await this.handleError(e, message, 0);
    }
  }
}

module.exports = new UpdateWebhooksRegistrationQueue(
  {
    queueName: QUEUE_NAMES.UPDATE_WEBHOOKS_REGISTRATION_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.UPDATE_WEBHOOKS_REGISTRATION_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
