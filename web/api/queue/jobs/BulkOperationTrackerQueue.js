// //@ts-check
const bulkOperationStatus = require("storeseo-enums/bulkOperationStatus");
const { RABBIT_MQ_CONNECTION } = require("..");
const bulkOperationTrackerService = require("../../services/BulkOperationTrackService");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const BaseQueue = require("./BaseQueue");
const logger = require("storeseo-logger");
const resourceTypes = require("storeseo-enums/resourceType");
const resourceOPType = require("storeseo-enums/resourceOPType");
const { handleBulOpComplete } = require("../../services/EventService");
const socketEvents = require("storeseo-enums/socketEvents");
const { extractShopifyIdFromGqlId } = require("../../utils/helper");
const { Op } = require("../../../sequelize");
const { literal } = require("sequelize");
const moment = require("moment");

class BulkOperationTrackerQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { shop_id, shopDomain, resourceType, opType, resourceId, isSuccess, reason } = decodedMessage;
    try {
      const resourceShopifyId = String(extractShopifyIdFromGqlId(resourceId));
      const cacheKeys = [
        "op_id",
        "batch_size",
        "op_status",
        "success_count",
        "failed_count",
        "success_items",
        "failed_items",
        resourceShopifyId,
      ];
      const pendingOpTracker = await bulkOperationTrackerService.getCachedBulkOperationTracker(
        shopDomain,
        resourceType,
        opType,
        cacheKeys
      );

      const batchSize = parseInt(pendingOpTracker.get("batch_size"));
      const opStatus = pendingOpTracker.get("op_status");
      const opTrackerId = pendingOpTracker.get("op_id");
      const successCount = parseInt(pendingOpTracker.get("success_count"));
      const failedCount = parseInt(pendingOpTracker.get("failed_count"));
      const resourceOpTrackerId = pendingOpTracker.get(resourceShopifyId);
      const successItems = pendingOpTracker.get("success_items")
        ? JSON.parse(pendingOpTracker.get("success_items"))
        : [];
      const failedItems = pendingOpTracker.get("failed_items") ? JSON.parse(pendingOpTracker.get("failed_items")) : [];

      if (resourceOpTrackerId && opTrackerId && resourceOpTrackerId === opTrackerId) {
        const success_count = isSuccess ? successCount + 1 : successCount;
        const failed_count = !isSuccess ? failedCount + 1 : failedCount;
        const isComplete = success_count + failed_count === batchSize;
        const success_items = isSuccess ? [...successItems, { id: resourceId }] : successItems;
        const failed_items = !isSuccess ? [...failedItems, { id: resourceId, reason }] : failedItems;

        const updateData = {
          success_count,
          failed_count,
          op_status: isComplete ? bulkOperationStatus.COMPLETED : opStatus,
          success_items: JSON.stringify(success_items),
          failed_items: JSON.stringify(failed_items),
        };

        await bulkOperationTrackerService.setCachedBulkOperationTracker(shopDomain, resourceType, opType, updateData);
        await bulkOperationTrackerService.clearCachedBulkOperationTracker(
          shopDomain,
          resourceType,
          opType,
          resourceShopifyId
        );

        if (isComplete) {
          const where = {
            shop_id,
            id: opTrackerId,
            resource_type: resourceType,
            op_type: opType,
            status: bulkOperationStatus.PENDING,
          };
          const data = {
            batch_size: batchSize,
            success_count,
            failed_count,
            status: bulkOperationStatus.COMPLETED,
            op_output: {
              success_items,
              failed_items,
            },
            end_date: new Date(),
          };
          await this.markTheBatchAsComplete(shopDomain, where, data);
        }
      } else {
        const today = moment().format("YYYY-MM-DD");

        let bulk_op_tracker = await bulkOperationTrackerService.getBulkOperationTrackerDetails(shop_id, {
          resource_type: resourceType,
          op_type: `AUTO_${opType}`,
          [Op.and]: [literal(`created_at::date = '${today}'`)],
        });

        if (!bulk_op_tracker) {
          bulk_op_tracker = await bulkOperationTrackerService.createBulkOperationTracker({
            shop_id,
            resource_type: resourceType,
            op_type: `AUTO_${opType}`,
            batch_size: 1,
            status: bulkOperationStatus.PROCESSING,
            start_date: new Date(),
          });
        }

        const successItems = bulk_op_tracker?.op_output?.success_items || [];
        const failedItems = bulk_op_tracker?.op_output?.failed_items || [];

        const success_count = isSuccess ? bulk_op_tracker.success_count + 1 : bulk_op_tracker.success_count;
        const failed_count = !isSuccess ? bulk_op_tracker.failed_count + 1 : bulk_op_tracker.failed_count;
        const success_items = isSuccess ? [...successItems, { id: resourceId }] : successItems;
        const failed_items = !isSuccess ? [...failedItems, { id: resourceId, reason }] : failedItems;

        const where = {
          shop_id,
          id: bulk_op_tracker.id,
        };
        const data = {
          batch_size: success_count + failed_count,
          success_count,
          failed_count,
          op_output: {
            success_items,
            failed_items,
          },
          end_date: moment().endOf("day").toDate(),
        };

        await bulkOperationTrackerService.updateBulkOperationTrackerByCondition(where, data);
      }

      channel.ack(message);
      console.log(`[${shopDomain}]: ${this.config.queueName} processed successfully.`);
    } catch (error) {
      channel.ack(message);
      console.error(`[${shopDomain}]: ${this.config.queueName} failed.`, error);
      logger.error(error);
      await this.handleError(error, message);
    }
  }

  /**
   * @param {string} shopDomain
   * @param {{id: number, shop_id: number, resource_type: keyof typeof resourceTypes, op_type: keyof typeof resourceOPType, status: string}} where
   * @param {{batch_size: number, success_count: number, failed_count: number, status: string, op_output: {success_items: any[], failed_items: any[]}, end_date: Date}} data
   */
  async markTheBatchAsComplete(shopDomain, where, data) {
    const { shop_id, resource_type, op_type } = where;
    const { batch_size, success_count } = data;
    await bulkOperationTrackerService.updateBulkOperationTrackerByCondition(where, data);
    await bulkOperationTrackerService.clearCachedBulkOperationTracker(shopDomain, resource_type, op_type);

    /**
     * @param {Parameters<typeof handleBulOpComplete>[0]} props
     */
    const onComplete = async (props) => {
      return await handleBulOpComplete({
        shopId: shop_id,
        shop: shopDomain,
        ...props,
      });
    };

    switch (resource_type) {
      case "PRODUCT_IMAGE":
        if (op_type === "AI_OPTIMIZATION") {
          await onComplete({
            type: socketEvents.IMAGE_ALT_OPTIMIZATION_BULK_OP_COMPLETE,
            notification: {
              title: "Image alt text generation completed",
              message: `${success_count}/${batch_size} of your selected images have been optimized with AI generated alt text.`,
            },
          });
        } else if (op_type === "IMAGE_OPTIMIZATION") {
          await onComplete({
            type: socketEvents.IMAGE_OPTIMIZATION_BULK_OP_COMPLETE,
            notification: {
              title: "Image optimization completed",
              message: `${success_count}/${batch_size} of your selected images have been optimized.`,
            },
          });
        }
        break;
      case "PRODUCT":
        if (op_type === "AI_OPTIMIZATION") {
          await onComplete({
            type: socketEvents.PRODUCT_AI_OPTIMIZATION_BULK_OP_COMPLETE,
            notification: {
              title: "Product AI optimization completed",
              message: `${success_count}/${batch_size} of your selected products have been optimized with AI generated metadata.`,
            },
          });
        }
        break;
      case "COLLECTION":
        if (op_type === "AI_OPTIMIZATION") {
          await onComplete({
            type: socketEvents.COLLECTION_AI_OPTIMIZATION_BULK_OP_COMPLETE,
            notification: {
              title: "Collection AI optimization completed",
              message: `${success_count}/${batch_size} of your selected collections have been optimized with AI generated metadata.`,
            },
          });
        }
        break;
      default:
        console.log("No action defined for this resource type");
        break;
    }
  }
}

module.exports = new BulkOperationTrackerQueue(
  {
    queueName: QUEUE_NAMES.BULK_OPERATION_TRACKER,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.BULK_OPERATION_TRACKER],
  },
  RABBIT_MQ_CONNECTION
);
