//@ts-check
const settingKeys = require("storeseo-enums/settingKeys");
const { RABBIT_MQ_CONNECTION } = require("..");
const ShopService = require("../../services/ShopService");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const BaseQueue = require("./BaseQueue");
const cache = require("../../cache");
const productService = require("../../services/ProductService");
const { AI_OPTIMIZER } = require("storeseo-enums/cacheKeys");
const AiOptimizationStatus = require("storeseo-enums/aiOptimization");
const ResourceOptimizationService = require("../../services/resource/ResourceOptimizationService");
const ResourceDataBackupService = require("../../services/resource/ResourceDataBackupService");
const { METAFIELD_KEYS } = require("storeseo-enums/metafields");
const AiService = require("../../services/AiService");
const resourceType = require("storeseo-enums/resourceType");
const { generateImageAltTextObjectByMediaId } = require("../../serializers/ProductImageSerializer");
const altTextOptimizationStatus = require("storeseo-enums/altTextOptimization");
const resourceOPType = require("storeseo-enums/resourceOPType");

/**
 * @typedef {{meta:boolean,tags:boolean,imageAltText:"allImages" | "featuredImage"}} AiOptimizationSettings
 */

class AutoAIOptimizer extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { shop: shopDomain, itemId } = decodedMessage;

    try {
      const status = await this.handleAutoOptimization({ shopDomain, itemId });
      channel.ack(message);

      console.info(
        `[${this.config.queueName}](${shopDomain}) processed successfully for item id: ${itemId}, Status: ${status}`
      );
    } catch (err) {
      channel.ack(message);
      console.error(
        `[${shopDomain}] ${this.config.queueName} failed. for item id: ${itemId}, item Message: ${err.message}`,
        err
      );
      await this.handleError(err, message);
    }
  }

  /**
   *
   * @param {{ shopDomain: string, itemId: number}} param0
   * @returns {Promise<"USAGE_LIMIT_EXCEEDED" | "QUEUED" | "SETTINGS_NOT_FOUND" | "AUTO_OPTIMIZER_TURNED_OFF" | "AI_OPTIMIZER_ADDON_DISABLED">}
   */
  async handleAutoOptimization({ shopDomain, itemId }) {
    const shop = await ShopService.getShop(shopDomain);

    if (!!shop?.plan_rules?.ai_optimizer === false) return "AI_OPTIMIZER_ADDON_DISABLED";
    if (await this.aiOptimizerUsageLimitExceeded(shopDomain)) return "USAGE_LIMIT_EXCEEDED";

    const optimizationSetting = await this.getAiOptimizerSettings(shop.id);

    if (!optimizationSetting) return "SETTINGS_NOT_FOUND";
    if (!optimizationSetting?.status) return "AUTO_OPTIMIZER_TURNED_OFF";

    await this.queueItemForOptimization({
      itemId,
      shop,
      optimizationSetting: optimizationSetting?.settings,
    });

    return "QUEUED";
  }

  /**
   *
   * @param {string} shopDomain
   * @returns {Promise<boolean>}
   */
  async aiOptimizerUsageLimitExceeded(shopDomain) {
    const aiOptimizerUsageLimit = await cache.addons.usageLimit(shopDomain, { addon: AI_OPTIMIZER });
    const aiOptimizerUsageCount = await cache.addons.usageCount(shopDomain, { addon: AI_OPTIMIZER });

    return aiOptimizerUsageCount >= aiOptimizerUsageLimit;
  }

  /**
   *
   * @param {number} shopId
   * @returns {Promise<{ status: boolean, settings?: AiOptimizationSettings} | null>}
   */
  async getAiOptimizerSettings(shopId) {
    try {
      const setting = await ShopService.getShopSetting(shopId, settingKeys.AI_CONTENT_SETTINGS);
      return setting?.value?.[resourceType.PRODUCT] ?? null;
    } catch (err) {
      console.error(`[${this.config.queueName}]`, err);
      return null;
    }
  }

  /**
   *
   * @param {{ itemId: number, shop: any, optimizationSetting: AiOptimizationSettings | undefined}} param0
   */
  async queueItemForOptimization({ itemId, shop, optimizationSetting }) {
    const product = await productService.getProductByCondition(shop.id, {
      id: itemId,
    });

    if (product) {
      const approximateIncrementUsageCountValue = AiService.calculateApproximateCreditUsage(
        optimizationSetting,
        product
      );

      try {
        await productService.updateProduct(shop.id, product.id, {
          ai_optimization_status: AiOptimizationStatus.PENDING,
        });

        // Find the resource optimization data for the product
        const resourceOptimization = await ResourceOptimizationService.getByCondition({
          shop_id: shop.id,
          resource_id: product.id,
          resource_type: resourceType.PRODUCT,
          resource_op_type: resourceOPType.AI_OPTIMIZATION,
        });

        const optimization_stats = {
          optimization_count: resourceOptimization
            ? // @ts-ignore
              resourceOptimization?.optimization_stats?.optimization_count + 1
            : 1,
          restore_count: resourceOptimization?.optimization_stats?.restore_count ?? 0,
          last_queued_date: new Date(),
          last_queue_process_completed_date:
            resourceOptimization?.optimization_stats?.last_queue_process_completed_date ?? null,
        };

        await ResourceOptimizationService.upsert({
          shop_id: shop.id,
          resource_id: product.id,
          resource_type: resourceType.PRODUCT,
          resource_op_type: resourceOPType.AI_OPTIMIZATION,
          optimization_setting: optimizationSetting,
          approximate_credit_usage: Number(approximateIncrementUsageCountValue),
          optimization_stats,
        });

        // Backup image alt text data if the image is not already AI optimized
        const filtertedImages = product.images.filter(
          (img) => img.alt_text_optimization_status !== altTextOptimizationStatus.OPTIMIZED
        );
        for (let img of filtertedImages) {
          const imagesObjectByMediaId = generateImageAltTextObjectByMediaId(img);

          await ResourceDataBackupService.upsert({
            shop_id: shop.id,
            resource_id: img.id,
            resource_type: resourceType.PRODUCT_IMAGE,
            resource_op_type: resourceOPType.AI_OPTIMIZATION,
            data: imagesObjectByMediaId,
          });
        }

        // Backup meta title and description data if the product is not already AI optimized
        if (product.ai_optimization_status !== altTextOptimizationStatus.OPTIMIZED) {
          const findMetaTitle = product.meta.find((item) => item.key === METAFIELD_KEYS.TITLE_TAG);
          const findMetaDescription = product.meta.find((item) => item.key === METAFIELD_KEYS.DESCRIPTION_TAG);

          const backupData = {
            metaTitle: findMetaTitle?.value ?? "",
            metaDescription: findMetaDescription?.value,
            tags: product.tags,
          };

          await ResourceDataBackupService.upsert({
            shop_id: shop.id,
            resource_id: product.id,
            resource_type: resourceType.PRODUCT,
            resource_op_type: resourceOPType.AI_OPTIMIZATION,
            data: backupData,
          });
        }
      } catch (error) {
        console.error(`[${this.config.queueName}] `, error);
      }
      await this.incrementRelatedUsageCount(shop.domain, approximateIncrementUsageCountValue);
    }

    await cache.aiOptimizer.addStoreToPendingOptimizationQueue(shop.domain);
  }

  /**
   *
   * @param {string} shopDomain
   * @param {string} value
   * @returns {Promise<void>}
   */
  async incrementRelatedUsageCount(shopDomain, value = "4.2") {
    await cache.addons.incrementUsageCount(shopDomain, {
      addon: AI_OPTIMIZER,
      incrementBy: value, // Approximate value. This will sync after AI content generation
    });
    await cache.addons.incrementTotalUsageCount(shopDomain, {
      addon: AI_OPTIMIZER,
      incrementBy: value, // Approximate value. This will sync after AI content generation
    });
    await cache.addons.incrementTotalAppUsageCount(AI_OPTIMIZER, value); // Approximate value. This will sync after AI content generation
  }
}

module.exports = new AutoAIOptimizer(
  {
    queueName: QUEUE_NAMES.AUTO_AI_OPTIMIZATION_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.AUTO_AI_OPTIMIZATION_QUEUE],
  },
  // @ts-ignore
  RABBIT_MQ_CONNECTION
);
