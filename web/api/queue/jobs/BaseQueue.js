const {
  QUEUE_NAMES,
  EXCHANGE_TYPES,
  EXCHANGE_NAMES,
  PREFETCH_LIMITS,
  MAX_RETRY_FOR_FAILED_MESSAGES,
} = require("../config");
const { isEmpty } = require("lodash/lang");
const { dispatchQueue } = require("../queueDispatcher");
const logger = require("storeseo-logger");

const defaultConfig = {
  queueName: QUEUE_NAMES.DEFAULT,
  prefetch: PREFETCH_LIMITS[QUEUE_NAMES.DEFAULT],
  queueOptions: {
    durable: true,
    autoDelete: false,
    exclusive: false,
  },
};

class BaseQueue {
  throttledDelay = 60000;

  /**
   * Connection to the queue server
   * @type { null | import("amqplib").Connection}
   */
  connection = null;

  /**
   * Channel dedicated to dispatch messages to this queue
   * @type { null | import("amqplib").Channel}
   */
  dispatcherChannel = null;

  /**
   * Channel dedicated to consume messages dispatched to this queue
   * @type { null | import("amqplib").Channel}
   */
  consumerChannel = null;
  runConsumer = false;

  /**
   * Messages that were not dispatched because the connection was not ready
   * @type {{ message: any, ttl: any }[]}
   */
  messagesToDispatch = [];

  /**
   *
   * @param {any} options
   * @param {Promise<import("amqplib").Connection>} connection
   */
  constructor(options, connection) {
    this.config = {
      ...defaultConfig,
      ...options,
    };

    this.attachConnectionToQueue(connection);
  }

  /**
   * Attach the connection to this queue & create necessary channels
   * @param {Promise<import("amqplib").Connection>} connection
   */
  async attachConnectionToQueue(connection) {
    this.connection = await connection;

    // // prepare dispatcher channel
    // this.dispatcherChannel = await this.connection.createChannel();
    // await this.bindChannelToQueue(this.dispatcherChannel);

    // prepare consumer channel
    if (this.runConsumer) {
      this.consumerChannel = await this.connection.createChannel();
      await this.bindChannelToQueue(this.consumerChannel);
      this.startConsumingMessages();
    }

    this.messagesToDispatch.map((m) => {
      this.dispatch(m.message, m.ttl);
    });

    // if (this.runConsumer) this.startConsumingMessages();
  }

  /**
   * Bind a specific channel to this queue
   * @param {import("amqplib").Channel} channel
   * @returns {Promise<import("amqplib").Channel>} same channel binded to the queue
   */
  async bindChannelToQueue(channel) {
    await channel.assertQueue(this.config.queueName);

    await channel.assertExchange(EXCHANGE_NAMES.DEFAULT, EXCHANGE_TYPES.DIRECT);
    // await channel.assertExchange(EXCHANGE_NAMES.EX_DELAYED, EXCHANGE_TYPES.DELAYED, {
    //   arguments: { "x-delayed-type": "direct" },
    // });

    await channel.bindQueue(this.config.queueName, EXCHANGE_NAMES.DEFAULT, this.config.queueName);
    // await channel.bindQueue(this.config.queueName, EXCHANGE_NAMES.EX_DELAYED, this.config.queueName);

    return channel;
  }

  async dispatch(message, ttl) {
    // if (!this.connection && !this.dispatcherChannel) this.messagesToDispatch.push({ message, ttl });

    if (!this.connection) this.messagesToDispatch.push({ message, ttl });

    const channel = await this.connection.createChannel();
    await this.bindChannelToQueue(channel);

    const isDelayedQueue = typeof ttl !== "undefined";

    if (isDelayedQueue) {
      channel.publish(EXCHANGE_NAMES.EX_DELAYED, this.config.queueName, Buffer.from(JSON.stringify(message)), {
        persistent: true,
        headers: { "x-delay": ttl },
      });
      // this.dispatcherChannel.publish(
      //   EXCHANGE_NAMES.EX_DELAYED,
      //   this.config.queueName,
      //   Buffer.from(JSON.stringify(message)),
      //   {
      //     persistent: true,
      //     headers: { "x-delay": ttl },
      //   }
      // );
    } else {
      channel.publish(EXCHANGE_NAMES.DEFAULT, this.config.queueName, Buffer.from(JSON.stringify(message)));
      // this.dispatcherChannel.publish(
      //   EXCHANGE_NAMES.DEFAULT,
      //   this.config.queueName,
      //   Buffer.from(JSON.stringify(message))
      // );
    }

    await channel.close();
  }

  startConsumingMessages = async () => {
    // console.info("");
    // console.info("starting consumer for ", this.config.queueName);

    // if (!this.connection && !this.consumerChannel) {
    if (!this.connection) {
      // console.info("Connection not ready! waiting for connection...\n");
      this.runConsumer = true;
      return;
    }

    // const channel = await this.connection.createChannel();
    // await this.bindChannelToQueue(channel);

    // channel.prefetch(this.config.prefetch);
    // channel.consume(
    //   this.config.queueName,
    //   (msg) => {
    //     const decodeToJSON = (msg) => JSON.parse(msg?.content.toString());
    //     this.handle(msg, channel, decodeToJSON);
    //   },
    //   {
    //     noAck: false,
    //   }
    // );

    this.consumerChannel.prefetch(this.config.prefetch);
    this.consumerChannel.consume(
      this.config.queueName,
      (msg) => {
        const decodeToJSON = (msg) => JSON.parse(msg?.content.toString());
        this.handle(msg, this.consumerChannel, decodeToJSON);
      },
      {
        noAck: false,
      }
    );

    // console.info(this.config.queueName, "consumer started!\n");
  };

  async handle(message, channel) {}

  async run(channel) {
    await channel.assertQueue(this.config.queueName);

    await channel.assertExchange(EXCHANGE_NAMES.DEFAULT, EXCHANGE_TYPES.DIRECT);
    await channel.assertExchange(EXCHANGE_NAMES.EX_DELAYED, EXCHANGE_TYPES.DELAYED, {
      arguments: { "x-delayed-type": "direct" },
    });

    await channel.bindQueue(this.config.queueName, EXCHANGE_NAMES.DEFAULT, this.config.queueName);
    await channel.bindQueue(this.config.queueName, EXCHANGE_NAMES.EX_DELAYED, this.config.queueName);

    channel.prefetch(this.config.prefetch);
    channel.consume(
      this.config.queueName,
      (msg) => {
        const decodeToJSON = (msg) => JSON.parse(msg?.content.toString());
        this.handle(msg, channel, decodeToJSON);
      },
      {
        noAck: false,
      }
    );
  }

  /**
   *
   * @param {import("amqplib").Message} msg
   * @returns {any}
   */
  decodeToJSON = (msg) => JSON.parse(msg?.content.toString());

  isThrottled(e) {
    return !isEmpty(e.response?.errors) && e.response.errors[0]?.message === "Throttled";
  }

  /**
   *
   * @param {any} e error throwed
   * @param {import("amqplib").Message} message message tried to consume from queue
   */
  async handleError(e, message, maxRetry = MAX_RETRY_FOR_FAILED_MESSAGES) {
    if (e?.message === "Session not found") {
      console.log(`[${this.config.queueName}] message marked as permanently failed!`, e?.message);
      return;
    }

    const decodedMessage = this.decodeToJSON(message);

    let queueName = this.config.queueName;
    let msg = decodedMessage;
    let ttl = 5000;
    let options = {};

    logger.error(e, { queueName, msg, ttl });

    if (message.properties.headers.retryCount >= maxRetry) {
      queueName = QUEUE_NAMES.FAILED_MESSAGES;
      ttl = null;
      msg = {
        queue: this.config.queueName,
        reason: e.message,
        message: decodedMessage,
      };

      console.log(
        `[${this.config.queueName}] message marked as permanently failed! Message: ${JSON.stringify(decodedMessage)}`
      );
    } else {
      options.headers = { retryCount: (message.properties.headers.retryCount || 0) + 1 };
      console.log(`[${this.config.queueName}] message will retry! Message: ${JSON.stringify(decodedMessage)}`);
    }

    dispatchQueue({
      queueName,
      message: msg,
      ttl,
      options,
    });
  }
}

module.exports = BaseQueue;
