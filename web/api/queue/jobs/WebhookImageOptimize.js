const { QUEUE_NAMES, PR<PERSON>ETCH_LIMITS, MAX_RETRY_FOR_FAILED_MESSAGES } = require("../config");
const BaseQueue = require("./BaseQueue");
const logger = require("storeseo-logger");
const { RABBIT_MQ_CONNECTION } = require("..");
const { dispatchQueue } = require("../queueDispatcher");
const { METAFIELD_KEYS } = require("storeseo-enums/metafields");
const ShopifyService = require("../../services/ShopifyService");
const imageOptimization = require("storeseo-enums/imageOptimization");
const ProductImageService = require("../../services/ProductImageService");
const cache = require("../../cache");
const { imageSizeHasDecreased, isValidImageUrl } = require("../../utils/imageOptimizer");
const {
  ImageNotFoundInDatabaseError,
  ShopifyMediaUpdateFailedError,
  ImageNotFoundInShopifyError,
} = require("../../../errors");
const { sizeToBytes } = require("../../utils/helper");
const resourceType = require("storeseo-enums/resourceType");
const resourceOPType = require("storeseo-enums/resourceOPType");
const ShopService = require("../../services/ShopService");

class WebhookImageOptimize extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { shop_domain, image_info, status, image_id } = decodedMessage;

    try {
      // If API rate limit is exceed flag is on, requeue the message instead of further processing
      if (await ShopService.apiRateLimitExceeded(shop_domain, "SHOPIFY_GRAPHQL_API")) {
        channel.ack(message);
        return dispatchQueue({
          queueName: this.config.queueName,
          message: decodedMessage,
          ttl: this.throttledDelay,
        });
      }

      const imageId = image_id || image_info?.image_id;
      let imageInDatabase = await this.getImageFromDatabase(imageId);

      if (status === "error") {
        await this.handleOptimizationFailedState(shop_domain, imageInDatabase);
      } else if (!imageSizeHasDecreased(image_info.percentage_optimized)) {
        await this.handleAlreadyOptimizedState(shop_domain, imageInDatabase);
      } else {
        await this.handleOptimizationSuccessfulState(shop_domain, imageInDatabase, image_info);
      }

      console.info(`[${shop_domain}] ${this.config.queueName}) processed successfully`);
      channel.ack(message);
      return true;
    } catch (e) {
      channel.ack(message);
      console.error(`[${shop_domain}] ${this.config.queueName} failed.`, e);

      if (this.isThrottled(e)) {
        await ShopService.apiRateLimitExceeded(shop_domain, "SHOPIFY_GRAPHQL_API", true, 90);
        // re-dispatch
        dispatchQueue({
          queueName: this.config.queueName,
          message: decodedMessage,
          ttl: this.throttledDelay,
        });
        console.info(`[${shop_domain}] ${this.config.queueName} redispatched.`);
        return;
      } else if (
        e instanceof ImageNotFoundInDatabaseError ||
        e instanceof ShopifyMediaUpdateFailedError ||
        e instanceof ImageNotFoundInShopifyError
      ) {
        await cache.imageOptimizer.incrementProcessingFailedByDate(1);
        return;
      } else if (message.properties.headers.retryCount >= MAX_RETRY_FOR_FAILED_MESSAGES) {
        await cache.imageOptimizer.incrementProcessingFailedByDate(1);
      }

      await this.handleError(e, message);
      return false;
    }
  }

  async getImageFromDatabase(id) {
    try {
      return await ProductImageService.getImage(id);
    } catch (err) {
      throw new ImageNotFoundInDatabaseError(id, { cause: err });
    }
  }

  /**
   *
   * @param {string} shop
   * @param {*} imageInDatabase
   */
  async handleOptimizationFailedState(shop, imageInDatabase) {
    const session = await ShopService.getSession({ domain: shop });

    dispatchQueue({
      queueName: QUEUE_NAMES.BULK_OPERATION_TRACKER,
      message: {
        shop_id: session.shopId,
        shopDomain: shop,
        resourceType: resourceType.PRODUCT_IMAGE,
        opType: resourceOPType.IMAGE_OPTIMIZATION,
        resourceId: imageInDatabase.media_id,
        isSuccess: false,
        reason: "Image optimization failed",
      },
    });

    if (!(await isValidImageUrl(imageInDatabase.src))) {
      await cache.incrementImageOptimizerInvalidImageCount(shop);
      await ProductImageService.deleteImage(imageInDatabase.id);
      return;
    }

    await ProductImageService.updateImage(imageInDatabase.id, {
      optimization_status: imageOptimization.NOT_OPTIMIZED,
    });

    await cache.updateImageOptimizerUsageCount(shop, false);
    await cache.imageOptimizer.incrementImagesFailedByDate(1);
  }

  async handleAlreadyOptimizedState(shop, imageInDatabase) {
    const session = await ShopService.getSession({ domain: shop });

    dispatchQueue({
      queueName: QUEUE_NAMES.BULK_OPERATION_TRACKER,
      message: {
        shop_id: session.shopId,
        shopDomain: shop,
        resourceType: resourceType.PRODUCT_IMAGE,
        opType: resourceOPType.IMAGE_OPTIMIZATION,
        resourceId: imageInDatabase.media_id,
        isSuccess: true,
      },
    });

    await ProductImageService.updateImage(imageInDatabase.id, {
      optimization_status: imageOptimization.ALREADY_OPTIMIZED,
      optimization_meta: {},
      optimized_at: new Date(),
    });
    await cache.imageOptimizer.incrementImagesProcessedByDate(1);
  }

  async handleOptimizationSuccessfulState(shop, imageInDatabase, optimizationMeta) {
    await ProductImageService.updateImage(imageInDatabase.id, {
      optimization_status: imageOptimization.SAVING,
      optimization_meta: optimizationMeta,
      optimized_at: new Date(),
      file_size: sizeToBytes(optimizationMeta["processed_size"]),
    });

    const session = await ShopService.getSession({ domain: shop });

    dispatchQueue({
      queueName: QUEUE_NAMES.BULK_OPERATION_TRACKER,
      message: {
        shop_id: session.shopId,
        shopDomain: shop,
        resourceType: resourceType.PRODUCT_IMAGE,
        opType: resourceOPType.IMAGE_OPTIMIZATION,
        resourceId: imageInDatabase.media_id,
        isSuccess: true,
      },
    });

    if (!imageInDatabase.media_id) {
      imageInDatabase.media_id = await this.fetchMediaIdFromShopify(session, imageInDatabase);
    }

    const updatedShopifyImage = await this.updateOptimizedImageInShopify(
      session,
      imageInDatabase.media_id,
      optimizationMeta.processed_image_url
    );

    if (updatedShopifyImage) {
      await this.updateOptimizedImageInDatabase(imageInDatabase, updatedShopifyImage, optimizationMeta);
    }

    await cache.imageOptimizer.incrementImagesProcessedByDate(1);
  }

  async fetchMediaIdFromShopify(session, imageInDatabase) {
    const imageFilename = imageInDatabase.fileName.replace(/\?.*/gim, "");

    const {
      files: { edges },
    } = await ShopifyService.getImageFilesFromShopify(session.shop, {
      limit: 1,
      filename: imageFilename,
      productId: imageInDatabase.product.product_id.split("/").reverse()[0],
    });

    if (edges.length === 0) {
      throw new ImageNotFoundInShopifyError(imageFilename);
    }

    const { node: shopifyImageFile } = edges[0];

    return shopifyImageFile.id;
  }

  async updateOptimizedImageInShopify(session, mediaId, optimizedImageUrl) {
    const files = [
      {
        id: mediaId,
        originalSource: optimizedImageUrl,
      },
    ];

    const {
      fileUpdate: { files: updatedImageFiles, userErrors },
    } = await ShopifyService.updateImageFiles(session.shop, files);

    if (userErrors.length > 0) {
      throw new ShopifyMediaUpdateFailedError(session.shop, mediaId, "IMAGE", { cause: userErrors });
    } else if (updatedImageFiles[0].fileErrors?.length) {
      throw new ShopifyMediaUpdateFailedError(session.shop, mediaId, "IMAGE", {
        cause: updatedImageFiles[0].fileErrors,
      });
    }

    return updatedImageFiles[0];
  }

  async updateOptimizedImageInDatabase(imageInDatabase, imageInShopify, optimizationMeta) {
    const updateData = {
      media_id: imageInDatabase.media_id,
      optimization_status: imageOptimization.OPTIMIZED,
      optimization_meta: optimizationMeta,
      src: imageInShopify.preview.image.url.split("?")[0],
    };

    await ProductImageService.updateImage(imageInDatabase.id, updateData);
  }
}

module.exports = new WebhookImageOptimize(
  {
    queueName: QUEUE_NAMES.WEBHOOK_IMAGE_OPTIMIZE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.WEBHOOK_IMAGE_OPTIMIZE],
  },
  RABBIT_MQ_CONNECTION
);
