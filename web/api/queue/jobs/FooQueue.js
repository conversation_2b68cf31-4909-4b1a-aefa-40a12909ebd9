const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const { RABBIT_MQ_CONNECTION } = require("..");
const { dispatchQueue } = require("../queueDispatcher");
const customLogger = require("../../../customLogger");

let counter = 0;

class FooQueue extends BaseQueue {
  async handle(message, channel) {
    customLogger.log("[x] %s Received %s", this.config.queueName, message.content.toString());
    setTimeout(() => {
      // console.info(`[-] ${message.content.toString()} processing done!`);
      channel.ack(message);

      if (message.content.toString().includes("from hello")) {
        counter++;
        dispatchQueue({
          queueName: this.config.queueName,
          message: `redispatched *** from FOO *** ${message.content.toString().replace("foo from hello -> ", "")}`,
          ttl: counter % 100 === 0 ? 1000 : null,
        });
      }
    }, 500);
  }
}

module.exports = new FooQueue(
  {
    queueName: QUEUE_NAMES.FOO,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.FOO],
  },
  RABBIT_MQ_CONNECTION
);
