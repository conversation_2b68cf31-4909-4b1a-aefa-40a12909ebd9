const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const logger = require("storeseo-logger");
const ShopifyService = require("../../services/ShopifyService");
const ProductService = require("../../services/ProductService");
const ProductAnalysisService = require("../../services/ProductAnalysisService");
const ShopService = require("../../services/ShopService");
const SitemapService = require("../../services/SitemapService");
const { isNull } = require("lodash/lang");
const { RABBIT_MQ_CONNECTION } = require("../index");
const FailedMessagesQueue = require("./FailedMessagesQueue");
const { execTimeTracker } = require("../../utils/helper");
const { compareShopifyProductWithDbProduct } = require("../../utils/comparer");
const { dispatchQueue } = require("../queueDispatcher");
const { isEmpty } = require("lodash");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const { flattenMediaImages } = require("../../serializers/ProductImageSerializer");
const resourceType = require("storeseo-enums/resourceType");

class WebhookProductCreateQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const execTime = execTimeTracker();
    const decodedMessage = decodeToJSON(message);
    const { headers, body } = decodedMessage;
    const shopDomain = headers["X-Shopify-Shop-Domain"] || headers["x-shopify-shop-domain"];
    const { admin_graphql_api_id: productGqlId, status, id } = body;

    try {
      // disptach product delete if product status is not 'active'
      if (status !== "active") {
        console.info(
          `[${this.config.queueName}](${shopDomain}) product : ${productGqlId} status is not active! Dispatching product delete queue for it.`
        );
        dispatchQueue({
          queueName: QUEUE_NAMES.WEBHOOK_PRODUCT_DELETE,
          message: decodedMessage,
        });
        channel.ack(message);
        return;
      }

      const shop = await ShopService.getShop(shopDomain);

      if (isEmpty(shop)) {
        channel.ack(message);
        console.log("Shop not found for domain = " + shopDomain);
        return;
      }

      const session = { shop: shop.domain, accessToken: shop.access_token };

      const oldProduct = await ProductService.getProductByCondition(shop.id, { product_id: productGqlId });

      const limit = !isNull(shop.plan_rules.products) ? shop.plan_rules.products : Number.POSITIVE_INFINITY;
      const productCount = await ProductService.countProducts(shop.id);

      // Stop product insert if more than limit
      if (!oldProduct && productCount >= limit) {
        channel.ack(message);
        console.info(`[${this.config.queueName}](${shopDomain}) - Limit exceeded. Product cannot be saved.`, {
          productCount,
          productLimit: limit,
        });
        return true;
      }

      // If API rate limit is exceed flag is on, requeue the message instead of further processing
      if (await ShopService.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API")) {
        channel.ack(message);
        console.log("-----", shopDomain, "Api rate limit exceeded.");
        return dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        // return;
      }

      const shopifyProduct = await ShopifyService.getProductFromShopify(session.shop, productGqlId);
      shopifyProduct.mediaImages = flattenMediaImages(shopifyProduct);

      if (!shopifyProduct) {
        channel.ack(message);
        console.info(`[${this.config.queueName}](${shopDomain})  product not found : ${productGqlId}`);
        return true;
      }

      let product;
      if (oldProduct && !compareShopifyProductWithDbProduct(shopifyProduct, oldProduct)) {
        product = await ProductService.saveOrUpdateProduct(shop.id, shopifyProduct);
        console.info(`[${this.config.queueName}](${shopDomain}) updated successfully : ${productGqlId}`);
      } else if (!oldProduct) {
        product = await ProductService.saveOrUpdateProduct(shop.id, shopifyProduct);
        console.info(`[${this.config.queueName}](${shopDomain}) created successfully : ${productGqlId}`);
      }

      if (!product) {
        channel.ack(message);
        console.info(
          `[${this.config.queueName}](${shopDomain}) product not found: ${productGqlId} -- ${execTime.get()}`
        );
        return true;
      }

      await SitemapService.storeSitemapData(product, analysisEntityTypes.PRODUCT);
      await ProductAnalysisService.analyseEachProduct({
        shopId: shop.id,
        product,
        oldProduct,
      });

      if (!product.focus_keyword) {
        dispatchQueue({
          queueName: QUEUE_NAMES.FOCUS_KEYWORD_GENERATOR,
          message: {
            shopId: shop.id,
            dbResourceId: product.id,
            resourceType: resourceType.PRODUCT,
          },
        });
      }

      dispatchQueue({
        queueName: QUEUE_NAMES.AUTO_IMAGE_OPTIMIZER_QUEUE,
        message: {
          shop: shopDomain,
          itemId: product.id,
          type: analysisEntityTypes.PRODUCT,
        },
      });

      dispatchQueue({
        queueName: QUEUE_NAMES.AUTO_AI_OPTIMIZATION_QUEUE,
        message: {
          shop: shopDomain,
          itemId: product.id,
        },
      });

      console.info(
        `[${this.config.queueName}](${shopDomain}) processed successfully : ${productGqlId} -- ${execTime.get()}`
      );

      channel.ack(message);
      // return true;
    } catch (e) {
      channel.ack(message);
      if (this.isThrottled(e)) {
        // re-dispatch
        await ShopService.apiRateLimitExceeded(shop.id, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.info(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(
          `[${shopDomain}] ${this.config.queueName} failed. Product ID: ${productGqlId}, Message: ${e.message}`,
          e
        );
        await this.handleError(e, message);
      }
      return false;
    }
  }
}

module.exports = new WebhookProductCreateQueue(
  {
    queueName: QUEUE_NAMES.WEBHOOK_PRODUCT_CREATE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.WEBHOOK_PRODUCT_CREATE],
  },
  RABBIT_MQ_CONNECTION
);
