// //@ts-check
const { RABBIT_MQ_CONNECTION } = require("..");
const { Op } = require("../../../sequelize");
const ProductImageService = require("../../services/ProductImageService");
const ShopifyService = require("../../services/ShopifyService");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const BaseQueue = require("./BaseQueue");
const logger = require("storeseo-logger");

class ImageFileSizeSyncQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { session, images } = decodedMessage;
    const { shop: shopDomain } = session;

    try {
      const imageDbIds = images.map((img) => img.id);
      const dbImages = await ProductImageService.getImagesByConditions({
        id: {
          [Op.in]: imageDbIds,
        },
      });
      // check if image file size is null
      const imageMediaIds = dbImages.filter((img) => img.file_size === null).map((img) => img.media_id);
      const mediaImages = await ShopifyService.getMediaImages(session.shop, imageMediaIds);

      if (mediaImages && mediaImages.length > 0) {
        for (let mediaImage of mediaImages) {
          const { id, originalSource } = mediaImage;
          const { fileSize } = originalSource;
          const image = images.find((img) => img.media_id === id);
          await ProductImageService.updateImage(image.id, { file_size: fileSize });
        }
      }
      console.log(`[${shopDomain}] ${this.config.queueName} successful.`);
      channel.ack(message);
    } catch (err) {
      channel.ack(message);
      console.error(`[${shopDomain}] ${this.config.queueName} failed.`, err);
      logger.error(err);
      await this.handleError(err, message);
    }
  }
}

module.exports = new ImageFileSizeSyncQueue(
  {
    queueName: QUEUE_NAMES.IMAGE_FILE_SIZE_SYNC,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.IMAGE_FILE_SIZE_SYNC],
  },
  RABBIT_MQ_CONNECTION
);
