const { RABBIT_MQ_CONNECTION } = require("..");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const { dispatchQueue } = require("../queueDispatcher");
const BaseQueue = require("./BaseQueue");
const ShopService = require("../../services/ShopService");
const BulkOperationService = require("../../services/BulkOperationService");
const ShopifyService = require("../../services/ShopifyService");
const bulkOperationTypes = require("storeseo-enums/bulkOperationTypes");
const bulkOperationStatus = require("storeseo-enums/bulkOperationStatus");
const cache = require("../../cache");
require("graphql-import-node/register");
const productMediaUpdateMutation = require("../../queries/bulk-operation/mutation.product-media-update.gql");
const logger = require("storeseo-logger");
const { sleep, downloadFile, isValidSession } = require("../../utils/helper");
const { Session } = require("@shopify/shopify-api");
const BackupService = require("../../services/BackupService");
const backupStatus = require("storeseo-enums/backupStatus");
const { Op } = require("sequelize");

class WebhookBulkOperationFinishQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { headers, body } = decodedMessage;
    const shopDomain = headers["X-Shopify-Shop-Domain"] || headers["x-shopify-shop-domain"];

    try {
      /**
       * Intentionally adding a delay using 'sleep' as immediate quick-fix to prevent 'lastRunningBatch' value
       * mismatch. This value mismatch concurrent issue only occures when a 'bulk product optimize' runs with
       * very few products.
       */
      await sleep(3000);

      const webhookId = headers["X-Shopify-Webhook-Id"] || headers["x-shopify-webhook-id"];
      const gqlId = body.admin_graphql_api_id;

      const shopData = await ShopService.getShop(shopDomain);
      const session = { shop: shopData.domain, accessToken: shopData.access_token };

      if (!isValidSession(session)) {
        console.info(`[${this.config.queueName}](${shopDomain}) processed successfully`, "Invalid session");
        this.consumerChannel.ack(message);
        return;
      }

      const bulkOperation = await this.#updateBulkOperationDetailsInDatabase({
        webhookId,
        gqlId,
        session,
        shopId: shopData.id,
      });

      if (bulkOperation.status !== bulkOperationStatus.COMPLETED) {
        console.info(`[${this.config.queueName}](${shopDomain}) processed successfully`);
        this.consumerChannel.ack(message);
        return;
      }

      if (bulkOperation.op_type === bulkOperationTypes.PRODUCT_OPTIMIZE) {
        await this.#handleProductOptimizeBulkOperation({
          session,
          shopData,
        });
      } else if (bulkOperation.op_type === bulkOperationTypes.PRODUCT_MEDIA_OPTIMIZE) {
        await this.#handleProductMediaOptimizeBulkOperation(shopDomain);
      } else if (
        bulkOperation.op_type === bulkOperationTypes.PRODUCT_SYNC ||
        bulkOperation.op_type === bulkOperationTypes.DATA_MIGRATE
      ) {
        await this.#handleProductBulkQueryOperation(shopDomain, gqlId, bulkOperation);
      } else if (bulkOperation.op_type === bulkOperationTypes.BULK_RESTORE) {
        await this.#handleResourceBulkRestoreOperation(shopData.id, shopData.domain);
      }

      console.info(`[${this.config.queueName}](${shopDomain}) processed successfully`);
      this.consumerChannel.ack(message);
    } catch (e) {
      this.consumerChannel.ack(message);

      if (this.isThrottled(e)) {
        // re-dispatch
        // await ShopService.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.info(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        logger.error(
          `[${shopDomain}] ${this.config.queueName} failed for shop [${shopDomain}] Message: ${e.message}`,
          e
        );
        await this.handleError(e, message);
      }
    }
  }

  /**
   *
   * @param {{ webhookId: string, gqlId: string, session: Session, shopId: number}} param0
   * @returns {Promise<import("../../../sequelize/models/bulkoperation").TBulkOperation>}
   */
  async #updateBulkOperationDetailsInDatabase({ webhookId, gqlId, session, shopId }) {
    const boDetailsShopify = await ShopifyService.getBulkOperationDetails(session.shop, gqlId);
    const boDetails = await BulkOperationService.getBulkOperationDetails({ shop_id: shopId, gql_id: gqlId });
    console.log("boDetailsShopify =", boDetailsShopify);

    return await BulkOperationService.updateBulkOperationData(boDetails.id, {
      status: boDetailsShopify.status,
      url: boDetailsShopify.url,
      partial_url: boDetailsShopify.partialDataUrl,
      webhook_id: webhookId,
      completed_at: boDetailsShopify.completedAt,
    });
  }

  /**
   *
   * @param {{session: Session, shopData: *}} param0
   */
  async #handleProductOptimizeBulkOperation({ session, shopData }) {
    const lastRunningBatch = Number(await cache.lastRunningBatchInOptimizationTask(session.shop)) || 0;
    const mediaCache = await cache.autoOptimizationMedia(session.shop);

    if (lastRunningBatch === mediaCache.batchNo && mediaCache.status === 201) {
      await BulkOperationService.processFileAndStartBulkMutation(session, {
        shopId: shopData.id,
        stagedTarget: mediaCache.stagedTarget,
        mutation: productMediaUpdateMutation,
        opType: bulkOperationTypes.PRODUCT_MEDIA_OPTIMIZE,
      });
    }
  }

  /**
   *
   * @param {string} shopDomain
   */
  async #handleProductMediaOptimizeBulkOperation(shopDomain) {
    dispatchQueue({
      queueName: QUEUE_NAMES.PRODUCT_OPTIMIZE_BULK_OPERATION,
      message: { shop: shopDomain },
    });
  }

  /**
   * @param {string} shopDomain
   * @param {string} gqlId
   * @param {import("../../../sequelize/models/bulkoperation").TBulkOperation} bulkOperation
   */
  async #handleProductBulkQueryOperation(shopDomain, gqlId, bulkOperation) {
    let inputFile = "";

    if (bulkOperation.url) {
      const filename = `bulk-${gqlId.split("/").pop()}.jsonl`;
      const filePath = `uploads/${shopDomain}/bulk-products/${filename}`;
      await downloadFile(bulkOperation.url, filePath);

      inputFile = filePath;
    }

    if (bulkOperation.partial_url) {
      const filename = `bulk-${gqlId.split("/").pop()}-partial.jsonl`;
      const filePath = `uploads/${shopDomain}/bulk-products/${filename}`;
      await downloadFile(bulkOperation.partial_url, filePath);

      inputFile = filePath;
    }

    await cache.product.syncFile(shopDomain, inputFile);
    await cache.product.syncFileCursor(shopDomain, 0);

    dispatchQueue({
      queueName:
        bulkOperation.op_type === bulkOperationTypes.PRODUCT_SYNC
          ? QUEUE_NAMES.PRODUCT_SYNC
          : QUEUE_NAMES.DATA_MIGRATION_QUEUE,
      message: {
        shopDomain,
      },
    });
  }

  async #handleResourceBulkRestoreOperation(shopId, shopDomain) {
    await BulkOperationService.updateBulkOperationByCondition(
      {
        shop_id: shopId,
        op_status: {
          [Op.in]: [bulkOperationStatus.PENDING, bulkOperationStatus.PROCESSING],
        },
        op_type: bulkOperationTypes.BULK_RESTORE,
      },
      {
        op_status: bulkOperationStatus.COMPLETED,
      }
    );

    const lastRunningRestore = await BackupService.getManualBackupByCondition(shopId, {
      restore_status: backupStatus.PROCESSING,
    });

    if (lastRunningRestore) {
      const newRestoreSteps = lastRunningRestore.restore_steps.bulk_mutations.map((step) => {
        if (step.bulk_op_gql && !step.complete) {
          return {
            ...step,
            complete: true,
            completed_at: new Date(),
          };
        }
        return step;
      });

      await BackupService.updateManualBackup(lastRunningRestore.id, {
        restore_steps: {
          ...lastRunningRestore.restore_steps,
          bulk_mutations: newRestoreSteps,
        },
      });

      await BackupService.dispatchNextRestoreStep(shopId, lastRunningRestore.id);
      await BackupService.markedManualRestoreAsComplete(shopId, shopDomain, lastRunningRestore.id);
    }
  }
}

module.exports = new WebhookBulkOperationFinishQueue(
  {
    queueName: QUEUE_NAMES.WEBHOOK_BULK_OPERATION_FINISH_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.WEBHOOK_BULK_OPERATION_FINISH_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
