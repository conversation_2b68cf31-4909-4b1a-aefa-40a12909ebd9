const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const { RABBIT_MQ_CONNECTION } = require("../index");
const { dispatchQueue } = require("../queueDispatcher");

class ProductIndexingQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const googleApiService = require("../../services/GoogleApiService");
    const urlNotificationType = require("storeseo-enums/urlNotificationType");
    const ProductService = require("../../services/ProductService");

    const decodedMessage = decodeToJSON(message);
    const { user, product, isEnabled } = decodedMessage;

    try {
      const indexType = isEnabled ? urlNotificationType.URL_UPDATED : urlNotificationType.URL_DELETED;

      const res = await googleApiService.submitGoogleIndexing(user, {
        url: product.url,
        type: indexType,
      });

      if (res.status === 200) {
        console.log(this.config.queueName, "Google indexing response: ", res.body);
        await ProductService.updateProduct(product.shop_id, product.id, {
          indexing_status: indexType,
          indexing_last_update: new Date().toISOString(),
        });
        // console.info(`[${user.shop}] ${this.config.queueName} successful.`, res);
      } else {
        console.error(`[${user.shop}] ${this.config.queueName} failed.`, res.statusText);
      }

      channel.ack(message);
      console.info(`[${this.config.queueName}](${user.shop}) processed successfully`);
      return true;
    } catch (e) {
      channel.ack(message);
      console.error(`[${user.shop}] ${this.config.queueName} failed.`, e);

      // if (this.isThrottled(e)) {
      //   // re-dispatch
      //   dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
      //   console.error(`[${user.shop}] ${this.config.queueName} redispatched.`);
      // } else {
      //   console.error(`[${user.shop}] ${this.config.queueName} failed.`, e);
      //   await this.handleError(e, message);
      // }
      // return false;
    }
  }
}

module.exports = new ProductIndexingQueue(
  {
    queueName: QUEUE_NAMES.PRODUCT_INDEXING_GOOGLE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.PRODUCT_INDEXING_GOOGLE],
  },
  RABBIT_MQ_CONNECTION
);
