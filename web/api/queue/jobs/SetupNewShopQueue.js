const { RABBIT_MQ_CONNECTION } = require("..");
const ShopService = require("../../services/ShopService");
const LocationService = require("../../services/LocationService");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const BaseQueue = require("./BaseQueue");
const { dispatchQueue } = require("../queueDispatcher");
const { defaultEmailNotificationSettings } = require("../../config/email-notification");
const ShopifyService = require("../../services/ShopifyService");
const settingKeys = require("storeseo-enums/settingKeys");
const cache = require("../../cache");
const { pick } = require("lodash");
const { defaultOnboardingSteps } = require("../../config/defaultValues");

class SetupNewShopQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { shop: shopDomain } = decodedMessage;

    try {
      const shop = await ShopService.getShop(shopDomain);
      const session = this.getSession(shop);

      ShopService.updateShopSetting(shop.id, {
        key: settingKeys.ONBOARD_STEPS,
        value: JSON.stringify(defaultOnboardingSteps),
        value_type: "json",
      });

      console.log(`[${this.config.queueName}]: saving shop locations for (${shopDomain})...`);
      await LocationService.saveAllNewLocationsFromShopify({
        ...session,
        shopId: shop.id,
      });
      console.log(`[${this.config.queueName}]: shop locations saved for (${shopDomain})!`);

      console.log("Enabling all email notification settings for the shop...");

      await ShopService.updateShopSetting(shop.id, defaultEmailNotificationSettings);

      console.log("Email notification settings enabled for the shop!");

      await this.createMultiLanguageSetting(shop);

      console.info(`[${this.config.queueName}](${shopDomain}) processed successfully`);
      this.consumerChannel.ack(message);
    } catch (e) {
      this.consumerChannel.ack(message);

      if (this.isThrottled(e)) {
        // re-dispatch
        // await ShopService.apiRateLimitExceeded(shopDomain, "SHOPIFY_GRAPHQL_API", true, 90);
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.info(`[${shopDomain}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(`[${shopDomain}] ${this.config.queueName} failed! Message: ${e.message}`);
        await this.handleError(e, message);
      }

      return;
    }
  }

  /**
   *
   * @param {*} shop
   * @returns {Session}
   */
  getSession = (shop) => {
    return {
      shop: shop.domain,
      accessToken: shop.access_token,
    };
  };

  createMultiLanguageSetting = async (shop) => {
    console.info(`[${this.config.queueName}](${shop.domain}) - generating multi-language setting...`);

    const shopLocales = await ShopifyService.localaizations.getShopLocales(shop.domain);
    const defaultLanguage = pick(
      shopLocales.find((l) => l.primary),
      ["locale", "name"]
    );
    await cache.shop.defaultLanguage(shop.domain, defaultLanguage);
    await cache.shop.aiContentGeneratorLanguage(shop.domain, defaultLanguage);
    await cache.shop.hasMultipleLanguages(shop.domain, shopLocales.length > 1);

    const setting = {
      enabled: false,
      shopLocales,
    };

    await ShopService.updateShopSetting(shop.id, {
      key: settingKeys.MULTI_LANGUAGE_SETTING,
      value: JSON.stringify(setting),
      value_type: "json",
    });
    console.info(`[${this.config.queueName}](${shop.domain}) - mulit-language setting generated`);
  };
}

module.exports = new SetupNewShopQueue(
  {
    queueName: QUEUE_NAMES.SETUP_NEW_SHOP,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.SETUP_NEW_SHOP],
  },
  RABBIT_MQ_CONNECTION
);
