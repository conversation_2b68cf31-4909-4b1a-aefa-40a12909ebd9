const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const BaseQueue = require("./BaseQueue");
const logger = require("storeseo-logger");
const { RABBIT_MQ_CONNECTION } = require("..");
const { dispatchQueue } = require("../queueDispatcher");
const EventService = require("../../services/EventService");

class BlogArticlesSyncQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    const { user, blog, nextPageQuery = {} } = decodedMessage;

    try {
      const ArticleService = require("../../services/ArticleService");
      const ShopService = require("../../services/ShopService");
      // If API rate limit is exceed flag is on, requeue the message instead of further processing
      if (await ShopService.apiRateLimitExceeded(user.shop, "SHOPIFY_HTTP_API")) {
        channel.ack(message);
        return dispatchQueue({
          queueName: this.config.queueName,
          message: decodedMessage,
          ttl: this.throttledDelay,
        });
      }

      const { nextPageQuery: nextPageToken, limitRemaining } = await ArticleService.saveOrUpdateArticlesFromShopify({
        user,
        blog,
        nextPageQuery,
        limit: 1,
      });

      // re-dispatch if Shopify API rate limit is exceeded & the req failed.
      if (limitRemaining === 0) {
        channel.ack(message);
        console.log(`[${user.shop}] ${this.config.queueName} HTTP rate limit excedded...`);
        await ShopService.apiRateLimitExceeded(user.shop, "SHOPIFY_HTTP_API", true, 90);
        return dispatchQueue({
          queueName: this.config.queueName,
          message: decodedMessage,
          ttl: this.throttledDelay,
        });
      } else if (nextPageToken) {
        channel.ack(message);
        console.log(`[${user.shop}] ${this.config.queueName} blog sync ongoing...`);
        return dispatchQueue({
          queueName: this.config.queueName,
          message: { ...decodedMessage, nextPageQuery: nextPageToken },
        });
      }

      const articles = await ArticleService.count(blog.shop_id);
      await EventService.handleBlogArticleSyncUpdate({ shop: user.shop, id: blog.id, articles });
      channel.ack(message);
      console.log(`[${this.config.queueName}](${user.shop}) processed successfully`);
      return true;
    } catch (e) {
      channel.ack(message);
      if (this.isThrottled(e)) {
        // re-dispatch
        dispatchQueue({
          queueName: this.config.queueName,
          message: decodedMessage,
          ttl: this.throttledDelay,
        });

        console.log(`[${user.shop}] ${this.config.queueName} redispatched.`);
      } else {
        console.error(`[${user.shop}] ${this.config.queueName} failed.`, e);
        await this.handleError(e, message);
      }
      return false;
    }
  }
}

module.exports = new BlogArticlesSyncQueue(
  {
    queueName: QUEUE_NAMES.BLOG_ARTICLES_SYNC,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.BLOG_ARTICLES_SYNC],
  },
  RABBIT_MQ_CONNECTION
);
