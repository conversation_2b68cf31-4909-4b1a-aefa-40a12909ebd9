require("graphql-import-node/register");
const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const OptimizationService = require("../../services/OptimizationService");
const EventService = require("../../services/EventService");
const { RABBIT_MQ_CONNECTION } = require("../index");
const { execTimeTracker, sleep } = require("../../utils/helper");
const { dispatchQueue } = require("../queueDispatcher");
const cache = require("../../cache");
const ShopService = require("../../services/ShopService");
const settingKeys = require("storeseo-enums/settingKeys");
const ShopifyService = require("../../services/ShopifyService");
const productUpdateMutation = require("../../queries/bulk-operation/mutation.product-update.gql");
const bulkOperationTypes = require("storeseo-enums/bulkOperationTypes");
const bpoOptions = require("storeseo-enums/bpoOptions");
const { bulkOptimizationBatchSize } = require("../../config/app");
const BulkOperationService = require("../../services/BulkOperationService");

const handleOptimizationTaskFinish = async (shopId, shop) => {
  console.info(`[${shop}] - PRODUCT OPTIMIZATION TASK FINISHED!`);
  // const logData = await OptimizationService.logOptimizationTaskFinishInfo(shopId, productsOptimized);

  await cache.resetOptimizationTaskInfo(shop);
  await cache.resetAutoOptimizationMedia(shop);
  await EventService.handleOptimizationTaskFinish({
    shopId,
    shop,
    // productsOptimized,
    // products: logData.products_to_optimize,
  });
};

const getMetaTemplatesForAutoOptimization = async (shopId) => {
  const setting = await ShopService.getShopSetting(shopId, settingKeys.META_TEMPLATES);

  return setting.value;
};

class ProductOptimizeQueueBulkOperation extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const execTime = execTimeTracker();
    const decodedMessage = decodeToJSON(message);
    const { shop: shopDomain, bpoOption = bpoOptions.NON_OPTIMIZED_PRODUCTS } = decodedMessage;

    try {
      const shop = await ShopService.getShop(shopDomain);
      const metaTemplates = await getMetaTemplatesForAutoOptimization(shop.id);

      const totalBatches = Number(await cache.totalBatchesInOptimizationTask(shopDomain)) || 0;
      const lastRunningBatch = Number(await cache.lastRunningBatchInOptimizationTask(shopDomain)) || 0;
      const batchSize = bulkOptimizationBatchSize;
      const productsToSkip = lastRunningBatch * batchSize;

      console.info(
        `[${shopDomain}] - last running batch: ${lastRunningBatch} | batch size: ${batchSize} | skip products count: ${productsToSkip}`
      );

      if (lastRunningBatch >= totalBatches) {
        await handleOptimizationTaskFinish(shop.id, shopDomain);
        console.info(`[${this.config.queueName}](${shopDomain}) processed successfully -- ${execTime.get()}`);
        this.consumerChannel.ack(message);
        return;
      }

      const { fileName, filePath, mediaFileName, mediaFilePath } =
        await OptimizationService.createInputFileForBulkOperation({
          shop,
          metaTemplates,
          skip: productsToSkip,
          limit: batchSize,
          bpoOption,
        });

      const session = { shop: shopDomain, accessToken: shop.access_token };

      const { status, stagedTarget } = await ShopifyService.uploadBulkOperationInputFileIntoShopify({
        shop: session.shop,
        fileName,
        filePath,
      });

      if (status === 201) {
        // console.table(["file upload successful..."]);
        await sleep(3000, false);

        await BulkOperationService.processFileAndStartBulkMutation(session, {
          shopId: shop.id,
          stagedTarget,
          mutation: productUpdateMutation,
          opType: bulkOperationTypes.PRODUCT_OPTIMIZE,
        });
      }

      await sleep(3000);

      const { status: mediaStatus, stagedTarget: mediaStagedTarget } =
        await ShopifyService.uploadBulkOperationInputFileIntoShopify({
          shop: session.shop,
          fileName: mediaFileName,
          filePath: mediaFilePath,
        });

      const batchNo = lastRunningBatch + 1;
      await cache.lastRunningBatchInOptimizationTask(shopDomain, batchNo);
      await cache.autoOptimizationMedia(shopDomain, mediaStatus, mediaStagedTarget, batchNo);

      console.info(`[${this.config.queueName}](${shopDomain}) processed successfully -- ${execTime.get()}`);
      this.consumerChannel.ack(message);
    } catch (e) {
      if (this.isThrottled(e)) {
        this.consumerChannel.ack(message);
        // re-dispatch
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.info(`[${shopDomain}] ${this.config.queueName} redispatched.`);
        console.error(
          "requestedQueryCost =",
          e.response.extensions.cost.requestedQueryCost,
          "currentlyAvailable =",
          e.response.extensions.cost.throttleStatus.currentlyAvailable
        );
      } else {
        this.consumerChannel.ack(message);
        console.error(`[${shopDomain}] ${this.config.queueName} failed. Message: ${e.message}`, e);
        await this.handleError(e, message);
      }
    }
  }
}

module.exports = new ProductOptimizeQueueBulkOperation(
  {
    queueName: QUEUE_NAMES.PRODUCT_OPTIMIZE_BULK_OPERATION,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.PRODUCT_OPTIMIZE_BULK_OPERATION],
  },
  RABBIT_MQ_CONNECTION
);
