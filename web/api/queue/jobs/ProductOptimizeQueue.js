const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const logger = require("storeseo-logger");
const OptimizationService = require("../../services/OptimizationService");
const analysisService = require("../../services/AnalysisService");
const EventService = require("../../services/EventService");
const { RABBIT_MQ_CONNECTION } = require("../index");
const { isEmpty } = require("lodash");
const FailedMessagesQueue = require("./FailedMessagesQueue");
const { execTimeTracker } = require("../../utils/helper");
const { dispatchQueue } = require("../queueDispatcher");
const cache = require("../../cache");

const handleOptimizationTaskFinish = async (shopId, shop) => {
  const productsOptimized = await cache.productsOptimizedByAutoOptimizationTask(shop);
  const logData = await OptimizationService.logOptimizationTaskFinishInfo(shopId, productsOptimized);

  await cache.resetOptimizationTaskInfo(shop);
  await EventService.handleOptimizationTaskFinish({
    shopId,
    shop,
    productsOptimized,
    products: logData.products_to_optimize,
  });
};

class ProductOptimizeQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const execTime = execTimeTracker();
    const decodedMessage = decodeToJSON(message);
    const { shopId, shop, productId, metaTemplates } = decodedMessage;

    try {
      const product = await OptimizationService.optimizeEachProduct({ shopId, productId, metaTemplates });
      if (product.is_optimized) {
        await cache.productsOptimizedByAutoOptimizationTask(shop, "INCR");
      }
      console.info(`[${this.config.queueName}](${shopId}) processed successfully -- ${execTime.get()}`);
    } catch (e) {
      if (this.isThrottled(e)) {
        // re-dispatch
        dispatchQueue({ queueName: this.config.queueName, message: decodedMessage, ttl: this.throttledDelay });
        console.info(`[${shopId}] ${this.config.queueName} redispatched.`);
        console.error(
          "requestedQueryCost =",
          e.response.extensions.cost.requestedQueryCost,
          "currentlyAvailable =",
          e.response.extensions.cost.throttleStatus.currentlyAvailable
        );
      } else {
        console.error(`[${shopId}] ${this.config.queueName} failed. Message: ${e.message}`);
        await this.handleError(e, message);
      }
    } finally {
      channel.ack(message);

      const productsRemaining = await cache.updateProductsCountPendingInAutoOptimizationTask(shop, "DECR");
      if (productsRemaining <= 0) {
        await handleOptimizationTaskFinish(shopId, shop);
      }
    }
  }
}

module.exports = new ProductOptimizeQueue(
  {
    queueName: QUEUE_NAMES.PRODUCT_OPTIMIZE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.PRODUCT_OPTIMIZE],
  },
  RABBIT_MQ_CONNECTION
);
