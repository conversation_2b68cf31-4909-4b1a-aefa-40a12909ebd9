const BaseQueue = require("./BaseQueue");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const logger = require("storeseo-logger");
const ShopService = require("../../services/ShopService");
const { dispatchQueue } = require("../queueDispatcher");
const { RABBIT_MQ_CONNECTION } = require("..");
const cache = require("../../cache");
const analysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const EventService = require("../../services/EventService");
const BulkOperationService = require("../../services/BulkOperationService");
const bulkOperationStatus = require("storeseo-enums/bulkOperationStatus");
const { Op } = require("../../../sequelize");
const bulkOperationTypes = require("storeseo-enums/bulkOperationTypes");
const ProductSyncService = require("../../services/products/ProductSyncService");
const BackupService = require("../../services/BackupService");
const { BulkProductParsingError } = require("../../../errors");
const SubscriptionPlanService = require("../../services/SubscriptionPlanService");
const AuthService = require("../../services/AuthService");

/**
 * @typedef {object} ProductSyncQueueMessage
 * @property {string} shopDomain
 */

class ProductSyncQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    /** @type {ProductSyncQueueMessage} */
    const decodedMessage = decodeToJSON(message);
    const { shopDomain } = decodedMessage;

    try {
      const shop = await ShopService.getShop(shopDomain);
      const migrateDataFromApp = await cache.product.migrateDataFromApp(shopDomain);

      const productSyncService = new ProductSyncService(shopDomain, migrateDataFromApp);
      const { syncedProducts, dbProductCount, planLimit } = await productSyncService.syncNextProducts();
      console.info(
        `[${shopDomain}] ${this.config.queueName} Synced products ${
          dbProductCount - syncedProducts.length
        }-${dbProductCount}, Limit: ${planLimit}.`
      );

      this.#dispatchImageOptimizationForSavedProducts(shopDomain, syncedProducts);

      await this.#handleSyncProgress({ shop, syncedProducts, dbProductCount });
      console.log(`[${shopDomain}] ${this.config.queueName} successful.`);
      channel.ack(message);
    } catch (err) {
      channel.ack(message);

      if (err instanceof BulkProductParsingError) {
        console.log(err);
        await this.#dispatchOldProductSync(shopDomain);
        return;
      }

      console.error(`[${shopDomain}] ${this.config.queueName} failed.`, err);
      logger.error(err);
      await this.handleError(err, message);
    }
  }

  #dispatchImageOptimizationForSavedProducts(shopDomain, savedProducts) {
    for (let product of savedProducts) {
      dispatchQueue({
        queueName: QUEUE_NAMES.AUTO_IMAGE_OPTIMIZER_QUEUE,
        message: {
          shop: shopDomain,
          itemId: product.id,
          type: analysisEntityTypes.PRODUCT,
        },
      });

      dispatchQueue({
        queueName: QUEUE_NAMES.AUTO_AI_OPTIMIZATION_QUEUE,
        message: {
          shop: shopDomain,
          itemId: product.id,
        },
      });
    }
  }

  /**
   *
   * @param {{ shop: *, syncedProducts: *[], dbProductCount: number}} param0
   */
  async #handleSyncProgress({ shop, syncedProducts, dbProductCount }) {
    // dispatch FluentCRM contact update
    dispatchQueue({
      queueName: QUEUE_NAMES.FLUENT_CRM_ADD_CONTACT,
      message: {
        domain: shop.domain,
      },
    });

    if (syncedProducts.length === 0) {
      console.info(`[${shop.domain}] All product synced successfully.`);

      EventService.handleProductSyncComplete({
        shop: shop.domain,
        total: dbProductCount,
        deleteSyncCursor: true,
      });

      const localFilePath = await cache.product.syncFile(shop.domain);
      try {
        console.log(`[${shop.domain}]: Backup initial products.`);
        if (localFilePath) {
          await BackupService.backupShopsInitialProducts(shop.domain, localFilePath);
        }
      } catch (error) {
        throw error;
      }
    } else {
      EventService.handleProductSyncUpdate({
        shop: shop.domain,
        synced: dbProductCount - syncedProducts.length,
        total: dbProductCount,
      });

      dispatchQueue({
        queueName: this.config.queueName,
        message: {
          shopDomain: shop.domain,
        },
        ttl: 1500,
      });
    }

    await BulkOperationService.updateBulkOperationByCondition(
      {
        shop_id: shop.id,
        op_status: {
          [Op.in]: [bulkOperationStatus.PENDING, bulkOperationStatus.PROCESSING],
        },
        op_type: bulkOperationTypes.PRODUCT_SYNC,
      },
      {
        synced: dbProductCount,
        op_status: syncedProducts.length ? bulkOperationStatus.PROCESSING : bulkOperationStatus.COMPLETED,
      }
    );
  }

  async #dispatchOldProductSync(shopDomain) {
    await cache.removeProductSyncCursor(shopDomain);
    const shop = await ShopService.getShop(shopDomain);
    const plan = await SubscriptionPlanService.getSubscriptionPlanNameAndType(shop.plan_id);
    const user = await AuthService.serializeAuthToken(shop, plan);

    dispatchQueue({
      queueName: QUEUE_NAMES.PRODUCT_SYNC_WITH_DATA_MIGRATION,
      message: {
        user,
      },
    });

    console.log("Dispatched old product sync queue for shop: ", shopDomain);
  }
}

module.exports = new ProductSyncQueue(
  {
    queueName: QUEUE_NAMES.PRODUCT_SYNC,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.PRODUCT_SYNC],
  },
  RABBIT_MQ_CONNECTION
);
