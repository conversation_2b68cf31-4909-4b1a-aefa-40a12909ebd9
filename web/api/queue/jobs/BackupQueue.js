// @ts-check
const { RABBIT_MQ_CONNECTION } = require("..");
const ShopService = require("../../services/ShopService");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const BaseQueue = require("./BaseQueue");
const logger = require("storeseo-logger");
const { backupAndRestoreSchema } = require("storeseo-schema/settings/backupAndRestore");
const BackupService = require("../../services/BackupService");
const BackupStatus = require("storeseo-enums/backupStatus");
const EventService = require("../../services/EventService");

class BackupQueue extends BaseQueue {
  async handle(message, channel, decodeToJSON) {
    const decodedMessage = decodeToJSON(message);
    /**
     * @type {{shopId: number, backupId: number, resources: import("yup").InferType<backupAndRestoreSchema>}}
     */
    const { shopId, backupId, resources } = decodedMessage;

    const shop = await ShopService.getShopById(shopId);
    if (!shop) {
      channel.ack(message);
      console.error(`Shop with id ${shopId} not found.`);
      return;
    }

    const shopDomain = shop.domain;

    try {
      // Create backup jsonl file for each resource type in our database data format
      await BackupService.createManualBackupJsonlFiles(resources, shopId, shopDomain);

      // Upload the backup jsonl file to the google cloud storage
      const uploadResult = await BackupService.uploadToGoogleCloudStorage(shop, backupId);

      if (!uploadResult) {
        throw new Error("Failed to upload backup file to google cloud storage.");
      }

      // Update the backup status to completed
      await BackupService.updateManualBackup(backupId, {
        backup_status: BackupStatus.COMPLETE,
        backup_complete_at: new Date(),
        backup_file_src: uploadResult.fileName,
      });

      // Send a notification to the user that the backup is completed
      await EventService.handleBackupCompleted({ shopId, shop: shopDomain, backupId });

      // Acknowledge the message
      channel.ack(message);
      console.log(`[${shopDomain}] ${this.config.queueName} successful.`);
    } catch (err) {
      // Handle the error
      channel.ack(message);
      console.error(`[${shopDomain}] ${this.config.queueName} failed.`, err);
      logger.error(err);
      await this.handleError(err, message);
    }
  }
}

module.exports = new BackupQueue(
  {
    queueName: QUEUE_NAMES.BACKUP_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.BACKUP_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
