// @ts-check
const cache = require("../../cache");
const logger = require("storeseo-logger");
const ShopService = require("../../services/ShopService");
const { sleep } = require("../../utils/helper");
const { dispatchQueue } = require("../queueDispatcher");
const { MAX_MESSAGE_PER_QUEUE } = require("./config");
const { listOfEmptyQueues } = require("./state");
const AiOptimizationStatus = require("storeseo-enums/aiOptimization");
const moment = require("moment");
const ProductService = require("../../services/ProductService");

/**
 *
 * @param {string} queue
 * @param {string} shop
 * @param {any[]} products
 */
function dispatchProducts(queue, shop, products) {
  for (let product of products) {
    dispatchQueue({
      queueName: queue,
      message: { product, shop },
    });
  }

  return true;
}

/**
 *
 * @param {any[]} products
 */
async function markDispatchedProducts(products) {
  for (let product of products)
    await ProductService.updateProductByCondition(
      { id: product.id },
      {
        ai_optimization_status: AiOptimizationStatus.DISPATCHED,
      }
    );

  return true;
}

/**
 *
 * @param {string} shop
 */
// async function requeueShopIfHasPendingProductssRemaining(shop) {
//   if (pagination.pageCount > 1) await cache.altTextOptimizer.addStoreToPendingOptimizationQueue(shop);
// }

/**
 *
 * @param {string} queue name of the queue
 * @param {string} shop myshopify domain of the shop
 */
async function allocatePendingProductsToQueue(queue, shop) {
  console.log(`Dispatching '${shop}' to queue '${queue}'`);
  const shopDetails = await ShopService.getShop(shop);

  const products = await ProductService.getFullProductsByCondition(
    shopDetails.id,
    {
      ai_optimization_status: AiOptimizationStatus.PENDING,
    },
    {
      limit: MAX_MESSAGE_PER_QUEUE,
    }
  );

  if (products.length) {
    const filteredProducts = products?.filter((product) => product.focus_keyword);
    dispatchProducts(queue, shop, filteredProducts);
    await markDispatchedProducts(filteredProducts);
    await cache.aiOptimizer.addStoreToPendingOptimizationQueue(shop);
  }
}

async function manageQueuesAndPendingProducts() {
  try {
    const queues = listOfEmptyQueues();
    const pendingStoresCount = await cache.aiOptimizer.numberOfStoresPendingOptimization();

    if (queues.length === 0) console.log("All queues are busy now...");
    // else console.log(`\nFree queues: ${JSON.stringify(queues, null, 2)}`);

    let queueUsageCount = 0;
    for (let queue of queues) {
      const shop = await cache.aiOptimizer.getNextStoreFromPendingOptimizationQueue();
      if (shop) {
        await allocatePendingProductsToQueue(queue, shop);
        queueUsageCount++;
      } else {
        console.log("No shops are in 'pending auto ai optimization' list!");
        break;
      }
    }

    await cache.aiOptimizer.updateMaxMinQueueUsageStats({
      pendingStoresCount,
      queueUsageCount,
    });
    await cache.aiOptimizer.addToPendingSyncStatisticDates(moment().format("YYYY-MM-DD"));
  } catch (err) {
    logger.error(err, {
      description: "Error in queue manager",
      transaction: "AiOptimizer",
    });
    console.log(err);
  } finally {
    await sleep(3000);
    await manageQueuesAndPendingProducts();
  }
}

module.exports = {
  manageQueuesAndPendingProducts,
};
