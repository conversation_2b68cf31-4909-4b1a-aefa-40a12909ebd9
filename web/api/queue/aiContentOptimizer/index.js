require("../../config");

const logger = require("storeseo-logger");
const { RABBIT_MQ_CONNECTION } = require("../index");
// const { bootConsumer } = require("./consumer");
const { bootDispatcher } = require("./dispatcher");
const { manageQueuesAndPendingProducts } = require("./manager");
const { resetState } = require("./state");
const { QueueStats } = require("../../../sequelize");
const cron = require("node-cron");
const { QUEUE_BASE_NAME } = require("./config");
const cache = require("../../cache");
const { bootConsumer } = require("./consumer");

cron.schedule("0 0 * * * *", async function syncStatsIntoDb() {
  try {
    logger.info("syncing product ai content optimizer stats data from redis to database...");

    const date = await cache.aiOptimizer.popOneDateFromPendingSyncStatisticsDates();
    if (!date) return console.log("No stats to sync!");

    const { max: maxQueueUsage, min: minQueueUsage } = await cache.aiOptimizer.getMaxMinQueueUsageCount(date);
    const { max: maxStoreCount, min: minStoreCount } = await cache.aiOptimizer.getMaxMinStoreCount(date);

    const productsQueued = await cache.aiOptimizer.productsQueuedByDate(date);
    const productsProcessed = await cache.aiOptimizer.productsProcessedByDate(date);
    const productsFailedToProcess = await cache.aiOptimizer.processingFailedByDate(date);

    await QueueStats.upsert({
      queue_name: QUEUE_BASE_NAME,
      date,

      statistics: {
        maxQueueUsage,
        minQueueUsage,

        maxStoreCount,
        minStoreCount,

        productsQueued,
        productsProcessed,
        productsFailedToProcess,
      },
    });

    console.log("stats data synced in database");
  } catch (err) {
    logger.error(err, {
      description: "Failed to sync stats data from redis to database",
      transaction: "AiContentOptimizer",
    });
  }
});

async function startTheProcess() {
  try {
    await RABBIT_MQ_CONNECTION;
    console.log("RabbitMQ connection is ready!");

    resetState();
    await bootDispatcher();
    await bootConsumer();

    manageQueuesAndPendingProducts();
  } catch (err) {
    logger.error(err);
  }
}

startTheProcess();

module.exports = {};
