// // @ts-check
const { isAxiosError } = require("axios");
const { RABBIT_MQ_CONNECTION } = require("..");
const logger = require("storeseo-logger");
const { EXCHANGE_NAMES, EXCHANGE_TYPES, NUMBER_OF_QUEUES } = require("./config");
const { getQueueName } = require("./helper");
const ProductService = require("../../services/ProductService");
const AiOptimizationStatus = require("storeseo-enums/aiOptimization");
const AiService = require("../../services/AiService");
const ResourceOptimizationService = require("../../services/resource/ResourceOptimizationService");
const resourceType = require("storeseo-enums/resourceType");
const ShopService = require("../../services/ShopService");
const cache = require("../../cache");
const aiContentTypes = require("storeseo-enums/aiContentTypes");
const subscriptionAddonGroup = require("storeseo-enums/subscriptionAddonGroup");
const { serializeImageAltTexts } = require("../../serializers/aiContentSerializer");
const ProductImageService = require("../../services/ProductImageService");
const altTextOptimizationStatus = require("storeseo-enums/altTextOptimization");
const ProductAnalysisService = require("../../services/ProductAnalysisService");
const { METAFIELD_KEYS } = require("storeseo-enums/metafields");
const imageOptimization = require("storeseo-enums/imageOptimization");
const resourceOPType = require("storeseo-enums/resourceOPType");
const { isEmpty } = require("lodash");
const { InactiveShopError } = require("../../../errors");
const { dispatchQueue } = require("../queueDispatcher");
const { QUEUE_NAMES } = require("../config");
const ShopifyService = require("../../services/ShopifyService");

/**
 * @typedef {{meta:boolean,tags:boolean,imageAltText:"allImages" | "featuredImage"}} AiOptimizationSettings
 */

/**
 * @type {import("amqplib").ConfirmChannel | null}
 */
let consumerChannel = null;

/**
 * Assert exchanges into existance for use in queue consume & dispatch
 * @param {import("amqplib").Channel} channel
 * @returns {Promise<import("amqplib").Channel} same channel provided in the input
 */
const assertExchanges = async (channel) => {
  await channel.assertExchange(EXCHANGE_NAMES.DEFAULT, EXCHANGE_TYPES.DIRECT);
  await channel.assertExchange(EXCHANGE_NAMES.EX_DELAYED, EXCHANGE_TYPES.DELAYED, {
    arguments: { "x-delayed-type": "direct" },
  });
  await channel.assertExchange(EXCHANGE_NAMES.MESSAGE_DUMP, EXCHANGE_TYPES.DIRECT);

  return channel;
};

/**
 * Create a new channel & perapre it for message dispatch
 */
const prepareConsumerChannel = async () => {
  console.log("creating consumer channel...");
  const connection = await RABBIT_MQ_CONNECTION;
  const channel = await connection.createChannel();
  await assertExchanges(channel);

  consumerChannel = channel;
  consumerChannel.prefetch(1);
  console.log("Consumer channel created!");
};

async function bindQueues() {
  console.log(`Queues to bind: ${NUMBER_OF_QUEUES}`);

  for (let i = 1; i <= NUMBER_OF_QUEUES; i++) {
    const queueName = getQueueName(i);
    await consumerChannel.bindQueue(queueName, EXCHANGE_NAMES.DEFAULT, queueName);
    await consumerChannel.bindQueue(queueName, EXCHANGE_NAMES.EX_DELAYED, queueName);
  }

  console.log("Queues binded!");
}

/**
 *
 * @param {string} queue
 * @param {import("amqplib").ConsumeMessage} message
 */
async function handleMessage(queue, message) {
  const { product, shop } = JSON.parse(message.content.toString());
  let approximate_credit_usage;
  const { title, description, focus_keyword } = product;
  const shopDetails = await ShopService.getShop(shop);

  try {
    await ShopifyService.hasValidSession(shop);

    let countTotalCreditUsages = 0; // Update this value as per the successful AI content generation

    const resourceOptimizationSetting = await ResourceOptimizationService.getByCondition({
      shop_id: shopDetails?.id,
      resource_id: product.id,
      resource_type: resourceType.PRODUCT,
      resource_op_type: resourceOPType.AI_OPTIMIZATION,
    });

    approximate_credit_usage = resourceOptimizationSetting?.approximate_credit_usage;

    let generatedAiContent = null;

    const contentTypes = generateContentTypes(resourceOptimizationSetting?.optimization_setting);
    // Generate ai content
    const aiContentGeneratorLanguage = await cache.shop.aiContentGeneratorLanguage(shop);
    const { data } = await AiService.generateProductContent({
      title,
      description: description ?? "",
      focusKeyword: focus_keyword ?? "",
      contentTypes,
      language: aiContentGeneratorLanguage.name,
    });

    if (data?.status === "success") {
      const { output, usage } = data;
      generatedAiContent = output;
      const aiContentCreditUsage = AiService.calculateCreditUsage(usage);
      countTotalCreditUsages += aiContentCreditUsage; // Update this value as per the successful AI content generation
    }

    const imagesToOptimize =
      resourceOptimizationSetting?.optimization_setting?.imageAltText === "featuredImage"
        ? product?.featuredImage
          ? [product.featuredImage]
          : []
        : product.images;

    // Generate alt text for all images
    const { generatedAltTexts, totalCreditUsagesByAltText } = await generateImageAltTexts({
      images: imagesToOptimize,
      title,
      description,
      focusKeyword: focus_keyword,
      shop,
      queue,
    });

    countTotalCreditUsages += totalCreditUsagesByAltText;

    const remainingCreditUsage = Number((approximate_credit_usage - countTotalCreditUsages).toFixed(2));

    // Sync the credit usage after AI content generation
    if (remainingCreditUsage < 0) {
      incrementRelatedUsageCount(shop, Math.abs(remainingCreditUsage));
    } else {
      decrementRelatedUsageCount(shop, Math.abs(remainingCreditUsage));
    }

    await handleSuccessfulProductDataGeneration(
      shopDetails,
      product,
      generatedAiContent,
      generatedAltTexts,
      resourceOptimizationSetting
    );
    console.log(`[${queue}] shop: ${shop} - product id: ${product.id} ai data generation successful!`);
    consumerChannel.ack(message);
  } catch (err) {
    if (err instanceof InactiveShopError) {
      logger.info(err, {
        queue,
        dispatchedMessage: JSON.parse(message.content.toString()),
        transaction: "AiOptimizer",
        domain: shop,
      });
      await handleFailedProductDataGeneration(
        shopDetails,
        product,
        approximate_credit_usage,
        JSON.stringify(err?.message || "Unknown error")
      );
      consumerChannel.ack(message);
    } else if (isAxiosError(err) && err.code !== "ECONNABORTED") {
      console.error("Failed due to axios error", err);
      logger.error("API call to generate content using AI failed", {
        description: `Failed to process product: ${product.id} for shop: ${shop} in queue: ${queue}`,
        queue,
        dispatchedMessage: JSON.parse(message.content.toString()),
        details: JSON.stringify(err.response.data),
        transaction: "AiOptimizer",
        domain: shop,
      });
      await handleFailedProductDataGeneration(
        shopDetails,
        product,
        approximate_credit_usage,
        JSON.stringify(err?.message || "Unknown error")
      );
      consumerChannel.ack(message);
    } else {
      console.error("Failed due to unknown error", err);
      logger.error(err, {
        description: `Failed to process product: ${product.id} for shop: ${shop} in queue: ${queue}`,
        queue,
        dispatchedMessage: JSON.parse(message.content.toString()),
        transaction: "AiOptimizer",
        domain: shop,
      });
      consumerChannel.nack(message);
    }
  }
}

// Extend this method as you see fit
/**
 *
 * @param {object} shop
 * @param {*} product
 * @param {*} generatedAiContent
 * @param {Array<*>} generatedAltTexts
 * @param {{optimization_setting: object, optimization_stats: object}} resourceOptimizationConfigs
 */
const handleSuccessfulProductDataGeneration = async (
  shop,
  product,
  generatedAiContent,
  generatedAltTexts,
  resourceOptimizationConfigs
) => {
  const session = await ShopService.getSession({ domain: shop?.domain });
  const { optimization_setting, optimization_stats } = resourceOptimizationConfigs;

  dispatchQueue({
    queueName: QUEUE_NAMES.BULK_OPERATION_TRACKER,
    message: {
      shop_id: shop.id,
      shopDomain: shop.domain,
      resourceType: resourceType.PRODUCT,
      opType: resourceOPType.AI_OPTIMIZATION,
      resourceId: product.product_id,
      isSuccess: true,
    },
  });

  const returendProduct = await ProductService.updateProductByCondition(
    { id: product.id },
    {
      ai_optimized_at: new Date(),
      ai_optimization_status: AiOptimizationStatus.OPTIMIZED,
    }
  );

  if (isEmpty(returendProduct)) return;

  const findProductMetaTitle = product.meta.find((item) => item.key === METAFIELD_KEYS.TITLE_TAG);
  const findProductMetaDescription = product.meta.find((item) => item.key === METAFIELD_KEYS.DESCRIPTION_TAG);
  const metaData = {
    ...(optimization_setting?.meta
      ? { metaTitle: generatedAiContent?.meta_title, metaDescription: generatedAiContent?.meta_description }
      : { metaTitle: findProductMetaTitle?.value, metaDescription: findProductMetaDescription?.value }),
    ...(optimization_setting?.tags ? { tags: generatedAiContent?.tags } : { tags: product.tags }),
  };

  await ProductService.updateProductDataToShopify(
    shop.id,
    product.id,
    {
      ...metaData,
      focusKeyword: product?.focus_keyword,
    },
    session
  );

  const update_optimization_stats = {
    ...optimization_stats,
    last_queue_process_completed_date: new Date(),
  };

  await ResourceOptimizationService.upsert({
    shop_id: shop.id,
    resource_id: product.id,
    resource_type: resourceType.PRODUCT,
    resource_op_type: resourceOPType.AI_OPTIMIZATION,
    optimization_stats: update_optimization_stats,
  });

  const updatedProduct = await ProductService.updateProductImageAltData(
    shop.id,
    product.id,
    generatedAltTexts,
    session
  );

  for (let image of generatedAltTexts) {
    const imageResourceOptimization = await ResourceOptimizationService.getByCondition({
      shop_id: shop.id,
      resource_id: image.id,
      resource_type: resourceType.PRODUCT_IMAGE,
      resource_op_type: resourceOPType.AI_OPTIMIZATION,
    });

    const alt_text_optimization_stats = {
      ...(imageResourceOptimization
        ? imageResourceOptimization.optimization_stats
        : imageOptimization.optimizationStats),
      optimization_count: imageResourceOptimization
        ? imageResourceOptimization.optimization_stats?.optimization_count + 1
        : imageOptimization.optimizationStats.optimization_count + 1,
      last_queue_process_completed_date: new Date(),
    };
    await ProductImageService.updateImage(image.id, {
      alt_text_optimized_at: new Date(),
      alt_text_optimization_status: altTextOptimizationStatus.OPTIMIZED,
    });

    await ResourceOptimizationService.upsert({
      shop_id: shop.id,
      resource_id: image.id,
      resource_type: resourceType.PRODUCT_IMAGE,
      resource_op_type: resourceOPType.AI_OPTIMIZATION,
      optimization_stats: alt_text_optimization_stats,
    });
  }

  await ProductAnalysisService.analyseEachProduct({
    product: updatedProduct,
    shopId: shop.id,
    oldProduct: product,
  });
};

// Extend this method as you see fit
const handleFailedProductDataGeneration = async (shop, product, usagesCreditsToRestore, error) => {
  dispatchQueue({
    queueName: QUEUE_NAMES.BULK_OPERATION_TRACKER,
    message: {
      shop_id: shop.id,
      shopDomain: shop.domain,
      resourceType: resourceType.PRODUCT,
      opType: resourceOPType.AI_OPTIMIZATION,
      resourceId: product.product_id,
      isSuccess: false,
      reason: error,
    },
  });

  await ProductService.updateProductByCondition(
    { id: product.id },
    {
      ai_optimization_status: AiOptimizationStatus.NOT_OPTIMIZED,
    }
  );

  decrementRelatedUsageCount(shop.domain, usagesCreditsToRestore);
};

const generateImageAltTexts = async ({ images, title, description, focusKeyword, shop, queue }) => {
  const generatedAltTexts = [];
  let totalCreditUsagesByAltText = 0;

  if (!description) {
    return { generatedAltTexts, totalCreditUsagesByAltText };
  }

  for (let img of images) {
    try {
      const { id, src } = img;
      const { data } = await AiService.generateImageAltTextGPT4o({
        domain: shop,
        images: [
          {
            id: String(id),
            src: `${src}?width=512&height=512`,
          },
        ],
        title,
        description,
        focusKeyword,
      });

      if (data.status === "success") {
        const { image_alt_texts } = data;
        try {
          if (image_alt_texts.length > 0) {
            const imageAltText = image_alt_texts[0];
            if (imageAltText.status === "success") {
              const newImageWithAltText = serializeImageAltTexts(imageAltText);
              generatedAltTexts.push({
                ...img,
                ...newImageWithAltText,
              });

              const { usage } = imageAltText;

              const creditUsage = AiService.calculateCreditUsage(usage, aiContentTypes.IMAGE);
              totalCreditUsagesByAltText += Number(creditUsage);
              console.log(`${queue} - Generated image alt-text for shop ${shop}, image id: ${img.id}`);
            }
          } else {
            console.log(`Image alt text generation failed for shop: ${shop}, image id: ${img.id}`);
          }
        } catch (error) {
          throw error;
        }
      }
    } catch (error) {
      return { generatedAltTexts, totalCreditUsagesByAltText };
    }
  }

  return { generatedAltTexts, totalCreditUsagesByAltText };
};

/**
 *
 * @param {AiOptimizationSettings} optimizationSetting
 * @returns {Array<string>}
 */
const generateContentTypes = (optimizationSetting) => {
  let contentTypes = [];

  if (optimizationSetting?.meta) {
    contentTypes.push("meta");
  }
  if (optimizationSetting?.tags) {
    contentTypes.push("tags");
  }

  return contentTypes;
};

/**
 *
 * @param {string} shopDomain
 * @param {string} value
 * @returns {Promise<void>}
 */
const incrementRelatedUsageCount = async (shopDomain, value = "4.2") => {
  await cache.addons.incrementUsageCount(shopDomain, {
    addon: subscriptionAddonGroup.AI_OPTIMIZER,
    incrementBy: value,
  });
  await cache.addons.incrementTotalUsageCount(shopDomain, {
    addon: subscriptionAddonGroup.AI_OPTIMIZER,
    incrementBy: value,
  });
  await cache.addons.incrementTotalAppUsageCount(subscriptionAddonGroup.AI_OPTIMIZER, value);
};

/**
 *
 * @param {string} shopDomain
 * @param {string} value
 * @returns {Promise<void>}
 */
const decrementRelatedUsageCount = async (shopDomain, value = "4.2") => {
  await cache.addons.decrementUsageCount(shopDomain, {
    addon: subscriptionAddonGroup.AI_OPTIMIZER,
    decrementBy: value, // Approximate value. This will sync after AI content generation
  });
  await cache.addons.decrementTotalUsageCount(shopDomain, {
    addon: subscriptionAddonGroup.AI_OPTIMIZER,
    decrementBy: value, // Approximate value. This will sync after AI content generation
  });
  await cache.addons.decrementTotalAppUsageCount(subscriptionAddonGroup.AI_OPTIMIZER, value); // Approximate value. This will sync after AI content generation
};

/**
 * @callback messageHandler
 * @param {import("amqplib").ConsumeMessage} message
 */

/**
 * @param {string} queue name of the queue
 * @return {messageHandler}
 */
function createHandler(queue) {
  return (message) => handleMessage(queue, message);
}

async function startConsumingMessages() {
  for (let i = 1; i <= NUMBER_OF_QUEUES; i++) {
    const queueName = getQueueName(i);
    const handler = createHandler(queueName);
    consumerChannel.consume(queueName, handler, {
      noAck: false,
    });
  }
}

async function bootConsumer() {
  try {
    await prepareConsumerChannel();
    await bindQueues();

    await startConsumingMessages();
  } catch (err) {
    console.log(err);
  }
}

module.exports = {
  bootConsumer,
};
