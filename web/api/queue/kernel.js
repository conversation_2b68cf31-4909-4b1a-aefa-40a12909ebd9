const ProductSyncQueue = require("./jobs/ProductSyncQueue");
const ReAnalyseProductQueue = require("./jobs/ReAnalyseProductQueue");
const ProductIndexingQueue = require("./jobs/ProductIndexingQueue");
const WebhookProductDeleteQueue = require("./jobs/WebhookProductDeleteQueue");
const SubscriptionDowngradeQueue = require("./jobs/SubscriptionDowngradeQueue");
const DeleteShopDataQueue = require("./jobs/DeleteShopDataQueue");

const PageSyncQueue = require("./jobs/pages/PageSyncQueue");
const PageMetafieldSyncQueue = require("./jobs/PageMetafieldSyncQueue");
const BlogArticlesSyncQueue = require("./jobs/BlogArticlesSyncQueue");
const ArticleMetafieldsSyncQueue = require("./jobs/ArticleMetafieldsSyncQueue");
const ProductSyncQueueWithDataMigration = require("./jobs/ProductSyncQueueWithDataMigration");
const DataMigrateQueue = require("./jobs/DataMigrateQueue");
const UpdateWebhooksRegistrationQueue = require("./jobs/UpdateWebhooksRegistrationQueue");
const WebhookAppSubscriptionUpdateQueue = require("./jobs/WebhookAppSubscriptionUpdateQueue");
const WebhookThemePublishQueue = require("./jobs/WebhookThemePublishQueue");
const WebhookShopUpdateQueue = require("./jobs/WebhookShopUpdateQueue");
const WebhookAppUninstallQueue = require("./jobs/WebhookAppUninstallQueue");
const WebhookLocationCreateQueue = require("./jobs/WebhookLocationCreateQueue");
const WebhookLocationUpdateQueue = require("./jobs/WebhookLocationUpdateQueue");
const WebhookLocationDeleteQueue = require("./jobs/WebhookLocationDeleteQueue");
const WebhookBulkOperationFinishQueue = require("./jobs/WebhookBulkOperationFinishQueue");
const SetupNewShopQueue = require("./jobs/SetupNewShopQueue");

const WebhookProductCreateQueue = require("./jobs/WebhookProductCreateQueue");
const WebhookProductUpdateQueue = require("./jobs/WebhookProductUpdateQueue");
const ProductOptimizationBulkOperationQueue = require("./jobs/ProductOptimizationBulkOperationQueue");
const FluentCrmAddContact = require("./jobs/FluentCrmAddContact");
const AddToMailchimpQueue = require("./jobs/AddToMailchimpQueue");
const WebhookImageOptimize = require("./jobs/WebhookImageOptimize");
const AutoImageOptimizerQueue = require("./jobs/AutoImageOptimizerQueue");
const AutoAiOptimizationQueue = require("./jobs/AutoAiOptimizationQueue");
const AutoAltTextOptimizerQueue = require("./jobs/AutoAltTextOptimizerQueue");

const CollectionSyncQueue = require("./jobs/collections/CollectionSyncQueue");
const WebhookCollectionDeleteQueue = require("./jobs/collections/WebhookCollectionDeleteQueue");
const WebhookCollectionUpdateQueue = require("./jobs/collections/WebhookCollectionUpdateQueue");
const WebhookCollectionCreateQueue = require("./jobs/collections/WebhookCollectionCreateQueue");
const WebhookOnetimePurchaseQueue = require("./jobs/WebhookOnetimePurchaseQueue");

const DocSyncQueue = require("./jobs/docs/DocSyncQueue");
const WebhookDocCreateQueue = require("./jobs/docs/WebhookDocCreateQueue");
const WebhookDocUpdateQueue = require("./jobs/docs/WebhookDocUpdateQueue");
const WebhookDocDeleteQueue = require("./jobs/docs/WebhookDocDeleteQueue");
const WebhookBetterDocsUninstalled = require("./jobs/docs/WebhookBetterDocsUninstallQueue");

const SendEmailQueue = require("./jobs/email/SendEmailQueue");
const EmailNotificationTriggerQueue = require("./jobs/email/EmailNotificationTriggerQueue");
const GenerateEmailQueue = require("./jobs/email/GenerateEmailQueue");

const ImageFileSizeSyncQueue = require("./jobs/ImageFileSizeSyncQueue");
const SyncShopLocalesListQueue = require("./jobs/localaizations/SyncShopLocalesListQueue");
const MultiLanguageProductSyncQueue = require("./jobs/products/MultiLanguageProductSyncQueue");
const BackupQueue = require("./jobs/BackupQueue");
const RestoreQueue = require("./jobs/RestoreQueue");
const RestoreManuallyQueue = require("./jobs/RestoreManuallyQueue");
const FocusKeywordGeneratorQueue = require("./jobs/focusKeyword/FocusKeywordGeneratorQueue");
const DefineIndustryQueue = require("./jobs/DefineIndustryQueue");
const ArticleSyncQueue = require("./jobs/articles/ArticleSyncQueue");
const BulkOperationTrackerQueue = require("./jobs/BulkOperationTrackerQueue");
const AddonUsageNotificationQueue = require("./addons/AddonUsageNotificationQueue");
const RemoveDeletedProductsQueue = require("./jobs/RemoveDeletedProductsQueue");
const AutoCollectionAiOptimizationQueue = require("./jobs/AutoCollectionAiOptimizationQueue");

module.exports = {
  combined: {
    ArticleMetafieldsSyncQueue,
    BlogArticlesSyncQueue,
    ProductSyncQueueWithDataMigration,
    MultiLanguageProductSyncQueue,
    DataMigrateQueue,
    DeleteShopDataQueue,
    PageMetafieldSyncQueue,
    ProductIndexingQueue,
    SetupNewShopQueue,
    SyncShopLocalesListQueue,
    SubscriptionDowngradeQueue,
    UpdateWebhooksRegistrationQueue,
    WebhookAppUninstallQueue,
    WebhookBulkOperationFinishQueue,
    WebhookLocationCreateQueue,
    WebhookLocationDeleteQueue,
    WebhookLocationUpdateQueue,
    WebhookProductDeleteQueue,
    WebhookShopUpdateQueue,
    WebhookThemePublishQueue,

    SendEmailQueue,

    ImageFileSizeSyncQueue,
    BackupQueue,
    RestoreQueue,
    RestoreManuallyQueue,

    FocusKeywordGeneratorQueue,
    DefineIndustryQueue,
    PageSyncQueue,
    ArticleSyncQueue,
    BulkOperationTrackerQueue,
    AddonUsageNotificationQueue,
    RemoveDeletedProductsQueue,
  },
  seperate: {
    AddToMailchimpQueue,
    AutoImageOptimizerQueue,
    AutoAltTextOptimizerQueue,
    CollectionSyncQueue,
    DocSyncQueue,
    FluentCrmAddContact,
    ProductSyncQueue,
    ProductOptimizationBulkOperationQueue,
    WebhookCollectionCreateQueue,
    WebhookCollectionDeleteQueue,
    WebhookCollectionUpdateQueue,
    WebhookProductCreateQueue,
    WebhookImageOptimize,
    WebhookProductUpdateQueue,
    WebhookAppSubscriptionUpdateQueue,
    WebhookOnetimePurchaseQueue,
    WebhookDocCreateQueue,
    WebhookDocUpdateQueue,
    WebhookDocDeleteQueue,
    WebhookBetterDocsUninstalled,
    AutoAiOptimizationQueue,
    EmailNotificationTriggerQueue,
    GenerateEmailQueue,
    ReAnalyseProductQueue,
    AutoCollectionAiOptimizationQueue,
  },
};
