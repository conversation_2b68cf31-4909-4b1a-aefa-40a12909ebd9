require("../../config");
const logger = require("storeseo-logger");
const EmailTrackerService = require("../../services/email/EmailTrackerService");
const { sleep } = require("../../utils/helper");
const { dispatchQueue } = require("../queueDispatcher");
const { QUEUE_NAMES } = require("../config");
const emailStatus = require("storeseo-enums/emailStatus");

console.log("watching emails...");

async function watchEmailsAndDispatchForDelivery() {
  try {
    const { emails, count } = await EmailTrackerService.emailsReadyForDelivery();

    logger.info(
      `[Email watcher] - found ${count} emails ready for delivery. Queuing ${emails.length} emails for delivery...`
    );

    dispatchEmails(emails);
  } catch (err) {
    console.log("err: ", err);
    logger.error(err);
  } finally {
    await sleep(2500);
    watchEmailsAndDispatchForDelivery();
  }
}
watchEmailsAndDispatchForDelivery();

async function dispatchEmails(emails) {
  for (let email of emails) {
    logger.info(
      `[Email watcher] - queuing email id: ${email.id}, topic: ${email.topic}, to: ${email.send_to} for delivery...`
    );
    dispatchQueue({
      queueName: QUEUE_NAMES.SEND_EMAIL,
      message: email,
      ttl: 2000,
    });

    await EmailTrackerService.updateEmailById(email.id, { status: emailStatus.QUEUED });
  }
}
