require("../../config");

const logger = require("storeseo-logger");
const { RABBIT_MQ_CONNECTION } = require("../index");
// const { bootConsumer } = require("./consumer");
const { bootDispatcher } = require("./dispatcher");
const { manageQueuesAndPendingImages } = require("./manager");
const { resetState } = require("./state");
const { QueueStats } = require("../../../sequelize");
const cron = require("node-cron");
const { QUEUE_BASE_NAME } = require("./config");
const cache = require("../../cache");
const { bootConsumer } = require("./consumer");

cron.schedule("0 0 * * * *", async function syncStatsIntoDb() {
  try {
    logger.info("syncing alt-text optimizer stats data from redis to database...");

    const date = await cache.altTextOptimizer.popOneDateFromPendingSyncStatisticsDates();
    if (!date) return console.log("No stats to sync!");

    const { max: maxQueueUsage, min: minQueueUsage } = await cache.altTextOptimizer.getMaxMinQueueUsageCount(date);
    const { max: maxStoreCount, min: minStoreCount } = await cache.altTextOptimizer.getMaxMinStoreCount(date);

    const imagesQueued = await cache.altTextOptimizer.imagesQueuedByDate(date);
    const imagesProcessed = await cache.altTextOptimizer.imagesProcessedByDate(date);
    const imagesFailedToProcess = await cache.altTextOptimizer.processingFailedByDate(date);
    const imagesFailed = await cache.altTextOptimizer.imagesFailedByDate(date);

    await QueueStats.upsert({
      queue_name: QUEUE_BASE_NAME,
      date,

      statistics: {
        maxQueueUsage,
        minQueueUsage,

        maxStoreCount,
        minStoreCount,

        imagesQueued,
        imagesProcessed,
        imagesFailedToProcess,
        imagesFailed,
      },
    });

    console.log("stats data synced in database");
  } catch (err) {
    logger.error(err, {
      description: "Failed to sync stats data from redis to database",
      transaction: "AltTextOptimizer",
    });
  }
});

async function startTheProcess() {
  try {
    await RABBIT_MQ_CONNECTION;
    console.log("RabbitMQ connection is ready!");

    resetState();
    await bootDispatcher();
    await bootConsumer();

    manageQueuesAndPendingImages();
  } catch (err) {
    logger.error(err);
  }
}

startTheProcess();

module.exports = {};
