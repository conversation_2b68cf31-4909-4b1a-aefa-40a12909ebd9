const { isAxiosError } = require("axios");
const { RABBIT_MQ_CONNECTION } = require("..");
const logger = require("storeseo-logger");
const ProductImageService = require("../../services/ProductImageService");
const { EXCHANGE_NAMES, EXCHANGE_TYPES, NUMBER_OF_QUEUES, PREFETCH_LIMIT } = require("./config");
const { getQueueName } = require("./helper");
const cache = require("../../cache");
const { isValidImageUrl } = require("../../utils/imageOptimizer");
const AiService = require("../../services/AiService");
const { serializeImageAltTexts } = require("../../serializers/aiContentSerializer");
const ShopService = require("../../services/ShopService");
const ProductService = require("../../services/ProductService");
const altTextOptimizationStatus = require("storeseo-enums/altTextOptimization");
const subscriptionAddonGroup = require("storeseo-enums/subscriptionAddonGroup");
const aiContentTypes = require("storeseo-enums/aiContentTypes");
const imageOptimization = require("storeseo-enums/imageOptimization");
const ResourceOptimizationService = require("../../services/resource/ResourceOptimizationService");
const resourceType = require("storeseo-enums/resourceType");
const resourceOPType = require("storeseo-enums/resourceOPType");
const { InactiveShopError } = require("../../../errors");
const { dispatchQueue } = require("../queueDispatcher");
const { QUEUE_NAMES } = require("../config");
const ShopifyService = require("../../services/ShopifyService");

/**
 * @type {import("amqplib").ConfirmChannel | null}
 */
let consumerChannel = null;

/**
 * Assert exchanges into existance for use in queue consume & dispatch
 * @param {import("amqplib").Channel} channel
 * @returns {Promise<import("amqplib").Channel} same channel provided in the input
 */
const assertExchanges = async (channel) => {
  await channel.assertExchange(EXCHANGE_NAMES.DEFAULT, EXCHANGE_TYPES.DIRECT);
  await channel.assertExchange(EXCHANGE_NAMES.EX_DELAYED, EXCHANGE_TYPES.DELAYED, {
    arguments: { "x-delayed-type": "direct" },
  });
  await channel.assertExchange(EXCHANGE_NAMES.MESSAGE_DUMP, EXCHANGE_TYPES.DIRECT);

  return channel;
};

/**
 * Create a new channel & perapre it for message dispatch
 */
const prepareConsumerChannel = async () => {
  console.log("creating consumer channel...");
  const connection = await RABBIT_MQ_CONNECTION;
  const channel = await connection.createChannel();
  await assertExchanges(channel);

  consumerChannel = channel;
  consumerChannel.prefetch(PREFETCH_LIMIT);
  console.log("Consumer channel created!");
};

async function bindQueues() {
  console.log(`Queues to bind: ${NUMBER_OF_QUEUES}`);

  for (let i = 1; i <= NUMBER_OF_QUEUES; i++) {
    const queueName = getQueueName(i);
    await consumerChannel.bindQueue(queueName, EXCHANGE_NAMES.DEFAULT, queueName);
    await consumerChannel.bindQueue(queueName, EXCHANGE_NAMES.EX_DELAYED, queueName);
  }

  console.log("Queues binded!");
}

/**
 *
 * @param {string} queue
 * @param {import("amqplib").ConsumeMessage} message
 */
async function handleMessage(queue, message) {
  const { image, shop } = JSON.parse(message.content.toString());

  try {
    await ShopifyService.hasValidSession(shop);

    const { id, src } = image;
    const { title, description, focus_keyword: focusKeyword } = image.products[0];

    if (!(await isValidImageUrl(src))) {
      await handleInvalidImage(image, shop);
      console.log(`[${queue}] shop: ${shop} - image id: ${id}, image url is invalid`);
      consumerChannel.ack(message);
      return;
    }

    const aiContentGeneratorLanguage = await cache.shop.aiContentGeneratorLanguage(shop);

    const payload = {
      domain: shop,

      title,
      description,
      focusKeyword,
      language: aiContentGeneratorLanguage.name,

      images: [
        {
          id: String(id),
          src: `${src}?width=512&height=512`,
        },
      ],
    };

    const { data } = await AiService.generateImageAltTextGPT4o(payload);

    if (data.status === "success") {
      const aiGeneratedAltText = serializeImageAltTexts(data.image_alt_texts)[0].altText;
      await handleSuccessfulAltTextGeneration(shop, image, aiGeneratedAltText);
      console.log(`[${queue}] shop: ${shop} - image id: ${id} alt-text generation successful!`);
    } else {
      await handleFailedAltTextGeneration(shop, image, JSON.stringify(data.message));
      console.log(`[${queue}] shop: ${shop} - image id: ${id} alt-text generation failed!`);
    }

    consumerChannel.ack(message);
  } catch (err) {
    console.log(`Error for image:`, image, `error:`, err);
    if (err instanceof InactiveShopError) {
      logger.info(err, {
        queue,
        dispatchedMessage: JSON.parse(message.content.toString()),
        transaction: "AltTextOptimizer",
        domain: shop,
      });
      await handleFailedAltTextGeneration(shop, image);
      consumerChannel.ack(message);
    } else if (isAxiosError(err) && err.code !== "ECONNABORTED") {
      logger.info("API call to generate alt-text using AI failed", {
        description: `Failed to process image: ${image.id} for shop: ${shop} in queue: ${queue}`,
        queue,
        dispatchedMessage: JSON.parse(message.content.toString()),
        details: err.response?.data,
        transaction: "AltTextOptimizer",
      });
      await handleFailedAltTextGeneration(shop, image, JSON.stringify(err?.message || "Unknown error"));
      consumerChannel.ack(message);
    } else {
      logger.error(err, {
        description: `Failed to process image: ${image.id} for shop: ${shop} in queue: ${queue}`,
        queue,
        dispatchedMessage: JSON.parse(message.content.toString()),
        transaction: "AltTextOptimizer",
      });
      consumerChannel.nack(message);
    }
  }
}

/**
 *
 * @param {*} image
 * @param {string} shop
 */
const handleInvalidImage = async (image, shop) => {
  try {
    await restoreCreditUsageForSingleImage(shop);
    await ProductImageService.deleteImage(image.id);
  } catch (err) {
    console.log(`Error handling invalid image - image id: ${image.id}, shop: ${shop}`, err);
  }
};

const handleSuccessfulAltTextGeneration = async (shop, image, aiGeneratedAltText) => {
  const updatedImage = {
    ...image,
    alt_text: aiGeneratedAltText,
  };

  const session = await ShopService.getSession({ domain: shop });
  const product = await ProductService.getProductDetails(session.shopId, updatedImage.products[0].id);

  dispatchQueue({
    queueName: QUEUE_NAMES.BULK_OPERATION_TRACKER,
    message: {
      shop_id: session.shopId,
      shopDomain: shop,
      resourceType: resourceType.PRODUCT_IMAGE,
      opType: resourceOPType.AI_OPTIMIZATION,
      resourceId: image.media_id,
      isSuccess: true,
    },
  });

  if (!product) {
    console.log("Not saving alt text for image as product not found!", "Image: ", JSON.stringify(image, null, 2));
    await ProductImageService.updateImage(image.id, {
      alt_text_optimization_status: altTextOptimizationStatus.NOT_OPTIMIZED,
    });
    return;
  }

  await ProductService.updateProductImageAltData(session.shopId, product.id, [updatedImage], session);

  // Update image alt text optimization status and stats
  const resourceOptimization = await ResourceOptimizationService.getByCondition({
    shop_id: session.shopId,
    resource_id: image.id,
    resource_type: resourceType.PRODUCT_IMAGE,
    resource_op_type: resourceOPType.AI_OPTIMIZATION,
  });

  const alt_text_optimization_stats = {
    ...(resourceOptimization ? resourceOptimization.optimization_stats : imageOptimization.optimizationStats),
    optimization_count: resourceOptimization
      ? resourceOptimization.optimization_stats?.optimization_count + 1
      : imageOptimization.optimizationStats.optimization_count + 1,
    last_queue_process_completed_date: new Date(),
  };
  await ProductImageService.updateImage(image.id, {
    alt_text_optimized_at: new Date(),
    alt_text_optimization_status: altTextOptimizationStatus.OPTIMIZED,
  });

  await ResourceOptimizationService.upsert({
    shop_id: session.shopId,
    resource_id: image.id,
    resource_type: resourceType.PRODUCT_IMAGE,
    resource_op_type: resourceOPType.AI_OPTIMIZATION,
    optimization_stats: alt_text_optimization_stats,
  });
};

const handleFailedAltTextGeneration = async (shop, image, error) => {
  const session = await ShopService.getSession({ domain: shop });

  dispatchQueue({
    queueName: QUEUE_NAMES.BULK_OPERATION_TRACKER,
    message: {
      shop_id: session.shopId,
      shopDomain: shop,
      resourceType: resourceType.PRODUCT_IMAGE,
      opType: resourceOPType.AI_OPTIMIZATION,
      resourceId: image.media_id,
      isSuccess: false,
      reason: error,
    },
  });

  await ProductImageService.updateImage(image.id, {
    alt_text_optimization_status: altTextOptimizationStatus.NOT_OPTIMIZED,
  });
  await restoreCreditUsageForSingleImage(shop);
};

const restoreCreditUsageForSingleImage = async (shop) => {
  const perImageCreditUsage = AiService.calculateCreditUsage({}, aiContentTypes.IMAGE);

  await cache.addons.decrementUsageCount(shop, {
    addon: subscriptionAddonGroup.AI_OPTIMIZER,
    decrementBy: perImageCreditUsage,
  });
  await cache.addons.decrementTotalUsageCount(shop, {
    addon: subscriptionAddonGroup.AI_OPTIMIZER,
    decrementBy: perImageCreditUsage,
  });
  await cache.addons.decrementTotalAppUsageCount(subscriptionAddonGroup.AI_OPTIMIZER, perImageCreditUsage);
};

/**
 * @callback messageHandler
 * @param {import("amqplib").ConsumeMessage} message
 */

/**
 * @param {string} queue name of the queue
 * @return {messageHandler}
 */
function createHandler(queue) {
  return (message) => handleMessage(queue, message);
}

async function startConsumingMessages() {
  for (let i = 1; i <= NUMBER_OF_QUEUES; i++) {
    const queueName = getQueueName(i);
    const handler = createHandler(queueName);
    consumerChannel.consume(queueName, handler, {
      noAck: false,
    });
  }
}

async function bootConsumer() {
  try {
    await prepareConsumerChannel();
    await bindQueues();

    await startConsumingMessages();
  } catch (err) {
    console.log(err);
  }
}

module.exports = {
  bootConsumer,
};
