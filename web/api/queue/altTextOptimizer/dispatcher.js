const logger = require("storeseo-logger");
const { RABBIT_MQ_CONNECTION } = require("../index");
const { NUMBER_OF_QUEUES, QUEUE_BASE_NAME, EXCHANGE_NAMES, EXCHANGE_TYPES } = require("./config");
const { getQueueName } = require("./helper");

/**
 * Assert exchanges into existance for use in queue consume & dispatch
 * @param {import("amqplib").Channel} channel
 * @returns {Promise<import("amqplib").Channel} same channel provided in the input
 */
const assertExchanges = async (channel) => {
  await channel.assertExchange(EXCHANGE_NAMES.DEFAULT, EXCHANGE_TYPES.DIRECT);
  await channel.assertExchange(EXCHANGE_NAMES.EX_DELAYED, EXCHANGE_TYPES.DELAYED, {
    arguments: { "x-delayed-type": "direct" },
  });
  await channel.assertExchange(EXCHANGE_NAMES.MESSAGE_DUMP, EXCHANGE_TYPES.DIRECT);

  return channel;
};

const declareQueue = async ({ name, exchanges, routingKeys = null, options = {} }) => {
  /**
   * @type {import("amqplib").Connection}
   */
  const connection = await RABBIT_MQ_CONNECTION;
  const channel = await connection.createChannel();

  await channel.assertQueue(name, options);

  await assertExchanges(channel);

  routingKeys = routingKeys || [name];

  for (let exchange of exchanges) {
    for (let routingKey of routingKeys) {
      await channel.bindQueue(name, exchange, routingKey);
    }
  }

  await channel.close();
};

const declareQueues = async () => {
  const { DEFAULT, EX_DELAYED } = EXCHANGE_NAMES;

  console.log(`Queues to declare: ${NUMBER_OF_QUEUES}`);

  for (let i = 1; i <= NUMBER_OF_QUEUES; i++) {
    const queueName = getQueueName(i);
    await declareQueue({ name: queueName, exchanges: [DEFAULT, EX_DELAYED] });
  }

  console.log("Queues declared!");
};

/**
 * @type {import("amqplib").ConfirmChannel | null}
 */
let dispatchChannel = null;

/**
 * Create a new channel & perapre it for message dispatch
 */
const prepareDispatchChannel = async () => {
  console.log("creating dispatch channel...");
  const connection = await RABBIT_MQ_CONNECTION;
  const channel = await connection.createConfirmChannel();
  await assertExchanges(channel);

  dispatchChannel = channel;
  console.log("Dispatch channel created!");
};

const bootDispatcher = async () => {
  await declareQueues();
  await prepareDispatchChannel();

  return true;
};

/**
 * Callback to verfiy that RabbitMQ has received the message & routed it properly
 * @param {any} message message to dispatch
 * @param {string} queue name of the target queue
 * @param {import("amqplib").Options.Publish & { ttl: number | null}} [options] additonal options
 * @returns rabbitmq confirm channel message deliver callback
 */
const messageDeliveryCallback =
  (message, queue, options = {}) =>
  (err, ok) => {
    if (err !== null) {
      logger.error(err, {
        description: `Failed to dispatch ${queue}`,
        dispatchMessage: message,
        transaction: "AltTextOptimizer",
      });
      console.log("Will try to dispatch again after 1.5 secs", { queue, dispatchMessage: message });
      setTimeout(() => dispatchMessage(message, queue, options), 1500);
    } else {
      console.log(`Dispatched ${queue}`);
    }
  };

/**
 *
 * @param {any} message message to dispatch
 * @param {string} queue name of the target queue
 * @param {import("amqplib").Options.Publish & { ttl: number | null}} [options] additonal options
 */
const dispatchMessage = (message, queue, options = {}) => {
  const { ttl, ...publishOptions } = options;

  if (ttl) {
    dispatchChannel.publish(
      EXCHANGE_NAMES.EX_DELAYED,
      queue,
      Buffer.from(JSON.stringify(message)),
      {
        persistent: true,
        ...publishOptions,
        headers: { "x-delay": ttl, ...(publishOptions.headers || {}) },
      },
      messageDeliveryCallback(message, queue, options)
    );
  } else {
    dispatchChannel.publish(
      EXCHANGE_NAMES.DEFAULT,
      queue,
      Buffer.from(JSON.stringify(message)),
      {
        persistent: true,
        ...publishOptions,
      },
      messageDeliveryCallback(message, queue, options)
    );
  }
};

module.exports = {
  bootDispatcher,
  dispatchMessage,
};
