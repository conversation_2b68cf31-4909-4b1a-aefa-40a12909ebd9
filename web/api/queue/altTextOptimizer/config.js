const { <PERSON>XCH<PERSON><PERSON>_NAMES, EXCHAN<PERSON>_TYPES, MAX_RETRY_FOR_FAILED_MESSAGES, KEYS } = require("../config");

const NUMBER_OF_QUEUES = Number(process.env.NUMBER_OF_ALT_TEXT_OPTIMIZER_QUEUES) || 3;
const QUEUE_BASE_NAME = "altText.optimizer.queue";
const MAX_MESSAGE_PER_QUEUE = 3;

const PREFETCH_LIMIT = 5;

module.exports = {
  NUMBER_OF_QUEUES,
  QUEUE_BASE_NAME,
  MAX_MESSAGE_PER_QUEUE,

  PREFETCH_LIMIT,

  EXCHANGE_NAMES,
  <PERSON>XCHANGE_TYPES,

  MAX_RETRY_FOR_FAILED_MESSAGES,

  KEYS,
};
