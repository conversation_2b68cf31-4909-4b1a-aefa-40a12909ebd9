const cache = require("../../cache");
const logger = require("storeseo-logger");
const ProductImageService = require("../../services/ProductImageService");
const ShopService = require("../../services/ShopService");
const { sleep } = require("../../utils/helper");
const { dispatchQueue } = require("../queueDispatcher");
const { MAX_MESSAGE_PER_QUEUE } = require("./config");
const { listOfEmptyQueues } = require("./state");
const altTextOptimizationStatus = require("storeseo-enums/altTextOptimization");
const moment = require("moment");

/**
 *
 * @param {string} queue
 * @param {string} shop
 * @param {any[]} images
 */
function dispatchImages(queue, shop, images) {
  for (let image of images) {
    dispatchQueue({
      queueName: queue,
      message: { image, shop },
    });
  }

  return true;
}

/**
 *
 * @param {any[]} images
 */
async function markDispatchedImages(images) {
  for (let image of images)
    await ProductImageService.updateImage(image.id, {
      alt_text_optimization_status: altTextOptimizationStatus.DISPATCHED,
    });

  return true;
}

/**
 *
 * @param {string} shop
 * @param {{ pageCount: number }} pagination
 */
async function requeueShopIfHasPendingImagesRemaining(shop, pagination) {
  if (pagination.pageCount > 1) await cache.altTextOptimizer.addStoreToPendingOptimizationQueue(shop);
}

/**
 *
 * @param {string} queue name of the queue
 * @param {string} shop myshopify domain of the shop
 */
async function allocatePendingImagesToQueue(queue, shop) {
  const shopDetails = await ShopService.getShop(shop);

  const { images = [], pagination } = await ProductImageService.getImagesByShop(shopDetails.id, {
    limit: MAX_MESSAGE_PER_QUEUE,
    alt_status: altTextOptimizationStatus.PENDING,
  });

  if (!images?.[Symbol.iterator]) return;

  const filterdImages = images?.filter((img) => img.products?.[0]?.focus_keyword);
  console.log(`Dispatching '${shop}' - ${filterdImages?.length} images to queue '${queue}'`);
  dispatchImages(queue, shop, filterdImages);
  await markDispatchedImages(filterdImages);

  if (filterdImages?.length !== images?.length) {
    pagination.pageCount++;
  }
  await requeueShopIfHasPendingImagesRemaining(shop, pagination);
}

async function manageQueuesAndPendingImages() {
  try {
    const queues = listOfEmptyQueues();
    const pendingStoresCount = await cache.altTextOptimizer.numberOfStoresPendingOptimization();

    if (queues.length === 0) console.log("All queues are busy now...");
    else console.log(`\nFree queues: ${JSON.stringify(queues, null, 2)}`);

    let queueUsageCount = 0;
    for (let queue of queues) {
      const shop = await cache.altTextOptimizer.getNextStoreFromPendingOptimizationQueue();
      if (shop) {
        await allocatePendingImagesToQueue(queue, shop);
        queueUsageCount++;
      } else {
        console.log("No shops are in 'pending image optimization' list!");
        break;
      }
    }

    await cache.altTextOptimizer.updateMaxMinQueueUsageStats({
      pendingStoresCount,
      queueUsageCount,
    });
    await cache.altTextOptimizer.addToPendingSyncStatisticDates(moment().format("YYYY-MM-DD"));
  } catch (err) {
    logger.error(err, {
      description: "Error in queue manager",
      transaction: "AltTextOptimizer",
    });
    console.log(err);
  } finally {
    await sleep(3000);
    await manageQueuesAndPendingImages();
  }
}

module.exports = {
  manageQueuesAndPendingImages,
};
