const amqp = require("amqplib");

const { CONNECTION_CONFIG } = require("./config");

const createConnection = async (name = "APP_SERVER") => {
  try {
    const conn = await amqp.connect(`amqp://${CONNECTION_CONFIG.host}:${CONNECTION_CONFIG.port}`, {
      credentials: amqp.credentials.plain(`${CONNECTION_CONFIG.username}`, `${CONNECTION_CONFIG.password}`),
      clientProperties: { connection_name: name },
    });

    // console.log("> RabbitMQ connection success =>", `${CONNECTION_CONFIG.host}:${CONNECTION_CONFIG.port}`);

    return conn;
  } catch (err) {
    console.error("RabbitMQ connection failed to", `${CONNECTION_CONFIG.host}:${CONNECTION_CONFIG.port}`, err);
    return false;
  }
};

const RABBIT_MQ_CONNECTION = createConnection(process.env?.QUEUE_CONN_NAME);

const runQueue = async (Queue, log = true) => {
  await Queue.startConsumingMessages();

  if (log) console.log("Running:", Queue.config.queueName);
};

const runQueues = async (queues) => {
  let sl = 1;
  for (let queue of queues.sort((a, b) => a.config.queueName.localeCompare(b.config.queueName))) {
    try {
      await runQueue(queue, false);
      console.log(sl, `${queue.config.queueName} -- running, prefetch=${queue.config.prefetch}`);
      sl++;
    } catch (error) {
      console.error(sl, "Error:", error);
    }
  }
};

module.exports = {
  createConnection,
  RABBIT_MQ_CONNECTION,
  runQueue,
  runQueues,
};
