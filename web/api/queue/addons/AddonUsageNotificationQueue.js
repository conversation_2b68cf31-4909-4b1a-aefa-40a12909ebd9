const { RABBIT_MQ_CONNECTION } = require("..");
const { QUEUE_NAMES, PREFETCH_LIMITS } = require("../config");
const cache = require("../../cache");

const BaseQueue = require("../jobs/BaseQueue");
const { calculateUsagePercentage } = require("../../utils/addonUtils");
const {
  imageOptimizerUsageNotification,
  defaultEmailNotificationSettings,
  aiOptimizerUsageNotification,
} = require("../../config/email-notification");
const { dispatchQueue } = require("../queueDispatcher");
const eventTopics = require("storeseo-enums/eventTopics");
const ShopService = require("../../services/ShopService");
const { EMAIL_NOTIFICATION } = require("storeseo-enums/settings/email-notification");
const { IMAGE_OPTIMIZER, AI_OPTIMIZER } = require("storeseo-enums/cacheKeys");

class AddonUsageQueue extends BaseQueue {
  async handle(message, channel, decodedToJSON) {
    const decodedMessage = decodedToJSON(message);

    try {
      const { shopDomain, addonGroup } = decodedMessage;

      const shop = await ShopService.getShopByCondition({ domain: shopDomain });

      if (!shop) {
        console.log(`[${this.queueName}] - shop not found for addon: ${addonGroup} for shop: ${shopDomain}`);
        return null;
      }

      let emailNotificationSettings = (await ShopService.getShopSetting(shop.id, EMAIL_NOTIFICATION)) ?? null;
      if (!emailNotificationSettings) {
        emailNotificationSettings =
          (await ShopService.updateShopSetting(shop.id, defaultEmailNotificationSettings)) ?? null;
      }

      console.log(
        `[${this.config.queueName}] - processing addon usage notification for addon: ${addonGroup} for shop: ${shopDomain}`
      );

      if (addonGroup === IMAGE_OPTIMIZER) {
        await this.triggerImageOptimizerUsageEmailQueue(shop, emailNotificationSettings);
      } else if (addonGroup === AI_OPTIMIZER) {
        await this.triggerAiContentOptimizerUsageEmailQueue(shop, emailNotificationSettings);
      }
    } catch (err) {
      console.error(`${this.config.queueName} failed. \n Message: ${err.message}`, err);
      await this.handleError(err, message);
    } finally {
      channel.ack(message);
    }
  }

  async triggerImageOptimizerUsageEmailQueue(shop, emailNotificationSettings) {
    const settingsItems = emailNotificationSettings?.value?.items ?? {};
    const imageOptimizerUsageNotificationSettings = settingsItems[eventTopics.IMAGE_OPTIMIZER_USAGE_NOTIFICATION];

    if (!imageOptimizerUsageNotificationSettings) return null;

    if (imageOptimizerUsageNotificationSettings?.enabled) {
      const currentUsage = await cache.addons.usageCount(shop.domain, { addon: IMAGE_OPTIMIZER });
      const usageLimit = await cache.addons.usageLimit(shop.domain, { addon: IMAGE_OPTIMIZER });

      const usagePercentage = await calculateUsagePercentage(currentUsage, usageLimit);

      const lastEmailSentUsagePercentage = Number(
        await cache.addons.lastEmailSentForUsagePercentage(shop.domain, {
          addon: IMAGE_OPTIMIZER,
        })
      );

      const usageLimitThresholds = Object.values(imageOptimizerUsageNotification).sort((a, b) => b - a);

      const nextThreshold =
        usageLimitThresholds.find(
          (threshold) => usagePercentage >= threshold && threshold > lastEmailSentUsagePercentage
        ) ?? null;

      if (nextThreshold) {
        await cache.addons.lastEmailSentForUsagePercentage(shop.domain, {
          addon: IMAGE_OPTIMIZER,
          usagePerecentage: nextThreshold,
        });

        dispatchQueue({
          queueName: QUEUE_NAMES.GENERATE_EMAIL_QUEUE,
          message: {
            shopDomain: shop.domain,
            topic: eventTopics.IMAGE_OPTIMIZER_USAGE_NOTIFICATION,
            deliveryDate: new Date(),
          },
        });
      } else {
        console.log(
          `[${this.config.queueName} - Image Optimizer Usage Notification Email already sent or next threshold have not been reached for ${shop.domain}`
        );
      }
    } else {
      console.log(
        ` [${this.config.queueName}] - ${eventTopics.IMAGE_OPTIMIZER_USAGE_NOTIFICATION} Notification Settings Disabled for shop: ${shop.domain}`
      );
    }
  }

  async triggerAiContentOptimizerUsageEmailQueue(shop, emailNotificationSettings) {
    const settingsItems = emailNotificationSettings?.value?.items ?? {};
    const imageOptimizerUsageNotificationSettings = settingsItems[eventTopics.AI_CONTENT_OPTIMIZER_USAGE_NOTIFICATION];

    if (!imageOptimizerUsageNotificationSettings) return null;

    if (imageOptimizerUsageNotificationSettings?.enabled) {
      const currentUsage = Number(await cache.addons.usageCount(shop.domain, { addon: AI_OPTIMIZER }));
      const usageLimit = await cache.addons.usageLimit(shop.domain, { addon: AI_OPTIMIZER });
      const usagePercentage = await calculateUsagePercentage(currentUsage, usageLimit);

      const lastEmailSentUsagePercentage = Number(
        await cache.addons.lastEmailSentForUsagePercentage(shop.domain, {
          addon: AI_OPTIMIZER,
        })
      );

      const usageLimitThresholds = Object.values(aiOptimizerUsageNotification).sort((a, b) => b - a);

      const nextThreshold =
        usageLimitThresholds.find(
          (threshold) => usagePercentage >= threshold && threshold > lastEmailSentUsagePercentage
        ) ?? null;

      if (nextThreshold) {
        await cache.addons.lastEmailSentForUsagePercentage(shop.domain, {
          addon: AI_OPTIMIZER,
          usagePerecentage: nextThreshold,
        });
        dispatchQueue({
          queueName: QUEUE_NAMES.GENERATE_EMAIL_QUEUE,
          message: {
            shopDomain: shop.domain,
            topic: eventTopics.AI_CONTENT_OPTIMIZER_USAGE_NOTIFICATION,
            deliveryDate: new Date(),
          },
        });
      } else {
        console.log(
          `[${this.config.queueName} - AI Content Optimizer Usage Notification Email already sent or next threshold have not been reached for ${shop.domain}`
        );
      }
    } else {
      console.log(
        ` [${this.config.queueName}] - ${eventTopics.AI_CONTENT_OPTIMIZER_USAGE_NOTIFICATION} Notification Settings Disabled for shop: ${shop.domain}`
      );
    }
  }
}

module.exports = new AddonUsageQueue(
  {
    queueName: QUEUE_NAMES.ADDON_USAGE_NOTIFICATION_QUEUE,
    prefetch: PREFETCH_LIMITS[QUEUE_NAMES.ADDON_USAGE_NOTIFICATION_QUEUE],
  },
  RABBIT_MQ_CONNECTION
);
