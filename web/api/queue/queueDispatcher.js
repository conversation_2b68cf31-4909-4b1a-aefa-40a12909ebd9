const { RABBIT_MQ_CONNECTION } = require(".");
const { QUEUE_NAMES, EXCHANGE_NAMES, EXCHANGE_TYPES } = require("./config");

let queuesDeclaredAndReady = false;

/**
 * @type {{ message: any, queueName: string, ttl: number | null, options: import("amqplib").Options.Publish}[]}
 */
const messagesToDispatch = [];

/**
 * @type {import("amqplib").ConfirmChannel | null}
 */
let dispatchChannel = null;

const DISPATCH_BATCH_SIZE = 250;
const MAX_IDLE_INVOKE_COUNT = 10;

let idleInvokeCount = 0;

let dispatchManagerStarted = false;

/**
 * current state of the dispatch manager
 * @type {"STOPPED" | "RUNNING" | "BOOTING"}
 */
let dispatchManagerState = "STOPPED";

/**
 * Assert exchanges into existance for use in queue consume & dispatch
 * @param {import("amqplib").Channel} channel
 * @returns {Promise<import("amqplib").Channel} same channel provided in the input
 */
const createExchanges = async (channel) => {
  await channel.assertExchange(EXCHANGE_NAMES.DEFAULT, EXCHANGE_TYPES.DIRECT);
  await channel.assertExchange(EXCHANGE_NAMES.EX_DELAYED, EXCHANGE_TYPES.DELAYED, {
    arguments: { "x-delayed-type": "direct" },
  });
  await channel.assertExchange(EXCHANGE_NAMES.MESSAGE_DUMP, EXCHANGE_TYPES.DIRECT);

  return channel;
};

/**
 * Prepare queue with exchange & routing key after asserting the queue into existance
 * @param {QueueDeclarationInput} input
 * @returns {Promise<void>}
 */
const declareQueue = async ({ queueName, exchanges, routingKeys = null, options = {} }) => {
  // Wait for the RabbitMQ connection to be ready
  const connection = await RABBIT_MQ_CONNECTION;

  // Check if connection is valid before proceeding
  if (!connection) {
    console.error(`Cannot declare queue '${queueName}': RabbitMQ connection is not available`);
    throw new Error("RabbitMQ connection is not available");
  }

  const channel = await connection.createChannel();

  await channel.assertQueue(queueName, options);

  await createExchanges(channel);

  routingKeys = routingKeys || [queueName];

  for (let exchange of exchanges) {
    for (let routingKey of routingKeys) {
      await channel.bindQueue(queueName, exchange, routingKey);
    }
  }

  await channel.close();
};

/**
 * Prepare & create all the queues with proper routing
 */
const declareQueues = async () => {
  if (queuesDeclaredAndReady) return true;

  const { MESSAGE_DUMP_QUEUE, ...queues } = QUEUE_NAMES;
  const { DEFAULT, EX_DELAYED, MESSAGE_DUMP } = EXCHANGE_NAMES;

  for (let queueName of Object.values(queues)) {
    await declareQueue({ queueName, exchanges: [DEFAULT, EX_DELAYED] });
  }

  // declare the message dump queue
  // await declareQueue({
  //   queueName: MESSAGE_DUMP_QUEUE,
  //   options: {
  //     deadLetterExchange: DEFAULT,
  //   },
  //   exchanges: [MESSAGE_DUMP, EX_DELAYED],
  //   routingKeys: Object.values(queues),
  // });

  console.log("queues declared and ready!");
  return (queuesDeclaredAndReady = true);
};

/**
 * Create a new channel & prepare it for message dispatch
 * @returns {Promise<void>}
 */
const prepareDispatchChannel = async () => {
  if (dispatchChannel) {
    console.log("Dispatch channel already exists");
    return;
  }

  // Wait for the RabbitMQ connection to be ready
  const connection = await RABBIT_MQ_CONNECTION;

  // Check if connection is valid before proceeding
  if (!connection) {
    console.error("Cannot create dispatch channel: RabbitMQ connection is not available");
    throw new Error("RabbitMQ connection is not available");
  }

  const channel = await connection.createConfirmChannel();
  await createExchanges(channel);

  dispatchChannel = channel;
  console.log("Dispatch channel created successfully");

  // Set up channel error handlers
  dispatchChannel.on("error", (err) => {
    console.error("Dispatch channel error:", err);
    dispatchChannel = null;
  });

  dispatchChannel.on("close", () => {
    console.warn("Dispatch channel closed");
    dispatchChannel = null;
  });
};

/**
 * Callback to verfiy that RabbitMQ has received the message & routed it properly
 * @param {{ message: any, queueName: string, ttl?: number, options: import("amqplib").Options.Publish }} message
 * @returns rabbitmq confirm channel message deliver callback
 */
const messageDeliveryCallback = (message) => (err, ok) => {
  if (err !== null) {
    console.error("Failed to dispatch: ", message.message, err);
    console.log("will try to dispatch again after 1.5 secs");
    // messagesToDispatch.push(message);
    setTimeout(() => handleDispatch(message), 1500);
  } else {
    console.log("dispatched ", message.queueName);
  }
};

/**
 * close the dispatch channel
 */
const closeDispatchChannel = async () => {
  const channel = dispatchChannel;
  dispatchChannel = null;

  await channel?.close();
};

/**
 *
 * @param {{ message: any, queueName: string, ttl: number | null, options: import("amqplib").Options.Publish }} messageDetails
 */
const handleDispatch = (messageDetails) => {
  const { message, queueName, ttl = null, options = {} } = messageDetails;

  if (ttl) {
    dispatchChannel.publish(
      EXCHANGE_NAMES.EX_DELAYED,
      queueName,
      Buffer.from(JSON.stringify(message)),
      {
        persistent: true,
        ...options,
        headers: { "x-delay": ttl, ...(options.headers || {}) },
        // expiration: 10,
      },
      messageDeliveryCallback(messageDetails)
    );
  } else {
    dispatchChannel.publish(
      // EXCHANGE_NAMES.MESSAGE_DUMP,
      EXCHANGE_NAMES.DEFAULT,
      queueName,
      Buffer.from(JSON.stringify(message)),
      {
        persistent: true,
        ...options,
        // expiration: 10,
      },
      messageDeliveryCallback(messageDetails)
    );
  }
};

const handleDispatchByBatch = async () => {
  await prepareDispatchChannel();

  const dispatchSize = Math.min(DISPATCH_BATCH_SIZE, messagesToDispatch.length);

  for (let i = 0; i < dispatchSize; i++) {
    const message = messagesToDispatch.shift();
    handleDispatch(message);
  }

  return;
};

const sendUndispatchedMessages = () => {
  console.log("sending undispatched messages....");
  while (messagesToDispatch.length) {
    const messageDetails = messagesToDispatch.shift();
    handleDispatch(messageDetails);
  }
  console.log("Done!");
};

const startDispatchManager = async () => {
  idleInvokeCount = messagesToDispatch.length ? 0 : idleInvokeCount + 1;

  console.log(
    "dispatch manager running...",
    Math.min(messagesToDispatch.length, DISPATCH_BATCH_SIZE),
    "/",
    messagesToDispatch.length
  );

  if (idleInvokeCount >= MAX_IDLE_INVOKE_COUNT) {
    console.log(
      "dispatch manager invoked",
      idleInvokeCount,
      "times witout any message to dispatch! "
      // "Closing channel..."
    );
    // await closeDispatchChannel();
    dispatchManagerStarted = false;
    return;
  }

  await declareQueues();
  await handleDispatchByBatch();
  setTimeout(startDispatchManager, 1000);
};

/**
 * Boot the dispatch manager and ensure RabbitMQ connection is ready
 * @returns {Promise<void>}
 */
const bootDispatchManager = async () => {
  dispatchManagerState = "BOOTING";
  console.log("Booting dispatch manager...");

  try {
    // Wait for the RabbitMQ connection to be ready
    const connection = await RABBIT_MQ_CONNECTION;

    // Check if connection is valid
    if (!connection) {
      console.error("Failed to boot dispatch manager: RabbitMQ connection is not available");
      dispatchManagerState = "STOPPED";
      throw new Error("RabbitMQ connection is not available");
    }

    console.log("RabbitMQ connection is ready");

    // Proceed with queue declaration and channel preparation
    await declareQueues();
    await prepareDispatchChannel();

    dispatchManagerState = "RUNNING";
    console.log("Dispatch manager is now running");
    sendUndispatchedMessages();
  } catch (error) {
    console.error("Failed to boot dispatch manager:", error);
    dispatchManagerState = "STOPPED";
    throw error;
  }
};

/**
 * Dispatch a message to a queue
 * @param {{ queueName: string, message: any, ttl?: number | null, options?: import("amqplib").Options.Publish}} param0
 * @returns {Promise<void>}
 */
const dispatchQueue = async ({ queueName, message, ttl = null, options = {} }) => {
  // If dispatch manager is running, handle the dispatch immediately
  if (dispatchManagerState === "RUNNING") {
    return handleDispatch({ message, queueName, ttl, options });
  }

  // If dispatch manager is stopped, try to boot it
  if (dispatchManagerState === "STOPPED") {
    try {
      await bootDispatchManager();
      return handleDispatch({ message, queueName, ttl, options });
    } catch (error) {
      console.error(`Failed to dispatch message to queue '${queueName}':`, error);
      // Even if boot fails, we'll still queue the message for later dispatch
    }
  }

  // If we're here, either the dispatch manager is booting or we failed to boot it
  // Queue the message for later dispatch
  console.log(`Queuing message for later dispatch to '${queueName}'`);
  messagesToDispatch.push({
    queueName,
    message,
    ttl,
    options,
  });
};

module.exports = {
  dispatchQueue,
};
