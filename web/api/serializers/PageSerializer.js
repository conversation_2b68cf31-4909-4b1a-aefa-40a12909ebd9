const { extractShopifyIdFromGqlId } = require("../utils/helper");

/**
 *
 * @param {number | string} shopId DB shop id for the relevant shop
 * @param {*} page page details from Shopify API
 */
const serializeShopifyPageData = (shopId, page) => {
  return {
    shop_id: shopId,
    page_id: page.id.replace(/.*\//gim, ""),
    title: page.title,
    handle: page.handle,
    author: page.author,
    body_html: page.body,
    template_suffix: page.templateSuffix,
    published_at: page.publishedAt,
    is_synced: true,

    meta: serializeShopifyPageMetaFields(page.metafields) || [],
  };
};

const serializeShopifyPageMetaFields = (metafields) => {
  return metafields?.edges?.map(({ node }) => {
    return {
      ...node,
      id: node.id.replace(/.*\//gim, ""),
      key: node.key.split(".").reverse()[0],
    };
  });
};

const serializPageMetas = (metas) =>
  metas.map((m) => ({
    id: m.gql_id,
    namespace: m.namespace,
    key: m.key,
    value: m.value,
    type: m.type,
  }));

const serializePageSitemap = (res) => {
  let page = res.toJSON();
  return {
    ...page,
    status: page.sitemap?.sitemap_disabled === 0,
    noFollow: page.sitemap?.no_follow === 1,
    noIndex: page.sitemap?.no_index === 1,
    isChecked: false,
    shopifyId: page.page_id,
  };
};

module.exports = {
  serializeShopifyPageData,
  serializPageMetas,
  serializePageSitemap,
};
