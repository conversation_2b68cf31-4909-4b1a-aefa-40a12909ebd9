// @ts-check
const { pick, isEmpty } = require("lodash");
const imageOptimization = require("storeseo-enums/imageOptimization");
const { extractShopifyIdFromGqlId, getPercentValue, calculateIsOptimized } = require("../utils/helper");
const SCORES = require("../config/collectionSeoScores");
const { NAMESPACE, METAFIELD_KEYS, METAFIELD_TYPES } = require("storeseo-enums/metafields");

/**
 * Serialize Shopify Collection data to the DB structure
 * @param {*} shopId - Shopify Shop ID
 * @param {*} collection - Shopify Collection data
 * @returns
 */
const serializeShopifyCollection = (shopId, collection) => ({
  shop_id: shopId,
  collection_id: collection.id,
  handle: collection.handle,
  title: collection.title,
  description: collection.descriptionHtml,
  image_id: collection?.image?.id,
  products_count: collection?.productsCount?.count,
});
/**
 * Serialize Shopify Collection Metafields
 * @param {*} shopifyCollection - Shopify Collection data
 * @returns {Array}
 */
const serializeShopifyCollectionMetas = (shopifyCollection) => {
  // serialize global metafields data
  const metadata =
    shopifyCollection?.metafields?.edges.length > 0
      ? shopifyCollection?.metafields?.edges
          .map((meta) => ({ ...meta.node, key: meta.node.key.replace("global.", "") }))
          .filter((meta) => meta.key == METAFIELD_KEYS.TITLE_TAG || meta.key == METAFIELD_KEYS.DESCRIPTION_TAG) // do not use === here
      : [];

  // serialize hidden metafields data
  if (!isEmpty(shopifyCollection?.metafield)) {
    metadata.push(shopifyCollection.metafield);
  }

  // serialize store_seo custom metafields data
  shopifyCollection.storeSEOMetaFields?.edges?.forEach((m) => metadata.push(m.node));
  return metadata;
};

/**
 * Serialize Shopify Collection Image data to the DB structure
 * @param {*} shopId
 * @param {*} collectionId
 * @param {*} shopifyCollection
 * @returns
 */
const serializeShopifyCollectionImage = (shopId, collectionId, shopifyCollection) => {
  const image = shopifyCollection?.image;
  if (!image) return null;
  return {
    shop_id: shopId,
    collection_id: collectionId,
    gql_id: image.id,
    src: image.url || image.src,
    alt_text: image.altText,
  };
};

/**
 * Serialize DB Collection data to the JSON column structure
 * @param {*} collection - DB Collection data
 * @returns
 */
const serializeSingleCollection = (collection) => {
  return {
    ...collection,
    analysis: serializeCollectionAnalysis(collection?.analysisData),
    meta: serializeCollectionMetas(collection?.meta),
    image: serializeCollectionSingleImage(collection?.img),
    ...getSocialMediaPreviewImages(collection),
    shopifyId: extractShopifyIdFromGqlId(collection.collection_id),
  };
};

/**
 * Serialize db Collections metas to the JSON column structure
 * @returns {{id: *}}
 * @param meta
 */
const serializeCollectionMetas = (meta) => {
  return (
    (meta &&
      meta.map((meta) => ({
        id: meta.gql_id,
        ...pick(meta, ["key", "type", "namespace", "value", "description"]),
      }))) ||
    []
  );
};

/**
 * Serialize DB Collections Analysis to the JSON column structure
 * @param {*} analysis
 * @returns
 */
const serializeCollectionAnalysis = (analysis) => {
  return {
    // Basic SEO
    unique_focus_keyword: analysis?.unique_focus_keyword || 0,
    focus_keyword_in_meta_title: analysis?.focus_keyword_in_meta_title || 0,
    meta_desc_within_char_limit: analysis?.meta_desc_within_char_limit || 0,
    meta_title_within_char_limit: analysis?.meta_title_within_char_limit || 0,
    // Detailed SEO
    focus_keyword_in_title: analysis?.focus_keyword_in_title || 0,
    focus_keyword_in_meta_desc: analysis?.focus_keyword_in_meta_desc || 0,
    focus_keyword_in_url: analysis?.focus_keyword_in_url || 0,
  };
};

/**
 * Serialize DB Collections Image to the JSON column structure
 * @param {*} image - Collection Image data from DB
 * @returns
 */
const serializeCollectionSingleImage = (image) => {
  if (!image) return null;
  return {
    ...image,
    src: image.src,
    altText: image.alt_text,
    position: image.position,
    isOptimized: image.optimization_status === imageOptimization.OPTIMIZED,
    isAlreadyOptimized: image.optimization_status === imageOptimization.ALREADY_OPTIMIZED,
  };
};
/**
 * Get Social Media Preview Images from DB Collections image and metafields
 * @param {*} collection - Collection data from DB
 * @returns
 */
const getSocialMediaPreviewImages = (collection) => {
  const featuredImage = serializeCollectionSingleImage(collection?.img)?.src || null;
  let facebookPreviewImage = featuredImage;
  let twitterPreviewImage = featuredImage;

  try {
    collection.meta.forEach((m) => {
      if (m.namespace === NAMESPACE.STORE_SEO && m.key === METAFIELD_KEYS.FACEBOOK_PREVIEW_IMAGE_URL) {
        facebookPreviewImage = m.value;
      } else if (m.namespace === NAMESPACE.STORE_SEO && m.key === METAFIELD_KEYS.TWITTER_PREVIEW_IMAGE_URL) {
        twitterPreviewImage = m.value;
      }
    });
  } catch (err) {}

  return { facebookPreviewImage, twitterPreviewImage };
};

const serializeCollectionSitemap = (res) => {
  let collection = res.toJSON();
  return {
    ...collection,
    featuredImage: serializeCollectionSingleImage(collection?.img),
    status: collection.sitemap?.sitemap_disabled === 0,
    noFollow: collection.sitemap?.no_follow === 1,
    noIndex: collection.sitemap?.no_index === 1,
    isChecked: false,
    shopifyId: extractShopifyIdFromGqlId(collection.collection_id),
  };
};

const serializeCollectionOptimizationDetails = (analysis) => {
  return [
    {
      title: "Basic SEO Analysis",
      key: "BASIC_SEO",
      values: [
        {
          key: "UNIQUE_FOCUS_KEYWORD",
          title: "Focus keyword is unique",
          value: analysis.unique_focus_keyword,
          percent: getPercentValue(analysis.unique_focus_keyword, SCORES.UNIQUE_FOCUS_KEYWORD),
          isOptimized: calculateIsOptimized(analysis.unique_focus_keyword, SCORES.UNIQUE_FOCUS_KEYWORD),
        },
        {
          key: "FOCUS_KEYWORD_IN_META_TITLE",
          title: "Focus keyword is used in the meta title",
          value: analysis.focus_keyword_in_meta_title,
          percent: getPercentValue(analysis.focus_keyword_in_meta_title, SCORES.FOCUS_KEYWORD_IN_META_TITLE),
          isOptimized: calculateIsOptimized(analysis.focus_keyword_in_meta_title, SCORES.FOCUS_KEYWORD_IN_META_TITLE),
        },
        {
          key: "META_DESC_WITHIN_CHAR_LIMIT",
          title: "Meta description is within 80-165 characters",
          value: analysis.meta_desc_within_char_limit,
          percent: getPercentValue(analysis.meta_desc_within_char_limit, SCORES.META_DESC_WITHIN_CHAR_LIMIT),
          isOptimized: calculateIsOptimized(analysis.meta_desc_within_char_limit, SCORES.META_DESC_WITHIN_CHAR_LIMIT),
          hint: "To get a higher score, write meta description using 165 characters",
        },
        {
          key: "META_TITLE_WITHIN_CHAR_LIMIT",
          title: "Collection Meta title is within 30-70 characters",
          value: analysis.meta_title_within_char_limit,
          percent: getPercentValue(analysis.meta_title_within_char_limit, SCORES.META_TITLE_WITHIN_CHAR_LIMIT),
          isOptimized: calculateIsOptimized(analysis.meta_title_within_char_limit, SCORES.META_TITLE_WITHIN_CHAR_LIMIT),
          hint: "To get a higher SEO score, make your Meta Title 70 characters long!",
        },
      ],
    },
    {
      title: "Detailed SEO Analysis",
      key: "DETAILED_SEO",
      values: [
        {
          key: "FOCUS_KEYWORD_IN_TITLE",
          title: "Focus keyword is used in the Collection title",
          value: analysis.focus_keyword_in_title,
          percent: getPercentValue(analysis.focus_keyword_in_title, SCORES.FOCUS_KEYWORD_IN_TITLE),
          isOptimized: calculateIsOptimized(analysis.focus_keyword_in_title, SCORES.FOCUS_KEYWORD_IN_TITLE),
        },
        {
          key: "FOCUS_KEYWORD_IN_META_DESC",
          title: "Focus keyword found in meta description",
          value: analysis.focus_keyword_in_meta_desc,
          percent: getPercentValue(analysis.focus_keyword_in_meta_desc, SCORES.FOCUS_KEYWORD_IN_META_DESC),
          isOptimized: calculateIsOptimized(analysis.focus_keyword_in_meta_desc, SCORES.FOCUS_KEYWORD_IN_META_DESC),
        },

        {
          key: "FOCUS_KEYWORD_IN_URL",
          title: "Focus keyword is used in the URL",
          value: analysis?.focus_keyword_in_url,
          percent: getPercentValue(analysis?.focus_keyword_in_url, SCORES.FOCUS_KEYWORD_IN_URL),
          isOptimized: calculateIsOptimized(analysis?.focus_keyword_in_url, SCORES.FOCUS_KEYWORD_IN_URL),
        },
      ],
    },
  ];
};

/**
 * Serialize collection meta data
 * @param collection
 * @param metaTitle
 * @param metaDescription
 * @returns {*[]}
 */
const serializeCollectionUpdatedMetadata = (collection, { metaTitle, metaDescription }) => {
  const metadata = [];

  const collectionMetaTitle = !isEmpty(collection.meta)
    ? collection.meta.find((md) => md.key === METAFIELD_KEYS.TITLE_TAG)
    : null;

  if (metaTitle !== undefined) {
    if (!isEmpty(collectionMetaTitle) && collectionMetaTitle.hasOwnProperty("id")) {
      metadata.push({ ...pick(collectionMetaTitle, ["id", "key"]), value: metaTitle });
    } else {
      metadata.push({
        namespace: NAMESPACE.GLOBAL,
        key: METAFIELD_KEYS.TITLE_TAG,
        value: metaTitle,
        type: METAFIELD_TYPES.STRING,
      });
    }
  }

  if (metaDescription !== undefined) {
    const collectionMetaDescription = !isEmpty(collection.meta)
      ? collection.meta.find((md) => md.key === METAFIELD_KEYS.DESCRIPTION_TAG)
      : null;
    if (!isEmpty(collectionMetaDescription) && collectionMetaDescription.hasOwnProperty("id")) {
      metadata.push({ ...pick(collectionMetaDescription, ["id", "key"]), value: metaDescription });
    } else {
      metadata.push({
        namespace: NAMESPACE.GLOBAL,
        key: METAFIELD_KEYS.DESCRIPTION_TAG,
        value: metaDescription,
        type: METAFIELD_TYPES.STRING,
      });
    }
  }

  return metadata;
};

/**
 * Serialize Collection data to the JSON column structure for frontend list view
 * @param {*} collection
 * @returns
 */
const serializeCollectionToListItem = (collection) => {
  return {
    ...collection,
    featuredImage: serializeCollectionSingleImage(collection?.img),
    ...getSocialMediaPreviewImages(collection),
    shopifyId: extractShopifyIdFromGqlId(collection.collection_id),
  };
};

/**
 * Format collection image by augmenting image object with the fileName
 * @param {object} image
 * @returns {object}
 */
const formatCollectionImage = (image) => ({
  ...image,
  fileName: image.src.split("/").pop(),
  media_id: image.gql_id,
  resources: [{ ...image?.collection, resource_id: image?.collection?.collection_id }],
  optimization_meta: image?.resourceOptimizationMeta?.optimization_meta,
});

/**
 * Format collection images by augmenting image object with the fileName
 * @param {Array<object>} images
 * @returns {Array<object> | null}
 */
const formatCollectionImages = (images) => {
  if (!images) return null;
  return images.map(formatCollectionImage);
};

module.exports = {
  serializeCollectionToListItem,
  serializeShopifyCollection,
  serializeShopifyCollectionMetas,
  serializeShopifyCollectionImage,
  serializeSingleCollection,
  serializeCollectionMetas,
  serializeCollectionAnalysis,
  serializeCollectionOptimizationDetails,
  serializeCollectionUpdatedMetadata,
  serializeCollectionSitemap,
  formatCollectionImage,
  formatCollectionImages,
};
