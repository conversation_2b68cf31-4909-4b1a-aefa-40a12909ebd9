/**
 * Serialize Product analysis to the JSON column structure
 * @param analysis
 */

const serializeProductAnalysis = (analysis) => {
  return {
    // Basic SEO
    unique_focus_keyword: analysis?.unique_focus_keyword || 0,
    focus_keyword_in_meta_title: analysis?.focus_keyword_in_meta_title || 0,
    meta_desc_within_char_limit: analysis?.meta_desc_within_char_limit || 0,
    meta_title_within_char_limit: analysis?.meta_title_within_char_limit || 0,
    unique_title: analysis?.unique_title || 0,
    // Detailed SEO
    focus_keyword_in_img_alt_text: analysis?.focus_keyword_in_img_alt_text || 0,
    focus_keyword_in_title: analysis?.focus_keyword_in_title || 0,
    focus_keyword_in_meta_desc: analysis?.focus_keyword_in_meta_desc || 0,
    focus_keyword_in_url: analysis?.focus_keyword_in_url || 0,
    focus_keyword_at_the_beginning_of_meta_title: analysis?.focus_keyword_at_the_beginning_of_meta_title || 0,
    keyword_density_in_desc: analysis?.keyword_density_in_desc || 0,
    desc_min_word_count_300: analysis?.desc_min_word_count_300 || 0,
    alt_text_in_all_img: analysis?.alt_text_in_all_img || 0,
    product_description_is_unique: analysis?.product_description_is_unique || 0,
    optimized_all_images: analysis?.optimized_all_images || 0,
  };
};

/**
 * Serialize Product analysis to the JSON column structure
 * @param analysis
 */

const serializeMultiLanguageProductAnalysis = (analysis) => {
  return {
    // Basic SEO
    unique_focus_keyword: analysis?.unique_focus_keyword || 0,
    focus_keyword_in_meta_title: analysis?.focus_keyword_in_meta_title || 0,
    meta_desc_within_char_limit: analysis?.meta_desc_within_char_limit || 0,
    meta_title_within_char_limit: analysis?.meta_title_within_char_limit || 0,
    unique_title: analysis?.unique_title || 0,
    // Detailed SEO
    focus_keyword_in_title: analysis?.focus_keyword_in_title || 0,
    focus_keyword_in_meta_desc: analysis?.focus_keyword_in_meta_desc || 0,
    focus_keyword_in_url: analysis?.focus_keyword_in_url || 0,
    focus_keyword_at_the_beginning_of_meta_title: analysis?.focus_keyword_at_the_beginning_of_meta_title || 0,
    keyword_density_in_desc: analysis?.keyword_density_in_desc || 0,
    desc_min_word_count_300: analysis?.desc_min_word_count_300 || 0,
    product_description_is_unique: analysis?.product_description_is_unique || 0,
  };
};

module.exports = {
  serializeProductAnalysis,
  serializeMultiLanguageProductAnalysis,
};
