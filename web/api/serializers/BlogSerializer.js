/**
 * Serialize shopify blog data for DB insert operation
 * @param {number} shopId
 * @param {object} blogData shopify blog data
 * @returns {BlogDetails} serialized blog data
 */
const serializeShopifyBlogData = (shopId, blogData) => {
  const { id, title, handle, tags, templateSuffix } = blogData;
  return {
    shop_id: shopId,

    blog_id: id.replace(/.*\//gim, ""),
    title,
    handle,
    tags,
    template_suffix: templateSuffix,

    is_synced: true,
  };
};

module.exports = {
  serializeShopifyBlogData,
};
