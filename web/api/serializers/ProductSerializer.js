const { serializeProductImages, serializeSingleImage } = require("./ProductImageSerializer");
const { serializeProductMetas, serializeMultiLanguageProductMetas } = require("./ProductMetaSerializer");
const { serializeProductAnalysis, serializeMultiLanguageProductAnalysis } = require("./ProductAnalysisSerializer");
const { isEmpty } = require("lodash");
const { getPercentValue, calculateIsOptimized, extractShopifyIdFromGqlId } = require("../utils/helper");
const SCORES = require("../config/seoScores");
const { NAMESPACE, METAFIELD_KEYS } = require("storeseo-enums/metafields");
const { extractFocusKeyword } = require("../utils/helper");

const serializeListProduct = (product) => {
  if (product.images) {
    product.images = serializeProductImages(product.images);
  }
  return {
    ...product,
    featuredImage: getFeaturedImage(product?.featuredImage),
    ...getSocialMediaPreviewImages(product),
    shopifyId: extractShopifyIdFromGqlId(product.product_id),
  };
};

const getFeaturedImage = (image) => {
  if (image) {
    return serializeSingleImage(image);
  }
  return null;
};

const getSocialMediaPreviewImages = (product) => {
  const featuredImage = getFeaturedImage(product?.featuredImage)?.src;
  let facebookPreviewImage = featuredImage;
  let twitterPreviewImage = featuredImage;

  try {
    product.meta.forEach((m) => {
      if (m.namespace === NAMESPACE.STORE_SEO && m.key === METAFIELD_KEYS.FACEBOOK_PREVIEW_IMAGE_URL) {
        facebookPreviewImage = m.value;
      } else if (m.namespace === NAMESPACE.STORE_SEO && m.key === METAFIELD_KEYS.TWITTER_PREVIEW_IMAGE_URL) {
        twitterPreviewImage = m.value;
      }
    });
  } catch (err) {}

  return { facebookPreviewImage, twitterPreviewImage };
};

const serializeSingleProduct = (product) => {
  return {
    ...product,
    analysis: serializeProductAnalysis(product?.analysis),
    meta: serializeProductMetas(product?.meta),
    images: serializeProductImages(product?.images),
    featuredImage: getFeaturedImage(product?.featuredImage),
    ...getSocialMediaPreviewImages(product),
    shopifyId: extractShopifyIdFromGqlId(product.product_id),
  };
};

const serializeMultiLanguageSingleProduct = (product) => {
  return {
    ...product.originalVersion,
    ...product,
    description: product.body_html || product.originalVersion?.description,
    analysis: serializeMultiLanguageProductAnalysis(product?.analysis),
    meta: serializeMultiLanguageProductMetas(product, product?.originalVersion?.meta),
    images: serializeProductImages(product?.originalVersion?.images),
    featuredImage: getFeaturedImage(product?.originalVersion?.featuredImage),
    ...getSocialMediaPreviewImages(product?.originalVersion),
    shopifyId: extractShopifyIdFromGqlId(product?.originalVersion?.product_id),
  };
};

const serialiseProduct = (product) => ({
  ...product,
  featuredImage: getFeaturedImage(product?.featuredImage),
  ...getSocialMediaPreviewImages(product),
  shopifyId: extractShopifyIdFromGqlId(product.product_id),
});

const serializeShopifyProduct = (shopId, product) => ({
  shop_id: shopId,
  product_id: product.id,
  handle: product.handle,
  cursor: product.defaultCursor,
  title: product.title,
  description: product.descriptionHtml,
  product_type: product.productType,
  vendor: product.vendor,
  featured_media_id: !isEmpty(product.featuredMedia) ? product.featuredMedia.id : null,
  online_store_url: product.onlineStoreUrl,
  online_store_preview_url: product.onlineStorePreviewUrl,
  tags: product.tags,
});

/**
 *
 * @param {{shopId: number, dbProductId: number, languageCode: string, mulitLangShopifyProductData: import("../services/shopify/LocalaizationService").TranslatableResource}} param0
 */
const serializeMulitLanguageShopifyProductData = ({
  shopId,
  dbProductId,
  languageCode,
  mulitLangShopifyProductData,
}) => {
  const originalContent = mulitLangShopifyProductData.translatableContent.reduce(
    (content, val) => ({ ...content, [val.key]: val.value }),
    {}
  );
  const translatedContent = mulitLangShopifyProductData.translations.reduce(
    (translation, val) => ({ ...translation, [val.key]: val.value }),
    {}
  );

  return {
    shop_id: shopId,
    product_id: dbProductId,
    language_code: languageCode,

    title: translatedContent.title || originalContent.title,
    meta_title: translatedContent.meta_title || translatedContent.title || originalContent.title,
    meta_description: translatedContent.meta_description || originalContent.meta_description,
    handle: translatedContent.handle || originalContent.handle,
    body_html: translatedContent.body_html || originalContent.body_html,
  };
};

const serialiseProductSitemap = (res) => {
  let product = res.toJSON();
  return {
    ...product,
    featuredImage: getFeaturedImage(product?.featuredImage),
    status: product.sitemap?.sitemap_disabled === 0,
    noFollow: product.sitemap?.no_follow === 1,
    noIndex: product.sitemap?.no_index === 1,
    isChecked: false,
    shopifyId: extractShopifyIdFromGqlId(product.product_id),
  };
};

const serializeProductOptimizationDetails = (analysis) => {
  return [
    {
      title: "Basic SEO Analysis",
      key: "BASIC_SEO",
      values: [
        {
          key: "UNIQUE_FOCUS_KEYWORD",
          title: "Focus keyword is unique",
          value: analysis.unique_focus_keyword,
          percent: getPercentValue(analysis.unique_focus_keyword, SCORES.UNIQUE_FOCUS_KEYWORD),
          isOptimized: calculateIsOptimized(analysis.unique_focus_keyword, SCORES.UNIQUE_FOCUS_KEYWORD),
        },
        {
          key: "FOCUS_KEYWORD_IN_META_TITLE",
          title: "Focus keyword is used in the meta title",
          value: analysis.focus_keyword_in_meta_title,
          percent: getPercentValue(analysis.focus_keyword_in_meta_title, SCORES.FOCUS_KEYWORD_IN_META_TITLE),
          isOptimized: calculateIsOptimized(analysis.focus_keyword_in_meta_title, SCORES.FOCUS_KEYWORD_IN_META_TITLE),
        },
        {
          key: "META_DESC_WITHIN_CHAR_LIMIT",
          title: "Meta description is within 120-165 characters",
          value: analysis.meta_desc_within_char_limit,
          percent: getPercentValue(analysis.meta_desc_within_char_limit, SCORES.META_DESC_WITHIN_CHAR_LIMIT),
          isOptimized: calculateIsOptimized(analysis.meta_desc_within_char_limit, SCORES.META_DESC_WITHIN_CHAR_LIMIT),
          hint: "To get a higher score, write meta description with 165 characters",
        },
        {
          key: "META_TITLE_WITHIN_CHAR_LIMIT",
          title: "Meta title is within 50-70 characters",
          value: analysis.meta_title_within_char_limit,
          percent: getPercentValue(analysis.meta_title_within_char_limit, SCORES.META_TITLE_WITHIN_CHAR_LIMIT),
          // isOptimized: calculateIsOptimized(analysis.meta_title_within_char_limit, SCORES.META_TITLE_WITHIN_CHAR_LIMIT),
          isOptimized: calculateIsOptimized(analysis.meta_title_within_char_limit, SCORES.META_TITLE_WITHIN_CHAR_LIMIT),
          hint: "To get a higher SEO score, make your Meta Title 70 characters long!",
        },
        {
          key: "UNIQUE_TITLE",
          title: "Product title is unique",
          value: analysis.unique_title,
          percent: getPercentValue(analysis.unique_title, SCORES.UNIQUE_TITLE),
          isOptimized: calculateIsOptimized(analysis.unique_title, SCORES.UNIQUE_TITLE),
        },
      ],
    },
    {
      title: "Detailed SEO Analysis",
      key: "DETAILED_SEO",
      values: [
        {
          key: "FOCUS_KEYWORD_IN_IMG_ALT_TEXT",
          title: "Focus keyword is used in image alt text",
          value: analysis.focus_keyword_in_img_alt_text,
          percent: getPercentValue(analysis.focus_keyword_in_img_alt_text, SCORES.FOCUS_KEYWORD_IN_IMG_ALT_TEXT),
          isOptimized: calculateIsOptimized(
            analysis.focus_keyword_in_img_alt_text,
            SCORES.FOCUS_KEYWORD_IN_IMG_ALT_TEXT
          ),
        },
        {
          key: "FOCUS_KEYWORD_IN_TITLE",
          title: "Focus keyword is used in the product title",
          value: analysis.focus_keyword_in_title,
          percent: getPercentValue(analysis.focus_keyword_in_title, SCORES.FOCUS_KEYWORD_IN_TITLE),
          isOptimized: calculateIsOptimized(analysis.focus_keyword_in_title, SCORES.FOCUS_KEYWORD_IN_TITLE),
        },
        {
          key: "FOCUS_KEYWORD_IN_META_DESC",
          title: "Focus keyword found in meta description",
          value: analysis.focus_keyword_in_meta_desc,
          percent: getPercentValue(analysis.focus_keyword_in_meta_desc, SCORES.FOCUS_KEYWORD_IN_META_DESC),
          isOptimized: calculateIsOptimized(analysis.focus_keyword_in_meta_desc, SCORES.FOCUS_KEYWORD_IN_META_DESC),
        },
        {
          key: "FOCUS_KEYWORD_AT_THE_BEGINNING_OF_META_TITLE",
          title: "Focus keyword is at the beginning of meta title",
          value: analysis.focus_keyword_at_the_beginning_of_meta_title,
          percent: getPercentValue(
            analysis.focus_keyword_at_the_beginning_of_meta_title,
            SCORES.FOCUS_KEYWORD_AT_THE_BEGINNING_OF_META_TITLE
          ),
          isOptimized: calculateIsOptimized(
            analysis.focus_keyword_at_the_beginning_of_meta_title,
            SCORES.FOCUS_KEYWORD_AT_THE_BEGINNING_OF_META_TITLE
          ),
        },
        {
          key: "KEYWORD_DENSITY_IN_DESC",
          title: "Focus keyword density is 3-6 times for product description",
          value: analysis.keyword_density_in_desc,
          percent: getPercentValue(analysis.keyword_density_in_desc, SCORES.KEYWORD_DENSITY_IN_DESC),
          isOptimized: calculateIsOptimized(analysis.keyword_density_in_desc, SCORES.KEYWORD_DENSITY_IN_DESC),
          hint: "To get a higher SEO score, use the Focus Keyword 6 times in the product description!",
        },
        {
          key: "FOCUS_KEYWORD_IN_URL",
          title: "Focus keyword is used in the URL",
          value: analysis?.focus_keyword_in_url,
          percent: getPercentValue(analysis?.focus_keyword_in_url, SCORES.FOCUS_KEYWORD_IN_URL),
          isOptimized: calculateIsOptimized(analysis?.focus_keyword_in_url, SCORES.FOCUS_KEYWORD_IN_URL),
        },
        {
          key: "DESC_MIN_WORD_COUNT_300",
          title: "Write a product description of 50 to 300 words",
          value: analysis.desc_min_word_count_300,
          percent: getPercentValue(analysis.desc_min_word_count_300, SCORES.DESC_MIN_WORD_COUNT_300),
          isOptimized: calculateIsOptimized(analysis.desc_min_word_count_300, SCORES.DESC_MIN_WORD_COUNT_300),
          hint: "To get a higher score, write a product description with 300 words",
        },
        {
          key: "ALT_TEXT_IN_ALL_IMG",
          title: "Add alt text to all images",
          value: analysis.alt_text_in_all_img,
          percent: getPercentValue(analysis.alt_text_in_all_img, SCORES.ALT_TEXT_IN_ALL_IMG),
          isOptimized: calculateIsOptimized(analysis.alt_text_in_all_img, SCORES.ALT_TEXT_IN_ALL_IMG),
        },
        {
          key: "OPTIMIZED_ALL_IMAGES",
          title: "All Images are optimized",
          value: analysis.optimized_all_images,
          percent: getPercentValue(analysis.optimized_all_images, SCORES.OPTIMIZED_ALL_IMAGES),
          isOptimized: calculateIsOptimized(analysis.optimized_all_images, SCORES.OPTIMIZED_ALL_IMAGES),
        },
        // {
        //   key: "PRODUCT_DESCRIPTION_IS_UNIQUE",
        //   title: "Product description is unique",
        //   value: analysis.product_description_is_unique,
        //   percent: getPercentValue(analysis.product_description_is_unique, SCORES.PRODUCT_DESCRIPTION_IS_UNIQUE),
        //   isOptimized: calculateIsOptimized(
        //     analysis.product_description_is_unique,
        //     SCORES.PRODUCT_DESCRIPTION_IS_UNIQUE
        //   ),
        // },
      ],
    },
  ];
};

const serializeMulitLanguageProductOptimizationDetails = (analysis) => {
  const serializedAnalysis = serializeProductOptimizationDetails(analysis);

  serializedAnalysis[1].values = serializedAnalysis[1].values.filter(
    (v) => !["FOCUS_KEYWORD_IN_IMG_ALT_TEXT", "ALT_TEXT_IN_ALL_IMG", "OPTIMIZED_ALL_IMAGES"].includes(v.key)
  );

  return serializedAnalysis;
};

const serializeWebhookPayloadToShopifyProduct = (webhookPayload) => {
  let {
    admin_graphql_api_id: id,
    handle,
    title,
    body_html: descriptionHtml,
    media,
    product_type: productType,
    vendor,
    tags,
    metafields,
  } = webhookPayload;

  metafields =
    metafields?.map((m) => ({
      node: {
        ...m,
        id: m.admin_graphql_api_id,
      },
    })) || [];

  const firstImage = media?.filter((m) => m.media_content_type === "IMAGE")?.[0];

  return {
    id,
    handle,
    title,
    descriptionHtml,

    // featuredMedia: media?.reduce(
    //   (featuredMedia, m) =>
    //     m.position === 1 && m.media_content_type === "IMAGE" ? { id: m.admin_graphql_api_id } : featuredMedia,
    //   null
    // ),
    featuredMedia: firstImage ? { id: firstImage.admin_graphql_api_id } : null,
    mediaImages: {
      edges: media
        ?.filter((m) => m.media_content_type === "IMAGE")
        ?.map((img) => ({
          node: { id: img.admin_graphql_api_id, altText: img.alt, src: img.preview_image.src, position: img.position },
        })),
    },

    metafields: {
      edges: metafields?.filter((m) => m.node.namespace === NAMESPACE.GLOBAL) || [],
    },
    metafield: metafields.find((m) => m.node.namespace === NAMESPACE.SEO && m.node.key === METAFIELD_KEYS.HIDDEN)?.node,

    storeSEOMetaFields: {
      edges: metafields?.filter((m) => m.node.namespace === NAMESPACE.STORE_SEO) || [],
    },

    productType,
    vendor,
    tags: tags.split(", "),
  };
};

module.exports = {
  serializeListProduct,
  getFeaturedImage,
  getSocialMediaPreviewImages,
  serializeSingleProduct,
  serialiseProduct,
  serializeShopifyProduct,
  serializeMulitLanguageShopifyProductData,
  serialiseProductSitemap,
  serializeProductOptimizationDetails,
  serializeMulitLanguageProductOptimizationDetails,
  serializeWebhookPayloadToShopifyProduct,
  serializeMultiLanguageSingleProduct,
};
