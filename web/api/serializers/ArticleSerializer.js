const imageOptimization = require("storeseo-enums/imageOptimization");

const serializeArticleSingleImage = (image) => {
  if (!image) return null;
  return {
    ...image,
    src: image.src,
    altText: image.alt_text,
    isOptimized: image.optimization_status === imageOptimization.OPTIMIZED,
    isAlreadyOptimized: image.optimization_status === imageOptimization.ALREADY_OPTIMIZED,
  };
};
const serializeArticleSitemap = (res) => {
  let article = res.toJSON();
  return {
    ...article,
    featuredImage: serializeArticleSingleImage(article?.img),
    status: article.sitemap?.sitemap_disabled === 0,
    noFollow: article.sitemap?.no_follow === 1,
    noIndex: article.sitemap?.no_index === 1,
    isChecked: false,
    shopifyId: article.article_id,
  };
};

/**
 * Serialize shopify article data for DB insert operation
 * @param {number} shopId relevant shop DB id
 * @param {number} blogId relevant blog DB id
 * @param {object} articleData shopify article data
 */
const serializeShopifyArticleData = (shopId, blogId, articleData) => {
  const { id, title, body, author, publishedAt, templateSuffix, handle, tags, image, metafields } = articleData;

  return {
    shop_id: shopId,
    blog_id: blogId,

    article_id: id.replace(/.*\//gim, ""),
    title,
    handle,
    author: author.name,
    body_html: body,
    template_suffix: templateSuffix,
    tags: tags,
    published_at: publishedAt,

    image: image
      ? {
          media_id: image.id.replace(/.*\//gim, ""),
          src: image.url,
          alt_text: image.altText,
        }
      : null,

    meta: serializeShopifyArticleMetaFields(metafields),

    is_synced: true,
  };
};

const serializeShopifyArticleMetaFields = (metafields) => {
  return metafields?.edges?.map(({ node }) => {
    return {
      ...node,
      id: node.id.replace(/.*\//gim, ""),
      key: node.key.split(".").reverse()[0],
    };
  });
};

const serializArticleMetas = (metas) =>
  metas.map((m) => ({
    id: m.gql_id,
    namespace: m.namespace,
    key: m.key,
    value: m.value,
    type: m.type,
  }));

/**
 * Format articles image by augmenting image object with the fileName
 * @param {object} image
 * @returns {object}
 */
const formatArticleImage = (image) => ({
  ...image,
  fileName: image.src.split("/").pop(),
  media_id: image?.shopify_media_id,
  resources: [{ ...image?.article, resource_id: image?.article?.shopify_gql_id }],
  optimization_meta: image?.resourceOptimizationMeta?.optimization_meta,
});

/**
 * Format articles images by augmenting image object with the fileName
 * @param {Array<object>} images
 * @returns {Array<object> | null}
 */
const formatArticleImages = (images) => {
  if (!images) return null;
  return images.map(formatArticleImage);
};

module.exports = {
  serializeArticleSitemap,
  serializeShopifyArticleData,
  serializArticleMetas,
  formatArticleImage,
  formatArticleImages,
};
