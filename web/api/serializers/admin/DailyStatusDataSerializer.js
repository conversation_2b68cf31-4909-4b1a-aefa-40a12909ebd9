const { unionBy, kebabCase } = require("lodash");

// Mapping of keys to labels
const labels = {
  newFreeStores: "New Free Stores",
  returningFreeStores: "Returning Free Stores",
  newProStores: "New Paying Stores",
  freeToProStores: "Free to Paying Stores",
  proToProStores: "Paying Migrations (Upgrade/Downgrade)",
  uninstalledFromFreeStores: "Uninstalled Free Stores",
  downgradeToFreeStores: "Downgrade to Free",
  cancelledStores: "Cancellation & Uninstallation",
  closedFromFreeStores: "Free Stores",
  closedFromProStores: "Paying Stores",
  reopendFromFreeStores: "Free Stores",
  reopendFromProStores: "Paying Stores",
  yearlyActiveStores: "Yearly",
  visionaryAciveStores: "Visionary",
  monthlyActiveStores: "Monthly",
  onetimeCharges: "Credit Purchases",
  onetimeChargesAll: "Total Credit Purchases",
};

// Main function to serialize daily stats data
const serializeDailyStatsData = (data, comparisonData) => ({
  freeStores: createGroup("Free Stores", ["newFreeStores", "returningFreeStores"], data, comparisonData),
  proStores: createGroup("Paying Stores", ["newProStores", "freeToProStores", "proToProStores"], data, comparisonData),
  uninstallations: createGroup("Uninstallations", ["uninstalledFromFreeStores"], data, comparisonData),
  cancellations: createGroup("Cancellations", ["downgradeToFreeStores", "cancelledStores"], data, comparisonData),
  reOpenedStores: createGroup(
    "Re-Opened Stores",
    ["reopendFromFreeStores", "reopendFromProStores"],
    data,
    comparisonData
  ),
  closedStores: createGroup("Closed Stores", ["closedFromFreeStores", "closedFromProStores"], data, comparisonData),
  activeStores: createGroup(
    "Active Stores",
    ["yearlyActiveStores", "visionaryAciveStores", "monthlyActiveStores"],
    data,
    comparisonData
  ),
  creditPurchases: createGroup("Credit Purchases", ["onetimeCharges"], data, comparisonData, false),
});

// Helper function to create a group with its items
const createGroup = (label, keys, data, comparisonData) => {
  const items = serializeItems(keys, data, comparisonData);
  const values = items.reduce(
    ({ value, prevValue }, item) => {
      value = Number(value) + Number(item.value);
      prevValue = Number(prevValue) + Number(item.prevValue);

      const { diff, symbol } = calculateDiff({ value, prevValue });

      return {
        value,
        prevValue,
        diff,
        symbol,
      };
    },
    { value: 0, prevValue: 0, diff: 0, symbol: "" }
  );
  return {
    label,
    ...values,
    items,
  };
};

// Helper function to calculate the difference and symbol
const calculateDiff = ({ value, prevValue }) => {
  const diff = value - prevValue;
  const symbol = diff > 0 ? "+" : diff < 0 ? "-" : "";
  return { diff: Math.abs(diff), symbol };
};

// Helper function to serialize items
const serializeSubitems = (value = [], prevValue = []) => {
  const allKeys = unionBy(value, prevValue, "title").sort((a, b) => a.key - b.key);

  return allKeys.map((item) => {
    const newValue = value.find((v) => v.title === item.title)?.count || 0;
    const newPrevValue = prevValue.find((v) => v.title === item.title)?.count || 0;
    const { diff, symbol } = calculateDiff({ value: newValue, prevValue: newPrevValue });
    return {
      key: kebabCase(item.title),
      label: item.title,
      value: newValue,
      prevValue: newPrevValue,
      diff,
      symbol,
    };
  });
};

// Helper function to serialize a single item
const serializeItem = ({ key, value, prevValue }) => {
  const items = serializeSubitems(value, prevValue);

  if (Array.isArray(value)) {
    value = value.reduce((count, item) => count + Number(item.count), 0);
  }

  if (Array.isArray(prevValue)) {
    prevValue = prevValue.reduce((count, item) => count + Number(item.count), 0);
  }

  const { diff, symbol } = calculateDiff({ value, prevValue });

  return {
    key,
    label: labels[key],
    value,
    prevValue,
    diff,
    symbol,
    items,
  };
};

// Helper function to serialize multiple items
const serializeItems = (keys = [], data, comparisonData) => {
  return keys.map((key) => {
    const value = data[key] || [];
    const prevValue = comparisonData[key] || [];
    return serializeItem({ key, value, prevValue });
  });
};

const prepareDailyStatusReportBlock = (date, data) => {
  const blocks = [
    {
      type: "header",
      text: {
        type: "plain_text",
        text: `Daily Status Report - ${date}`,
        emoji: true,
      },
    },
    {
      type: "context",
      elements: [
        {
          type: "plain_text",
          text: `:calendar: ${date}`,
          emoji: true,
        },
      ],
    },
  ];

  Object.entries(data).forEach(([key, section], idx) => {
    const sectionJson = {
      type: "rich_text",
      elements: [],
    };

    let items = [];
    section.items?.forEach((item, idx2) => {
      items.push({
        type: "text",
        text: `${item.label} => ${item.value} `,
        style: {
          bold: true,
        },
      });

      items.push({
        type: "text",
        text: `${item.symbol}${item.diff}`,
        style: {
          bold: true,
          code: true,
        },
      });

      items.push({
        type: "text",
        text: "\n",
      });

      if (item.items.length > 0) {
        item.items.forEach((si) => {
          items.push({
            type: "text",
            text: `  • ${si.label} => ${si.value} `,
            style: {
              italic: true,
            },
          });

          items.push({
            type: "text",
            text: `${si.symbol}${si.diff}`,
            style: {
              code: true,
              italic: true,
            },
          });

          items.push({
            type: "text",
            text: "\n",
          });
        });
      }
    });

    if (key !== "creditPurchases") {
      sectionJson.elements.push({
        type: "rich_text_section",
        elements: [
          {
            type: "text",
            text: `${idx > 0 ? "\n" : ""}${section.label} => ${section.value} `,
            style: {
              bold: true,
            },
          },
          {
            type: "text",
            text: `${section.symbol}${section.diff}`,
            style: {
              bold: true,
              code: true,
            },
          },
          {
            type: "text",
            text: "\n",
          },
        ],
      });
    }

    sectionJson.elements.push({
      type: "rich_text_quote",
      elements: items,
    });

    blocks.push({
      type: "divider",
    });
    blocks.push(sectionJson);
  });

  return blocks;
};

module.exports = { serializeDailyStatsData, prepareDailyStatusReportBlock };
