const { formatBytes } = require("../utils/helper");

const serializeImageFile = (file) => {
  const {
    id,
    preview: {
      image: { id: imageId, url },
    },
    originalSource: { fileSize },
    metafield = {},
  } = file;

  return {
    id,
    imageId,
    url,
    fileSize: formatBytes(fileSize),
    metafield: {
      ...(metafield || {}),
      value: JSON.parse(metafield?.value || "null"),
    },
  };
};

module.exports = {
  serializeImageFile,
};
