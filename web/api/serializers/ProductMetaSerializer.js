const { isEmpty, pick } = require("lodash");
const { NAMESPACE, METAFIELD_KEYS, METAFIELD_TYPES } = require("storeseo-enums/metafields");

const serializeShopifyProductMetas = (shopifyProduct) => {
  // serialize global metafields data
  const metadata =
    shopifyProduct.metafields.edges.length > 0
      ? shopifyProduct.metafields.edges
          .map((meta) => ({ ...meta.node, key: meta.node.key.replace("global.", "") }))
          .filter((meta) => meta.key == METAFIELD_KEYS.TITLE_TAG || meta.key == METAFIELD_KEYS.DESCRIPTION_TAG) // do not use === here
      : [];

  // serialize hidden metafields data
  if (!isEmpty(shopifyProduct.metafield)) {
    metadata.push(shopifyProduct.metafield);
  }

  // serialize store_seo custom metafields data
  shopifyProduct.storeSEOMetaFields?.edges?.forEach((m) => metadata.push(m.node));
  return metadata;
};

/**
 * Serialize Product meta to the JSON column structure
 * @returns {{id: *}}
 * @param meta
 */
const serializeProductMetas = (meta) => {
  return (
    meta.map((meta) => ({
      id: meta.gql_id,
      ...pick(meta, ["key", "type", "namespace", "value", "description"]),
    })) || []
  );
};

/**
 * Serialize product meta data
 * @param {object} product
 * @param {string} metaTitle
 * @param {string} metaDescription
 * @returns {*[]}
 */
const serializeUpdatedMetadata = (product, { metaTitle, metaDescription }) => {
  const metadata = [];

  const productMetaTitle = !isEmpty(product?.meta)
    ? product.meta.find((md) => md.key === METAFIELD_KEYS.TITLE_TAG)
    : null;

  if (metaTitle !== undefined) {
    if (!isEmpty(productMetaTitle) && productMetaTitle.hasOwnProperty("id")) {
      metadata.push({ ...pick(productMetaTitle, ["id", "key"]), value: metaTitle.trim() });
    } else {
      metadata.push({
        namespace: NAMESPACE.GLOBAL,
        key: METAFIELD_KEYS.TITLE_TAG,
        value: metaTitle.trim(),
        type: METAFIELD_TYPES.STRING,
      });
    }
  }

  if (metaDescription !== undefined) {
    const productMetaDescription = !isEmpty(product?.meta)
      ? product.meta.find((md) => md.key === METAFIELD_KEYS.DESCRIPTION_TAG)
      : null;
    if (!isEmpty(productMetaDescription) && productMetaDescription.hasOwnProperty("id")) {
      metadata.push({ ...pick(productMetaDescription, ["id", "key"]), value: metaDescription });
    } else {
      metadata.push({
        namespace: NAMESPACE.GLOBAL,
        key: METAFIELD_KEYS.DESCRIPTION_TAG,
        value: metaDescription,
        type: METAFIELD_TYPES.STRING,
      });
    }
  }

  return metadata;
};

/**
 * Serialize Multi-language Product meta to the JSON column structure
 * @param {*} multiLanguageProduct,
 * @param {*} originalProductMeta
 */
const serializeMultiLanguageProductMetas = (multiLanguageProduct, originalProductMeta) => {
  const meta =
    originalProductMeta.map((meta) => ({
      id: meta.gql_id,
      ...pick(meta, ["key", "type", "namespace", "value", "description"]),
    })) || [];

  const titleMetaIndex = meta.findIndex((m) => m.key === METAFIELD_KEYS.TITLE_TAG);
  if (titleMetaIndex >= 0) {
    meta[titleMetaIndex].value = multiLanguageProduct.meta_title || multiLanguageProduct.title;
  } else {
    meta.push({
      key: METAFIELD_KEYS.TITLE_TAG,
      namespace: NAMESPACE.GLOBAL,
      type: METAFIELD_TYPES.STRING,
      value: multiLanguageProduct.meta_title || multiLanguageProduct.title,
    });
  }

  const descriptionMetaIndex = meta.findIndex((m) => m.key === METAFIELD_KEYS.DESCRIPTION_TAG);
  if (descriptionMetaIndex >= 0) {
    meta[descriptionMetaIndex].value = multiLanguageProduct.meta_description;
  } else {
    meta.push({
      key: METAFIELD_KEYS.DESCRIPTION_TAG,
      namespace: NAMESPACE.GLOBAL,
      type: METAFIELD_TYPES.STRING,
      value: multiLanguageProduct.meta_description,
    });
  }

  return meta;
};

module.exports = {
  serializeShopifyProductMetas,
  serializeProductMetas,
  serializeUpdatedMetadata,

  serializeMultiLanguageProductMetas,
};
