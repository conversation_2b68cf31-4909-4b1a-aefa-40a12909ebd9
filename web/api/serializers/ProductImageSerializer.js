//@ts-check
const { isEmpty, isArray } = require("lodash");
const imageOptimization = require("storeseo-enums/imageOptimization");

const flattenMediaImages = (shopifyProduct) => {
  return {
    edges: shopifyProduct.mediaImages.edges
      .filter((media) => media.node.image)
      .map((media) => ({
        node: {
          id: media.node.id,
          url: media.node.image.url,
          altText: media.node.image.altText,
          fileSize: media.node.originalSource?.fileSize,
        },
      })),
  };
};

const serializeShopifyProductImages = (shopifyProduct) => {
  return !isEmpty(shopifyProduct.mediaImages) && shopifyProduct.mediaImages.edges.length > 0
    ? shopifyProduct.mediaImages.edges.map((img, idx) => ({ ...img.node, position: img.position ?? idx }))
    : [];
};

/**
 * Serialize Product images to the JSON column structure
 * @param images
 */
const serializeProductImages = (images) => {
  return images.map((image) => serializeSingleImage(image)).sort((a, b) => a.position - b.position) || [];
};

const serializeSingleImage = (image) => {
  return {
    ...image,

    src: image.src,
    position: image.position,

    altText: image.alt_text,
    alt_text: undefined,

    isOptimized: image.optimization_status === imageOptimization.OPTIMIZED,
    isAlreadyOptimized: image.optimization_status === imageOptimization.ALREADY_OPTIMIZED,
  };
};

/**
 * @typedef {{media_id: string, altText?: string, alt_text?: string}} image
 * @param {Array<image> | image} images
 * @returns {{[media_id: string]: string}}
 */
const generateImageAltTextObjectByMediaId = (images) => {
  if (isArray(images)) {
    return images.reduce((acc, image) => {
      acc[image.media_id] = image.altText || image.alt_text || "";
      return acc;
    }, {});
  }

  return {
    [images.media_id]: images.altText || images.alt_text || "",
  };
};

module.exports = {
  flattenMediaImages,

  serializeShopifyProductImages,
  serializeProductImages,
  serializeSingleImage,
  generateImageAltTextObjectByMediaId,
};
