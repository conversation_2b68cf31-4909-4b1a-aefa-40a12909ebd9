const { getPercentValue, calculateIsOptimized } = require("../utils/helper");
const SCORES = require("../config/docSeoScores");
const imageOptimization = require("storeseo-enums/imageOptimization");
const { NAMESPACE, METAFIELD_KEYS, METAFIELD_TYPES } = require("storeseo-enums/metafields");
const { pick, isEmpty } = require("lodash");

/**
 * Serialize BetterDocs data to the DB structure
 * @param {*} shopId - Shopify Shop ID
 * @param {*} doc - BetterDocs Doc data
 * @returns
 */
const serializeDoc = (shopId, doc) => ({
  shop_id: shopId,
  doc_id: doc.id,
  title: doc.title,
  handle: doc.slug,
  url: doc.url_handel,
  description: doc.description,
  metafields: [
    { key: "title_tag", value: doc.seo_meta_title },
    { key: "description_tag", value: doc.seo_meta_description },
    { key: "no_index", value: doc?.seo_meta_robots?.noindex ? "1" : "0" },
    { key: "no_follow", value: doc?.seo_meta_robots?.nofollow ? "1" : "0" },
  ],
  featured_image_id: doc.featured_image?.id,
  tags: doc.tags,
  categories: doc.categories,
  status: doc.status === "published" ? 1 : 0,
});

/**
 * Serialize BetterDocs Image data to the DB structure
 * @param {*} shopId
 * @param {*} docId
 * @param {*} docData
 * @returns
 */
const serializeDocImage = (shopId, docId, docData) => {
  const image = docData?.featured_image;
  if (!image) return null;
  return {
    shop_id: shopId,
    doc_id: docId,
    media_id: image.id,
    src: image.url || image.src,
    alt_text: image.alt_text,
  };
};

const serializeDocUpdatedMetadata = (doc, { metaTitle, metaDescription }) => {
  const metadata = [];

  const docMetaTitle = !isEmpty(doc.metafields)
    ? doc.metafields.find((md) => md.key === METAFIELD_KEYS.TITLE_TAG)
    : null;

  if (metaTitle !== undefined) {
    if (!isEmpty(docMetaTitle)) {
      metadata.push({ ...pick(docMetaTitle, ["key"]), value: metaTitle });
    } else {
      metadata.push({
        // namespace: NAMESPACE.GLOBAL,
        key: METAFIELD_KEYS.TITLE_TAG,
        value: metaTitle,
        // type: METAFIELD_TYPES.STRING,
      });
    }
  }

  if (metaDescription !== undefined) {
    const docMetaDescription = !isEmpty(doc.metafields)
      ? doc.metafields.find((md) => md.key === METAFIELD_KEYS.DESCRIPTION_TAG)
      : null;
    if (!isEmpty(docMetaDescription)) {
      metadata.push({ ...pick(docMetaDescription, ["key"]), value: metaDescription });
    } else {
      metadata.push({
        // namespace: NAMESPACE.GLOBAL,
        key: METAFIELD_KEYS.DESCRIPTION_TAG,
        value: metaDescription,
        // type: METAFIELD_TYPES.STRING,
      });
    }
  }

  return metadata;
};

/**
 * Get Social Media Preview Images from DB Collections image and metafields
 * @param {*} doc - Doc data from DB
 * @returns
 */
const getSocialMediaPreviewImages = (doc) => {
  const featuredImage = serializeDocSingleImage(doc?.img)?.src || null;
  let facebookPreviewImage = featuredImage;
  let twitterPreviewImage = featuredImage;

  try {
    doc?.metafields.forEach((m) => {
      if (m.namespace === NAMESPACE.STORE_SEO && m.key === METAFIELD_KEYS.FACEBOOK_PREVIEW_IMAGE_URL) {
        facebookPreviewImage = m.value;
      } else if (m.namespace === NAMESPACE.STORE_SEO && m.key === METAFIELD_KEYS.TWITTER_PREVIEW_IMAGE_URL) {
        twitterPreviewImage = m.value;
      }
    });
  } catch (err) {}

  return { facebookPreviewImage, twitterPreviewImage };
};

const serializeSingleDoc = (doc) => {
  return {
    ...doc,
    analysis: serializeDocAnalysis(doc?.analysisData),
    image: serializeDocSingleImage(doc?.img),
    ...getSocialMediaPreviewImages(doc),
    // shopifyId: extractShopifyIdFromGqlId(collection.collection_id),
  };
};

/**
 * Serialize DB Doc Image to the JSON column structure
 * @param {*} image - Doc Image data from DB
 * @returns
 */
const serializeDocSingleImage = (image) => {
  if (!image) return null;
  return {
    ...image,
    src: image.src,
    altText: image.alt_text,
    // position: image.position,
    isOptimized: image.optimization_status === imageOptimization.OPTIMIZED,
    isAlreadyOptimized: image.optimization_status === imageOptimization.ALREADY_OPTIMIZED,
  };
};

/**
 * Serialize DB Doc Analysis to the JSON column structure
 * @param {*} analysis
 * @returns
 */
const serializeDocAnalysis = (analysis) => {
  return {
    // Basic SEO
    unique_focus_keyword: analysis?.unique_focus_keyword || 0,
    focus_keyword_in_introduction: analysis?.focus_keyword_in_introduction || 0,
    content_more_than_200_words: analysis?.content_more_than_200_words || 0,
    focus_keyword_density: analysis?.focus_keyword_density || 0,
    // Detailed SEO
    focus_keyword_in_img_alt_text: analysis?.focus_keyword_in_img_alt_text || 0,
    focus_keyword_in_subheading: analysis?.focus_keyword_in_subheading || 0,
    focus_keyword_in_meta_desc: analysis?.focus_keyword_in_meta_desc || 0,
    focus_keyword_in_url: analysis?.focus_keyword_in_url || 0,
    meta_desc_within_160_limit: analysis?.meta_desc_within_160_limit || 0,
    internal_link_in_content: analysis?.internal_link_in_content || 0,
    external_link_in_content: analysis?.external_link_in_content || 0,
    alt_text_in_all_img: analysis?.alt_text_in_all_img || 0,
  };
};

const serializeDocOptimizationDetails = (analysis) => {
  return [
    {
      title: "Basic SEO Analysis",
      key: "BASIC_SEO",
      values: [
        {
          key: "UNIQUE_FOCUS_KEYWORD",
          title: "Focus keyword is unique",
          value: analysis?.unique_focus_keyword,
          percent: getPercentValue(analysis?.unique_focus_keyword, SCORES.UNIQUE_FOCUS_KEYWORD),
          isOptimized: calculateIsOptimized(analysis?.unique_focus_keyword, SCORES.UNIQUE_FOCUS_KEYWORD),
        },
        {
          key: "FOCUS_KEYWORD_IN_INTRODUCTION",
          title: "Focus keyword is used in the introduction",
          value: analysis?.focus_keyword_in_introduction,
          percent: getPercentValue(analysis?.focus_keyword_in_introduction, SCORES.FOCUS_KEYWORD_IN_INTRODUCTION),
          isOptimized: calculateIsOptimized(
            analysis?.focus_keyword_in_introduction,
            SCORES.FOCUS_KEYWORD_IN_INTRODUCTION
          ),
        },
        // {
        //   key: "CONTENT_MORE_THAN_800_WORDS",
        //   title: "Content should be more than 800 words",
        //   value: analysis?.content_more_than_800_words,
        //   percent: getPercentValue(analysis?.content_more_than_800_words, SCORES.CONTENT_MORE_THEN_800_WORDS),
        //   isOptimized: calculateIsOptimized(analysis?.content_more_than_800_words, SCORES.CONTENT_MORE_THEN_800_WORDS),
        //   hint: "To get a higher score, write content more than 800 words",
        // },

        {
          key: "CONTENT_MORE_THAN_200_WORDS",
          title: "Content should be more than 200 words",
          value: analysis?.content_more_than_200_words,
          percent: getPercentValue(analysis?.content_more_than_200_words, SCORES.CONTENT_MORE_THEN_200_WORDS),
          isOptimized: calculateIsOptimized(analysis?.content_more_than_200_words, SCORES.CONTENT_MORE_THEN_200_WORDS),
          // hint: "To get a higher score, write content more than 200 words",
        },
        {
          key: "FOCUS_KEYWORD_DENSITY",
          title: "Focus keyword is used 1-2% times of the doc content",
          value: analysis?.focus_keyword_density,
          percent: getPercentValue(analysis?.focus_keyword_density, SCORES.FOCUS_KEYWORD_DENSITY),
          isOptimized: calculateIsOptimized(analysis?.focus_keyword_density, SCORES.FOCUS_KEYWORD_DENSITY),
          hint: "To get a higher SEO score, use focus keywords more in the content",
        },
      ],
    },
    {
      title: "Detailed SEO Analysis",
      key: "DETAILED_SEO",
      values: [
        {
          key: "FOCUS_KEYWORD_IN_IMG_ALT_TEXT",
          title: "Focus keyword is used in image alt text",
          value: analysis.focus_keyword_in_img_alt_text,
          percent: getPercentValue(analysis.focus_keyword_in_img_alt_text, SCORES.FOCUS_KEYWORD_IN_IMG_ALT_TEXT),
          isOptimized: calculateIsOptimized(
            analysis.focus_keyword_in_img_alt_text,
            SCORES.FOCUS_KEYWORD_IN_IMG_ALT_TEXT
          ),
        },
        {
          key: "FOCUS_KEYWORD_IN_SUBHEADING",
          title: "Focus keyword found in subheadings",
          value: analysis.focus_keyword_in_subheading,
          percent: getPercentValue(analysis.focus_keyword_in_subheading, SCORES.FOCUS_KEYWORD_IN_SUBHEADING),
          isOptimized: calculateIsOptimized(analysis.focus_keyword_in_subheading, SCORES.FOCUS_KEYWORD_IN_SUBHEADING),
        },

        {
          key: "FOCUS_KEYWORD_IN_META_DESC",
          title: "Focus keyword found in meta description",
          value: analysis?.focus_keyword_in_meta_desc,
          percent: getPercentValue(analysis?.focus_keyword_in_meta_desc, SCORES.FOCUS_KEYWORD_IN_META_DESC),
          isOptimized: calculateIsOptimized(analysis?.focus_keyword_in_meta_desc, SCORES.FOCUS_KEYWORD_IN_META_DESC),
        },
        {
          key: "FOCUS_KEYWORD_IN_URL",
          title: "Focus Keyword used in the URL",
          value: analysis?.focus_keyword_in_url,
          percent: getPercentValue(analysis?.focus_keyword_in_url, SCORES.FOCUS_KEYWORD_IN_URL),
          isOptimized: calculateIsOptimized(analysis?.focus_keyword_in_url, SCORES.FOCUS_KEYWORD_IN_URL),
        },
        {
          key: "META_DESC_WITHIN_160_CHAR",
          title: "Meta description must be within 160 characters",
          value: analysis?.meta_desc_within_160_limit,
          percent: getPercentValue(analysis?.meta_desc_within_160_limit, SCORES.META_DESC_WITHIN_160_CHAR),
          isOptimized: calculateIsOptimized(analysis?.meta_desc_within_160_limit, SCORES.META_DESC_WITHIN_160_CHAR),
        },
        {
          key: "INTERNAL_LINK_IN_CONTENT",
          title: "1 internal link found in doc content",
          value: analysis?.internal_link_in_content,
          percent: getPercentValue(analysis?.internal_link_in_content, SCORES.INTERNAL_LINK_IN_CONTENT),
          isOptimized: calculateIsOptimized(analysis?.internal_link_in_content, SCORES.INTERNAL_LINK_IN_CONTENT),
        },
        {
          key: "EXTERNAL_LINK_IN_CONTENT",
          title: "1 external link found in doc content",
          value: analysis?.external_link_in_content,
          percent: getPercentValue(analysis?.external_link_in_content, SCORES.EXTERNAL_LINK_IN_CONTENT),
          isOptimized: calculateIsOptimized(analysis?.external_link_in_content, SCORES.EXTERNAL_LINK_IN_CONTENT),
        },
        {
          key: "ALT_TEXT_IN_ALL_IMG",
          title: "Add alt text in all images",
          value: analysis?.alt_text_in_all_img,
          percent: getPercentValue(analysis?.alt_text_in_all_img, SCORES.ALT_TEXT_IN_ALL_IMG),
          isOptimized: calculateIsOptimized(analysis?.alt_text_in_all_img, SCORES.ALT_TEXT_IN_ALL_IMG),
        },
      ],
    },
  ];
};

module.exports = {
  serializeDoc,
  serializeDocImage,
  serializeDocOptimizationDetails,
  serializeSingleDoc,
  getSocialMediaPreviewImages,
  serializeDocUpdatedMetadata,
};
