const googleApiService = require("../services/GoogleApiService");

const serializeShopifyLocationData = async (data, shop_id) => {
  const latestAddress = {
    address1: data.address1,
    address2: data.address2,
    city: data.city,
    zip: data.zip,
    country: data.country,
  };
  const { lat: latitude = "", lng: longitude = "" } =
    (await googleApiService.getLatLongFromAddress(latestAddress)) ?? {};

  return {
    shop_id,
    location_id: data.id,
    name: data.name,
    address1: data.address1,
    address2: data.address2,
    city: data.city,
    zip: data.zip,
    province: data.province,
    country: data.country,
    phone: data.phone,
    country_code: data.country_code,
    country_name: data.country_name,
    province_code: data.province_code,
    legacy: data.legacy,
    active: data.active,
    localized_country_name: data.localized_country_name,
    localized_province_name: data.localized_province_name,
    latitude,
    longitude,
  };
};

module.exports = {
  serializeShopifyLocationData,
};
