const { shopStatusByPlanName } = require("../utils/helper");

/**
 * @typedef {Object} ShopNode
 * @property {string} id
 * @property {string} name
 * @property {string} email
 * @property {string} url
 * @property {string} myshopifyDomain
 * @property {string} description
 * @property {string} accessToken
 * @property {string} currencyCode
 * @property {string} ianaTimezone
 * @property {Object} billingAddress
 * @property {{edges: Array<Object>}} metafields
 * @property {{displayName: string, shopifyPlus: boolean}} plan
 * @property {string} [logo_path]
 */

/**
 * @typedef {Object} SerializedShop
 * @property {string} [shop_id]
 * @property {string} name
 * @property {string} email
 * @property {string} url
 * @property {string} domain
 * @property {string} [description]
 * @property {string} [access_token]
 * @property {string} currency_code
 * @property {string} ianaTimezone
 * @property {Object} [billing_address]
 * @property {Array<Object>} [meta]
 * @property {string} shopify_plan_name
 * @property {boolean} [shopify_plus_subscription]
 * @property {string} [logo_path]
 * @property {string} [status]
 */

/**
 * @typedef {Object} WebhookShopData
 * @property {string} name
 * @property {string} email
 * @property {string} domain
 * @property {string} myshopify_domain
 * @property {string} currency
 * @property {string} plan_display_name
 * @property {string} plan_name
 * @property {string} iana_timezone
 */

/**
 * Serializes Shopify shop data into a consistent format
 * @param {ShopNode} shopifyShop
 * @returns {SerializedShop}
 */
const serializeShopifyShopData = (shopifyShop) => {
  return {
    shop_id: shopifyShop.id,
    name: shopifyShop.name,
    email: shopifyShop.email,
    url: shopifyShop.url,
    domain: shopifyShop.myshopifyDomain,
    description: shopifyShop.description,
    access_token: shopifyShop.accessToken,
    currency_code: shopifyShop.currencyCode,
    ianaTimezone: shopifyShop.ianaTimezone,
    billing_address: shopifyShop.billingAddress,
    meta: shopifyShop.metafields.edges.map((metafield) => metafield),
    shopify_plan_name: shopifyShop.plan.displayName,
    shopify_plus_subscription: shopifyShop.plan.shopifyPlus,
    logo_path: shopifyShop.logo_path,
  };
};

/**
 * Serializes webhook shop data into a consistent format
 * @param {WebhookShopData} body
 * @returns {SerializedShop}
 */
const serializeWebhookShopData = (body) => {
  return {
    name: body.name,
    email: body.email,
    url: `https://${body.domain}`,
    domain: body.myshopify_domain,
    currency_code: body.currency,
    shopify_plan_name: body.plan_display_name,
    status: shopStatusByPlanName(body.plan_name),
    ianaTimezone: body.iana_timezone,
  };
};

module.exports = {
  serializeShopifyShopData,
  serializeWebhookShopData,
};
