const planType = require("storeseo-enums/planType");
const { pick, omit, unionBy, isNull } = require("lodash");
const { MONTHLY, ANNUALLY } = require("storeseo-enums/planInterval");
const moment = require("moment");
const {
  calculatePlanValidity,
  extractRulesFromPlan,
  prepareIntervalText,
  preparePlanDescription,
} = require("../utils/helper");
const subscriptionAddonInterval = require("storeseo-enums/subscriptionAddonInterval");
const { features, featuresTitles } = require("../config/subscription-plan");

const serializeShopPlanData = (shop, subscriptionPlan, activeSubscription = null, addons = []) => {
  const serializedAddons = serilizeAddonsData(shop.plan_info, addons);

  const shouldUpdateRules = !(
    shop?.plan_info?.type === planType.FREE &&
    subscriptionPlan.type === planType.FREE &&
    shop.is_verified
  );

  return {
    plan_id: subscriptionPlan.id,
    plan_validity: calculatePlanValidity(subscriptionPlan.type, subscriptionPlan.interval),
    plan_rules: extractRulesFromPlan(shop.plan_rules, subscriptionPlan, serializedAddons, shouldUpdateRules),
    appSubscriptionId: activeSubscription?.id || null,
    appSubscriptionData: activeSubscription || null,
    plan_status: activeSubscription?.status || null,
    plan_info: serializePlanInfo(subscriptionPlan, serializedAddons),
    subscribed_at: moment(activeSubscription?.createdAt),
    is_verified: subscriptionPlan.type === planType.PRO ? true : shop.is_verified,
  };
};

const serializePlanInfo = (plan, addons = []) => {
  return {
    ...pick(plan, ["id", "name", "slug", "type", "interval", "price", "discount", "subtotal"]),
    price: parseFloat(plan.price.toFixed(2)),
    discount: parseFloat(plan.discount.toFixed(2)),
    subtotal: parseFloat(plan.subtotal.toFixed(2)),
    duration: plan?.meta?.duration,
    cappedAmount: plan?.meta?.cappedAmount,
    trialDays: plan?.meta?.trialDays,
    addons,
  };
};

const serilizeAddonsData = (planInfo, addons = []) => {
  const newAddons = addons?.map((addon) => omit(addon, ["featured", "order", "status", "created_at", "updated_at"]));

  const prevAddons = planInfo?.addons || [];
  const previouslyPurchasedCreditAddons = prevAddons.filter(
    (addon) => addon.interval === subscriptionAddonInterval.CREDIT
  );

  return unionBy(newAddons, previouslyPurchasedCreditAddons, "group");
};

const serializeSinglePlan = (plan, activePlan = null, withFeatures = false, combinedAddons = []) => {
  const { calculateCouponDiscount, calculatePlanIsUpgradable } = require("../utils/subscriptionCalculations");
  let couponDiscount = calculateCouponDiscount(plan, activePlan);

  combinedAddons = plan.addons ? combinedAddons.filter((addon) => plan.addons.includes(addon.id)) : [];

  const rules = {
    ...plan.rules,
    ...serializeAddonsPlanRules(plan.rules, combinedAddons),
  };

  return {
    ...plan,
    products: rules.products,
    rules,
    ...couponDiscount,
    isSubscribed: plan.id === activePlan?.id,
    isUpgradable: calculatePlanIsUpgradable(plan, activePlan),
    isFree: plan.type === planType.FREE,
    isMonthly: plan.interval === MONTHLY,
    isYearly: plan.interval === ANNUALLY,
    intervalText: prepareIntervalText(plan.interval),
    description: preparePlanDescription(plan.interval, couponDiscount.totalPrice, plan.meta?.duration),
    features: withFeatures ? subscriptionPlanFeaturesSerializer(plan) : undefined,
    combinedAddons,
  };
};

const subscriptionPlanFeaturesSerializer = (plan) => {
  return Object.keys(features).map((key) => ({
    key,
    title: featuresTitles[key],
    value: plan.rules[key],
  }));
};

/**
 * Serialize subscription plans
 * @param plans
 * @param activePlan
 * @returns {*}
 */
const subscriptionPlanSerializer = (plans, activePlan, combinedAddons = []) => {
  return plans.map((plan) => {
    return serializeSinglePlan(plan, activePlan, false, combinedAddons);
  });
};

const serializeSubscriptionPlansForTable = (plans, activePlan) => {
  const { calculateCouponDiscount, calculatePlanIsUpgradable } = require("../utils/subscriptionCalculations");
  const headings = [];
  const features = [];

  for (let i = 0; i < plans.length; i++) {
    const plan = plans[i];
    let couponDiscount = calculateCouponDiscount(plan, activePlan);
    headings.push({
      name: plan.name,
      interval: plan.interval,
      ...couponDiscount,
      isSubscribed: plan.id === activePlan?.id,
      isUpgradable: calculatePlanIsUpgradable(plan, activePlan),
      isFree: plan.type === planType.FREE,
      isMonthly: plan.interval === MONTHLY,
      isYearly: plan.interval === ANNUALLY,
      intervalText: prepareIntervalText(plan.interval),
      description: preparePlanDescription(plan.interval, couponDiscount.totalPrice, plan.meta?.duration),
    });
  }

  return {
    headings,
  };
};

const serializeCouponData = (coupon) => {
  return omit(coupon, ["id", "status", "created_at", "updated_at"]);
};

const serializeSubscriptionAddonPricing = (plan, addons = []) => {
  if (addons.length === 0) {
    return plan;
  }

  const total = addons?.reduce((price, row) => {
    let addonPrice = addons.find((addon) => addon.id === row.id)?.subtotal;
    return price + addonPrice;
  }, plan.subtotal);

  console.log(total, plan.subtotal);
};

const serializeCreditAddonsPlanRules = (planRules = {}, addons = []) => {
  return addons
    .filter((a) => a.interval === subscriptionAddonInterval.CREDIT)
    .reduce((rules, addon) => {
      let key = addon.group.toLowerCase();
      let limit = (rules?.[key] || 0) + addon.limit;

      return {
        ...rules,
        [key]: limit,
      };
    }, planRules);
};

const serializeAddonsPlanRules = (planRules = {}, addons = []) => {
  return addons.reduce((rules, addon) => {
    let key = addon.group.toLowerCase();
    let limit = (rules?.[key] || 0) + addon.limit;

    return {
      ...rules,
      [key]: limit,
    };
  }, planRules);
};

const serializeCreditAddonsPlanInfo = (planInfo, addons) => {
  const newAddons = addons?.map((addon) => omit(addon, ["featured", "order", "status", "created_at", "updated_at"]));

  let prevAddons = planInfo?.addons || [];

  planInfo.addons = unionBy(newAddons, prevAddons, (addon) => `${addon.group}-${addon.interval}`);

  return planInfo;
};

const subscriptionPlansFeaturesSerializer = (plans) => {
  const sortedPlans = plans.filter((p) => p.interval !== ANNUALLY).sort((a, b) => a.price - b.price);

  return {
    headings: ["Features", ...sortedPlans.map((plan) => plan.name)],
    features: Object.keys(features).map((key) => {
      const title = featuresTitles[key];
      return [key, title, ...sortedPlans.map((plan) => (isNull(plan.rules[key]) ? "Unlimited" : plan.rules[key]))];
    }),
  };
};

module.exports = {
  serializePlanInfo,
  serializeCouponData,
  serializeSinglePlan,
  serializeShopPlanData,
  subscriptionPlanSerializer,
  serializeSubscriptionPlansForTable,
  serializeSubscriptionAddonPricing,
  serilizeAddonsData,
  serializeCreditAddonsPlanRules,
  serializeCreditAddonsPlanInfo,
  subscriptionPlansFeaturesSerializer,
};
