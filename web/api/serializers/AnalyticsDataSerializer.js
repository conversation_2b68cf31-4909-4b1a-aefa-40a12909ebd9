const { google } = require("@google-analytics/data/build/protos/protos");
const { webmasters_v3 } = require("googleapis");
const aggregations = require("storeseo-enums/analytics/aggregations");
const dimensions = require("storeseo-enums/analytics/dimensions");
const metrics = require("storeseo-enums/analytics/metrics");

/**
 * @param {{ metricPos: number, dimensionPos: number, report: google.analytics.data.v1beta.IRunReportResponse }} param0
 * @returns {{ [string]: string }}
 */
const createMetricsByDimensionValueMap = ({ report, metricPos, dimensionPos }) => {
  const { rows } = report;
  const valueMap = {};

  for (let row of rows) {
    const { dimensionValues, metricValues } = row;
    const dimensionValue = dimensionValues[dimensionPos]?.value;
    const metricValue = metricValues[metricPos]?.value || 0;

    valueMap[dimensionValue] = metricValue;
  }

  return valueMap;
};

/**
 *
 * @param {{ dateRange: dateRange, report: google.analytics.data.v1beta.IRunReportResponse}} param0
 * @returns {import("storeseo-enums/analytics/jsDocTypes").SingleDimensionMetrics[]}
 */
const serializeSingleDimensionReportIntoMetricsArray = ({ report, dateRange }) => {
  const { dimensionHeaders, metricHeaders, totals } = report;
  console.log("\n\nreport: ", JSON.stringify(report, null, 2), "\n\n");
  const dimensionName = dimensionHeaders[0].name;

  return metricHeaders.map((header, idx) => ({
    metricName: header.name,
    dimensionName,
    dateRange,
    total: Number(totals[0].metricValues[idx]?.value) || 0,
    values: createMetricsByDimensionValueMap({ report, metricPos: idx, dimensionPos: 0 }),
  }));
};

/**
 * @param {{ currentArr: any[], prevArr: any[], dataKey: string, attachmentKey?: string, merge?: boolean}} param0
 *
 * @returns {any[]}
 */
const attachPrevDataToCurrentDataArr = ({
  currentArr,
  prevArr,
  dataKey,
  attachmentKey = "prevData",
  merge = false,
}) => {
  const prevValueMap = {};

  for (let data of prevArr) prevValueMap[data[dataKey]] = data;

  return currentArr.map((data) => ({
    ...data,
    ...(merge ? prevValueMap[data[dataKey]] : { [attachmentKey]: prevValueMap[data[dataKey]] }),
  }));
};

/**
 *
 * @param {{ report: google.analytics.data.v1beta.IRunReportResponse}} param0
 * @returns {import("storeseo-enums/analytics/jsDocTypes").PageReport[]}
 */
const serializeAnalyticsToPageReports = ({ report }) => {
  const { dimensionHeaders, metricHeaders } = report;

  const titlePos = dimensionHeaders.findIndex((h) => h.name === dimensions.UNIFIED_SCREEN_NAME);
  const pagePathPos = dimensionHeaders.findIndex((h) => h.name === dimensions.PAGE_PATH);

  const metricsNames = metricHeaders.map((h) => h.name);

  return report.rows.map((row) => {
    const { dimensionValues, metricValues } = row;
    return {
      title: dimensionValues[titlePos].value,
      path: dimensionValues[pagePathPos].value,
      metrics: metricsNames.reduce((map, name, idx) => ({ ...map, [name]: metricValues[idx]?.value || 0 }), {}),
    };
  });
};

/**
 *
 * @param {{ dateRange: import("storeseo-enums/analytics/jsDocTypes").dateRange}} param0
 * @returns {import("@google-analytics/data").protos.google.analytics.data.v1beta.IRunReportRequest}
 */
const craftRequestOptionsForKeywordsReport = ({ dateRange }) => {
  return {
    metrics: [{ name: metrics.TOTAL_USERS }],
    dimensions: [{ name: dimensions.SESSION_SA_360_KEYWORD }],
    dateRanges: [dateRange],
    metricAggregations: [aggregations.TOTAL],
    keepEmptyRows: true,
  };
};

/**
 * @param {{ dateRange: import("storeseo-enums/analytics/jsDocTypes").dateRange, report: google.analytics.data.v1beta.IRunReportResponse}} param0
 * @returns {import("storeseo-enums/analytics/jsDocTypes").KeywordsReport}
 */
const serializeAnalyticsDataToKeywordsReport = ({ report, dateRange }) => {
  const { totals } = report;
  return {
    dateRange,
    totalUsers: Number(totals[0].metricValues[0]?.value || 0),
  };
};

/**
 *
 * @param {{ data: webmasters_v3.Schema$ApiDataRow[], dimensions: string[]}} param0
 * @returns {import("storeseo-enums/searchConsole/jsDocTypes").SearchConsoleAnalyticsQueryData[]}
 */
const serializeSearchConsoleAnalyticsQueryData = ({ data, dimensions }) => {
  const mapKeysToDimensionValue = (keys, dimensions) => {
    const keyValueMap = {};

    dimensions.map((d, idx) => {
      keyValueMap[d] = keys[idx];
    });

    return keyValueMap;
  };

  return data.map(({ keys, ...d }) => ({
    ...d,
    ...mapKeysToDimensionValue(keys, dimensions),
  }));
};

/**
 *
 * @param {{ data: webmasters_v3.Schema$ApiDataRow[], key: string}} param0
 * @returns {{ [dateString]: number}}
 */
const mapDateWiseSearchConsoleAnalyticsQueryDataToSpecificKey = ({ data, key }) => {
  const mappedData = {};

  for (let d of data) {
    const { keys: dimensions } = d;

    mappedData[dimensions[0]] = d[key];
  }

  return mappedData;
};

module.exports = {
  serializeSingleDimensionReportIntoMetricsArray,
  serializeAnalyticsToPageReports,
  craftRequestOptionsForKeywordsReport,
  serializeAnalyticsDataToKeywordsReport,
  attachPrevDataToCurrentDataArr,
  serializeSearchConsoleAnalyticsQueryData,
  mapDateWiseSearchConsoleAnalyticsQueryDataToSpecificKey,
};
