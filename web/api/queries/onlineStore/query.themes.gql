query getThemes($first: Int, $after: String, $roles: [ThemeRole!], $fileNames: [String!]) {
  themes(first: $first, after: $after, roles: $roles) {
    edges {
      node {
        name
        id
        role
        files(filenames: $fileNames) {
          edges {
            node {
              filename
              body {
                ... on OnlineStoreThemeFileBodyText {
                  content
                }
              }
            }
          }
        }
      }
    }

    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
    }
  }
}
