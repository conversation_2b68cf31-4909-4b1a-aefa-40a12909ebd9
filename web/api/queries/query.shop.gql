query Shop {
  shop {
    id
    name
    email
    description
    url
    myshopifyDomain
    currencyCode
    ianaTimezone
    billingAddress {
      id
      address1
      address2
      city
      country
      zip
      company
      formatted
      formattedArea
      phone
    }
    plan {
      displayName
      partnerDevelopment
      shopifyPlus
    }
    metafields(first: 10, namespace: "global") {
      edges {
        node {
          id
          key
          description
          value
          type
          updatedAt
          namespace
        }
      }
    }
  }
}
