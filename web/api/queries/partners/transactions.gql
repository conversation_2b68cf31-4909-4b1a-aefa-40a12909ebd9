query Transactions(
  $appId: ID!
  $first: Int!
  $cursor: String
  $createdAtMin: DateTime
  $createdAtMax: DateTime
  $shopId: ID
) {
  transactions(
    first: $first
    after: $cursor
    appId: $appId
    createdAtMin: $createdAtMin
    createdAtMax: $createdAtMax
    shopId: $shopId
  ) {
    edges {
      cursor
      node {
        createdAt
        id
        __typename
        ... on AppSubscriptionSale {
          billingInterval
          id
          chargeId
          createdAt
          grossAmount {
            ...MoneyProps
          }
          netAmount {
            ...MoneyProps
          }
          shopifyFee {
            ...MoneyProps
          }
          shop {
            ...ShopProps
          }
        }
        ... on AppSaleAdjustment {
          id
          chargeId
          createdAt
          grossAmount {
            ...MoneyProps
          }
          netAmount {
            ...MoneyProps
          }
          shopifyFee {
            ...MoneyProps
          }
          shop {
            ...ShopProps
          }
        }
        ... on AppUsageSale {
          id
          chargeId
          createdAt
          grossAmount {
            ...MoneyProps
          }
          netAmount {
            ...MoneyProps
          }
          shopifyFee {
            ...MoneyProps
          }
          shop {
            ...ShopProps
          }
        }
        ... on AppOneTimeSale {
          id
          chargeId
          createdAt
          grossAmount {
            ...MoneyProps
          }
          netAmount {
            ...MoneyProps
          }
          shopifyFee {
            ...MoneyProps
          }
          shop {
            ...ShopProps
          }
        }
        ... on AppSaleCredit {
          id
          chargeId
          createdAt
          grossAmount {
            ...MoneyProps
          }
          netAmount {
            ...MoneyProps
          }
          shopifyFee {
            ...MoneyProps
          }
          shop {
            ...ShopProps
          }
        }
      }
    }
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
  }
}

fragment MoneyProps on Money {
  amount
  currencyCode
}

fragment ShopProps on Shop {
  id
  myshopifyDomain
}
