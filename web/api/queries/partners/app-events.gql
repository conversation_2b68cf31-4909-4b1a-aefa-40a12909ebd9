query AppEvents(
  $appId: ID!
  $first: Int!
  $cursor: String
  $occurredAtMin: DateTime
  $occurredAtMax: DateTime
  $shopId: ID
  $types: [AppEventTypes!]
) {
  app(id: $appId) {
    id
    name
    events(
      first: $first
      after: $cursor
      occurredAtMin: $occurredAtMin
      occurredAtMax: $occurredAtMax
      shopId: $shopId
      types: $types
    ) {
      edges {
        cursor
        node {
          occurredAt
          type
          shop {
            avatarUrl
            id
            myshopifyDomain
            name
          }
          ... on SubscriptionChargeActivated {
            occurredAt
            type
            charge {
              id
              name
              test
              amount {
                amount
                currencyCode
              }
              billingOn
            }
          }
          ... on SubscriptionChargeCanceled {
            occurredAt
            type
            charge {
              id
              name
              test
              amount {
                amount
                currencyCode
              }
              billingOn
            }
          }
          ... on SubscriptionChargeDeclined {
            occurredAt
            type
            charge {
              id
              name
              test
              amount {
                amount
                currencyCode
              }
              billingOn
            }
          }
          ... on SubscriptionChargeExpired {
            occurredAt
            type
            charge {
              id
              name
              test
              amount {
                amount
                currencyCode
              }
              billingOn
            }
          }
          ... on SubscriptionChargeFrozen {
            occurredAt
            type
            charge {
              id
              name
              test
              amount {
                amount
                currencyCode
              }
              billingOn
            }
          }
          ... on SubscriptionChargeUnfrozen {
            occurredAt
            type
            charge {
              id
              name
              test
              amount {
                amount
                currencyCode
              }
              billingOn
            }
          }
          ... on UsageChargeApplied {
            occurredAt
            type
            charge {
              id
              name
              test
              amount {
                amount
                currencyCode
              }
            }
          }
          ... on OneTimeChargeActivated {
            occurredAt
            type
            charge {
              id
              name
              test
              amount {
                amount
                currencyCode
              }
            }
          }
        }
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
      }
    }
  }
}
