fragment CollectionNode on Collection {
  id
  handle
  title
  descriptionHtml
  productsCount {
    count
  }
  image {
    id
    altText
    url
  }
  metafields(first: 2, keys: ["global.title_tag", "global.description_tag"]) {
    edges {
      node {
        ...MetafieldNode
      }
    }
  }
  metafield(key: "hidden", namespace: "seo") {
    ...MetafieldNode
  }
  storeSEOMetaFields: metafields(first: 10, namespace: "store_seo") {
    edges {
      node {
        ...MetafieldNode
      }
    }
  }
}
