fragment ProductNode on Product {
  id
  handle
  title
  descriptionHtml
  createdAt
  defaultCursor
  onlineStoreUrl
  onlineStorePreviewUrl
  featuredMedia {
    id
  }
  mediaImages: media(first: 250, sortKey: POSITION, query: "mediaContentType:IMAGE") {
    edges {
      node {
        ... on MediaImage {
          ...MediaImageNode
        }
      }
    }
  }
  metafields(first: 2, keys: ["global.title_tag", "global.description_tag"]) {
    edges {
      node {
        ...MetafieldNode
      }
    }
  }
  metafield(namespace: "seo", key: "hidden") {
    ...MetafieldNode
  }
  storeSEOMetaFields: metafields(first: 10, namespace: "store_seo") {
    edges {
      node {
        ...MetafieldNode
      }
    }
  }
  productType
  vendor
  tags
}
