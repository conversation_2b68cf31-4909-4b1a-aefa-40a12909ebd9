mutation ($product: ProductUpdateInput!) {
  productUpdate(product: $product) {
    product {
      metafields(first: 2, keys: ["global.title_tag", "global.description_tag"]) {
        edges {
          node {
            ...MetafieldNode
          }
        }
      }
      metafield(namespace: "seo", key: "hidden") {
        ...MetafieldNode
      }
      storeSEOMetaFields: metafields(first: 10, namespace: "store_seo") {
        edges {
          node {
            ...MetafieldNode
          }
        }
      }
    }
    userErrors {
      field
      message
    }
  }
}
