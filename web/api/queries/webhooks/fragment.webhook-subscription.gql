fragment WebhookSubscriptionNode on WebhookSubscription {
  id
  topic
  createdAt
  updatedAt
  format
  endpoint {
    __typename
    ... on WebhookHttpEndpoint {
      callbackUrl
    }
    ... on WebhookEventBridgeEndpoint {
      arn
    }
    ... on WebhookPubSubEndpoint {
      pubSubProject
      pubSubTopic
    }
  }
  includeFields
  metafieldNamespaces
  apiVersion {
    displayName
    handle
  }
}
