fragment AppSubscriptionNode on AppSubscription {
  id
  name
  createdAt
  currentPeriodEnd
  returnUrl
  status
  test
  trialDays
  lineItems {
    id
    plan {
      pricingDetails {
        __typename
        ... on AppRecurringPricing {
          interval
          discount {
            durationLimitInIntervals
            priceAfterDiscount {
              amount
              currencyCode
            }
            remainingDurationInIntervals
            value {
              __typename
              ... on AppSubscriptionDiscountAmount {
                amount {
                  amount
                  currencyCode
                }
              }

              ... on AppSubscriptionDiscountPercentage {
                percentage
              }
            }
          }
          price {
            amount
            currencyCode
          }
        }
        ... on AppUsagePricing {
          balanceUsed {
            amount
            currencyCode
          }
          cappedAmount {
            amount
            currencyCode
          }
          interval
          terms
        }
      }
    }
  }
}
