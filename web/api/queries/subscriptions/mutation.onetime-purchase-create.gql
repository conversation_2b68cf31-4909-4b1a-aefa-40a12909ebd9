mutation appPurchaseOneTimeCreate($name: String!, $price: MoneyInput!, $returnUrl: URL!, $test: Boolean!) {
  appPurchaseOneTimeCreate(name: $name, price: $price, returnUrl: $returnUrl, test: $test) {
    appPurchaseOneTime {
      id
      createdAt
      name
      price {
        amount
        currencyCode
      }
      status
      test
    }
    confirmationUrl
    userErrors {
      field
      message
    }
  }
}
