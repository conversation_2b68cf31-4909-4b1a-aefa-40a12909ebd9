//@ts-check
import TableEmptyState from "@/components/common/TableEmptyState";
import BulkImageOptimizationModal from "@/components/image-optimizer/BulkImageOptimizationModal.jsx";
import ImageRows from "@/components/images/ImageRows";
import { useSelector } from "react-redux";
import imageOptimization from "storeseo-enums/imageOptimization";

import useUserAddon from "@/hooks/useUserAddon.js";
import { useImageOptimizeActions, useImagesList } from "@/lib/hooks/image-optimizer";
import useIndexTablePagination from "@/lib/hooks/useIndexTablePagination";
import useIndexTableSort from "@/lib/hooks/useIndexTableSort";
import useIsRunningBackupRestore from "@/lib/hooks/useIsRunningBackupRestore.jsx";
import {
  generateBulkOptimizePayload,
  generateBulkRestorePayload,
} from "@/lib/utils/image-optimizer/imageOptimizerUtils.js";
import { useImageOptimizerLayoutContext } from "@/providers/ImageOptimizerLayoutProvider";
import { isSvgImage } from "@/utility/helpers.jsx";
import { IndexTable, useBreakpoints } from "@shopify/polaris";
import { isEmpty } from "lodash";
import { memo, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import ResourceType from "storeseo-enums/resourceType";

/**
 * @template T
 * @typedef {import("@shopify/polaris/build/ts/src/types.js").NonEmptyArray<T>} NonEmptyArray
 */

const resourceName = {
  singular: "image",
  plural: "images",
};

/**
 * @type {NonEmptyArray<{ title: string, key: string, alignment?: "start" | "center" | "end" }>}
 */
const tableHeadings = [
  { title: "", key: "sl" },
  { title: "File name", key: "file_name" },
  { title: "Status", key: "optimization_status" },
  { title: "File size", key: "file_size" },
  { title: "Article", key: "article.title" },
  { title: "Action", key: "action", alignment: "center" },
];

const ArticlesImageOptimizer = memo(() => {
  const { t } = useTranslation();
  const { smDown: isSmallDevice } = useBreakpoints();
  const { isRunning: isRunningBackupOrRestore } = useIsRunningBackupRestore();
  const { handleImagesUpdate, setImageToCompare } = useImageOptimizerLayoutContext();

  const { imageOptimizerUsageCount, imageOptimizerUsageLimit } = useUserAddon();

  // Use the reusable hook for images list
  const {
    images,
    formattedImages,
    pagination,
    hasEmptyContent,
    isLoading,
    queryKey,
    selectedResources,
    allResourcesSelected,
    handleSelectionChange,
    clearSelection,
    removeSelectedResources,
    selectableImagesCount,
  } = useImagesList(ResourceType.ARTICLE);

  // Use reusable image optimize actions hooks
  const { bulkOptimize, bulkRestore } = useImageOptimizeActions({
    queryKey,
    resourceType: ResourceType.ARTICLE,
    onOptimizeSuccess: () => {
      clearSelection();
      setShowBulkImageOptimizeModal(false);
    },
    onRestoreSuccess: () => {
      clearSelection();
    },
  });

  // Modal state
  const [showBulkImageOptimizeModal, setShowBulkImageOptimizeModal] = useState(false);

  useEffect(
    function removeUnoptimizableImagesFromSelection() {
      const imagesHash = images?.reduce((hash, img) => ({ ...hash, [img.id]: img }), {});

      const filteredResources = isRunningBackupOrRestore
        ? selectedResources.map((id) => id)
        : selectedResources.filter(
            (id) => imagesHash[id].optimization_status === imageOptimization.PENDING || isSvgImage(imagesHash[id].src)
          );

      if (filteredResources.length) removeSelectedResources(filteredResources);
    },
    [selectedResources, images, isRunningBackupOrRestore, removeSelectedResources]
  );

  const handleBulkOptimize = ({ compressionType, compressionFormat, resizeType }) => {
    const payload = generateBulkOptimizePayload({
      selectedResources,
      images,
      compressionType,
      compressionFormat,
      resizeType,
    });

    if (payload) {
      bulkOptimize.mutate(payload);
    }
  };

  const handleListRestore = () => {
    const payload = generateBulkRestorePayload({
      selectedResources,
      formattedImages,
    });

    if (payload) {
      bulkRestore.mutate(payload);
    }
  };
  const { handleSort, sortDir, sortIndex, defaultSortDir } = useIndexTableSort({ tableHeadings });
  const { paginationConfigs } = useIndexTablePagination(pagination);

  const emptyStateMarkup = (
    <TableEmptyState
      title="No images found"
      description="Try changing the filters or search term"
      withIllustration
    />
  );

  return !hasEmptyContent ? (
    <>
      <IndexTable
        condensed={isSmallDevice}
        resourceName={resourceName}
        itemCount={selectableImagesCount || formattedImages?.length || 0}
        // @ts-ignore
        headings={tableHeadings.map((heading) => ({
          ...heading,
          title: t(heading.title),
          alignment: heading.alignment || "start",
        }))}
        selectable={true}
        selectedItemsCount={allResourcesSelected ? "All" : selectedResources.length}
        onSelectionChange={(e, f, g) => {
          handleSelectionChange(e, f, g);
        }}
        onSort={handleSort}
        sortable={[false, false, true, true, false, false]}
        sortColumnIndex={sortIndex}
        // @ts-ignore
        sortDirection={sortDir}
        defaultSortDirection={defaultSortDir}
        emptyState={emptyStateMarkup}
        promotedBulkActions={[
          {
            content: `${t("Restore")} (${selectedResources.length})`,
            disabled: bulkRestore.isLoading,
            onAction: handleListRestore,
          },
          {
            content: `${t("Optimize")} (${selectedResources.length})`,
            disabled:
              bulkRestore.isLoading || imageOptimizerUsageCount + selectedResources.length > imageOptimizerUsageLimit,
            onAction: () => setShowBulkImageOptimizeModal(true),
          },
        ]}
        pagination={paginationConfigs}
      >
        {formattedImages.map((image, index) => (
          <ImageRows
            image={image}
            key={index}
            rowPosition={index}
            isFetching={isLoading}
            selectedResources={selectedResources}
            resourceType="ARTICLE"
            onOptimize={(images) => {
              handleImagesUpdate(images, queryKey);
            }}
            onRestore={(images) => {
              handleImagesUpdate(images, queryKey);
            }}
            onCompare={setImageToCompare}
          />
        ))}
      </IndexTable>

      <BulkImageOptimizationModal
        isOpen={showBulkImageOptimizeModal}
        onClose={() => setShowBulkImageOptimizeModal(false)}
        onOptimize={handleBulkOptimize}
        selectedCount={selectedResources.length}
        isLoading={bulkOptimize.isLoading}
      />
    </>
  ) : null;
});

export default ArticlesImageOptimizer;
