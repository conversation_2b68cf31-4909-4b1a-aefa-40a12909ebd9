//@ts-check
import { useImageApi } from "@/hooks";
import useUserAddon from "@/hooks/useUserAddon.js";
import { updateImagesInCache } from "@/lib/utils/image-optimizer/imageOptimizerUtils.js";
import { useMutation, useQueryClient } from "react-query";

/**
 * @typedef {keyof Pick<typeof import("storeseo-enums/resourceType"), "PRODUCT" | "COLLECTION" | "PAGE" | "ARTICLE">} ImageResourceType
 */

/**
 * Hook for bulk image optimization
 * @param {{
 *   queryKey: any[],
 *   onSuccess?: (data: any) => void
 * }} params
 * @returns {{
 *   isLoading: boolean,
 *   mutate: (data: any) => void
 * }}
 */
export const useBulkImageOptimize = ({ queryKey, onSuccess }) => {
  const imageApi = useImageApi();
  const queryClient = useQueryClient();
  const { updateImageOptimizerUsage } = useUserAddon();

  const { isLoading, mutate } = useMutation({
    mutationFn: (data) => imageApi.optimizeImagesViaQueue(data),
    onSuccess: (data) => {
      // Update cache with optimized images
      updateImagesInCache({
        queryClient,
        queryKey,
        updatedImages: data.images,
      });

      // Update usage
      updateImageOptimizerUsage(data.images.length);

      // Call custom onSuccess if provided
      if (onSuccess) {
        onSuccess(data);
      }
    },
  });

  return { isLoading, mutate };
};

/**
 * Hook for bulk image restoration
 * @param {{
 *   queryKey: any[],
 *   resourceType: ImageResourceType,
 *   onSuccess?: (data: any) => void
 * }} params
 * @returns {{
 *   isLoading: boolean,
 *   mutate: (data: any) => void
 * }}
 */
export const useBulkImageRestore = ({ queryKey, resourceType, onSuccess }) => {
  const imageApi = useImageApi();
  const queryClient = useQueryClient();

  const { isLoading, mutate } = useMutation({
    mutationFn: (data) => imageApi.restoreImage(data, resourceType),
    onSuccess: (data) => {
      // Update cache with restored images
      updateImagesInCache({
        queryClient,
        queryKey,
        updatedImages: data.images,
      });

      // Call custom onSuccess if provided
      if (onSuccess) {
        onSuccess(data);
      }
    },
  });

  return { isLoading, mutate };
};

/**
 * Combined hook that provides bulk optimize, restore, and single optimize functionality
 * @param {{
 *   queryKey: any[],
 *   resourceType: ImageResourceType,
 *   onOptimizeSuccess?: (data: any) => void,
 *   onRestoreSuccess?: (data: any) => void
 * }} params
 * @returns {{
 *   bulkOptimize: {
 *     isLoading: boolean,
 *     mutate: (data: any) => void
 *   },
 *   bulkRestore: {
 *     isLoading: boolean,
 *     mutate: (data: any) => void
 *   }
 * }}
 */
export const useImageOptimizeActions = ({
  queryKey,
  resourceType,
  onOptimizeSuccess,
  onRestoreSuccess,
}) => {
  const bulkOptimize = useBulkImageOptimize({
    queryKey,
    onSuccess: onOptimizeSuccess,
  });

  const bulkRestore = useBulkImageRestore({
    queryKey,
    resourceType,
    onSuccess: onRestoreSuccess,
  });

  return {
    bulkOptimize,
    bulkRestore,
  };
};
