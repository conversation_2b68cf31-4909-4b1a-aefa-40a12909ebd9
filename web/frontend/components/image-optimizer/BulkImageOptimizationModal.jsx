//@ts-check
import { useImageOptimizeSettings } from "@/lib/hooks/image-optimizer/query";
import BackupRestoreInfoBanner from "@/modules/components/BackupRestoreInfoBanner.jsx";
import {
  BlockStack,
  InlineGrid,
  Modal,
  Select,
  Text,
} from "@shopify/polaris";
import { AlertCircleIcon } from "@shopify/polaris-icons";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { IMAGE_OPTIMIZER_OPTIONS } from "../../config/index.js";
import { useBanner } from "../../hooks/useBanner.jsx";
import HintText from "../common/HintText.jsx";
import RadioGroup from "../common/RadioGroup.jsx";
import { IMAGE_OPTIMIZER_INITIAL_SETTINGS } from "./SettingsCard.jsx";

/**
 * Reusable Bulk Image Optimization Modal Component
 *
 * @example
 * ```jsx
 * <BulkImageOptimizationModal
 *   isOpen={showModal}
 *   onClose={() => setShowModal(false)}
 *   onOptimize={({ compressionType, compressionFormat, resizeType }) => {
 *     // Handle optimization with the selected settings
 *     console.log('Optimize with:', { compressionType, compressionFormat, resizeType });
 *   }}
 *   selectedCount={selectedImages.length}
 *   isLoading={isOptimizing}
 * />
 * ```
 *
 * @param {{
 *   isOpen: boolean,
 *   onClose: () => void,
 *   onOptimize: (settings: {compressionType: string, compressionFormat: string, resizeType: string}) => void,
 *   selectedCount: number,
 *   isLoading?: boolean
 * }} props
 */
export default function BulkImageOptimizationModal({
  isOpen,
  onClose,
  onOptimize,
  selectedCount,
  isLoading = false,
}) {
  const { t } = useTranslation();
  const { data: imageOptimizeSettings = IMAGE_OPTIMIZER_INITIAL_SETTINGS } = useImageOptimizeSettings();

  // Optimization settings state
  const [compressionType, setCompressionType] = useState(IMAGE_OPTIMIZER_INITIAL_SETTINGS.compressionType);
  const [compressionFormat, setCompressionFormat] = useState(IMAGE_OPTIMIZER_INITIAL_SETTINGS.format);
  const [resizeType, setResizeType] = useState(IMAGE_OPTIMIZER_INITIAL_SETTINGS.resize);

  // Handlers for settings changes
  const handleCompressionTypeSelectionChange = useCallback((item) => setCompressionType(item.value), []);
  const handleResizeTypeSelectionChange = useCallback((item) => setResizeType(item), []);

  // Error banner for validation
  const { Banner: OptimizationSettingError, showBanner: ShowOptimizationSettingError } = useBanner({
    title: t(
      "Please select at least one of 'image compression' or 'image format' or 'resize' options. Currently all selected settings are 'none'."
    ),
    noMargin: true,
    icon: AlertCircleIcon,
    tone: "critical",
  });

  // Initialize settings from saved preferences
  useEffect(
    function setInitialOptimizationSetting() {
      setCompressionType(imageOptimizeSettings?.compressionType || IMAGE_OPTIMIZER_INITIAL_SETTINGS.compressionType);
      setCompressionFormat(imageOptimizeSettings?.format || IMAGE_OPTIMIZER_INITIAL_SETTINGS.format);
      setResizeType(imageOptimizeSettings?.resize || IMAGE_OPTIMIZER_INITIAL_SETTINGS.resize);
      ShowOptimizationSettingError(false);
    },
    [imageOptimizeSettings, isOpen]
  );

  // Handle optimization action
  const handleOptimize = () => {
    // Validate that at least one optimization option is selected
    if (compressionType === "none" && compressionFormat === "none" && resizeType === "none") {
      return ShowOptimizationSettingError(true);
    }

    // Call the parent's optimize handler with current settings
    onOptimize({
      compressionType,
      compressionFormat,
      resizeType,
    });
  };

  return (
    <Modal
      open={isOpen}
      onClose={onClose}
      title={
        <>
          <Text
            as="h2"
            variant="headingMd"
            fontWeight="bold"
          >
            {t("Optimize Image")}
          </Text>
          <Text as="p" tone="subdued">
            {selectedCount} {t("Images")}
          </Text>
        </>
      }
      primaryAction={{
        content: t("Optimize"),
        onAction: handleOptimize,
        loading: isLoading,
      }}
    >
      <Modal.Section>
        <InlineGrid>
          <div
            style={{
              padding: "5px 16px 5px 0",
            }}
          >
            <BackupRestoreInfoBanner />
            <BlockStack gap="800">
              <OptimizationSettingError />
              <BlockStack gap="300">
                <BlockStack gap="100">
                  <Text
                    as="h3"
                    variant="headingMd"
                  >
                    {t("Image Compression Settings")}
                  </Text>
                  <Text
                    as="p"
                    tone="subdued"
                  >
                    {t("Choose your default image compression settings")}
                  </Text>
                </BlockStack>
                <div>
                  <Text as="p">{t("Compression Types")}:</Text>
                  <RadioGroup
                    items={IMAGE_OPTIMIZER_OPTIONS.COMPRESSION_TYPES}
                    selected={compressionType}
                    onChange={handleCompressionTypeSelectionChange}
                  ></RadioGroup>
                  <HintText
                    content={
                      IMAGE_OPTIMIZER_OPTIONS.COMPRESSION_TYPES?.find((ct) => ct.value === compressionType)?.hint
                    }
                  />
                </div>
                <Text as="p">
                  {IMAGE_OPTIMIZER_OPTIONS.COMPRESSION_TYPES.find((t) => t.value === compressionType)?.description}
                </Text>
              </BlockStack>
              <BlockStack gap="300">
                <BlockStack gap="100">
                  <Text
                    as="h3"
                    variant="headingMd"
                  >
                    {t("Advance Image Resizer")}
                  </Text>
                  <Text
                    as="p"
                    tone="subdued"
                  >
                    {t("Choose your default image size")}
                  </Text>
                </BlockStack>

                <Select
                  label={t("Resize your image to")}
                  options={[
                    { label: "Do not resize", value: "none" },
                    { label: "4000px", value: "4000" },
                    { label: "2048px (Recommended by Shopify)", value: "2048" },
                    { label: "1600px", value: "1600" },
                  ]}
                  onChange={handleResizeTypeSelectionChange}
                  value={resizeType}
                />
              </BlockStack>
            </BlockStack>
          </div>
        </InlineGrid>
      </Modal.Section>
    </Modal>
  );
}
