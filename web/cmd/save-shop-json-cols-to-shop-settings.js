const ShopService = require("../api/services/ShopService");
const { Shop, ShopSetting } = require("../sequelize");

(async () => {
  try {
    const shops = (await Shop.findAll()).map((s) => s.toJSON());

    console.log("\n---");
    console.log("Shops found: ", shops.length);
    console.log("---\n");

    for (let shop of shops) {
      const { id, domain, billing_address, jsonld_data, google_integration_info, mailchimp, meta } = shop;

      try {
        console.log("\nSaving JSON columns for shop: ", domain);
        const jsonCols = { billing_address, jsonld_data, google_integration_info, mailchimp, meta };

        for (key in jsonCols) {
          const data = {
            key,
            shop_id: id,
            value_type: "json",
            value: JSON.stringify(jsonCols[key]),
          };

          await ShopSetting.create(data);
        }

        console.log("Done!");
      } catch (err) {
        console.log("Error saving JSON columns for shop ", domain, err);
      }
    }
  } catch (err) {
    console.log("err: ", err);
  } finally {
    console.log("\n---\nAll Done!!\n---\n");
    process.exit(0);
  }
})();
