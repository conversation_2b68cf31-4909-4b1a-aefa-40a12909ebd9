const { sequelize } = require("../sequelize");

(async () => {
  try {
    console.log("Starting to delete orphaned article images...\n");

    // Execute raw SQL query to delete orphaned article images
    const [results, metadata] = await sequelize.query(`
      DELETE FROM article_images
      WHERE NOT EXISTS (
        SELECT 1 FROM articles
        WHERE articles.id = article_images.article_id
        AND articles.shop_id = article_images.shop_id
      )
    `);

    console.log("\nFinal Summary:");
    console.log("==============");
    console.log(`Total orphaned images deleted: ${metadata.rowCount}`);
  } catch (error) {
    console.error("Error in main process:", error);
  } finally {
    process.exit();
  }
})();
