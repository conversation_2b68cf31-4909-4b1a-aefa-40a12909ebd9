const { Shop } = require("../../sequelize");
const { ACTIVE } = require("storeseo-enums/shopStatus");
const cache = require("../../api/cache");

(async () => {
  console.log("----------------------------");
  console.log("⏳ Updating webhooks..");
  console.log("----------------------------");

  const shops = await Shop.findAll({
    attributes: ["id", "plan_id", "plan_rules", "domain"],
    where: { status: ACTIVE },
    order: [["id", "desc"]],
  });
  let sl = 1;

  for (let shop of shops) {
    await cache.webhooks.addShopToPendingWebhookRegistrationList(shop.domain);
    console.log(`${sl}/${shops.length} - Added ${shop.domain} to pending webhook registration list`);
    sl++;
  }

  console.log("Done");
})();
