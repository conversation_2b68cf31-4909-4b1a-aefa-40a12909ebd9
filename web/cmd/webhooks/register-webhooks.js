const ShopService = require("../../api/services/ShopService");
const WebhookService = require("../../api/services/WebhookService");

(async () => {
  try {
    const shopDomain = process.argv[2];
    if (shopDomain) {
      const shop = await ShopService.getShop(shopDomain);
      await registerWebhooks(shop);
    } else {
      const shops = await ShopService.getActiveShopDomains();
      for (const shop of shops) {
        await registerWebhooks(shop);
      }
    }
  } catch (err) {
    console.error(err);
  }
  process.exit();
})();

const registerWebhooks = async (shop) => {
  const session = {
    shop: shop.domain,
    accessToken: shop.access_token,
  };

  console.log("------------------------");
  console.log(`Registering webhooks for the shop: "${shop.domain}", PlanId: ${shop.plan_id}`);
  console.log("------------------------");

  await WebhookService.registerAllWebhooks(session, shop.id, shop.plan_id);
};
