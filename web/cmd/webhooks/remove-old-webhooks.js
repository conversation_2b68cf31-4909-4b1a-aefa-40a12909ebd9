// require("dotenv").config();
// const ShopifyWebhookService = require("../api/services/ShopifyWebhookService");
// const { WEBHOOKS } = require("../api/config/webhook");
// const TOPICS = require("storeseo-enums/webhook-topics");
// const ProductService = require("../services/ProductService");
const ShopifyWebhookService = require("../../api/services/ShopifyWebhookService");
const WebhookService = require("../../api/services/WebhookService");
const { sequelize } = require("../../sequelize");
const { DeliveryMethod } = require("@shopify/shopify-api");
const { QueryTypes } = require("sequelize");

const callbackUrl = "https://app.storeseo.com/webhooks";

(async () => {
  try {
    const dbWebhooks = await sequelize.query(
      `SELECT webhooks.id,webhooks.wh_subs_id,webhooks.shop_id,shops.domain,shops.access_token FROM webhooks LEFT JOIN shops ON shops.id=webhooks.shop_id WHERE webhooks.delivery_method='http' AND webhooks.response->'endpoint'->>'callbackUrl'='${callbackUrl}' ORDER BY webhooks.shop_id;`,
      { type: QueryTypes.SELECT }
    );

    let sl = 1;

    for (const dbWebhook of dbWebhooks) {
      let stats = `[${sl}/${dbWebhooks.length}]`;
      try {
        const session = {
          shop: dbWebhook.domain,
          accessToken: dbWebhook.access_token,
        };

        const webhooks = await ShopifyWebhookService.webhookSubscriptions(session.shop, {
          first: 20,
          topics: ["APP_UNINSTALLED"],
          callbackUrl: "https://app.storeseo.com/webhooks",
        });

        for (let webhook of webhooks) {
          try {
            await ShopifyWebhookService.webhookSubscriptionDelete(session.shop, webhook.id);
            await WebhookService.deleteWebhookByConditions({ wh_subs_id: webhook.id });
            console.info(
              stats,
              dbWebhook.domain,
              `Deleted ${webhook.topic} webhook for callbackURL: ${webhook.endpoint.callbackUrl}. Webhook ID: ${webhook.id}`
            );
          } catch (err) {
            await WebhookService.deleteWebhookByConditions({ wh_subs_id: webhook.id });
            console.error(3, stats, dbWebhook.domain, err.message);
          }
        }
        console.info(stats, dbWebhook.domain, "Processed Successfully.");
      } catch (err) {
        await WebhookService.deleteWebhookByConditions({ shop_id: dbWebhook.shop_id });
        console.error(2, stats, dbWebhook.domain, err.message);
      }
      sl++;
    }
  } catch (err) {
    console.error(1, err.message);
  }

  process.exit();
})();
