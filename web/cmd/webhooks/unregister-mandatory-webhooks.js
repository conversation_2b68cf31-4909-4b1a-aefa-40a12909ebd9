const ShopService = require("../../api/services/ShopService");
const { ACTIVE } = require("storeseo-enums/shopStatus");
const WebhookService = require("../../api/services/WebhookService");

(async () => {
  console.time("res");
  console.group();
  try {
    const shopDomain = process.argv[2];
    if (shopDomain) {
      const shop = await ShopService.getShop(shopDomain, ["domain", "access_token"]);
      const session = {
        shop: shop.domain,
        accessToken: shop.access_token,
      };
      await removeAndLog(session);
    } else {
      let sl = 1;
      for await (let shops of ShopService.iterateOverAllShops({ status: ACTIVE }, ["domain", "access_token"])) {
        for (let shop of shops) {
          const session = {
            shop: shop.domain,
            accessToken: shop.access_token,
          };

          await removeAndLog(session, sl);
          sl++;
        }
      }
    }
  } catch (err) {
    console.error(">> Webhook deletion error", err.message);
  }
  console.timeEnd("res");
  console.groupEnd();
})();

const removeAndLog = async (session, sl = 1) => {
  try {
    console.log(`>>`, sl, `(${session.shop}) - Removing mandatory webhooks`);
    const count = await WebhookService.removeMandatoryWebhooks({ session });
    console.log(`>>`, sl, `(${session.shop}) - Deleted ${count} webhooks \r\n`);
  } catch (error) {
    console.error(`>>`, sl, `(${session.shop}) -`, error.message, "\r\n");
  }
};
