const { Shop, Op } = require("../../sequelize");
const WebhookService = require("../../api/services/WebhookService");

(async () => {
  try {
    const domain = process.argv[2];

    let shop = await Shop.findOne({
      attributes: ["id", "domain", "access_token"],
      where: { domain },
    });

    shop = shop.toJSON();
    console.log(`Removing webhooks for shop (${shop.domain})`);
    await WebhookService.unregisterAllWebhooks({ shop: domain, accessToken: shop.access_token }, shop.id);
    console.log(`>> Webhook deleted for shop (${shop.id} - ${shop.domain}) done!`);
  } catch (err) {
    console.error(">> Webhook deletion error", err);
  }
  process.exit();
})();
