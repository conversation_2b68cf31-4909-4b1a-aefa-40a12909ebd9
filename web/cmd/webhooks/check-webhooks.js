const ShopifyWebhookService = require("../../api/services/ShopifyWebhookService");

(async () => {
  try {
    const domain = process.argv[2];

    console.log(`Shop: ${domain}`);

    const res = await ShopifyWebhookService.webhookSubscriptions(domain, { first: 50 });

    if (res.length === 0) {
      console.log("Webhook subscriptions not found.");
      return;
    }

    console.table(
      res.map((r) => ({
        topic: r.topic,
        // apiVersion: r?.apiVersion?.handle || "",
        createdAt: r.createdAt,
        updatedAt: r.updatedAt,
        callbackUrl: r.endpoint.callbackUrl,
      }))
    );

    // for (let i = 0; i < res.length; i++) {
    //   console.log(i, res[i].createdAt, res[i].topic, res[i].id, res[i].endpoint);
    // }
  } catch (err) {
    console.error("err =", err);
  }

  process.exit();
})();
