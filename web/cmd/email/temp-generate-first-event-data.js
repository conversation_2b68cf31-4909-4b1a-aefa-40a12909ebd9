const { QUEUE_NAMES } = require("../../api/queue/config");
const { dispatchQueue } = require("../../api/queue/queueDispatcher");
const { sequelize } = require("../../sequelize");

(async () => {
  const [shops] = await sequelize.query(`
    SELECT shops.id, shops."domain" FROM shops
    LEFT JOIN email_tracker et on shops.id = et.shop_id
    WHERE shops.plan_id > 0 AND shops.status = 'ACTIVE' AND et.topic is NULL;
  `);

  console.log(`Total Shops: `, shops.length);

  for (let shop of shops) {
    dispatchQueue({
      queueName: QUEUE_NAMES.EVENT_WEEKLY_STORE_REPORT,
      message: {
        shopDomain: shop?.domain,
      },
    });
  }

  console.log("Done.");
})();
