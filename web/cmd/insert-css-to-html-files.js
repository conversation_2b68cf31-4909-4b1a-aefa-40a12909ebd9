const path = require("path");
const fsPromise = require("fs/promises");
const csso = require("csso");

const BASE_PATH = path.resolve(__dirname, "../frontend");
const CSS_BASE_PATH = `${BASE_PATH}`;

const HTML_FILES = [
  "index.html",
  // "subscription.html",
  // "onboard.html"
];
const CSS_FILES = [
  "/assets/css/main.css",
  "/assets/css/skeleton.css",
  "/node_modules/react-multi-carousel/lib/styles.css",
];

const CLOSING_HEAD_TAG_STR = "</head>";

const STYLE_TAG_REG_EXP = /[\n\r\t\s]*<style inserted-by-cmd='true'>[^<]*<\/style>[\n\r\t]*/gim;

/**
 *
 * @param {string} basePath
 * @param {string[]} files
 * @returns
 */
const loadFiles = async (basePath, files) => {
  const loadedFiles = [];

  for (let filePath of files) {
    const content = await fsPromise.readFile(`${basePath}/${filePath}`, { encoding: "utf-8" });
    loadedFiles.push({ file: filePath, content });
  }

  return loadedFiles;
};

/**
 *
 * @param {string} basePath
 * @param {{ file: string, content: string}[]} files
 * @returns
 */
const writeFiles = async (basePath, files) => {
  for (let f of files) {
    console.log("Writing file: ", f.file);
    const content = await fsPromise.writeFile(`${basePath}/${f.file}`, f.content, { encoding: "utf-8" });
  }
};

/**
 *
 * @param {string} originalStr
 * @param {string} textToInsert
 * @param {string | RegExp} insertPoint
 */
const insertTextInString = (originalStr, textToInsert, insertPoint) => {
  const partitions = originalStr.split(insertPoint);

  return `${partitions[0]}${textToInsert}${insertPoint}${partitions[1]}`;
};

/**
 *
 * @param {string} originalStr
 * @param {RegExp | string} textToRemove
 */
const removeTextFromString = (originalStr, textToRemove) => {
  return originalStr.replace(textToRemove, "");
};

/**
 *
 * @param {string} cssString
 * @returns {string}
 */
const minifyCSS = (cssString) => {
  const result = csso.minify(cssString, {
    restructure: false,
  });

  return result.css;
};

/**
 *
 * @param {{ file: string, content: string }[]} htmlFiles
 * @param {{file: string, content: string}[]} cssFiles
 */
const insertCssToHtmlFiles = (htmlFiles, cssFiles) => {
  for (let html of htmlFiles) {
    for (let css of cssFiles) {
      const minifiedCss = minifyCSS(css.content);
      const cssStyleSheet = `
      <style inserted-by-cmd='true'>
      \t${minifiedCss}
      </style>
      `;

      html.content = insertTextInString(html.content, cssStyleSheet, CLOSING_HEAD_TAG_STR);
    }
  }

  return htmlFiles;
};

/**
 *
 * @param {{ file: string, content: string }[]} htmlFiles
 * @param {string | RegExp} searchValue
 */
const removeContentsFromHtmlFiles = (htmlFiles, searchValue) => {
  const regExp = new RegExp(searchValue);

  for (let f of htmlFiles) {
    f.content = removeTextFromString(f.content, regExp);
  }

  return htmlFiles;
};

(async () => {
  try {
    console.log("\n\nLoading CSS files...");
    const cssFiles = await loadFiles(BASE_PATH, CSS_FILES);

    console.log("Loading HTML files...");
    const htmlFiles = await loadFiles(BASE_PATH, HTML_FILES);

    console.log("Removing old CSS styles...");
    removeContentsFromHtmlFiles(htmlFiles, STYLE_TAG_REG_EXP);

    console.log("Adding CSS to HTML files...");
    insertCssToHtmlFiles(htmlFiles, cssFiles);

    console.log("Writing HTML files...");
    await writeFiles(BASE_PATH, htmlFiles);
    console.log("Done!!\n\n");
  } catch (err) {
    console.log("err: ", err);
  } finally {
    process.exit(0);
  }
})();
