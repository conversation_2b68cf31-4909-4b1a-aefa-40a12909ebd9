// const { Shop, SubscriptionPlan, sequelize } = require("../sequelize");
// const { serializePlanInfo } = require("../api/serializers/SubscriptionSerializer");
// const SubscriptionUsageService = require("../api/services/SubscriptionUsageService");
// const ShopifyService = require("../api/services/ShopifyService");
// const { calculateCouponDiscount } = require("../api/utils/subscriptionCalculations");

// (async () => {
//   const [shops] = await sequelize.query(`
//     SELECT
//       shops.id,
//       shops.domain,
//       shops.access_token,
//       su.price,
//       shops.plan_id,
//       shops.plan_info
//     FROM
//       shops
//       LEFT JOIN subscription_usages su ON shops.id = su.shop_id
//       LEFT JOIN shopify_transactions st ON st.shop_domain = shops. "domain"
//     WHERE
//       shops.plan_id = 8
//       AND su.price > 53.82
//       AND shops.status = 'ACTIVE'
//       AND shops.email NOT LIKE '%@wpdeveloper.%'
//       AND st.transaction_id NOT LIKE '%/AppUsageSale/%'
//     GROUP BY
//       shops.id,
//       shops.domain,
//       su.price
//     ORDER BY
//       shops.subscribed_at DESC;
//   `);

//   let subscriptionPlan = (await SubscriptionPlan.findByPk(8)).toJSON();

//   const total = shops.length;
//   let sl = 1;
//   let success = 0;
//   let failed = 0;

//   for (let shop of shops) {
//     console.log("--------------------------------");
//     console.log(`${sl}/${total}`, `Creating charge for the shop "${shop.domain}"`);

//     try {
//       const session = {
//         shop: shop.domain,
//         accessToken: shop.access_token,
//       };

//       let calculatedPrice = calculateCouponDiscount(subscriptionPlan);

//       subscriptionPlan = { ...subscriptionPlan, ...calculatedPrice };

//       const serializedAddons = shop.plan_info?.addons || [];
//       const planInfo = serializePlanInfo(subscriptionPlan, serializedAddons);

//       const [rows, updatedShops] = await Shop.update(
//         { plan_info: planInfo },
//         { where: { id: shop.id }, returning: true }
//       );

//       const updatedShop = updatedShops[0].toJSON();

//       await SubscriptionUsageService.deleteUsageByConditions({ shop_id: shop.id });
//       const usage = await SubscriptionUsageService.updateOrCreateUsage(updatedShop);
//       const res = await ShopifyService.createUsageCharge(session, usage);
//       await SubscriptionUsageService.updateUsage(usage.id, { meta: { ...res } });
//       console.log("Usage record created with these data", JSON.stringify(res));
//       success++;
//     } catch (error) {
//       console.error("error =", error);
//       failed++;
//     }

//     sl++;
//     console.log("-------------------------------- \n");
//   }

//   console.table({ total, success, failed });

//   setTimeout(process.exit, 3000);
// })();
