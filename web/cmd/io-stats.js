const redisClient = require("../api/cache/client");
const cacheKeys = require("storeseo-enums/cacheKeys");
const moment = require("moment");
const SlackService = require("../api/services/SlackService");

(async () => {
  try {
    console.log("==================================================================");
    let date = process.argv[2];
    let send = process.argv[3] || false;
    const startTime = moment(date).subtract(1, "d").tz("Asia/Dhaka").startOf("D").toISOString();
    const endTime = moment(date).subtract(1, "d").tz("Asia/Dhaka").endOf("D").toISOString();

    console.log("Start Time =", startTime);
    console.log("End Time =", endTime);

    const subscriptionData = await SlackService.getIOSubscriptionData(startTime, endTime);

    console.log("** Subscription data =");
    console.table(subscriptionData);

    const ioData = await SlackService.imageOptimizerData(date);

    console.log("------------------------------------------------------------------");
    console.log("** Image optimizer data =");
    console.log(ioData);
    console.log("------------------------------------------------------------------");

    const totalOptimized = await redisClient.get(`${cacheKeys.IMAGE_OPTIMIZER}:${cacheKeys.TOTAL_USAGE_COUNT}`);
    console.log("** Total optimized image =", totalOptimized);

    if (send) await SlackService.sendImageOptimizerStats(date);
  } catch (error) {
    console.error(error);
  }

  process.exit();
})();
