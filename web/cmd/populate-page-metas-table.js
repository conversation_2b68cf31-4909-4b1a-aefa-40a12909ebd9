require("../api/config");
const pageTypes = require("storeseo-enums/pageType");
const { Page: P, PageMeta: Pm } = require("../sequelize");

/**
 * @type {typeof import("sequelize").Model}
 */
const Page = P;
/**
 * @type {typeof import("sequelize").Model}
 */
const PageMeta = Pm;

(async () => {
  try {
    const pages = (await Page.findAll({ order: [["id", "ASC"]] })).map((p) => p.toJSON());

    console.log("Total page found: ", pages.length, "\n");

    for (let page of pages) {
      try {
        const { id: page_id, shop_id, metafields = [] } = page;

        console.log("Inserting meta for page id: ", page_id);

        for (let meta of metafields) {
          const { id, namespace, key, type, value } = meta;
          const data = {
            shop_id,
            page_id,
            rest_meta_id: page.page_type === pageTypes.HOMEPAGE ? `${pageTypes.HOMEPAGE}-${key}` : id,
            namespace,
            key,
            type,
            value,
          };

          await PageMeta.upsert(data);
        }
        console.log("Done.\n");
      } catch (err) {
        console.log("Failed, ", err);
        console.log("\n");
      }
    }

    console.log("\n\n====");
    console.log("All done!");
    console.log("====\n\n");
  } catch (err) {
    console.log("err: ", err);
  } finally {
    process.exit(0);
  }
})();
