const { Shop } = require("../sequelize");
const ShopifyService = require("../api/services/ShopifyService");
const ShopService = require("../api/services/ShopService");
const { CLOSED, UNINSTALLED } = require("storeseo-enums/shopStatus");
const { shopStatusByApiResponseCode } = require("../api/utils/helper");

(async () => {
  try {
    const shops = await Shop.findAll({
      where: { status: "ACTIVE" },
      attributes: ["id", "name", "shop_id", "domain", "access_token", "status"],
      order: ["created_at"],
    });

    let activeCount = 0;
    let closedCount = 0;
    let uninstalledCount = 0;
    for (let i = 0; i < shops.length; i++) {
      const shop = shops[i].toJSON();
      const user = {
        shop: shop.domain,
        accessToken: shop.access_token,
      };

      try {
        const handle = await ShopifyService.getAppHandle(user.shop);
        console.log(i + 1, `Shop -- ${shop.domain} -- ACTIVE.`);
        activeCount++;
      } catch (err) {
        let status = shopStatusByApiResponseCode(err?.response?.code);
        await ShopService.updateShop(shop.id, { status });
        console.error(i + 1, `Shop -- ${shop.domain} -- ${status}.`, err.message);

        if (status === CLOSED) {
          closedCount++;
        }
        if (status === UNINSTALLED) {
          uninstalledCount++;
        }
      }
    }

    console.log("-------------------");
    console.log("Total Shops:", shops.length);
    console.log("Active Shops:", activeCount);
    console.log("Closed Shops:", closedCount);
    console.log("Uninstalled Shops:", uninstalledCount);
    console.log("-------------------");
  } catch (err) {
    console.error(err);
  }
})();
