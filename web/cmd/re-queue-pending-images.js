/**
 * ********
 * Re-queue 'pending/processing/dispatched/saving' state images for optimization and
 * restore the usage count for each store accordingly
 * ********
 * example: node cmd/re-queue-pending-images.js
 */

const { ProductImage } = require("../sequelize");
const { Op } = require("sequelize");
const imageOptimization = require("storeseo-enums/imageOptimization");
const ShopService = require("../api/services/ShopService");
const cache = require("../api/cache");

(async () => {
  try {
    console.log("\n\nFetching list of pending images from database...");

    const pendingImages = await listOfPendingImages();
    console.log(`Found ${pendingImages.length} images pending.\n\n`);

    console.log("Restting images for re-optimization...");
    await resetPendingImagesForReoptimization();
    console.log("Done!\n\n");

    console.log("Determining the list of associated stores for the resetted images...");
    const shopIds = new Set();
    pendingImages.forEach((img) => shopIds.add(img.shop_id));

    console.log("Queueing the stores for image optimization...");

    for (let id of shopIds) {
      try {
        const shop = await ShopService.getShopById(id);
        await cache.addStoreToPendingImageOptimizationQueue(shop.domain);
      } catch (err) {
        shopIds.delete(id);
      }
    }
    console.log("Done!\n\n");

    console.log(`Found ${shopIds.size} stores...`);
    console.log("List of stores by id:");
    console.table(Array.from(shopIds));
  } catch (err) {
    console.log(err);
  } finally {
    process.exit(0);
  }
})();

async function listOfPendingImages() {
  const images = await ProductImage.findAll({
    where: {
      optimization_status: {
        [Op.in]: [
          imageOptimization.PENDING,
          imageOptimization.DISPATCHED,
          imageOptimization.PROCESSING,
          imageOptimization.SAVING,
        ],
      },
    },
  });

  return images.map((img) => img.toJSON());
}

async function resetPendingImagesForReoptimization() {
  await ProductImage.update(
    {
      optimization_status: imageOptimization.PENDING,
    },
    {
      where: {
        optimization_status: {
          [Op.in]: [
            imageOptimization.PENDING,
            imageOptimization.DISPATCHED,
            imageOptimization.PROCESSING,
            imageOptimization.SAVING,
          ],
        },
      },
    }
  );
}
