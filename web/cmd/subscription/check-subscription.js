require("dotenv").config();
const { omit } = require("lodash");
const ShopifyService = require("../../api/services/ShopifyService");
const ShopService = require("../../api/services/ShopService");

(async () => {
  try {
    const domain = process.argv[2];

    const shop = await ShopService.getShop(domain);

    const session = {
      shop: domain,
      accessToken: shop.access_token,
    };

    const activeSubscription = await ShopifyService.getActiveSubscription(session.shop);

    console.log(`Shop ID: ${shop.id}, Status: ${shop.status}, Plan ID: ${shop.plan_id}`);

    console.table({ plan_info: omit(shop.plan_info, ["addons"]) });
    console.table({ plan_rules: shop.plan_rules });

    if (shop.plan_info?.addons) {
      console.table(
        shop.plan_info?.addons.reduce((ad, item) => {
          ad[item.group] = item;
          return ad;
        }, {})
      );
    }

    console.table({ appSubscriptionData: omit(shop.appSubscriptionData, ["lineItems"]) });

    console.table({ activeSubscription: omit(activeSubscription, ["lineItems"]) });

    if (activeSubscription?.lineItems) console.log(JSON.stringify(activeSubscription?.lineItems, null, 2));
  } catch (err) {}

  process.exit();
})();
