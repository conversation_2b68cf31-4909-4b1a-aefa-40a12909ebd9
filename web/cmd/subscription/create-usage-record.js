const ShopService = require("../../api/services/ShopService");
const { USAGE } = require("storeseo-enums/planInterval");
const SubscriptionUsageService = require("../../api/services/SubscriptionUsageService");
const ShopifyService = require("../../api/services/ShopifyService");

(async () => {
  try {
    const domain = process.argv[2];

    const shop = await ShopService.getShop(domain);

    if (shop?.plan_info?.interval === USAGE) {
      const session = {
        shop: shop.domain,
        accessToken: shop.access_token,
      };
      const usage = await SubscriptionUsageService.updateOrCreateUsage(shop);
      const res = await ShopifyService.createUsageChargeUsingDBSession(session, usage);
      console.log("Usage record created with these data =", JSON.stringify(res));
      await SubscriptionUsageService.updateUsage(usage.id, { meta: { ...res } });
    }
  } catch (e) {
    console.error(e);
  }

  setTimeout(process.exit, 3000);
})();
