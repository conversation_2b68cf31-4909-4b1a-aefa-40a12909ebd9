require("dotenv").config();
const ShopifyService = require("../../api/services/ShopifyService");
const ShopService = require("../../api/services/ShopService");
const { sleep } = require("../../api/utils/helper");

(async () => {
  try {
    const domain = process.argv[2];

    const shop = await ShopService.getShop(domain);

    const session = {
      shop: domain,
      accessToken: shop.access_token,
    };

    console.info("Current Subscription ID =", shop.appSubscriptionId);

    await ShopifyService.appSubscriptionCancel(session.shop, shop.appSubscriptionId);

    console.log(`ℹ️ Subscription cancelled for the shop =`, domain);

    await sleep(3000, false);

    const updatedShop = await ShopService.getShop(domain);

    console.table({ plan_info: omit(updatedShop.plan_info, ["addons"]) });
    console.table({ plan_rules: updatedShop.plan_rules });

    if (updatedShop.plan_info?.addons) {
      console.table(
        updatedShop.plan_info?.addons.reduce((ad, item) => {
          ad[item.group] = item;
          return ad;
        }, {})
      );
    }

    console.table({ appSubscriptionData: omit(updatedShop.appSubscriptionData, ["lineItems"]) });
  } catch (err) {}

  process.exit();
})();
