const { QueryTypes } = require("sequelize");
const { sequelize } = require("../../sequelize");
const ShopService = require("../../api/services/ShopService");
const SubscriptionUsageService = require("../../api/services/SubscriptionUsageService");
const ShopifyService = require("../../api/services/ShopifyService");
const { USAGE } = require("storeseo-enums/planInterval");

(async () => {
  try {
    const stores = await sequelize.query(
      `SELECT
      sae.shop_domain,
      sae.charge_name,
      shops.status
    FROM
      shopify_app_events AS sae
      JOIN shops ON shops."domain" = sae.shop_domain AND shops.status='ACTIVE'
      LEFT JOIN subscription_usages as su ON su.shop_id = shops.id
    WHERE
      sae.event_type = 'SUBSCRIPTION_CHARGE_ACTIVATED'
      AND sae.charge_name ILIKE '%visionary%'
      AND sae.occurred_at > '2024-09-19'
      AND sae.is_test = FALSE
      AND su.subscription_id is NULL
      AND su.subscription_li_id is NULL
      AND su.meta is NULL
    ORDER BY 
      occurred_at DESC;`,
      { type: QueryTypes.SELECT }
    );

    const total = stores.length;
    let success = 0;
    let failed = 0;
    let row = 0;
    console.log(`Total Shops found to create visionary charge =`, total);

    for (let store of stores) {
      const sl = `(${++row}/${total})`;
      try {
        const shop = await ShopService.getShop(store.shop_domain);

        if (shop?.plan_info?.interval === USAGE) {
          const session = {
            shop: shop.domain,
            accessToken: shop.access_token,
          };
          const usage = await SubscriptionUsageService.updateOrCreateUsage(shop);
          const res = await ShopifyService.createUsageChargeUsingDBSession(session, usage);
          await SubscriptionUsageService.updateUsage(usage.id, { meta: { ...res } });

          console.log(
            sl,
            `Usage record created for "${store.shop_domain}", Amount: $${res.price.amount}, LI: ${res.subscriptionLineItem.id}`
          );

          success++;
        } else {
          failed++;

          console.log(sl, "Not in visionary", store.shop_domain);
        }
      } catch (error) {
        failed++;
        console.error(sl, "error =", error.message);
      }
    }

    console.table({ total, success, failed });
  } catch (error) {
    console.error(error);
  } finally {
    process.exit();
  }
})();
