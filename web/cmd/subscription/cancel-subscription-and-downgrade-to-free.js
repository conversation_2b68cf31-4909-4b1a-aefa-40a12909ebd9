const subscriptionAddonInterval = require("storeseo-enums/subscriptionAddonInterval");
const { QUEUE_NAMES } = require("../../api/queue/config");
const { dispatchQueue } = require("../../api/queue/queueDispatcher");
const { serializeShopPlanData } = require("../../api/serializers/SubscriptionSerializer");
const AddonUsageService = require("../../api/services/AddonUsageService");
const ShopifyService = require("../../api/services/ShopifyService");
const ShopService = require("../../api/services/ShopService");
const SubscriptionPlanService = require("../../api/services/SubscriptionPlanService");
const SubscriptionService = require("../../api/services/SubscriptionService");
const SubscriptionTransactionService = require("../../api/services/SubscriptionTransactionService");
const appSubscriptionStatus = require("storeseo-enums/appSubscriptionStatus");

(async () => {
  try {
    const domain = process.argv[2];
    const shopData = await ShopService.getShop(domain);
    const shopId = shopData.id;
    const session = {
      shop: domain,
      accessToken: shopData.access_token,
    };
    const subscriptionPlan = await SubscriptionPlanService.getFreeSubscriptionPlan();
    const creditAddons =
      shopData.plan_info?.addons?.filter((a) => a.interval === subscriptionAddonInterval.CREDIT) || [];
    const recurringAddons =
      shopData.plan_info?.addons?.filter(
        (a) => a.interval === subscriptionAddonInterval.MONTHLY || a.interval === subscriptionAddonInterval.ANNUALLY
      ) || [];

    const currentSubscriptionId = shopData.appSubscriptionId || shopData.appSubscriptionData?.id;
    if (currentSubscriptionId) await ShopifyService.appSubscriptionCancel(session.shop, currentSubscriptionId);

    const updateData = serializeShopPlanData(
      shopData,
      subscriptionPlan,
      {
        status: appSubscriptionStatus.ACTIVE,
      },
      creditAddons
    );

    const plan_rules = { ...subscriptionPlan.rules, ...updateData.plan_rules };

    const updatedShopData = await SubscriptionService.updateShopSubscriptionPlan(
      { id: shopId },
      { ...updateData, plan_rules }
    );

    if (recurringAddons.length > 0) {
      for (rAddon of recurringAddons) {
        await AddonUsageService.updateRecordById(rAddon.id, { is_active: false });
      }
    }
    await SubscriptionTransactionService.createTransaction(updatedShopData);

    dispatchQueue({
      queueName: QUEUE_NAMES.SUBSCRIPTION_DOWNGRADE,
      message: {
        shopId,
        plan: subscriptionPlan,
      },
    });

    console.log(`☑️ -- Subscription plan cancelled and downgraded to FREE`, {
      shop: domain,
      permissions: updatedShopData.plan_rules,
      addons: updatedShopData.plan_info?.addons,
    });
  } catch (error) {
    console.error("error =", error);
  } finally {
    setTimeout(process.exit, 5000);
  }
})();
