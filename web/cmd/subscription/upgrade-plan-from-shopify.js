/**
 * Checks subscription from shopify and updates the database
 * takes shop domain, plan slug & addon ids as params
 */
require("../api/config");
const cache = require("../api/cache");
const { Shop } = require("../sequelize");
const ShopifyService = require("../api/services/ShopifyService");
const SubscriptionService = require("../api/services/SubscriptionService");

(async () => {
  try {
    const shopDomain = process.argv[2];
    const slug = process.argv[3];
    const addons = process.argv?.[4]?.split(",") || [];

    if (!shopDomain || !slug) {
      console.error("Please put a shop domain and slug as arguments.");
      process.exit();
    }

    const shop = await Shop.findOne({
      where: { domain: shopDomain },
      attributes: ["id", "email", "domain", "plan_id", "plan_rules", "plan_info", "access_token", "shop_id"],
    });

    const session = { shop: shopDomain, accessToken: shop.access_token };

    const app_subscription = await ShopifyService.getActiveSubscription(session.shop);

    console.info("app_subscription =", app_subscription);

    await cache.tempSubsctiptionSet(shopDomain, {
      id: app_subscription.id,
      plan: slug,
      addons: Object.values(addons),
    });

    await SubscriptionService.subscriptionUpdate(
      session,
      {
        ...app_subscription,
        admin_graphql_api_shop_id: shop.shop_id,
        admin_graphql_api_id: app_subscription.id,
      },
      true
    );
  } catch (err) {
    console.error(err);
  }
  process.exit();
})();
