/**
 * Manually upgrades plan to database without checking actual plan
 * takes shop domain, plan slug & addon ids as params
 */
const SubscriptionService = require("../../api/services/SubscriptionService");
const SubscriptionPlanService = require("../../api/services/SubscriptionPlanService");
const { ACTIVE } = require("storeseo-enums/appSubscriptionStatus");
const { pick } = require("lodash");
const SubscriptionAddonService = require("../../api/services/SubscriptionAddonService");
const ShopService = require("../../api/services/ShopService");
const { serializeShopPlanData } = require("../../api/serializers/SubscriptionSerializer");

const upgradePlan = async (shopDomain, planSlug, addonIds = "") => {
  try {
    const subscriptionPlan = await SubscriptionPlanService.getSubscriptionPlanBySlug(planSlug);
    let shop = await ShopService.getShop(shopDomain);
    let addons = [];

    if (addonIds) {
      addons = await SubscriptionAddonService.getSelectedAddons(addonIds.split(","));
    }

    const updateData = serializeShopPlanData(shop, subscriptionPlan, { status: ACTIVE }, addons);

    shop = await SubscriptionService.updateShopSubscriptionPlan({ domain: shopDomain }, updateData);
    console.log("Subscription upgraded", pick(shop, ["id", "domain", "plan_id", "plan_rules", "plan_info"]));
  } catch (err) {
    console.log("Error upgrading subscription", err);
  }
};

(async () => {
  const [, , shopDomain, planSlug, addonIds] = process.argv;
  console.log(`Upgrading subscription of shop: ${shopDomain} to plan: ${planSlug}, Addons: ${addonIds}`);
  await upgradePlan(shopDomain, planSlug, addonIds);
  process.exit();
})();
