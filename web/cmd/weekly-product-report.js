require("dotenv").config();
require("../api/config");
const { QUEUE_NAMES } = require("../api/queue/config");
const moment = require("moment");
const ReportService = require("../api/services/ReportService");
const ShopService = require("../api/services/ShopService");
const eventTopics = require("storeseo-enums/eventTopics");
const { EMAIL_NOTIFICATION } = require("storeseo-enums/settings/email-notification");
const emailStatus = require("storeseo-enums/emailStatus");
const ShopifyService = require("../api/services/ShopifyService");
const mailService = require("../api/services/MailService");
const { emailReportTemplate } = require("../api/utils/emailTemplateBuilder");

(async () => {
  try {
    const shopDomain = process.argv[2];
    const emailAddress = process.argv[3];
    const currentDate = moment();

    console.log("Sending weekly store report notification for =", currentDate.format());
    const shop = await ShopService.getShopByCondition({
      domain: shopDomain,
    });

    if (shop) {
      const emailData = await ReportService.getWeeklyEmailNotificationProductReport(shop);

      const emailNotificationSettings = await ShopService.getShopSetting(shop.id, EMAIL_NOTIFICATION);
      const settingsItems = emailNotificationSettings.value?.items ?? {};
      const settings =
        Object.keys(settingsItems).findIndex((key) => key === eventTopics.WEEKLY_STORE_REPORT) !== -1
          ? settingsItems[eventTopics.WEEKLY_STORE_REPORT]
          : null;
      const { day, time, enabled } = settings;

      const email = {
        shop_id: shop.id,
        topic: eventTopics.WEEKLY_STORE_REPORT,
        send_to: shop.email,
        data: emailData,
        scheduled_delivery_date: enabled
          ? moment({ minute: 0 }).tz(shop.ianaTimezone).day(day).hour(time).utc().format()
          : null,
        delivered_at: null,
        status: enabled ? emailStatus.READY_FOR_DELIVERY : null,
        failed_reason: null,
      };

      console.log(QUEUE_NAMES.SEND_EMAIL, ` - sending email to : ${emailAddress || shop.email}`);

      const { topic, send_to, data, scheduled_delivery_date } = email;

      const appName = await ShopifyService.getAppHandle(shop.domain);

      const emailTemplateData = {
        ...data,
        date: moment(scheduled_delivery_date).format("ll"),
      };

      const result = await mailService.send({
        to: emailAddress || send_to,
        subject: "Weekly Product Optimization Report from StoreSEO",
        html: await emailReportTemplate(appName, topic, emailTemplateData),
      });

      console.log("Email sent result: ", result);

      if (result.status === 200) {
        console.log("Email sent successfully");
      } else {
        console.log("Email sending failed");
      }
    }
  } catch (err) {
    console.error(err);
  }
  process.exit();
})();
