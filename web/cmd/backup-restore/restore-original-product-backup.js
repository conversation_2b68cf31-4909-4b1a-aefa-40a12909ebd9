require("../../api/config");
const BackupService = require("../../api/services/BackupService");
const fs = require("fs");
const readline = require("readline");
const ShopifyService = require("../../api/services/ShopifyService");
const { sleep } = require("../../api/utils/helper");
const { METAFIELD_DEFINITIONS } = require("storeseo-enums/metafields");

(async () => {
  try {
    const domain = process.argv.slice(2)[0];
    const fileName = process.argv.slice(3)[0] || null;
    console.log("\n---\nRestoring original product backup for ", domain, "\n---");

    const downloadedFilePath = await BackupService.downloadBackupFile(domain, fileName);
    const lineReader = readline.createInterface({
      input: fs.createReadStream(downloadedFilePath),
      crlfDelay: Infinity,
    });

    let count = 1;
    for await (const line of lineReader) {
      const item = JSON.parse(line);
      await sleep(50);

      if (item.id.includes("Product")) {
        console.log(`[Line: ${count}] - Restoring product ${item.id}...`);
        await ShopifyService.saveShopifyProductMeta(domain, {
          productId: item.id,
          handle: item.handle,
          tags: item.tags,
        });
        console.log(`[Line: ${count}] - Product ${item.id} restored successfully.\n\n`);
      }

      if (item.id.includes("MediaImage")) {
        console.log(`[Line: ${count}] - Restoring media image ${item.id} for product ${item.__parentId}...`);
        await ShopifyService.updateImageFiles(domain, [
          {
            id: item.id,
            originalSource: item.image.url,
            alt: item.image.alt,
            referencesToAdd: [item.__parentId],
          },
        ]);
        console.log(
          `[Line: ${count}] - Media image ${item.id} for product ${item.__parentId} restored successfully.\n\n`
        );
      }

      if (item.id.includes("Metafield") && METAFIELD_DEFINITIONS[item.key]) {
        console.log(
          `[Line: ${count}] - Restoring metafield ${item.id}, key: ${item.key} for product ${item.__parentId}...`
        );
        await ShopifyService.setMetafield(domain, {
          ownerId: item.__parentId,
          key: item.key,
          value: item.value,
        });
        console.log(
          `[Line: ${count}] - Metafield ${item.id}, key: ${item.key} for product ${item.__parentId} restored successfully.\n\n`
        );
      }

      count++;
    }

    console.log("\n---\nDone!\n---\n");
  } catch (err) {
    console.log("err: ", err);
  } finally {
    process.exit(0);
  }
})();