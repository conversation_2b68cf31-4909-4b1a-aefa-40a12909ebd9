(async () => {
  try {
    require("../api/config");
    const { Shop } = require("../sequelize");
    const cache = require("../api/cache");
    const shops = await Shop.findAll({
      attributes: ["id", "domain", "plan_id", "onboard_step"],
    });

    console.log("shops: ", shops.length);
    let sl = 1;

    for (let shop of shops) {
      if (shop.plan_id) {
        await cache.planId(shop.domain, shop.plan_id);
        // await cache.onboardStep(shop.domain, shop.onboard_step);
        console.log(`${sl}/${shops.length}`, "Stored: ", shop.domain, "plan_id: ", shop.plan_id, "\n");
      } else {
        console.log(`${sl}/${shops.length}`, "NOT SUBSCRIBED: ", shop.domain, "\n");
      }
      sl++;
    }
  } catch (err) {
    console.log("err: ", err);
  } finally {
    process.exit(0);
  }
})();
