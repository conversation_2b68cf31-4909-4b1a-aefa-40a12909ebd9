/**
 * ********
 * Trigger collection sync for a specific store
 * ********
 * example: node cmd/resync-db-collections-with-shopify <myshopify_domain_of_the_store>
 */
const dotenv = require("dotenv");
const ShopService = require("../api/services/ShopService");
const CollectionService = require("../api/services/collections/CollectionService");

const { Collection } = require("../sequelize");
const ShopifyService = require("../api/services/ShopifyService");
const CollectionAnalysisService = require("../api/services/collections/CollectionAnalysisService");

dotenv.config();

const sleep = () => {
  return new Promise((resolve, reject) => {
    setTimeout(() => resolve(true), 350);
  });
};

async function resyncCollections() {
  try {
    const shopDomain = process.argv[2];

    const shop = await ShopService.getShop(shopDomain);
    const user = {
      shopId: shop.id,
      shop: shopDomain,
      accessToken: shop.access_token,
    };

    const collections = (await Collection.findAll({ where: { shop_id: shop.id } })).map((c) => c.toJSON());

    const failedUpdates = [];

    for (let i = 0; i < collections.length; i++) {
      const sl = `${i + 1}/${collections.length}, ${collections[i].collection_id}`;
      const updatedSuccessfully = await updateCollectionWithShopifyData(collections[i].collection_id, user);

      if (updatedSuccessfully) {
        console.log(
          sl,
          `Collection -  id: ${collections[i].id}, title: ${collections[i].title} handle: ${collections[i].handle} updated successfully!\n`
        );
        await sleep();
      } else {
        console.log(
          sl,
          `Failed to update collection - id: ${collections[i].id}, title: ${collections[i].title} handle: ${collections[i].handle}!`
        );
        console.log();
        failedUpdates.push({ id: collections[i].id, title: collections[i].title, handle: collections[i].handle });
      }
    }

    console.log("\n----");
    console.log("Total Collections in DB: ", collections.length);
    console.log("Update successful: ", collections.length - failedUpdates.length);
    console.log("Update failed: ", failedUpdates.length);
    if (failedUpdates.length) {
      console.log("List of failed updates: ");
      console.table(failedUpdates);
    }
    console.log("----\n");
  } catch (err) {
    console.log("Error: ", err);
  } finally {
    setTimeout(() => process.exit(0), 500);
  }
}

async function updateCollectionWithShopifyData(collectionGqlId, user) {
  try {
    const shopifyCollection = await ShopifyService.getCollectionById(user.shop, collectionGqlId);
    const collection = await CollectionService.upsertRelatedData(user.shopId, shopifyCollection);

    await CollectionAnalysisService.analysis({
      shopId: user.shopId,
      collection,
    });

    return true;
  } catch (err) {
    console.log("err: ", err);
    return false;
  }
}

resyncCollections();
