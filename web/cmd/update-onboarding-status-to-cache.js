require("dotenv").config();
require("../api/config");
const { Op } = require("sequelize");
const { ACTIVE } = require("storeseo-enums/shopStatus");
const ShopService = require("../api/services/ShopService");
const cache = require("../api/cache");

(async () => {
  try {
    let sl = 1;
    for await (let shops of ShopService.iterateOverAllShops(
      {
        plan_id: { [Op.gt]: 0 },
        status: ACTIVE,
        onboard_step: null,
      },
      ["domain", "onboard_step"]
    )) {
      for (let shop of shops) {
        try {
          await cache.isOnboardingCompleted(shop.domain, true);
          console.log(sl, `Updated onboarding cache of the shop (${shop.domain})... `);
        } catch (error) {
          console.error(sl, `Error updating onboarding cache of the shop (${shop.domain}):`);
        }
        sl++;
      }
    }
  } catch (err) {
    console.error(err);
  }
  // process.exit();
})();
