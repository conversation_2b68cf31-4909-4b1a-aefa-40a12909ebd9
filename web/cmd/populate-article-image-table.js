require("../api/config");
const { Article: A, ArticleImage: Am } = require("../sequelize");

/**
 * @type {typeof import("sequelize").Model}
 */
const Article = A;
/**
 * @type {typeof import("sequelize").Model}
 */
const ArticleImage = Am;

(async () => {
  try {
    const articles = (await Article.findAll({ order: [["id", "ASC"]] })).map((a) => a.toJSON());

    console.log("Total articles found: ", articles.length, "\n");

    for (let article of articles) {
      const { id: article_id, blog_id, shop_id, image } = article;

      console.log("Inserting image for article id: ", article_id);

      try {
        if (image) {
          const { alt, src } = image;
          const data = {
            shop_id,
            blog_id,
            article_id,
            alt_text: alt,
            src,
          };

          await ArticleImage.upsert(data);
        }
        console.log("Done.\n");
      } catch (err) {
        console.log(err);
      }
    }

    console.log("\n\n====");
    console.log("All done!");
    console.log("====\n\n");
  } catch (err) {
    console.log("err: ", err);
  } finally {
    process.exit(0);
  }
})();
