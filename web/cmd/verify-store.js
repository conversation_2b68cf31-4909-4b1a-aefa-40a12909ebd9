require("dotenv").config();
const ShopService = require("../api/services/ShopService");

(async () => {
  try {
    const domain = process.argv[2];

    if (!domain) throw new Error("Domain is required to perform the action.");

    const shop = await ShopService.getShop(domain);

    if (shop.plan_id > 1) {
      console.warn(domain, "- Store is not in FREE plan.", shop.plan_id);
      return;
    }

    const updateData = {
      is_verified: true,
      plan_rules: {
        ...shop.plan_rules,
        products: 100,
      },
    };

    await ShopService.updateShop(shop.id, updateData);

    console.info(domain, "- Store is verified & product limit increased to 100.", updateData);
  } catch (err) {
    console.error(err);
  }

  process.exit();
})();
