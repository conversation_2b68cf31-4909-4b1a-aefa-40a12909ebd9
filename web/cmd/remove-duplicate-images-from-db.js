const { sequelize } = require("../sequelize");

(async () => {
  try {
    console.log("Deleting duplicate images...");

    const result = await sequelize.query(`
     WITH duplicates AS (
      SELECT 
          ctid,
          media_id,
          ROW_NUMBER() OVER (PARTITION BY media_id ORDER BY ctid) as rnum
      FROM 
          product_images
    )
    DELETE FROM product_images
    WHERE ctid IN (
        SELECT ctid
        FROM duplicates
        WHERE media_id IS NULL OR rnum > 1
    );
    `);

    console.log(`Deleted ${result[1].rowCount} duplicate images from database`);
  } catch (err) {
    console.log(err);
  } finally {
    process.exit(0);
  }
})();
