const { QueryTypes } = require("sequelize");
const { DeliveryMethod } = require("@shopify/shopify-api");
const { sequelize } = require("../sequelize");
const { WEBHOOKS } = require("../api/config/webhook");
const ShopService = require("../api/services/ShopService");
const ShopifyWebhookService = require("../api/services/ShopifyWebhookService");
const WebhookService = require("../api/services/WebhookService");

const callbackUrl = "https://app.storeseo.com/webhooks";

const updateInvalidWebhook = async () => {
  const shops = await sequelize.query(
    `SELECT shops.id,shops.domain,shops.access_token,shops.plan_id,COUNT(webhooks.id)AS webhook_count FROM shops LEFT JOIN webhooks ON shops.id=webhooks.shop_id WHERE shops.status='ACTIVE' AND shops.plan_id IS NOT NULL GROUP BY  shops.id, shops.domain,shops.access_token,shops.plan_id HAVING COUNT(webhooks.id)<=2;`,
    { type: QueryTypes.SELECT }
  );

  let sl = 1;

  for (let i = 0; i < shops.length; i++) {
    try {
      const { domain, access_token, id, plan_id } = shops[i];

      await WebhookService.registerAllWebhooks({ shop: domain, accessToken: access_token }, id, plan_id);

      console.log(`-------------Registered shops (${sl}/${shops.length})-----------`);
    } catch (err) {
      console.error(`Failed to register (${sl}/${shops.length})`, err.message);
    }
    sl++;
  }
};

(async () => {
  try {
    await updateInvalidWebhook();
  } catch (err) {
    console.error(err);
  } finally {
    process.exit();
  }
})();
