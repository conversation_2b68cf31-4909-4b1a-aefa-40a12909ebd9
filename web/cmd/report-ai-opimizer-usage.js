const { QueryTypes } = require("sequelize");
const { sequelize } = require("../sequelize");
const cache = require("../api/cache");
const { AI_OPTIMIZER } = require("storeseo-enums/cacheKeys");
const FileService = require("../api/services/FileService");

(async () => {
  let sl = 1;
  const usages = [["Shop ID", "Shop Domain", "Credit Limit", "Credit Used", "Optimized Products"]];
  try {
    const shops = await sequelize.query(
      "SELECT id, DOMAIN, plan_rules->>'ai_optimizer' as ai_optimizer FROM shops WHERE plan_rules->>'ai_optimizer' is NOT NULL",
      {
        type: QueryTypes.SELECT,
      }
    );

    for (let shop of shops) {
      const usage = await cache.addons.usageCount(shop.domain, {
        addon: AI_OPTIMIZER,
      });

      const limit = await cache.addons.usageLimit(shop.domain, {
        addon: AI_OPTIMIZER,
      });

      const optimized = await FileService.countAiOptimizedItems(shop.domain);

      usages.push([shop.id, shop.domain, limit, usage, optimized]);
      console.log(sl, shop.domain, usage, limit, optimized);
      sl++;
    }

    const data = usages.map((row) => row.join(",")).join("\n");

    await FileService.writeCsvFile("uploads/ai-usage.csv", data);
  } catch (err) {
    console.error(err);
  } finally {
    process.exit();
  }
})();
