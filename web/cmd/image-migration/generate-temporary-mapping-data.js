const { sequelize } = require("../../sequelize");
const { spawnSync } = require("child_process");
const { graphQLClient } = require("../../api/utils/shopify.clients");

const sleep = (ms) => new Promise((resolve) => setTimeout(() => resolve(true), ms));

/**
 * @callback command
 * @returns {Promise}
 */

/**
 * @type {{label: string, command: command, progress: number}[]}
 */
const STEPS = [
  // {
  //   label: "Analyse the database",
  //   command: analyseDatabase,
  //   progress: 5,
  // },
  // {
  //   label: "Delete images connected to inactive stores",
  //   command: deleteInvalidImagesConnectedToInactiveStores,
  //   progress: 10,
  // },
  // {
  //   label: "Pull missing media ids from the Shopify platform",
  //   command: pullMissingMediaIdsFromShopify,
  //   progress: 55,
  // },
  // {
  //   label: "Generate temporary image data map",
  //   command: generateTemporaryImageMapData,
  //   progress: 60,
  // },
  // {
  //   label: "Remove images with duplicate media id",
  //   command: removeImagesWithDuplicateMediaId,
  //   progress: 65,
  // },
  // {
  //   label: "Run migration to add featured media id column to products table",
  //   command: addFeaturedMediaColumnToProductsTable,
  //   progress: 70,
  // },
  // {
  //   label: "Add featured media data to each product",
  //   command: addFeaturedMediaToEachProduct,
  //   progress: 75,
  // },
  // {
  //   label: "Alter image table with necessary changes",
  //   command: alterImageTable,
  //   progress: 80,
  // },
  // {
  //   label: "Create the 'through' table for M:N association between products & images",
  //   command: createMxNAssociationThroughTable,
  //   progress: 85,
  // },
  {
    label: "Generate M:N association data for products & images",
    command: generateImageAssociationData,
    progress: 95,
  },
  {
    label: "Cleanup temp data",
    command: cleanupTempData,
    progress: 100,
  },
  {
    label: "Done",
    command: () => {},
    progress: 100,
  },
];
let currentStep = 0;
let currentProgress = 0;
let PER_IMAGE_PERCENTAGE = 0;

const SHOP_IDS = new Set();
const SHOP_SESSIONS = {};

(async () => {
  try {
    await logProgress();

    for (let s of STEPS) {
      await s.command();
      currentStep++;
      currentProgress = s.progress;
      await sleep(3500);
    }
    // console.log("----");
    // console.log("metadata: ", metadata);
  } catch (err) {
    console.log(err);
  } finally {
    await sleep(3500);
    process.exit(0);
  }
})();

async function logProgress() {
  const { default: logUpdate } = await import("log-update");
  const { default: chalk } = await import("chalk");

  const frames = [
    "( ●    )",
    "(  ●   )",
    "(   ●  )",
    "(    ● )",
    "(     ●)",
    "(    ● )",
    "(   ●  )",
    "(  ●   )",
    "( ●    )",
    "(●     )",
  ];
  let index = 0;

  let percent = 0;

  console.clear();
  setInterval(() => {
    const frame = frames[(index = ++index % frames.length)];
    const steps = formatStepLabels(frame, chalk);

    logUpdate(
      chalk.bold(
        `



                  +----------------------------------------------------------------+
                  |  CMD: MIGRATE PRODUCT AND IMAGE DATA FOR SHOPIFY API APRIL 24  |
                  +----------------------------------------------------------------+
                                                      
`
      ),
      `                 `,
      chalk.bold.bgGreen(` ${Math.min(currentProgress, 100).toFixed(2)}% `),
      "\n\n",
      ...steps
    );
  }, 80);
}

function formatStepLabels(frame, chalk) {
  return STEPS.map((step, idx) => {
    if (idx === currentStep) return `          ${chalk.bold.green(`${frame} ${step.label}...\n`)}`;
    else if (idx < currentStep) return `                 ${chalk.green.bold(`✔ `) + chalk(`${step.label}\n`)}`;
    else if (idx > currentStep) return `                 ${chalk.gray.italic(`◌ ${step.label}\n`)}`;
  });
}

async function analyseDatabase() {
  await pullListOfActiveShopsWithMissingImageMediaId();
  await generateSessionsForListedShops();
}

async function pullListOfActiveShopsWithMissingImageMediaId() {
  const [results] = await sequelize.query(`
    SELECT DISTINCT
      product_images.shop_id as shop_id
    FROM
      product_images
      LEFT JOIN shops ON product_images.shop_id = shops.id
    WHERE
      product_images.media_id IS NULL
      AND shops.status = 'ACTIVE'
    `);

  for (let r of results) {
    SHOP_IDS.add(r.shop_id);
  }
}

async function generateSessionsForListedShops() {
  const shopIds = Array.from(SHOP_IDS).join(", ");
  if (!shopIds) return;

  const [sessions] = await sequelize.query(`
    SELECT
      id AS "shopId",
      DOMAIN AS shop,
      access_token AS "accessToken"
    FROM
      shops
    WHERE
      id IN(${shopIds})
    `);

  for (let s of sessions) {
    SHOP_SESSIONS[s.shopId] = s;
  }
}

async function deleteInvalidImagesConnectedToInactiveStores() {
  const shopIds = Array.from(SHOP_IDS).join(", ");
  if (!shopIds) return;

  try {
    const [result, metadata] = await sequelize.query(`
      DELETE FROM product_images
      WHERE shop_id IN (
        SELECT id 
        FROM shops 
        WHERE status != 'ACTIVE'
      )`);

    await sequelize.query(`
      DELETE FROM product_images
      WHERE NOT EXISTS (
          SELECT 1
          FROM products
          WHERE products.id = product_images.product_id
      )
      `);
  } catch (err) {}
}

async function pullMissingMediaIdsFromShopify() {
  const [result] = await sequelize.query(`SELECT COUNT(*) FROM product_images WHERE media_id IS NULL`);
  const count = Number(result[0].count);

  PER_IMAGE_PERCENTAGE = 40 / count;

  while (SHOP_IDS.size > 0) {
    const shopIds = getNextBatchOfShops();

    /** @type {Promise<number>[]} */
    const resultPromise = [];
    for (let shopId of shopIds) {
      resultPromise.push(pullMissingMediaIdsByShop(shopId));
    }
    await Promise.all(resultPromise);
  }
}

function getNextBatchOfShops() {
  const shopIds = Array.from(SHOP_IDS);
  const batchSize = 100;

  return shopIds.slice(0, batchSize);
}

async function pullMissingMediaIdsByShop(shopId, limit = 1000) {
  const [images] = await sequelize.query(`
    SELECT
      id,
      shop_id AS "shopId",
      product_id AS "productId",
      REGEXP_REPLACE(REGEXP_REPLACE(src, '.*\/', ''), '\\?.*', '') AS "fileName"
    FROM
      product_images
    WHERE
      shop_id = ${shopId} AND media_id IS NULL
    LIMIT ${limit}
    `);

  if (images.length === 0) {
    SHOP_IDS.delete(shopId);
    return;
  }

  const chunkSize = 250;
  const imageChunks = Array.from({ length: Math.ceil(images.length / chunkSize) }, (v, i) =>
    images.slice(i * chunkSize, i * chunkSize + chunkSize)
  );

  /** @type {Promise<any>[]} */
  const promiseChunks = [];
  for (let chunk of imageChunks) {
    promiseChunks.push(getShopifyImages(shopId, chunk));
  }

  const shopifyImages = [];
  const res = await Promise.allSettled(promiseChunks);
  for (let r of res) {
    if (r.status === "rejected") {
      SHOP_IDS.delete(shopId);

      const [result, metadata] = await sequelize.query(`
        DELETE FROM product_images
        WHERE shop_id = ${shopId} and media_id IS NULL;
        `);
    } else if (r.status === "fulfilled") shopifyImages.push(...r.value);
  }

  await updateImagesInDb(shopId, images, shopifyImages);
}

const FILES_QUERY = `
query files($after: String, $query: String) {
  files(first: 250, reverse: true, after: $after, query: $query) {
    edges {
      node {
        ... on MediaImage {
          id
          image {
            id
            url
          }
        }
      }
    }
    pageInfo {
      hasNextPage
      hasPreviousPage
      startCursor
      endCursor
    }
  }
}
`;

async function getShopifyImages(shopId, images) {
  const fileNameQuery = images.map((img) => `filename:*${img.fileName}`).join(" OR ");
  const input = {
    after: null,
    query: "media_type:IMAGE " + fileNameQuery,
  };

  const session = SHOP_SESSIONS[shopId];
  const { data } = await graphQLClient(session.shop, { query: FILES_QUERY, variables: input });
  return data.files.edges.map((edge) => {
    return {
      mediaId: edge.node.id,
      url: edge.node.image.url,
      fileName: edge.node.image.url.replace(/(?:(?:.*\/)|(\?.*))/gim, ""),
    };
  });
}

async function updateImagesInDb(shopId, dbImages, shopifyImages) {
  const shopifyImagesByFileName = {};
  for (let img of shopifyImages) {
    shopifyImagesByFileName[img.fileName] = img;
  }

  for (let img of dbImages) {
    try {
      const mediaId = shopifyImagesByFileName[img?.fileName]?.mediaId;
      if (!mediaId) {
        await sequelize.query(`
          DELETE FROM product_images
          WHERE shop_id = ${shopId} AND id = ${img.id}
          `);
      } else
        await sequelize.query(`
        UPDATE product_images
        SET media_id = '${shopifyImagesByFileName[img.fileName].mediaId}'
        WHERE id = ${img.id} AND shop_id = ${shopId}
        `);
      currentProgress = Math.min(currentProgress + PER_IMAGE_PERCENTAGE, 55);
    } catch (err) {
      console.log(
        "err: ",
        "shopId",
        shopId,
        "img",
        img,
        "hash",
        shopifyImagesByFileName[img.fileName],
        "image hash",
        shopifyImagesByFileName,
        err
      );
    }
  }
}

async function generateTemporaryImageMapData() {
  await createTemporaryImageMapperTable();
  await sleep(7000);
  await insertTemporaryImageData();
}

async function createTemporaryImageMapperTable() {
  const cmd = spawnSync("sequelize-cli", [
    "db:migrate",
    "--name",
    "20240726053249-create-temporary-image-migration-table.js",
  ]);

  console.log(cmd.stdout.toString());
  if (cmd.error) {
    console.log(cmd.stderr.toString());
    throw new Error("Failed to run temporary image migration");
  }
}

async function insertTemporaryImageData() {
  const [results, metadata] = await sequelize.query(`
    INSERT INTO temp_image_migration_table (
      shop_id,
      product_id,
      image_id,
      gql_id,
      media_id,
      created_at,
      updated_at
    )
    SELECT
      shop_id,
      product_id,
      id,
      gql_id,
      media_id,
      NOW(),
      NOW()
    FROM product_images
    WHERE media_id IS NOT NULL
    `);
}

async function removeImagesWithDuplicateMediaId() {
  await sequelize.query(`
    WITH duplicates AS (
     SELECT 
         ctid,
         media_id,
         ROW_NUMBER() OVER (PARTITION BY media_id ORDER BY ctid) as rnum
     FROM 
         product_images
   )
   DELETE FROM product_images
   WHERE ctid IN (
       SELECT ctid
       FROM duplicates
       WHERE media_id IS NULL OR rnum > 1
   );
   `);
}

async function addFeaturedMediaColumnToProductsTable() {
  const cmd = spawnSync("sequelize-cli", [
    "db:migrate",
    "--name",
    "20240704034223-add-featured-media-id-to-products-table.js",
  ]);

  console.log(cmd.stdout.toString());
  await sleep(5000);

  if (cmd.error) {
    console.log(cmd.stderr.toString());
    throw new Error("Failed to run migration to add featured media column to products table");
  }
}

async function addFeaturedMediaToEachProduct() {
  await sequelize.query(`
    UPDATE products
    SET featured_media_id = temp.media_id
    FROM temp_image_migration_table temp
    WHERE products.featured_image_id = temp.gql_id
    `);
}

async function alterImageTable() {
  const cmd = spawnSync("sequelize-cli", [
    "db:migrate",
    "--name",
    "20240707055751-update-product-image-table-constraints.js",
  ]);

  console.log(cmd.stdout.toString());
  await sleep(5000);
  console.log(cmd.stderr.toString());
  if (cmd.error) {
    throw new Error("Failed to run temporary image migration");
  }
}

async function createMxNAssociationThroughTable() {
  const cmd = spawnSync("sequelize-cli", ["db:migrate", "--name", "20240707095025-create-product-image-mapping.js"]);

  console.log(cmd.stdout.toString());
  await sleep(5000);

  console.log(cmd.stderr.toString());
  if (cmd.error) {
    throw new Error("Failed to run temporary image migration");
  }
}

async function generateImageAssociationData() {
  const [results, metadata] = await sequelize.query(`
    INSERT INTO product_image_mapper (
      shop_id,
      product_id,
      image_id,
      created_at,
      updated_at
    )
    SELECT
      temp.shop_id,
      temp.product_id,
      temp.image_id,
      NOW(),
      NOW()
    FROM temp_image_migration_table temp
    JOIN products prod ON temp.product_id = prod.id
    JOIN product_images pm ON temp.image_id = pm.id
    `);
}

async function cleanupTempData() {
  const cmd = spawnSync("sequelize-cli", [
    "db:migrate:undo",
    "--name",
    "20240726053249-create-temporary-image-migration-table.js",
  ]);

  console.log(cmd.stdout.toString());

  if (cmd.error) {
    console.log(cmd.stderr.toString());
    throw new Error("Failed to run temporary image migration");
  }
}
