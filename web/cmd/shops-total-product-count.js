const { Shop, Op } = require("../sequelize");
const ShopifyService = require("../api/services/ShopifyService");
const SubscriptionService = require("../api/services/SubscriptionService");
const { ACTIVE } = require("storeseo-enums/shopStatus");
const { isEmpty, pick } = require("lodash");
const ShopService = require("../api/services/ShopService");
const { shopStatusByApiResponseCode } = require("../api/utils/helper");

(async () => {
  const shops = await Shop.findAll({
    where: { status: ACTIVE, access_token: { [Op.ne]: null } },
    attributes: ["id", "domain", "access_token", "meta"],
  });

  const totalShops = shops.length;
  console.log(`Found Shops = ${totalShops}`);

  let sl = 1;

  for (let shop of shops) {
    try {
      const session = { shop: shop.domain, accessToken: shop.access_token };
      // const shopifyProductCount = await ShopifyService.getShopifyProductCount(session);

      const updatedData = await ShopService.updateShop(shop.id, { meta: { ...shop.meta, shopifyProductCount } });

      console.log(`${sl}/${totalShops}`, `Updated Product Count =`, updatedData.meta.shopifyProductCount);
    } catch (err) {
      let status = shopStatusByApiResponseCode(err?.response?.code);
      await ShopService.updateShop(shop.id, { status });
      console.error(`${sl}/${totalShops}`, err.message);
    }
    sl++;
  }

  // process.exit();
})();
