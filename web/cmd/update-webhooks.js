const planType = require("storeseo-enums/planType");
const SubscriptionPlanService = require("../api/services/SubscriptionPlanService");
const { limits, features } = require("../api/config/subscription-plan");
const { Shop } = require("../sequelize");
const { ACTIVE } = require("storeseo-enums/shopStatus");
const { dispatchQueue } = require("../api/queue/queueDispatcher");
const { QUEUE_NAMES } = require("../api/queue/config");

(async () => {
  console.log("----------------------------");
  console.log("⏳ Updating webhooks..");
  console.log("----------------------------");

  const shops = await Shop.findAll({
    attributes: ["id", "plan_id", "plan_rules", "domain"],
    where: { status: ACTIVE },
    order: [["id", "desc"]],
  });
  let sl = 1;

  console.log("----------------------------");
  console.log("⏳ Updating shops..");
  console.log("----------------------------");

  for (let shop of shops) {
    dispatchQueue({ queueName: QUEUE_NAMES.UPDATE_WEBHOOKS_REGISTRATION_QUEUE, message: { shopDomain: shop.domain } });
    sl++;
  }

  console.log("Done");

})();
