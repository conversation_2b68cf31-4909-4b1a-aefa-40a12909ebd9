const { Shop, Op } = require("../sequelize");
const ShopifyService = require("../api/services/ShopifyService");
const SubscriptionService = require("../api/services/SubscriptionService");
const { ACTIVE } = require("storeseo-enums/shopStatus");
const { isEmpty } = require("lodash");

(async () => {
  const shops = await Shop.findAll({
    where: { status: ACTIVE, access_token: { [Op.ne]: null } },
    attributes: ["id", "email", "domain", "plan_id", "plan_rules", "plan_info", "access_token", "shop_id"],
  });

  let sl = 1;
  for (let shop of shops) {
    try {
      const session = { shop: shop.domain, accessToken: shop.access_token };
      const app_subscription = await ShopifyService.getActiveSubscription(session.shop);

      if (!isEmpty(app_subscription)) {
        await SubscriptionService.subscriptionUpdate(session, {
          ...app_subscription,
          admin_graphql_api_shop_id: shop.shop_id,
        });
      }

      console.log(`------ Updated Subscription ${sl}/${shops.length} ------`);
    } catch (err) {
      console.error(`------ Error Updating Subscription ${sl}/${shops.length} ------`);
    }
    sl++;
  }
})();
