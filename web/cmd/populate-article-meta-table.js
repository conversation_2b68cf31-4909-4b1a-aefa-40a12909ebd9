require("../api/config");
const { Article: A, ArticleMeta: Am } = require("../sequelize");

/**
 * @type {typeof import("sequelize").Model}
 */
const Article = A;
/**
 * @type {typeof import("sequelize").Model}
 */
const ArticleMeta = Am;

(async () => {
  try {
    const articles = (await Article.findAll({ order: [["id", "ASC"]] })).map((a) => a.toJSON());

    console.log("Total articles found: ", articles.length, "\n");

    for (let article of articles) {
      const { id: article_id, blog_id, shop_id, metafields = [] } = article;

      console.log("Inserting meta for article id: ", article_id);

      try {
        for (let meta of metafields) {
          const { id, namespace, key, type, value } = meta;
          const data = {
            shop_id,
            blog_id,
            article_id,
            rest_meta_id: id,
            namespace,
            key,
            type,
            value,
          };

          await ArticleMeta.upsert(data);
        }
      } catch (err) {
        console.log(err);
      }
      console.log("Done.\n");
    }

    console.log("\n\n====");
    console.log("All done!");
    console.log("====\n\n");
  } catch (err) {
    console.log("err: ", err);
  } finally {
    process.exit(0);
  }
})();
