const planType = require("storeseo-enums/planType");
const SubscriptionPlanService = require("../api/services/SubscriptionPlanService");
const { limits, features } = require("../api/config/subscription-plan");
const { Shop } = require("../sequelize");
const { ACTIVE } = require("storeseo-enums/shopStatus");
const cache = require("../api/cache");

(async () => {
  const plans = await SubscriptionPlanService.getSubscriptionPlans();
  let updatedPlans = [];

  console.log("----------------------------");
  console.log("⏳ Updating plans..");
  console.log("----------------------------");

  for (let plan of plans) {
    const limit = limits[plan.slug];

    const rules = {
      products: limit,
      seo_analysis: true,
      fix_instruction: true,
      alt_text: true,
      control_sitemap: true,
      unlimited_tags: true,
      preview_snippets: true,
      html_sitemap: true,
      keyword_analytics: true,
      seo_reports: true,
      bulk_seo: true,
      sitemap_submission: plan.type === planType.PRO,
      instant_indexing: plan.type === planType.PRO,
      google_analytics: plan.type === planType.PRO,
      json_ld: plan.type === planType.PRO,
      rich_snippet: plan.type === planType.PRO,
      google_console: plan.type === planType.PRO,
      local_seo: plan.type === planType.PRO,
      redirect_out_of_stock: plan.type === planType.PRO,
      multi_language_seo: /(?:premium)|(?:enterprise)/gim.test(plan.slug),
    };

    const updatedPlan = await SubscriptionPlanService.updatePlan({ id: plan.id }, { rules });

    updatedPlans.push(updatedPlan);
    console.info(updatedPlan.slug, "updated.");
  }
  console.log("----------------------------");
  console.log("✅ Plans updated.");
  console.log("----------------------------");

  const shops = await Shop.findAll({
    attributes: ["id", "plan_id", "plan_rules", "domain"],
    // where: { status: ACTIVE },
    order: [["id", "desc"]],
  });

  const totalShops = shops.length;
  let success = 0;
  let failed = 0;
  let sl = 1;

  console.log("----------------------------");
  console.log("⏳ Updating shops..");
  console.log("----------------------------");

  for (let shop of shops) {
    const id = shop.get("id");
    const domain = shop.get("domain");
    const planId = shop.get("plan_id");
    const currentRules = shop.get("plan_rules");
    try {
      const currentPlan = updatedPlans.find((up) => up.id === planId);

      const planRules = {
        products: currentRules.products,
        image_optimizer: currentRules?.image_optimizer || null,
        ai_optimizer: currentRules?.ai_optimizer || null,
      };

      for (let feature of Object.values(features)) {
        planRules[feature] = currentPlan.rules[feature];
      }

      await Shop.update({ plan_rules: planRules }, { where: { id: id } });
      console.log(`${sl}/${totalShops} -- updated plan rules for the shop ${domain}.`);
      success++;
    } catch (error) {
      console.warn(`${sl}/${totalShops} -- cannot updated plan rules for the shop ${domain}.`, error.message);
      failed++;
    }
    sl++;
  }

  console.log("----------------------------");
  console.log("✅ Shops updated.");
  console.table({ totalShops, success, failed });
  console.log("----------------------------");

  process.exit();
})();
