// ******
// Translate command:
//    1. Append the strings for translation in "/public/locales/en/translation.json" file.
//    2. Run this script & voilà.
// ******
// Note: To a add new language for translation, add the following item to the "/src/config/languages.js" file
// { label: "display title of the language", code: "language short code"}
// ******

const fs = require("fs");
const path = require("path");
const { difference, chunk } = require("lodash");

const BASE_PATH = path.resolve(__dirname, "../public/locales");
const TRANSLATION_FILE_NAME = "translation.json";
const originalStrings = JSON.parse(fs.readFileSync(`${BASE_PATH}/en/${TRANSLATION_FILE_NAME}`));
const originalKeyList = Object.keys(originalStrings);

// initialize the google translate service
const { Translate } = require("@google-cloud/translate").v2;
const translate = new Translate({
  keyFilename: path.resolve(__dirname, "../api/config/keys/store-seo-translation-service-account.json"),
});

const languages = [
  { label: "English", value: "en" },
  { label: "हिन्दी", value: "hi" },
  { label: "Français", value: "fr" },
  { label: "Español", value: "es" },
  { label: "Nederlands", value: "nl" },
  { label: "Italiano", value: "it" },
  { label: "Deutsch", value: "de" },
  { label: "中国人", value: "zh" },
  { label: "日本語", value: "ja" },
  { label: "Polish", value: "pl" },
];

const generateTranslations = async (lang, stringArr) => {
  const chunks = chunk(stringArr, 125);
  const translationMap = {};

  for (let chunk of chunks) {
    const textArr = chunk.reduce((arr, e) => [...arr, e[1]], []);

    let [translations] = await translate.translate(textArr, lang);
    translations = Array.isArray(translations) ? translations : [translations];

    translations.forEach((translation, i) => {
      translationMap[chunk[i][0]] = translation;
    });
  }

  return translationMap;
};

const startTranslating = async () => {
  for (let lang of languages.filter((l) => l.value !== "en")) {
    const langCode = lang.value;
    const filePath = `${BASE_PATH}/${langCode}/${TRANSLATION_FILE_NAME}`;
    let currentTranslations = {};

    console.log(`\nTranslating to ${lang.label} - ${lang.value} ...`);
    try {
      currentTranslations = JSON.parse(fs.readFileSync(filePath));
    } catch (err) {
      if (!fs.existsSync(filePath)) {
        fs.mkdirSync(path.dirname(filePath), { recursive: true });
      }
    }

    try {
      const missingKeys = difference(originalKeyList, Object.keys(currentTranslations));
      if (missingKeys.length) {
        const missing = [];
        missingKeys.forEach((k) => missing.push([k, originalStrings[k]]));
        const newTranslations = await generateTranslations(langCode, missing);
        fs.writeFileSync(filePath, JSON.stringify({ ...currentTranslations, ...newTranslations }, null, 2));
      }
    } catch (err) {
      console.log("error translating...", err);
    }

    console.log("Done!\n");
  }
};

startTranslating();
