require("../../api/config");
const cache = require("../../api/cache");
const ShopService = require("../../api/services/ShopService");
const PageService = require("../../api/services/PageService");
const EventService = require("../../api/services/EventService");
const AnalysisService = require("../../api/services/AnalysisService");
const { dispatchQueue } = require("../../api/queue/queueDispatcher");
const { QUEUE_NAMES } = require("../../api/queue/config");
const ResourceType = require("storeseo-enums/resourceType");
const DocService = require("../../api/services/docs/DocService");
const SitemapService = require("../../api/services/SitemapService");
const AnalysisEntityTypes = require("storeseo-enums/analysisEntityTypes");
const { sleep } = require("../../api/utils/helper");

(async () => {
  try {
    const domain = process.argv[2];

    console.log("Starting page sync for shop:", domain);
    const shopDetails = await ShopService.getShop(domain);
    const { id: shopId, access_token: accessToken, domain: shop, url } = shopDetails;
    

    await PageService.markPagesAsNotSynced(shopId);
    await cache.pageSyncOngoing(shop, true);

    EventService.handlePageSyncUpdate({ shop, message: "Syncing homepage..." });
    const user = {
      shop,
      accessToken,
      url,
      shopId,
    };
    const homepage = await PageService.saveOrUpdateHomepage(user, shopId, shop);
    await AnalysisService.analyseEachPage({ shopId, page: homepage, url });
    if (homepage && !homepage.focus_keyword) {
      dispatchQueue({
        queueName: QUEUE_NAMES.FOCUS_KEYWORD_GENERATOR,
        message: { shopId, resourceType: ResourceType.PAGE, dbResourceId: homepage.id },
      });
    }

    const isBetterDocsInstalled = await cache.betterDocInstallationStatus(shop);
    if (isBetterDocsInstalled) {
      const docHomepage = await DocService.upsertDocHomepage(shop, shopId);
      await AnalysisService.analyseEachPage({ shopId, page: docHomepage, url });
      if (docHomepage && !docHomepage.focus_keyword) {
        dispatchQueue({
          queueName: QUEUE_NAMES.FOCUS_KEYWORD_GENERATOR,
          message: { shopId, resourceType: ResourceType.PAGE, dbResourceId: docHomepage.id },
        });
      }
    }

    EventService.handlePageSyncUpdate({ shop, message: "Syncing all pages..." });
    await SitemapService.deleteAllSitemaps(shopId, AnalysisEntityTypes.PAGE);
    await sleep();

    dispatchQueue({
      queueName: QUEUE_NAMES.PAGE_SYNC,
      message: {
        shopId,
        shopDomain: shop,
        shopUrl: url,
        session: user,
        cursor: null,
      },
    });
  } catch (err) {
    console.log(err);
  } finally {
    setTimeout(() => {
      process.exit(0);
    }, 2000);
  }
})();
