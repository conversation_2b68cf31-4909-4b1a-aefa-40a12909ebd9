require("../../api/config");
const cache = require("../../api/cache");
const ShopService = require("../../api/services/ShopService");
const BlogService = require("../../api/services/BlogService");
const ArticleService = require("../../api/services/ArticleService");
const { dispatchQueue } = require("../../api/queue/queueDispatcher");
const { QUEUE_NAMES } = require("../../api/queue/config");

(async () => {
  try {
    const domain = process.argv[2];

    console.log("Starting blog sync for shop:", domain);
    const shop = await ShopService.getShop(domain);

    await cache.blogSyncOngoing(domain, true);

    await BlogService.markBlogsAsNotSynced(shop.id);
    await ArticleService.markArticlesAsNotSynced(shop.id);

    dispatchQueue({
      queueName: QUEUE_NAMES.ARTICLE_SYNC,
      message: {
        shopId: shop.id,
        shopDomain: domain,
        shopUrl: shop.url,
        session: {
          shop: domain,
          accessToken: shop.access_token,
          url: shop.url,
          shopId: shop.id,
        },
        cursor: null,
      },
    });
    console.log("Blog sync started successfully");
  } catch (err) {
    console.log(err);
  } finally {
    setTimeout(() => {
      process.exit(0);
    }, 2000);
  }
})();
