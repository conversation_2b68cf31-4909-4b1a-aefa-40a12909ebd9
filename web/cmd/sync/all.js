require("dotenv").config();
const ShopifyService = require("../../api/services/ShopifyService");
const ShopService = require("../../api/services/ShopService");
const { QUEUE_NAMES } = require("../../api/queue/config");
const { dispatchQueue } = require("../../api/queue/queueDispatcher");
const ProductService = require("../../api/services/ProductService");
const { sleep } = require("../../api/utils/helper");
const AuthService = require("../../api/services/AuthService");

(async () => {
  try {
    const shopDomain = process.argv[2];

    await cache.removeProductSyncCursor(shopDomain);
    const shop = await ShopService.getShop(shopDomain);
    const plan = await SubscriptionPlanService.getSubscriptionPlanNameAndType(shop.plan_id);
    const user = await AuthService.serializeAuthToken(shop, plan);

    dispatchQueue({
      queueName: QUEUE_NAMES.PRODUCT_SYNC_WITH_DATA_MIGRATION,
      message: { user },
    });

    console.log("Dispatched old product sync queue for shop: ", shopDomain);
  } catch (err) {
    console.log(err);
  } finally {
    process.exit();
  }
})();
