const MailchimpService = require("../api/services/MailchimpService");
const ShopService = require("../api/services/ShopService");
const { isEmpty } = require("lodash");
const { INSTALLED } = require("storeseo-enums/mailchimp/staticTags");
const { Shop } = require("../sequelize");
const ShopStatus = require("storeseo-enums/shopStatus");
const { sleep } = require("../api/utils/helper");

(async () => {
  const totalShops = await Shop.count({});
  let counter = 1;
  console.log(`Found ${totalShops} shops.`);

  for await (let shops of ShopService.iterateOverAllShops())
    for (let shop of shops) {
      await sleep(250);
      const { plan_info, domain } = shop;

      if (isEmpty(plan_info)) {
        console.warn(`[${counter++}/${totalShops}]`, `Failed to update member tags for ${domain}.`);
        continue;
      }
      const tags = [plan_info?.name, shop.status === ShopStatus.ACTIVE ? INSTALLED : shop.status.toUpperCase()];
      // const tagsToRemove = [UNINSTALLED];

      try {
        // Add/Update member
        const { email_address: email } = await MailchimpService.addMember(domain);

        // Update member's tags
        await MailchimpService.updateMemberTags(email, tags, []);

        // update the member data to shop
        await MailchimpService.updateShopDataToDb(domain, email);
        console.log(`[${counter++}/${totalShops}]`, `Added member & updated tags for ${domain}.`);
      } catch (err) {
        console.error(err.message);
      }
    }

  console.log("\n\n\n------");
  console.log("All done!");
  console.log("------");
  // process.exit();
})();
