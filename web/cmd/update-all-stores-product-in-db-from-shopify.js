require("../api/config");
const { Shop, Product, Op } = require("../sequelize");
const ShopifyService = require("../api/services/ShopifyService");
const ProductService = require("../api/services/ProductService");
const ProductAnalysisService = require("../api/services/ProductAnalysisService");
const { flattenMediaImages } = require("../api/serializers/ProductImageSerializer");
const { where } = require("sequelize");

(async () => {
  try {
    const whereQuery = {
      status: "ACTIVE",
      id: {
        [Op.lt]: 22078,
      },
    };

    const totalShops = await Shop.count({
      where: whereQuery,
    });

    let count = 0;
    while (count < totalShops) {
      const shop = await Shop.findOne({
        order: [["created_at", "DESC"]],
        where: whereQuery,
        offset: count,
      });

      console.log("\n\n-----");
      console.log(`[${count + 1} / ${totalShops}]`, "Updating product for shop: ", shop.domain);
      console.log("-----\n\n");
      const progress = ((count / totalShops) * 100).toFixed(2) + "%";
      await updateExistingProductDataFromShopify(shop, progress);

      count++;
    }

    console.log("\n\n++++++++++++++++++++++++++++++++++++");
    console.log("+    All shop data update done!    +");
    console.log("++++++++++++++++++++++++++++++++++++\n\n");
  } catch (err) {
    console.log(err);
  } finally {
    // process.exit(0);
  }
})();

function sleep() {
  return new Promise((resolve, reject) => {
    setTimeout(() => resolve(true), 150);
  });
}

async function updateExistingProductDataFromShopify(shop, progress) {
  try {
    const products = (await Product.findAll({ where: { shop_id: shop.id } })).map((p) => p.toJSON());

    const user = { shop: shop.domain, accessToken: shop.access_token };

    const failedUpdate = [];

    for (let i = 0; i < products.length; i++) {
      const sl = `${i + 1}/${products.length}, ${products[i].product_id}`;
      try {
        const shopifyProduct = await ShopifyService.getProductFromShopify(user.shop, products[i].product_id);
        shopifyProduct.mediaImages = flattenMediaImages(shopifyProduct);
        const product = await ProductService.saveOrUpdateProduct(shop.id, shopifyProduct);
        await ProductAnalysisService.analyseEachProduct({ shopId: shop.id, product });
        console.log(progress, sl, `Product ${products[i].id} - ${products[i].handle} updated successfully!\n`);
        await sleep();
      } catch (err) {
        console.log(progress, sl, `Failed to update product ${products[i].id} - ${products[i].handle}!`, err);
        console.log();
        failedUpdate.push({ id: products[i].id, handle: products[i].handle });

        if (err.response?.code) break;
      }
    }

    console.log("\n----");
    console.log("Total Products: ", products.length);
    console.log("Updated: ", products.length - failedUpdate.length);
    console.log("Update failed: ", failedUpdate.length);
    if (failedUpdate.length) {
      console.log("List of failed products: ");
      console.table(failedUpdate);
    }
    console.log("----\n");
  } catch (err) {
    console.log("Error in updating existing product data from shopify", err);
  }
}
