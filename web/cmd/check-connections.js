#!/usr/bin/env node
try {
  require("dotenv").config();
  const { sequelize } = require("../sequelize");
  const { RABBIT_MQ_CONNECTION } = require("../api/queue");

  // Track connection status
  let hasErrors = false;

  // Helper to measure connection time
  const measureConnectionTime = async (connectionFn) => {
    const startTime = Date.now();
    try {
      const result = await connectionFn();
      const endTime = Date.now();
      const connectionTime = endTime - startTime;
      return { success: true, result, connectionTime };
    } catch (error) {
      const endTime = Date.now();
      const connectionTime = endTime - startTime;
      return { success: false, error, connectionTime };
    }
  };

  // Helper to print status
  const printStatus = (service, isConnected, details = null, connectionTime = null) => {
    const status = isConnected ? "✅ CONNECTED" : "❌ FAILED";
    const timeInfo = connectionTime !== null ? `[${connectionTime}ms]` : "";
    console.log(`- ${status}: ${service}`, details && `(${details})`, `=> ${timeInfo}`);
    // if (details) {
    //   console.log(`Details: ${details} \n`);
    // }
    if (!isConnected) hasErrors = true;
  };

  // Check all connections
  (async () => {
    console.log("Checking connections...\n");

    // Check Sequelize connection
    const sequelizeResult = await measureConnectionTime(async () => {
      await sequelize.authenticate();
      return sequelize.config;
    });

    if (sequelizeResult.success) {
      const { host, port, database } = sequelizeResult.result;
      printStatus("PostgreSQL/Sequelize", true, `${host}:${port}/${database}`, sequelizeResult.connectionTime);
    } else {
      printStatus("PostgreSQL/Sequelize", false, sequelizeResult.error.message, sequelizeResult.connectionTime);
    }

    // Check Redis connection
    const redisResult = await measureConnectionTime(async () => {
      // Create a new Redis client for testing to avoid interference with the existing one
      const { createClient } = require("redis");
      const { host, port, tls, username, password } = require("../api/config/redis");

      const testRedisClient = createClient({
        socket: { host, port, tls, connectTimeout: 5000 },
        username,
        password,
      });

      // Set up event handlers
      testRedisClient.on("error", (err) => {
        throw new Error(`Redis connection error: ${err.message}`);
      });

      // Connect and test
      await testRedisClient.connect();

      // Close the test connection
      await testRedisClient.quit();

      return { host, port };
    });

    if (redisResult.success) {
      const { host, port } = redisResult.result;
      printStatus("Redis", true, `${host}:${port}`, redisResult.connectionTime);
    } else {
      printStatus("Redis", false, redisResult.error.message, redisResult.connectionTime);
    }

    // Check RabbitMQ connection
    const rabbitResult = await measureConnectionTime(async () => {
      const connection = await RABBIT_MQ_CONNECTION;
      if (!connection) {
        throw new Error("Connection failed");
      }
      return require("../api/queue/config").CONNECTION_CONFIG;
    });

    if (rabbitResult.success) {
      const { host, port } = rabbitResult.result;
      printStatus("RabbitMQ", true, `${host}:${port}`, rabbitResult.connectionTime);
    } else {
      printStatus("RabbitMQ", false, rabbitResult.error.message, rabbitResult.connectionTime);
    }

    console.log("\nConnection check complete.");

    // Exit with appropriate code after a short delay to ensure all console output is displayed
    setTimeout(() => {
      process.exit(hasErrors ? 1 : 0);
    }, 100);
  })().catch((error) => {
    console.error("Error in connection check:", error);
    process.exit(1);
  });
} catch (error) {
  console.error("error =", error);
}
