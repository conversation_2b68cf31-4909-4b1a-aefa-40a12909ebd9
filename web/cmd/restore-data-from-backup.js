const BackupService = require("../api/services/BackupService");

require("../api/config");

(async () => {
  try {
    const domain = process.argv.slice(2)[0];
    const fileName = process.argv.slice(3)[0] || null;
    console.log("\n---\nRestoring data for ", domain, "\n---");
    await BackupService.restoreData(domain, fileName);
    console.log("\n---\nDone!\n---\n");
  } catch (err) {
    console.log("err: ", err);
  } finally {
    process.exit(0);
  }
})();
