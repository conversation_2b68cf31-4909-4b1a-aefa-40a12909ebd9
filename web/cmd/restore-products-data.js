const { select, input, confirm, checkbox } = require("@inquirer/prompts");
const chalk = require("chalk");
const createSpinner = require("yocto-spinner").default;
const path = require("path");
const fs = require("fs");
const readline = require("readline");
const BackupService = require("../api/services/BackupService");
const ShopService = require("../api/services/ShopService");
const ProductService = require("../api/services/ProductService");
const ProductAnalysisService = require("../api/services/ProductAnalysisService");
const { graphQLClient } = require("../api/utils/shopify.clients");
const updateProductMetaMutation = require("../api/queries/products/mutation.update-product-meta.gql");
const metaFragment = require("../api/queries/fragment.metafield.gql");
const { isEmpty } = require("lodash");

/**
 * Main function to restore product data from backups
 */
(async () => {
  try {
    const spin = createSpinner();
    console.log(chalk.blue.bold("🔄 Product Data Restore Tool"));
    console.log("---------------------------------------------------");

    // Step 1: Get shop domain from user
    const shopDomain = await input({
      message: "Enter the shop domain (e.g. mystore.myshopify.com):",
      validate: (value) => {
        if (!value) return "Shop domain is required";
        if (!value.includes(".myshopify.com")) return "Please enter a valid myshopify domain";
        return true;
      },
    });

    // Step 2: Find shop by domain
    console.log(chalk.cyan("Looking for shop..."));
    spin.start();

    const shop = await ShopService.getShopByCondition({ domain: shopDomain });
    spin.stop();

    if (!shop) {
      console.log(chalk.red(`❌ Shop with domain ${shopDomain} not found`));
      return;
    }

    console.log(chalk.green(`✅ Found shop: ${shop.domain}`));

    // Step 3: Get all backups for the shop
    console.log(chalk.cyan("Fetching backups..."));
    spin.start();
    const backups = await BackupService.getAllManualBackupByShop(shop.id);
    spin.stop();

    if (!backups || backups.length === 0) {
      console.log(chalk.red("❌ No backups found for this shop"));
      return;
    }

    // Step 4: Show list of backups for selection
    const backupChoices = backups.map((backup) => ({
      name: `${backup.title} (${backup.resources.join(",")})`,
      value: backup,
    }));

    const selectedBackup = await select({
      message: "Select a backup to restore from:",
      choices: backupChoices,
    });

    console.log(chalk.green(`Selected backup: ${selectedBackup.title}`));

    // Step 5: Show data restoration options
    const dataToRestore = await checkbox({
      message: "Select what data to restore:",
      choices: [
        { name: "Tags", value: "tags", checked: true },
        { name: "Focus Keywords", value: "focus_keyword", checked: false },
        // Add more options later as needed
      ],
    });

    if (dataToRestore.length === 0) {
      console.log(chalk.yellow("⚠️ No data types selected for restoration"));
      return;
    }

    // Step 6: Confirm restoration
    const confirmed = await confirm({
      message: `Are you sure you want to restore ${dataToRestore.join(", ")} from backup: ${selectedBackup.title}?`,
      default: false,
    });

    if (!confirmed) {
      console.log(chalk.yellow("Operation cancelled by user"));
      return;
    }

    // Step 7: Download and extract backup file
    console.log(chalk.cyan("Downloading backup file..."));
    spin.start();

    const downloadFileName = selectedBackup.backup_file_src;
    await BackupService.downloadAndExtractManualBackupFile(shopDomain, downloadFileName);

    spin.stop();
    console.log(chalk.green("✅ Backup file downloaded and extracted"));

    // Step 8: Process and restore the selected data
    console.log(chalk.cyan(`Starting to restore ${dataToRestore.join(", ")} for products...`));
    spin.start();

    const backupDirectory = path.resolve(__dirname, `../uploads`);
    const extractDir = `${backupDirectory}/${shopDomain}/restore`;
    const productsFilePath = `${extractDir}/products.jsonl`;

    if (!fs.existsSync(productsFilePath)) {
      spin.stop();
      console.log(chalk.red("❌ Products data not found in the backup"));
      return;
    }

    // Read and process the products file
    let processedCount = 0;
    let updatedCount = 0;

    const rl = readline.createInterface({
      input: fs.createReadStream(productsFilePath),
      crlfDelay: Infinity,
    });

    for await (const line of rl) {
      processedCount++;
      const product = JSON.parse(line);

      // Prepare update data based on selected options
      const restoreData = {};
      for (const option of dataToRestore) {
        restoreData[option] = product[option];
      }

      // Add more options here as needed

      // Update product if we have data to update
      if (Object.keys(restoreData).length > 0) {
        try {
          const oldProduct = await ProductService.getProductDetails(shop.id, product.id);

          const { tags } = restoreData;

          const variables = {
            product: {
              id: product.product_id,
              ...(tags ? { tags: !isEmpty(tags) ? tags : null } : {}),
            },
          };

          console.log("\n***\ngraphql variables in product update: ", JSON.stringify(variables, null, 2));

          const {
            data: {
              productUpdate: { product: shopifyProduct, userErrors },
            },
          } = await graphQLClient(shopDomain, {
            variables,
            query: updateProductMetaMutation,
            fragments: [metaFragment],
          });

          if (userErrors.length > 0) {
            console.error(chalk.red(`Error updating product ${product.product_id}: ${userErrors[0].message}`));
            continue;
          }

          await ProductService.upsertProductMetadata(shop.id, product.id, shopifyProduct);

          const updateData = {};
          for (const key in restoreData) {
            updateData[key] = restoreData[key];
          }
          console.log("updateData = ", updateData);

          const productDetails = await ProductService.updateProduct(shop.id, product.id, updateData);

          await ProductAnalysisService.analyseEachProduct({
            product: productDetails,
            shopId: shop.id,
            oldProduct: oldProduct,
          });
          updatedCount++;
        } catch (error) {
          spin.stop();
          console.error(chalk.red(`Error updating product ${product.product_id}: ${error.message}`));
          spin.start();
        }
      }

      // Update progress every 10 products
      if (processedCount % 10 === 0) {
        spin.stop();
        console.log(chalk.cyan(`Processed ${processedCount} products, updated ${updatedCount}...`));
        spin.start();
      }
    }

    spin.stop();

    // Step 9: Clean up
    console.log(chalk.cyan("Cleaning up temporary files..."));
    spin.start();

    await BackupService.deleteAllFilesInDirectory(`${extractDir}`);

    spin.stop();

    // Step 10: Show completion message
    console.log(chalk.green("✅ Restoration completed successfully!"));
    console.log(chalk.green(`Total products processed: ${processedCount}`));
    console.log(chalk.green(`Total products updated: ${updatedCount}`));
  } catch (error) {
    console.error(chalk.red("Error in main process:"), error);
  } finally {
    process.exit();
  }
})();
