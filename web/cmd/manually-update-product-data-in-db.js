// ********
// Fetch & update all existing product that are stored in DB for a particular shop
// ********
const dotenv = require("dotenv");
const ProductService = require("../api/services/ProductService");
const ProductAnalysisService = require("../api/services/ProductAnalysisService");
const { Shop: ShopModel, Product: ProductModel, ProductMeta } = require("../sequelize");
const ShopifyService = require("../api/services/ShopifyService");
const { flattenMediaImages } = require("../api/serializers/ProductImageSerializer");

dotenv.config();

/**
 * @type {typeof import("sequelize").Model}
 */
const Shop = ShopModel;

/**
 * @type {typeof import("sequelize").Model}
 */
const Product = ProductModel;

const sleep = () => {
  return new Promise((resolve, reject) => {
    setTimeout(() => resolve(true), 120);
  });
};

const updateExistingProductDataFromShopify = async (domain) => {
  try {
    const shop = (await Shop.findOne({ where: { domain } })).toJSON();
    const products = (await Product.findAll({ where: { shop_id: shop.id } })).map((p) => p.toJSON());

    const user = { shop: shop.domain, accessToken: shop.access_token };

    const failedUpdate = [];

    for (let i = 0; i < products.length; i++) {
      const sl = `(${i + 1}/${products.length}, ${products[i].product_id})`;
      try {
        const shopifyProduct = await ShopifyService.getProductFromShopify(user.shop, products[i].product_id);
        shopifyProduct.mediaImages = flattenMediaImages(shopifyProduct);
        await ProductMeta.destroy({
          where: {
            product_id: products[i].id,
            shop_id: shop.id,
          },
        });
        const product = await ProductService.saveOrUpdateProduct(shop.id, shopifyProduct);
        await ProductAnalysisService.analyseEachProduct({ shopId: shop.id, product });
        console.log(sl, `Product ${products[i].id} - ${products[i].handle} updated successfully!\n`);
        await sleep();
      } catch (err) {
        console.log(sl, `Failed to update product ${products[i].id} - ${products[i].handle}!`, err);
        console.log();
        failedUpdate.push({ id: products[i].id, handle: products[i].handle });
      }
    }

    console.log("\n----");
    console.log("Total Products: ", products.length);
    console.log("Updated: ", products.length - failedUpdate.length);
    console.log("Update failed: ", failedUpdate.length);
    if (failedUpdate.length) {
      console.log("List of failed products: ");
      console.table(failedUpdate);
    }
    console.log("----\n");
  } catch (err) {
    console.log("Error in updating existing product data from shopify", err);
  }
};

const domain = process.argv[2];
updateExistingProductDataFromShopify(domain);

module.exports = {
  updateExistingProductDataFromShopify,
};
