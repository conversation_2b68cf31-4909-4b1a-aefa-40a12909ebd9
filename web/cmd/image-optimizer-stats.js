/**
 * Returns the image optimizer process usage stats stored in database between 2 dates
 *
 * SIGNATURE: node cmd/image-optimizer-stats.js [start-date(ex: 2024-02-11)] [end-date]
 */

require("../api/config");
const { QueueStats, Op } = require("../sequelize");

(async () => {
  try {
    let startDate = process.argv[2];
    let endDate = process.argv[3];

    if (!endDate) endDate = startDate;

    const stats = await QueueStats.findAll({
      where: {
        date: {
          [Op.between]: [startDate, endDate],
        },
      },
    });

    console.log("\nShowing statistics data from", startDate, "to", endDate);

    for (let stat of stats) {
      printStatistic(stat.toJSON());
    }
  } catch (err) {
    console.log("Error gettting stats: ", err);
  } finally {
    process.exit(0);
  }
})();

function printStatistic(statData) {
  const { date, queue_name: queueName, statistics } = statData;
  const { maxQueueUsage, minQueueUsage, maxStoreCount, minStoreCount, imagesQueued, imagesProcessed, imagesFailed } =
    statistics;

  console.group(`\n---\nDate: ${date}\n---`);
  console.log("Queue name: ", queueName);

  console.group("Queue usage:");
  console.log("max -", maxQueueUsage);
  console.log("min -", minQueueUsage);
  console.groupEnd();

  console.group("Stores queued:");
  console.log("max -", maxStoreCount);
  console.log("min -", minStoreCount);
  console.groupEnd();

  console.group("Images:");
  console.log("queued for optimization -", imagesQueued);
  console.log("successfully optimized -", imagesProcessed);
  console.log("failed to optimize -", imagesFailed);
  console.groupEnd();

  console.groupEnd();
  console.log("\n");
}
