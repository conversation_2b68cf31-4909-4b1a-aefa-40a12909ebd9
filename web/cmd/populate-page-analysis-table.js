require("../api/config");
const { Page: P, PageAnalysis: Pa } = require("../sequelize");

/**
 * @type {typeof import("sequelize").Model}
 */
const Page = P;
/**
 * @type {typeof import("sequelize").Model}
 */
const PageMeta = Pa;

(async () => {
  try {
    const pages = (await Page.findAll({ order: [["id", "ASC"]] })).map((page) => page.toJSON());

    console.log("Total page found: ", pages.length, "\n");

    for (let page of pages) {
      try {
        const { id: page_id, shop_id, analysis } = page;
        const data = { page_id, shop_id };

        console.log("Inserting analysis data for page id: ", page_id);

        const simplified = Object.keys(analysis).reduce((prev, a) => ({ ...prev, ...analysis[a] }), {});
        for (let key in simplified) {
          data[key.toLowerCase()] = simplified[key];
        }

        await PageMeta.upsert(data);
        console.log("Done.\n");
      } catch (err) {
        console.log("Failed, ", err);
        console.log("\n");
      }
    }

    console.log("\n\n====");
    console.log("All done!");
    console.log("====\n\n");
  } catch (err) {
    console.log("err: ", err);
  } finally {
    process.exit(0);
  }
})();
