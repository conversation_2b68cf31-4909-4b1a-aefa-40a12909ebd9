const { sequelize } = require("../sequelize");

(async () => {
  try {
    console.log("Starting migration of shop AI content settings...\n");

    // Execute raw SQL query to update the settings
    const [_results, metadata] = await sequelize.query(`
      UPDATE shop_settings
      SET value = jsonb_build_object(
        'PRODUCT', jsonb_build_object(
          'settings', (value::jsonb->'settings'),
          'status', (value::jsonb->>'status')::boolean
        )
      )::text,
      value_type = 'json'
      WHERE key = 'ai_content_settings'
      AND value_type = 'json'
      AND value::jsonb->>'settings' IS NOT NULL;
    `);

    console.log("\nFinal Summary:");
    console.log("==============");
    console.log(`Total settings updated: ${metadata.rowCount}`);
  } catch (error) {
    console.error("Error in migration process:", error);
  } finally {
    process.exit();
  }
})();
