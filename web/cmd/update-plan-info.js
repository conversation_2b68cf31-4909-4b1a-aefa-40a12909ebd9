const { Shop } = require("../sequelize");

(async () => {
  const shops = await Shop.findAll({
    attributes: ["id", "name", "plan_info", "domain"],
    where: { status: "ACTIVE" },
    order: [["id", "desc"]],
  });

  for (let i = 1; i <= shops.length; i++) {
    try {
      let shop = shops[i].toJSON();
      console.log(
        `[${i}/${shops.length}] -- ${shop.domain}: Updating plan info`,
        JSON.stringify(shop.plan_info.addons)
      );
      const addons = shop.plan_info?.addons?.map((addon) => {
        if (addon.group === "IMAGE_OPTIMIZER") {
          addon.limit = addon.features.image_optimizer;
          delete addon["features"];
          return addon;
        }

        return addon;
      });

      const plan_info = { ...shop.plan_info, addons };

      await Shop.update(
        {
          plan_info,
        },
        { where: { id: shop.id } }
      );

      console.log(`[${i}/${shops.length}] -- ${shop.domain}: Updated plan info`, JSON.stringify(plan_info.addons));
    } catch (error) {
      console.log(`[${i}/${shops.length}]`, error);
    }
  }

  process.exit();
})();
