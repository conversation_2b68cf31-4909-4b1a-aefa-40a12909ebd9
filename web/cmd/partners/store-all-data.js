const moment = require("moment");
const { dispatchQueue } = require("../../api/queue/queueDispatcher");
const { QUEUE_NAMES } = require("../../api/queue/config");

(async () => {
  const fromDate = process?.argv[2] || "2021-01-01";
  const startDate = moment(fromDate).startOf("D").toISOString();
  const endDate = moment().endOf("D").toISOString();

  dispatchQueue({
    queueName: QUEUE_NAMES.PARTNER_SAVE_APP_EVENTS,
    message: { startDate, endDate },
  });

  dispatchQueue({
    queueName: QUEUE_NAMES.PARTNER_SAVE_TRANSACTIONS,
    message: { startDate, endDate },
  });

  console.log("🤩 Dispached queues to store partner data.", { fromDate, startDate, endDate });
  setTimeout(process.exit, 5000);
})();
