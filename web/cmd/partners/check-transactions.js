const { pick } = require("lodash");
const TransactionService = require("../../api/services/partners/TransactionService");
const dayjs = require("dayjs");
const timezone = require("dayjs/plugin/timezone");
const utc = require("dayjs/plugin/utc");
const { BillingInterval } = require("@shopify/shopify-api");
const app = require("../../api/config/app");

dayjs.extend(utc);
dayjs.extend(timezone);

(async () => {
  try {
    const date = process.argv[2];
    const startDate = dayjs.tz(date, app.report.tz).startOf("day").toISOString();
    const endDate = dayjs.tz(date, app.report.tz).endOf("day").toISOString();
    const { transactions } = await TransactionService.getTransactionsFromShopify(startDate, endDate, undefined, 100);

    console.table(
      transactions
        .sort((a, b) => b.gross_amount - a.gross_amount)
        .map((transaction) =>
          pick(transaction, [
            "shop_domain",
            "billing_interval",
            "transaction_id",
            "transaction_created_at",
            "gross_amount",
          ])
        )
    );

    console.log("Summary: ", dayjs(date).format("DD MMM, YYYY"));
    console.log("--------------------------------");
    console.group();
    console.log("Total Transactions =", transactions.length);
    console.log(
      "Monthly Amount =",
      transactions
        .filter((transaction) => transaction.billing_interval === BillingInterval.Every30Days)
        .reduce((acc, transaction) => acc + Number(transaction.gross_amount), 0)
        .toLocaleString("en-US", {
          style: "currency",
          currency: "USD",
        })
    );
    console.log(
      "Annual Amount =",
      transactions
        .filter((transaction) => transaction.billing_interval === BillingInterval.Annual)
        .reduce((acc, transaction) => acc + Number(transaction.gross_amount), 0)
        .toLocaleString("en-US", {
          style: "currency",
          currency: "USD",
        })
    );
    console.log(
      "Onetime Amount =",
      transactions
        .filter((transaction) => transaction.billing_interval === undefined)
        .reduce((acc, transaction) => acc + Number(transaction.gross_amount), 0)
        .toLocaleString("en-US", {
          style: "currency",
          currency: "USD",
        })
    );

    console.log(
      "Refunded Amount =",
      transactions
        .filter((transaction) => transaction.billing_interval === null)
        .reduce((acc, transaction) => acc + Number(transaction.gross_amount), 0)
        .toLocaleString("en-US", {
          style: "currency",
          currency: "USD",
        })
    );
    console.log(
      "Total Amount =",
      transactions
        .reduce((acc, transaction) => acc + Number(transaction.gross_amount), 0)
        .toLocaleString("en-US", {
          style: "currency",
          currency: "USD",
        })
    );
    console.groupEnd();
  } catch (error) {
    console.error(error);
  } finally {
    process.exit(0);
  }
})();
