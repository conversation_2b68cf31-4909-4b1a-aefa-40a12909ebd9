const { omit } = require("lodash");
const AppEventService = require("../../api/services/partners/AppEventService");
const dayjs = require("dayjs");
const timezone = require("dayjs/plugin/timezone");
const utc = require("dayjs/plugin/utc");
const appEventTypes = require("storeseo-enums/shopify/appEventTypes");
const { sleep } = require("../../api/utils/helper");
const app = require("../../api/config/app");

dayjs.extend(utc);
dayjs.extend(timezone);

(async () => {
  try {
    const date = process.argv[2];
    const startDate = dayjs.tz(date, app.report.tz).startOf("day").toISOString();
    const endDate = dayjs.tz(date, app.report.tz).endOf("day").toISOString();

    let allEvents = [];
    let cursor;
    let hasNextPage = true;

    while (hasNextPage) {
      const { events, pageInfo, lastCursor } = await AppEventService.getAppEvents(startDate, endDate, cursor, 50);
      allEvents = [...allEvents, ...events];
      cursor = lastCursor;
      hasNextPage = pageInfo.hasNextPage;
      await sleep(1000);
    }

    console.table(
      allEvents
        .sort((a, b) => a.event_type.localeCompare(b.event_type))
        .map((event) => ({
          ...omit(event, ["charge_currency_code", "shopify_shop_id", "charge_id", "charge_amount"]),
          charge_name: event?.charge_name ? `${event.charge_name} ($${event.charge_amount})` : undefined,
        }))
    );

    console.log("Summary: ", dayjs(date).format("DD MMM, YYYY"));
    console.log("--------------------------------");
    console.group();
    console.info("Start Date =", startDate);
    console.info("End Date =", endDate);
    console.log("--------------------------------");
    console.log("Total Events =", allEvents.length);
    console.log("--------------------------------");
    console.log(
      "Installations =",
      allEvents.filter((event) => event.event_type === appEventTypes.RELATIONSHIP_INSTALLED).length
    );
    console.log(
      "Re-Opened =",
      allEvents.filter((event) => event.event_type === appEventTypes.RELATIONSHIP_REACTIVATED).length
    );
    console.log(
      "Uninstallations =",
      allEvents.filter((event) => event.event_type === appEventTypes.RELATIONSHIP_UNINSTALLED).length
    );

    console.log(
      "Closed =",
      allEvents.filter((event) => event.event_type === appEventTypes.RELATIONSHIP_DEACTIVATED).length
    );
    console.log("--------------------------------");
    console.log(
      "Subscriptions Activated =",
      allEvents.filter((event) => event.event_type === appEventTypes.SUBSCRIPTION_CHARGE_ACTIVATED).length
    );
    console.log(
      "Subscriptions Deactivated =",
      allEvents.filter((event) => event.event_type === appEventTypes.SUBSCRIPTION_CHARGE_CANCELED).length
    );
    console.log(
      "Subscriptions Frozen =",
      allEvents.filter((event) => event.event_type === appEventTypes.SUBSCRIPTION_CHARGE_FROZEN).length
    );
    console.log(
      "Subscriptions Unfrozen =",
      allEvents.filter((event) => event.event_type === appEventTypes.SUBSCRIPTION_CHARGE_UNFROZEN).length
    );
    console.log("--------------------------------");
    console.log(
      "Onetime Charges =",
      allEvents.filter((event) => event.event_type === appEventTypes.ONE_TIME_CHARGE_ACTIVATED).length
    );
    console.log(
      "Usage Charges =",
      allEvents.filter((event) => event.event_type === appEventTypes.USAGE_CHARGE_APPLIED).length
    );
    console.groupEnd();
  } catch (error) {
    console.error(error);
  } finally {
    process.exit(0);
  }
})();
