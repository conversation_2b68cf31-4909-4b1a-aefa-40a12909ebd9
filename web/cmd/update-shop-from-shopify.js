const { serializeShopifyShopData } = require("../api/serializers/ShopSerializer");
const GoogleApiService = require("../api/services/GoogleApiService");
const ShopService = require("../api/services/ShopService");
const ShopifyService = require("../api/services/ShopifyService");
const LocationService = require("../api/services/LocationService");
const SEOService = require("../api/services/SEOService");
const { METAFIELD_KEYS } = require("storeseo-enums/metafields");

require("../api/config");

(async () => {
  try {
    const shopDomain = process.argv[2];

    console.log(`[${shopDomain}]`, "Pulling shop data from Shopify...");
    const shopifyShop = await ShopifyService.getShopDetailsFromShopify(shopDomain);
    const { billing_address, meta, ...shopData } = serializeShopifyShopData(shopifyShop, accessToken);

    console.log("Updating data in database...");
    await ShopService.updateShop(id, shopData);
    await ShopService.upsertBillingAddress(id, billing_address);
    await ShopService.upsertMetaData(id, meta);

    console.log("Updating local SEO data...");
    let jsonldData = await ShopService.getJsonldSettings(id);
    const { lat: latitude = "", lng: longitude = "" } =
      (await GoogleApiService.getLatLongFromAddress(billing_address)) ?? {};

    const { address1 = "", address2 = "", city = "", zip = "", country = "" } = billing_address || {};
    jsonldData = {
      ...jsonldData,
      name: shopData.name,
      url: shopData.url,
      address: { address1, address2, city, zip, country },
      geo: { latitude, longitude },
    };
    await ShopService.upsertJsonldSetting(id, jsonldData);

    const locations = await LocationService.getAllLocations(id);
    const jsonldSchema = SEOService.generateJSONLDSchemaForLocalSEO(jsonldData, locations);
    await ShopifyService.setMetafield(
      { shop, accessToken },
      {
        ownerId: shopifyShopId,
        key: METAFIELD_KEYS.LOCAL_SEO,
        value: JSON.stringify(jsonldSchema),
      }
    );

    console.log("Done!");
  } catch (err) {
    console.log("Error: ", err);
  } finally {
    process.exit(0);
  }
})();
