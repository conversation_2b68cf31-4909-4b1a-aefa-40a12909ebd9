require("dotenv").config();
require("../api/config");
const { Op } = require("sequelize");
const { ACTIVE } = require("storeseo-enums/shopStatus");
const settingKeys = require("storeseo-enums/settingKeys");
const ShopService = require("../api/services/ShopService");
const {
  LOCAL_SEO_ORGANIZATION_SCHEMA,
  LOCAL_SEO_LOCAL_BUSINESS_SCHEMA,
  LOCAL_SEO_COMMON_SCHEMA,
} = require("storeseo-enums/localSEOSchemaTypes");
const ShopifyService = require("../api/services/ShopifyService");
const { METAFIELD_KEYS } = require("storeseo-enums/metafields");

(async () => {
  try {
    const allShops = await ShopService.getAllShopByCondition({
      plan_id: { [Op.gt]: 0 },
      status: ACTIVE,
    });

    let currentShopIndex = 1;

    for await (let shops of ShopService.iterateOverAllShops({
      plan_id: { [Op.gt]: 0 },
      status: ACTIVE,
    })) {
      for (let shop of shops) {
        const shopId = shop.id;
        const { domain, access_token, shop_id } = shop;
        const session = {
          shop: domain,
          accessToken: access_token,
        };

        console.log(
          `${currentShopIndex++}/${allShops.length} Migrating ld+json settings to new SEO Schema settings for (${
            shop.domain
          })...`
        );
        try {
          const existingJsonLd = await ShopService.getShopSetting(shopId, settingKeys.JSONLD_DATA);
          if (existingJsonLd && existingJsonLd.value) {
            // Extract the required fields from the existing JSON LD settings
            const {
              name,
              telephone,
              image,
              priceRange,
              socialMediaLinks: { facebookURL, instagramURL, twitterURL, linkedinURL, pinterestURL },
              address: { address1, zip, city, country },
            } = existingJsonLd.value;

            // Update organization schema settings with the extracted fields
            const organizationSchemaResult = await ShopService.getShopSetting(shopId, LOCAL_SEO_ORGANIZATION_SCHEMA);
            const newOrganizationSchema = {
              ...(organizationSchemaResult && organizationSchemaResult.value),
              settings: {
                socialProfiles: {
                  facebook: facebookURL,
                  instagram: instagramURL,
                  twitter: twitterURL,
                  linkedin: linkedinURL,
                  pinterest: pinterestURL,
                },
                address: {
                  streetAddress: address1,
                  city,
                  state: city,
                  postalCode: zip,
                  telephone,
                  country,
                },
              },
            };

            const newOrganizationSchemaSettings = {
              key: LOCAL_SEO_ORGANIZATION_SCHEMA,
              value: JSON.stringify(newOrganizationSchema),
              value_type: "json",
            };

            await ShopService.updateShopSetting(shopId, newOrganizationSchemaSettings);
            await ShopifyService.setMetafield(session.shop, {
              ownerId: shop_id,
              key: METAFIELD_KEYS.LOCAL_SEO_ORGANIZATION_SCHEMA,
              value: JSON.stringify(newOrganizationSchema),
            });

            // Update local business schema settings with the extracted fields
            const localBusinessSchemaResult = await ShopService.getShopSetting(shopId, LOCAL_SEO_LOCAL_BUSINESS_SCHEMA);
            const newLocalBusinessSchema = {
              ...(localBusinessSchemaResult ? localBusinessSchemaResult.value : {}),
              settings: {
                ...localBusinessSchemaResult?.value?.settings,
                address: {
                  ...localBusinessSchemaResult?.value?.settings?.address,
                  streetAddress: address1,
                  locality: city,
                  region: country,
                  postalCode: zip,
                  telephone,
                },
              },
            };
            const newLocalBusinessSchemaSettings = {
              key: LOCAL_SEO_LOCAL_BUSINESS_SCHEMA,
              value: JSON.stringify(newLocalBusinessSchema),
              value_type: "json",
            };
            await ShopService.updateShopSetting(shopId, newLocalBusinessSchemaSettings);
            await ShopifyService.setMetafield(session.shop, {
              ownerId: shop_id,
              key: METAFIELD_KEYS.LOCAL_SEO_LOCAL_BUSINESS_SCHEMA,
              value: JSON.stringify(newLocalBusinessSchema),
            });

            // Update Common SEO settings with the extracted fields
            const commonLocalSeoSettings = await ShopService.getShopSetting(shopId, LOCAL_SEO_COMMON_SCHEMA);
            const newCommonLocalSeoSettings = {
              ...(commonLocalSeoSettings && commonLocalSeoSettings.value),
              basicInformation: {
                ...commonLocalSeoSettings?.value?.basicInformation,
                businessName: name,
                businessPriceRange: priceRange,
              },
              store_logo: image,
            };
            const newCommonLocalSeoSettingsSettings = {
              key: LOCAL_SEO_COMMON_SCHEMA,
              value: JSON.stringify(newCommonLocalSeoSettings),
              value_type: "json",
            };
            await ShopService.updateShopSetting(shopId, newCommonLocalSeoSettingsSettings);
            await ShopifyService.setMetafield(session.shop, {
              ownerId: shop_id,
              key: METAFIELD_KEYS.LOCAL_SEO_COMMON_SCHEMA,
              value: JSON.stringify(newCommonLocalSeoSettings),
            });
          }
        } catch (error) {
          console.error(
            `${currentShopIndex++}/${allShops.length} Error migrating ld+json settings for the shop (${shop.domain}):`,
            error
          );
        }
      }
    }
  } catch (err) {
    console.error(err);
  }
})();
