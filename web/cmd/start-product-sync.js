require("dotenv").config();
const ShopifyService = require("../api/services/ShopifyService");
const ShopService = require("../api/services/ShopService");
const { QUEUE_NAMES } = require("../api/queue/config");
const { dispatchQueue } = require("../api/queue/queueDispatcher");
const ProductService = require("../api/services/ProductService");
const { sleep } = require("../api/utils/helper");

(async () => {
  try {
    const domain = process.argv[2];

    const shop = await ShopService.getShop(domain);

    const session = {
      shopId: shop.id,
      shop: domain,
      accessToken: shop.access_token,
      permission: shop.plan_rules,
    };

    console.log(`\n\n---[${session.shop}] - dispatching product sync...\n---\n\n`);
    dispatchQueue({
      queueName: QUEUE_NAMES.PRODUCT_SYNC_WITH_DATA_MIGRATION,
      message: {
        user: session,
        migrateDataFromApp: undefined,
      },
    });

    await ProductService.syncOngoing(shop, true);
    await sleep(5000);
  } catch (err) {
    console.log(err);
  } finally {
    process.exit();
  }
})();
