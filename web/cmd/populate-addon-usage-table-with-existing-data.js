require("../api/config");
const cache = require("../api/cache");
const AddonUsageService = require("../api/services/AddonUsageService");
const ShopService = require("../api/services/ShopService");
const { Shop } = require("../sequelize");

(async () => {
  try {
    const totalShops = await Shop.count({});
    let counter = 1;

    for await (let shops of ShopService.iterateOverAllShops()) {
      for (let shop of shops) {
        try {
          console.log(`\n---\n[${counter}/${totalShops}]Checking shop: ${shop.domain}...\n---`);

          const addons = addonsPurchased(shop);
          const recurringAddons = addons.filter((a) => a.interval !== "CREDIT");
          const creditAddons = addons.filter((a) => a.interval === "CREDIT");

          console.group();
          console.log("Addons purchased: ", addons.length);
          addons.map((a) => console.log(a.group));

          await AddonUsageService.updateRecurringUsagePermissions({
            shopId: shop.id,
            shopDomain: shop.domain,
            addons: recurringAddons,
            purchaseDate: shop.subscribed_at,
          });

          await AddonUsageService.updateCreditUsagePermissions({
            shopId: shop.id,
            shopDomain: shop.domain,
            addons: creditAddons,
            purchaseDate: shop.subscribed_at,
          });

          console.log("Usage records created for purchased add-ons!");
          console.groupEnd();
        } catch (err) {
          console.log("err: ", err);
        } finally {
          counter++;
        }
      }
    }
  } catch (err) {
    console.log("err: ", err);
  } finally {
    console.log("\n***\nAll done!\n***\n");
    // process.exit(0);
  }
})();

function addonsPurchased(shop) {
  /** @type {import('../sequelize/models/subscriptionaddon').SubscriptionAddon[]} */
  const addons = [];

  if (shop.plan_info?.addons) {
    for (let addon of shop.plan_info?.addons) {
      addon.limit = Math.max(shop.plan_rules[addon.group.toLowerCase()], addon.limit);

      addons.push(addon);
    }
  }

  return addons;
}
