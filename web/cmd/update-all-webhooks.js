const { Shop } = require("../sequelize");
const { ACTIVE } = require("storeseo-enums/shopStatus");
const cache = require("../api/cache");

(async () => {
  try {
    const shops = await Shop.findAll({
      attributes: ["id", "domain"],
      where: { status: ACTIVE },
      order: [["id", "desc"]],
    });

    console.log("-------------------");
    console.log("Total Shops found = ", shops.length);

    let sl = 1;
    for (let shop of shops) {
      const { domain } = shop;
      try {
        await cache.webhooks.addShopToPendingWebhookRegistrationList(domain);
        console.log(`${sl}/${shops.length} -- [${domain}] -- added to pending webhook registration list.`);
      } catch (err) {
        console.error(`${sl}/${shops.length}`, `Error adding ${domain} to pending webhook registration list`, err);
      }
      sl++;
    }
    console.log("-------------------");
  } catch (error) {
    console.error(error);
  }
  // process.exit();
})();
