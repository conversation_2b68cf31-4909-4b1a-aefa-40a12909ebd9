/**
 * restore redis data from database
 * Signature node cmd/restore-redis-data.js {?yourdomain.myshopify.com}
 */

const { sequelize } = require("../sequelize");
const settingKeys = require("storeseo-enums/settingKeys");
const { QueryTypes } = require("sequelize");
const redisClient = require("../api/cache/client");
const ShopService = require("../api/services/ShopService");

const restoreRedisData = async (domain) => {
  const settingsData = await sequelize.query(
    `SELECT shop_settings.*, shops."domain" FROM shop_settings LEFT JOIN shops ON shop_settings.shop_id=shops.id WHERE shop_settings.key=:key and domain=:domain`,
    {
      type: QueryTypes.SELECT,
      replacements: {
        key: settingKeys.REDIS_DATA,
        domain,
      },
    }
  );

  if (settingsData.length === 0) {
    console.log("settings not found", domain);
    return;
  }
  const redisValues = JSON.parse(settingsData[0]?.value);

  if (redisValues.length === 0) {
    console.log("no values found to update", domain);
    return;
  }

  const keys = Object.keys(redisValues);
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    const value = redisValues[key];
    await redisClient.set(key, value);
  }

  console.log("➡️", "Redis data restored of the shop =", domain);
};

(async () => {
  const domain = process.argv[2];

  if (domain) {
    try {
      await restoreRedisData(domain);
    } catch (error) {
      console.error("error =", error);
    }
  } else {
    const shops = await ShopService.getActiveShopDomains();

    const totalShops = shops.length;
    let success = 0;
    let failed = 0;

    for (let k = 0; k < shops.length; k++) {
      const { domain, id } = shops[k];
      try {
        await restoreRedisData(domain);
        success++;
      } catch (error) {
        console.error("error =", error);
        failed++;
      }
    }

    console.log("--------------------------------------------");
    console.log("Total Shops =", totalShops);
    console.log("Total Successed: ", success);
    console.log("Total Failed: ", failed);
    console.log("--------------------------------------------");
  }

  process.exit();
})();
