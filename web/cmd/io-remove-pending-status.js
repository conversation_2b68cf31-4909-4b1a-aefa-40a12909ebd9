const ProductImageService = require("../api/services/ProductImageService");
const ShopService = require("../api/services/ShopService");
const { ProductImage, Op } = require("../sequelize");
const cache = require("../api/cache");

const { NOT_OPTIMIZED, ALREADY_OPTIMIZED, OPTIMIZED } = require("storeseo-enums/imageOptimization");

(async () => {
  try {
    const shopDomain = process.argv[2];
    const { id } = await ShopService.getShop(shopDomain);

    const productImages = await ProductImageService.getImagesByConditions(
      {
        shop_id: id,
        optimization_status: {
          [Op.notIn]: [NOT_OPTIMIZED, ALREADY_OPTIMIZED, OPTIMIZED],
        },
      },
      ["id"]
    );

    const ids = productImages.map((pi) => pi.id);

    const [count] = await ProductImage.update(
      { optimization_status: NOT_OPTIMIZED },
      {
        where: {
          id: {
            [Op.in]: ids,
          },
        },
      }
    );

    console.log("count =", count);

    if (count) {
      const usage = await cache.imageOptimizerUsageCount(shopDomain);
      await cache.imageOptimizerUsageCount(shopDomain, usage - count);
      const updatedUsage = await cache.imageOptimizerUsageCount(shopDomain);
      console.log(`Shop: ${shopDomain}, Usage Updated: ${updatedUsage}`);
    }
  } catch (err) {
    console.error(err);
  }

  process.exit();
})();
