require("../../api/config");
const subscriptionAddonGroup = require("storeseo-enums/subscriptionAddonGroup");
const cache = require("../../api/cache");
const cacheKeys = require("storeseo-enums/cacheKeys");

(async () => {
  try {
    const [domain, addon] = process.argv.slice(2);

    if (!domain || !addon) throw Error("Required args missing, Args: <myShopifyDomain> <addon>");

    if (!subscriptionAddonGroup[addon] || !cacheKeys[subscriptionAddonGroup[addon]])
      throw Error("Invalid addon passed as args.");

    console.log(`Resetting ${addon} for ${domain}`);
    console.group();

    const currentLimit = await cache.addons.usageLimit(domain, { addon });
    const currentCount = await cache.addons.usageCount(domain, { addon });
    const resetCount = await cache.addons.usageCount(domain, { addon, count: 0 });

    console.log(`Limit: ${currentLimit}`);
    console.log(`Usage before reset: ${currentCount}`);
    console.log(`Usage after reset: ${resetCount}`);
    console.groupEnd();
    console.log(`Reset ${addon} for ${domain}`);
  } catch (error) {
    console.error(error);
  } finally {
    process.exit();
  }
})();
