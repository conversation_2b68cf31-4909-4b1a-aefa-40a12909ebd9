/**
 * Removes the deleted products are not deleted by webhook or not synced properly
 * This command takes shop's myshopify domain as parameter
 * Ex: node cmd/remove-delete-products.js nahian104.myshopify.com
 */

const { Shop, Product } = require("../sequelize");
const { graphQLClient } = require("../api/utils/shopify.clients");
const { isNull } = require("lodash");

(async () => {
  let sl = 1;
  try {
    const domain = process.argv[2];
    const shop = await Shop.findOne({
      where: { domain },
      fields: ["id", "access_token"],
    });
    const session = {
      shop: domain,
      accessToken: shop.access_token,
    };

    const products = await Product.findAll({
      where: { shop_id: shop.id },
      attributes: ["id", "product_id"],
    });

    console.log(`Found ${products.length} products for the shop ${domain}.`);

    const query = `query ($id: ID!){
      product(id: $id){
        id
        status
      }
    }`;

    for (let i = 0; i < products.length; i++) {
      try {
        const id = products[i].product_id;
        const {
          data: { product },
        } = await graphQLClient(session.shop, { query, variables: { id } });

        if (isNull(product) || product?.status !== "ACTIVE") {
          await Product.destroy({ where: { product_id: id } });
          console.log(`Delete product -- ${id}.`);
          sl++;
        }
      } catch (err) {
        console.error(2, err.message);
      }
    }
  } catch (err) {
    console.error(1, err.message);
  } finally {
    console.log(`------- Total Delete ${sl} products`);
    process.exit();
  }
})();
