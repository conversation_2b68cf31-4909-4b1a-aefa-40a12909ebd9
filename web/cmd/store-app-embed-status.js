(async () => {
  try {
    require("dotenv").config();
    const cache = require("../api/cache");
    const ShopService = require("../api/services/ShopService");
    const ShopifyService = require("../api/services/ShopifyService");

    const shops = await ShopService.getActiveShopDomains();

    let enabledCount = 0;
    let disabledCount = 0;

    for (let i = 0; i < shops.length; i++) {
      try {
        const shop = shops[i];

        const session = { shop: shop.domain, accessToken: shop.access_token };

        const currentTheme = await ShopifyService.onlineStore.getCurrentTheme(session.shop);
        const settingsJsonAsset = await ShopifyService.getThemeAsset(session, {
          themeId: currentTheme.id,
          assetKey: "config/settings_data.json",
        });

        const settings = JSON.parse(settingsJsonAsset.value);
        let enabled = false;

        if (settings?.current?.blocks) {
          const storeSeoMetaBlock = Object.values(settings?.current?.blocks).find((block) =>
            block.type.includes("store_seo_meta")
          );

          enabled = storeSeoMetaBlock ? !storeSeoMetaBlock.disabled : false;
        }

        await cache.appEmbedStatus(shop, enabled);

        console.log(`[${i + 1}/${shops.length}] = Stored app embed status of the shop: ${shop.domain} = ${enabled}`);

        if (enabled) {
          enabledCount++;
        } else {
          disabledCount++;
        }
      } catch (error) {
        console.error(`[${i + 1}/${shops.length}]`, "error =", error);
      }
    }

    console.log("----------------------------------------");
    console.log("Enabled =", enabledCount);
    console.log("Disabled =", disabledCount);
    console.log("----------------------------------------");
  } catch (error) {
    console.error("error =", error);
  }

  // process.exit();
})();
