require("dotenv").config();
require("../api/config");
const resourceType = require("storeseo-enums/resourceType");
const altTextOptimizationStatus = require("storeseo-enums/altTextOptimization");
const { sequelize, ResourceOptimization, ResourceDataBackup } = require("../sequelize");
const { isEqual, last } = require("lodash");
const imageOptimization = require("storeseo-enums/imageOptimization");
const resourceOPType = require("storeseo-enums/resourceOPType");
const { sleep } = require("../api/utils/helper");

/**
 *
 * @param {import("sequelize").WhereOptions} conditions
 */
const iterateOverAllImages = async function* () {
  let limit = 2000;
  let image_id = 0;
  let images = [1];

  while (images.length > 0) {
    images = await sequelize.query(
      `
      SELECT *
      FROM product_images
      WHERE (original_alt_text != '' OR original_alt_text IS NOT NULL)
        AND id > :image_id
      ORDER BY id ASC
      LIMIT :limit;
      `,
      {
        replacements: {
          image_id,
          limit,
        },
        type: sequelize.QueryTypes.SELECT,
      }
    );

    image_id = last(images)?.id;

    yield images;
  }
};

(async () => {
  let batch = 1;
  let sl = 1;
  let totalImages = 0;
  let ResourceOptimizationCount = 0;
  let ResourceDataBackupCount = 0;
  try {
    const [data] = await sequelize.query(
      `SELECT MAX(id)FROM product_images WHERE(original_alt_text='' OR original_alt_text IS NULL);`,
      {
        type: sequelize.QueryTypes.SELECT,
      }
    );

    for await (let images of iterateOverAllImages()) {
      totalImages += images.length;
      for (let image of images) {
        console.log(
          `#${sl++}/${totalImages}: Migrating image original alt text to backup table. ID: ${image.id}/${data.max}`
        );
        try {
          if (!isEqual(image?.alt_text_optimization_stats, imageOptimization.optimizationStats)) {
            await ResourceOptimization.create({
              shop_id: image.shop_id,
              resource_id: image.id,
              resource_type: resourceType.PRODUCT_IMAGE,
              resource_op_type: resourceOPType.AI_OPTIMIZATION,
              optimization_stats: image.alt_text_optimization_stats,
              optimization_meta: image.alt_text_optimization_meta,
            });

            ResourceOptimizationCount++;
          }
          // Migrate image original alt text to backup table only if image alt text is already optimized
          if (image.alt_text_optimization_status === altTextOptimizationStatus.OPTIMIZED) {
            await ResourceDataBackup.create({
              shop_id: image.shop_id,
              resource_id: image.id,
              resource_type: resourceType.PRODUCT_IMAGE,
              resource_op_type: resourceOPType.AI_OPTIMIZATION,
              data: { [image.media_id]: image.original_alt_text },
            });
            ResourceDataBackupCount++;
          }
        } catch (error) {
          console.error(`Error migrating image origianl alt text to backup table.`);
        }
      }

      console.table({ batch, ResourceOptimizationCount, ResourceDataBackupCount });
      // sleep for 1 second
      await sleep(1000);
      batch++;
    }
  } catch (err) {
    console.error(err);
  }
})();
