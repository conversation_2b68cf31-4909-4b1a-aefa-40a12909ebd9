require("../api/config");
const cache = require("../api/cache");
const { QUEUE_NAMES } = require("../api/queue/config");
const { dispatchQueue } = require("../api/queue/queueDispatcher");
const AuthService = require("../api/services/AuthService");
const ShopService = require("../api/services/ShopService");
const SubscriptionPlanService = require("../api/services/SubscriptionPlanService");
const { sleep } = require("../api/utils/helper");

(async () => {
  try {
    const shopDomain = process.argv[2];

    await cache.removeProductSyncCursor(shopDomain);
    const shop = await ShopService.getShop(shopDomain);
    const plan = await SubscriptionPlanService.getSubscriptionPlanNameAndType(shop.plan_id);
    const user = await AuthService.serializeAuthToken(shop, plan);

    dispatchQueue({
      queueName: QUEUE_NAMES.PRODUCT_SYNC_WITH_DATA_MIGRATION,
      message: {
        user,
      },
    });

    console.log("Dispatched old product sync queue for shop: ", shopDomain);
  } catch (err) {
    console.log(err);
  } finally {
    await sleep(3000);
    process.exit(0);
  }
})();
