require("dotenv");
require("../api/config");
const planInterval = require("storeseo-enums/planInterval");
const { SubscriptionPlan } = require("../sequelize");
const FluentCrmService = require("../api/services/FluentCrmService");

const createTagBySubscriptionPlan = async (plan) => {
  try {
    let title = plan.name;

    if (plan.interval === planInterval.ANNUALLY) {
      title += "-Yearly";
    }

    const description = `Name: ${plan.name}, Interval: ${plan.interval}`;
    const slug = plan.slug;

    console.log(`\nCreating tag for ${title}`);

    const tag = await FluentCrmService.createTag(title, slug, description);
    tag.tag_id = tag.id;
    delete tag.id;
    await FluentCrmService.saveTagInDb(tag);

    console.log("Done!");
  } catch (err) {
    console.log(err);
    console.log("Failed!");
  }
};

const createAppUninstalledTag = async () => {
  try {
    console.log("\n Creating APP_UNINSTALLED tag");
    const tag = await FluentCrmService.createTag("Uninstalled", "app-uninstalled", "App uninstalled from store");
    tag.tag_id = tag.id;
    delete tag.id;
    await FluentCrmService.saveTagInDb(tag);

    console.log("Done!");
  } catch (err) {
    console.log(err);
    console.log("Failed!");
  }
};

(async () => {
  try {
    const plans = (await SubscriptionPlan.findAll()).map((p) => p.toJSON());
    for (let plan of plans) {
      await createTagBySubscriptionPlan(plan);
    }

    await createAppUninstalledTag();
  } catch (err) {
    console.log(err);
  }
})();
