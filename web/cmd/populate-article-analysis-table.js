require("../api/config");
const { Article: A, ArticleAnalysis: Aa } = require("../sequelize");

/**
 * @type {typeof import("sequelize").Model}
 */
const Article = A;
/**
 * @type {typeof import("sequelize").Model}
 */
const ArticleMeta = Aa;

(async () => {
  try {
    const articles = (await Article.findAll({ order: [["id", "ASC"]] })).map((a) => a.toJSON());

    console.log("Total articles found: ", articles.length, "\n");

    for (let article of articles) {
      const { id: article_id, blog_id, shop_id, analysis } = article;
      const data = { article_id, shop_id, blog_id };

      console.log("Inserting analysis data for article id: ", article_id);

      try {
        const simplified = Object.keys(analysis).reduce((prev, a) => ({ ...prev, ...analysis[a] }), {});
        for (let key in simplified) {
          data[key.toLowerCase()] = simplified[key];
        }

        await ArticleMeta.upsert(data);
        console.log("Done.\n");
      } catch (err) {
        console.log(err);
      }
    }

    console.log("\n\n====");
    console.log("All done!");
    console.log("====\n\n");
  } catch (err) {
    console.log("err: ", err);
  } finally {
    process.exit(0);
  }
})();
