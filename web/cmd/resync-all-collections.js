const cache = require("../api/cache");
const { QUEUE_NAMES } = require("../api/queue/config");
const { dispatchQueue } = require("../api/queue/queueDispatcher");
const { sequelize } = require("../sequelize");

(async () => {
  try {
    const [shops] = await sequelize.query(
      "select distinct(collections.shop_id) as shop_id, shops.domain, shops.access_token from collections left join shops on collections.shop_id = shops.id where shops.status = 'ACTIVE'"
    );

    let count = 0;
    let total = shops.length;

    for (let shop of shops) {
      count++;
      try {
        const user = {
          shopId: shop.shop_id,
          shop: shop.domain,
          accessToken: shop.access_token,
        };

        await cache.removeCollectionSyncCursor(user.shop);

        dispatchQueue({
          queueName: QUEUE_NAMES.COLLECTION_SYNC_QUEUE,
          message: { user },
          ttl: 3000,
        });

        console.log(`[${++count}/${total}] ${user.shop} - ${QUEUE_NAMES.COLLECTION_SYNC_QUEUE} dispatched.`);
      } catch (error) {
        console.error(
          `[${++count}/${total}] ${user.shop} - ${QUEUE_NAMES.COLLECTION_SYNC_QUEUE} failed.`,
          error.message
        );
      }
    }
  } catch (error) {
    console.error("Error occurred:", error);
  }
})();
