const { QUEUE_NAMES } = require("../api/queue/config");
const { dispatchQueue } = require("../api/queue/queueDispatcher");
const ShopService = require("../api/services/ShopService");

(async () => {
  try {
    console.time("res");

    const shop = process.argv[2];

    if (shop) {
      dispatchQueue({
        queueName: QUEUE_NAMES.SYNC_SHOP_LOCALES_LIST,
        message: { headers: { "x-shopify-shop-domain": shop } },
      });
    } else {
      const shops = await ShopService.getActiveShopDomains();

      for (let { domain } of shops) {
        dispatchQueue({
          queueName: QUEUE_NAMES.SYNC_SHOP_LOCALES_LIST,
          message: { headers: { "x-shopify-shop-domain": domain } },
        });
      }
    }

    console.timeEnd("res");
  } catch (error) {
    console.error("error =", error);
  } finally {
    setTimeout(() => process.exit(0), 5000);
  }
})();
