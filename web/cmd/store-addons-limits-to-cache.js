const ShopService = require("../api/services/ShopService");
const cache = require("../api/cache");
const { pick } = require("lodash");

(async () => {
  try {
    const domain = process.argv[2];

    const shop = await ShopService.getShop(domain);

    async function getAddonUsageLimit(shop, addon) {
      const { group } = addon;
      const limit = shop.plan_rules?.[group.toLowerCase()];
      const addonKey = cache.keys[group];

      const usageLimit = await cache.addons.usageLimit(shop.domain, { limit, addon: addonKey });

      return { ...pick(addon, ["id", "name", "group", "interval", "limit"]), usageLimit };
    }

    const res = await Promise.allSettled(shop.plan_info.addons.map((addon) => getAddonUsageLimit(shop, addon)));

    console.table(res.map((r) => r.value));

    console.log("✅ Limit updated of the shop =", domain);
  } catch (error) {
    console.error("error =", error);
  }

  process.exit();
})();
