const SEOService = require("../api/services/SEOService");
const ShopService = require("../api/services/ShopService");

(async () => {
  try {
    const domain = process.argv.slice(2)[0];

    console.log("\n---");
    console.log("Removing redirect out of stock logic from shop: ", domain);

    const shop = await ShopService.getShop(domain);
    const { domain: shopDomain, access_token: accessToken, theme_id: themeId } = shop;
    const session = { shop: shopDomain, accessToken };

    await SEOService.removeRedirectOutOfStockLogicFromTheme(session, themeId);

    console.log("Done!");
  } catch (err) {
    console.log("Failed!\n");
    console.log("err: ", err);
  } finally {
    console.log("---\n");
    process.exit(0);
  }
})();
