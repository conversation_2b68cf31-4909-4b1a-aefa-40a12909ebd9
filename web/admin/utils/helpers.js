const normalizePath = (url) => {
  const pathParts = url.split("?")[0].split("/v1");
  return pathParts.filter((part) => part).join("/");
};
const matchRoute = (path, routes) => {
  for (const route in routes) {
    const routePattern = new RegExp(`^${route.replace(/:[^/]+/g, "[^/]+")}$`);
    if (routePattern.test(path)) {
      return route;
    }
  }
  return null;
};

module.exports = { normalizePath, matchRoute };
