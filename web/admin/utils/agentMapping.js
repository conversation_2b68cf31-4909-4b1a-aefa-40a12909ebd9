const agentMappings = {
  browsers: {
    "Google Chrome": "Google Chrome",
    "Mozilla Firefox": "Mozilla Firefox",
    "Microsoft Edge": "Microsoft Edge",
    Chrome: "Google Chrome",
    Chromium: "Chromium",
    Firefox: "Mozilla Firefox",
    Safari: "Safari",
  },
  operatingSystems: {
    Windows: "Windows",
    macOS: "mac",
    Linux: "Linux",
    Android: "Android",
  },
};

const mapAgentValues = (osname, browsername) => {
  const browser = agentMappings?.browsers[browsername] || "";
  const os = agentMappings?.operatingSystems[osname] || "";
  return `["${browser}","${os}"]`;
};

module.exports = mapAgentValues;
