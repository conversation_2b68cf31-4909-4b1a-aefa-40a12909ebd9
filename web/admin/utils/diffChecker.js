// function diffChecker(initial, current) {
//   const prevDataValues = {};
//   const updatedDataValues = {};

//   if (initial === null || current === null) {
//     if (!initial?.created_at || !initial?.updated_at || !current?.updated_at || !current?.created_at) {
//       return {
//         prevDataValues: initial || null,
//         updatedDataValues: current || null,
//       };
//     } else {
//       const { created_at, updated_at, ...restCurrent } = current;

//       return {
//         prevDataValues: initial || null,
//         updatedDataValues: restCurrent || null,
//       };
//     }
//   }

//   const convertToString = (value) => (value === null || value === undefined ? null : value.toString());

//   for (const key in current) {
//     if (key === "created_at" || key === "updated_at" || key === "password" || key === "id") {
//       continue;
//     }

//     if (key === "features") {
//       const initialFeatures = initial?.features ? initial.features.map(convertToString) : [];
//       const currentFeatures = current?.features ? current.features.map(convertToString) : [];

//       if (JSON.stringify(initialFeatures) !== JSON.stringify(currentFeatures)) {
//         prevDataValues[key] = initialFeatures;
//         updatedDataValues[key] = currentFeatures;
//       }
//     } else if (key === "scopes") {
//       const initialScopes = initial?.scopes ? initial.scopes.map(convertToString) : [];
//       const currentScopes = current?.scopes ? current.scopes.map(convertToString) : [];

//       const removedScopes = initialScopes.filter((scope) => !currentScopes.includes(scope));
//       const addedScopes = currentScopes.filter((scope) => !initialScopes.includes(scope));

//       if (removedScopes.length > 0 || addedScopes.length > 0) {
//         prevDataValues[key] = initialScopes;
//         updatedDataValues[key] = currentScopes;
//       }
//     } else {
//       const initialValue = convertToString(initial ? initial[key] : null);
//       const currentValue = convertToString(current ? current[key] : null);

//       if (initialValue !== currentValue) {
//         prevDataValues[key] = initialValue;
//         updatedDataValues[key] = currentValue;
//       }
//     }
//   }

//   return { prevDataValues, updatedDataValues };
// }

function diffChecker(initial, current) {
  const prevDataValues = {};
  const updatedDataValues = {};

  if (initial === null || current === null) {
    return {
      prevDataValues: initial || null,
      updatedDataValues: current || null,
    };
  }

  const convertToString = (value) => (value === null || value === undefined ? null : value.toString());

  for (const key in current) {
    if (key === "created_at" || key === "updated_at" || key === "password" || key === "id") {
      continue;
    }

    if (key === "rules") {
      const initialRules = initial?.rules || {};
      const currentRules = current?.rules || {};
      const changedPrevRules = {};
      const changedNewRules = {};

      for (const ruleKey in currentRules) {
        if (currentRules[ruleKey] !== initialRules[ruleKey]) {
          changedPrevRules[ruleKey] = initialRules[ruleKey];
          changedNewRules[ruleKey] = currentRules[ruleKey];
        }
      }

      if (Object.keys(changedNewRules).length > 0) {
        prevDataValues[key] = changedPrevRules;
        updatedDataValues[key] = changedNewRules;
      }
    } else if (key === "scopes") {
      const initialScopes = initial?.scopes ? initial?.scopes?.map(convertToString) : [];
      const currentScopes = current?.scopes ? current?.scopes?.map(convertToString) : [];

      const removedScopes = initialScopes?.filter((scope) => !currentScopes.includes(scope));
      const addedScopes = currentScopes?.filter((scope) => !initialScopes.includes(scope));

      if (removedScopes?.length > 0 || addedScopes?.length > 0) {
        prevDataValues[key] = initialScopes;
        updatedDataValues[key] = currentScopes;
      }
    } else if (key === "addons") {
      const initialAddons = initial?.addons ? initial?.addons : [];
      const currentAddons = current?.addons ? current?.addons : [];

      const diffAddons = currentAddons?.filter((addon, index) => {
        return JSON.stringify(addon) !== JSON.stringify(initialAddons[index]);
      });

      if (initialAddons?.length === 0 && currentAddons?.length > 0) {
        prevDataValues[key] = null;
        updatedDataValues[key] = currentAddons;
      } else if (diffAddons.length > 0) {
        prevDataValues[key] = initialAddons;
        updatedDataValues[key] = diffAddons;
      }
    } else {
      const initialValue = convertToString(initial ? initial[key] : null);
      const currentValue = convertToString(current ? current[key] : null);

      if (initialValue !== currentValue) {
        prevDataValues[key] = initialValue;
        updatedDataValues[key] = currentValue;
      }
    }
  }

  return { prevDataValues, updatedDataValues };
}

module.exports = diffChecker;
