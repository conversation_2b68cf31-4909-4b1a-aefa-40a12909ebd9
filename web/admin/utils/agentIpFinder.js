const mapAgentValues = require("./agentMapping");
const bowser = require("bowser");

function agentIpFinder(headers, socket) {
  const userAgent = bowser.parse(headers["user-agent"]);
  const browserName = userAgent?.browser?.name ?? null;
  const osName = userAgent?.os?.name ?? null;

  const clientIp = headers["x-forwarded-for"] || socket?.remoteAddress;
  const mappedAgent = mapAgentValues(headers?.osname ?? osName, headers?.browsername ?? browserName);

  return { clientIp, mappedAgent };
}

module.exports = agentIpFinder;
