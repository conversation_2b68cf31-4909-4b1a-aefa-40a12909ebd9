function featuresMapper(data) {
  let formattedFeatures = Object.keys(data).reduce(
    (acc, key) => {
      if (typeof data[key] === "boolean" && key !== "featured") {
        if (data[key]) {
          acc.features.push(key);
        }
      } else {
        acc[key] = data[key];
      }
      return acc;
    },
    { features: [] }
  );

  // console.log("formatted", formattedFeatures);

  return formattedFeatures;
}

module.exports = featuresMapper;
