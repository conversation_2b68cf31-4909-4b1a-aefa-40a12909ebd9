const moment = require("moment");
/**
 * Time period generator for the given start and end date range.
 * @param {Date} start
 * @param {Date} end
 * @param {String} unitOfTime - years, months, weeks, days, hours, minutes, seconds
 * @returns
 */
const generateTimePeriod = (start, end, unitOfTime = "days") => {
  const startDate = moment(start).startOf("day");
  const endDate = moment(end).endOf("day");
  return new Array(endDate.add(1, unitOfTime).diff(startDate, unitOfTime))
    .fill(null)
    .map((_, index) => startDate.clone().add(index, unitOfTime));
};

const monthOrder = {
  Jan: 0,
  Feb: 1,
  Mar: 2,
  Apr: 3,
  May: 4,
  Jun: 5,
  Jul: 6,
  Aug: 7,
  Sep: 8,
  Oct: 9,
  Nov: 10,
  Dec: 11,
};

module.exports = {
  generateTimePeriod,
  monthOrder,
};
