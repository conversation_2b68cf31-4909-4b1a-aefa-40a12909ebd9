const subscriptionPlanRules = require("storeseo-enums/subscriptionPlanRules");

function planRulesFormatter(data) {
  const features = data?.features;

  let rules = Object.fromEntries(Object.values(subscriptionPlanRules).map((rule) => [rule, features.includes(rule)]));

  rules = { ...rules, products: data?.products, image_optimzer: null, ai_optimizer: null };

  return rules;
}

module.exports = planRulesFormatter;
