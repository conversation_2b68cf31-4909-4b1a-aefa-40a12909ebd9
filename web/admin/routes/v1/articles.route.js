const express = require("express");
const auth = require("../../middlewares/auth");
const checkPermissions = require("../../middlewares/checkPermissions");
const articlesController = require("../../controllers/articles.controller");

const router = express.Router();
router.use(auth());

router.get("/", checkPermissions, articlesController.getArticlesList);
router.get("/:id", checkPermissions, articlesController.getArticleDetails);
router.get("/image/:id", checkPermissions, articlesController.getArticleImage);
router.get("/meta/:id", checkPermissions, articlesController.getArticleMeta);

module.exports = router;
