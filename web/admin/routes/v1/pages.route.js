const express = require("express");
const auth = require("../../middlewares/auth");
const checkPermissions = require("../../middlewares/checkPermissions");
const pagesController = require("../../controllers/pages.controller");

const router = express.Router();
router.use(auth());

router.get("/", checkPermissions, pagesController.getPagesList);
router.get("/:id", checkPermissions, pagesController.getPageDetails);
router.get("/meta/:id", checkPermissions, pagesController.getPageMeta);

module.exports = router;
