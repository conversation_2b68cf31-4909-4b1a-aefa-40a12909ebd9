// @ts-check
const express = require("express");
const validate = require("../../middlewares/validate");
const { authValidation } = require("../../validations");
const { authController } = require("../../controllers");
// const auth = require("../../middlewares/auth");

const router = express.Router();

router.post("/login", validate(authValidation.login), authController.login);
// router.post("/logout", validate(authValidation.logout), authController.logout);
// router.get("/me", auth(), authController.authenticatedUser);
router.post("/refresh-tokens", validate(authValidation.refreshTokens), authController.refreshTokens);
// router.post("/forgot-password", validate(authValidation.forgotPassword), authController.forgotPassword);
// router.post("/reset-password", validate(authValidation.resetPassword), authController.resetPassword);

module.exports = router;
