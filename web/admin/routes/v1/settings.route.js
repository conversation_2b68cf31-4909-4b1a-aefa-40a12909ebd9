const express = require("express");
const auth = require("../../middlewares/auth");
const settingsController = require("../../controllers/settings.controller");
const couponsController = require("../../controllers/coupons.controller");
const subscriptionPlansController = require("../../controllers/subscriptionPlans.controller");
const subscriptionAddonsController = require("../../controllers/subscriptionAddons.controller");
const checkPermissions = require("../../middlewares/checkPermissions");
const partnersController = require("../../controllers/partners.controller");
const validate = require("../../middlewares/validate");
const { planValidation } = require("../../validations");
const { addonValidation } = require("../../validations");
const { couponValidation } = require("../../validations");
const { adminValidation } = require("../../validations");
const { partnerValidation } = require("../../validations");

const router = express.Router();

router.use(auth());

// admin routes
router.post("/users", checkPermissions, validate(adminValidation.addAdmin), settingsController.addAdmin);
router.put("/users/:id", checkPermissions, validate(adminValidation.updateAdmin), settingsController.updateAdmin);
router.put(
  "/users/password/:id",
  checkPermissions,
  validate(adminValidation.updatePassword),
  settingsController.updatePassword
);
router.get("/users", checkPermissions, settingsController.getAdminList);
router.get("/users/:id", checkPermissions, settingsController.getSingleAdmin);
router.delete("/users/:id", checkPermissions, settingsController.deleteAdmin);

// coupon routes
router.get("/coupons", checkPermissions, couponsController.getCouponList);
router.delete("/coupons/:id", checkPermissions, couponsController.deleteCoupon);
router.post("/coupons", checkPermissions, validate(couponValidation.addCoupon), couponsController.addCoupon);
router.put("/coupons/:id", checkPermissions, validate(couponValidation.updateCoupon), couponsController.updateCoupon);
router.get("/coupon/status", checkPermissions, couponsController.getCouponStatus);
router.get("/coupons/:id", checkPermissions, couponsController.getSingleCoupon);

// subscription plans routes
router.get("/subscription/plans", checkPermissions, subscriptionPlansController.getSubscriptionPlan);
router.post(
  "/subscription/plans",
  checkPermissions,
  validate(planValidation.addPlan),
  subscriptionPlansController.addSubscriptionPlan
);
router.put(
  "/subscription/plans/:id",
  checkPermissions,
  validate(planValidation.updatePlan),
  subscriptionPlansController.updateSubscriptionPlan
);
router.get("/subscription/plans/:id", checkPermissions, subscriptionPlansController.getSingleSubscriptionPlan);
router.delete("/subscription/plans/:id", checkPermissions, subscriptionPlansController.deleteSubscriptionPlan);

// Subscription Addons Routes
router.get("/subscription/addons", checkPermissions, subscriptionAddonsController.getSubscriptionAddonsList);
router.get("/subscription/addons/:id", checkPermissions, subscriptionAddonsController.getSingleSubscriptionAddon);
router.post(
  "/subscription/addons",
  checkPermissions,
  validate(addonValidation.addAddon),
  subscriptionAddonsController.addSubscriptionAddon
);
router.put(
  "/subscription/addons/:id",
  checkPermissions,
  validate(addonValidation.updateAddon),
  subscriptionAddonsController.updateSubscriptionAddon
);
router.delete("/subscription/addons/:id", checkPermissions, subscriptionAddonsController.deleteSubscriptionAddon);

// partners Routes
router.get("/partners", checkPermissions, partnersController.getPartnersList);
router.get("/partners/:id", checkPermissions, partnersController.getSinglePartner);
router.put(
  "/partners/:id",
  checkPermissions,
  validate(partnerValidation.updatePartner),
  partnersController.updatePartner
);
router.post("/partners", checkPermissions, validate(partnerValidation.addPartner), partnersController.addPartner);
router.delete("/partners/:id", checkPermissions, partnersController.deletePartner);

// scope route
router.get("/scopes", settingsController.getAdminScopes);

module.exports = router;
