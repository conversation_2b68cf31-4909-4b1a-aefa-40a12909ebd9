const express = require("express");
const auth = require("../../middlewares/auth");
const storesController = require("../../controllers/stores.controller");
const checkPermissions = require("../../middlewares/checkPermissions");
const validate = require("../../middlewares/validate");
const { storeValidation } = require("../../validations");

const router = express.Router();
router.use(auth());

router.get("/", checkPermissions, storesController.getStoresList);
router.get("/counter", checkPermissions, storesController.getStoresStatistics);
router.get("/names", checkPermissions, storesController.getStoresName);
router.get("/plans/:id", checkPermissions, storesController.getPlanDetails);
router.get("/details/:id", checkPermissions, storesController.getStoreDetails);
router.put("/store-verified/:id", checkPermissions, storesController.storeVerified);
router.put("/remove-branding/:id", checkPermissions, storesController.removeBranding);
router.put("/cancel-subscription/:id", checkPermissions, storesController.cancelSubscription);
router.put("/reset-cache/:id", checkPermissions, storesController.resetCache);
router.put("/reset-product-sync/:id", checkPermissions, storesController.resetProductSync);
router.put("/update-plan/:id", checkPermissions, storesController.updateStorePlanData);
router.get("/shop-data/:id", checkPermissions, storesController.getShopData);
router.get("/webhooks/:id", checkPermissions, storesController.getWebhooksData);
router.delete("/remove-data/:id", checkPermissions, storesController.removeShopData);
router.put("/register-webhooks/:id", checkPermissions, storesController.registerWebhooks);
router.get("/cache-data/:id", checkPermissions, storesController.getAllCacheData);
router.put("/update-cache/:id", checkPermissions, storesController.updateCacheData);
router.get("/addons/:id", checkPermissions, storesController.getAddonsData);
router.delete("/remove-deleted-products/:id", checkPermissions, storesController.removeDeletedProducts);
router.put(
  "/change-shop-status/:id",
  checkPermissions,
  validate(storeValidation.changeShopStatus),
  storesController.changeShopStatus
);

module.exports = router;
