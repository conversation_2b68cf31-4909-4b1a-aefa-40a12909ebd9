const express = require("express");
const auth = require("../../middlewares/auth");
const checkPermissions = require("../../middlewares/checkPermissions");
const collectionsController = require("../../controllers/collections.controller");

const router = express.Router();
router.use(auth());

router.get("/", checkPermissions, collectionsController.getCollectionsList);
router.get("/:id", checkPermissions, collectionsController.getCollectionDetails);
router.get("/image/:id", checkPermissions, collectionsController.getCollectionImage);
router.get("/meta/:id", checkPermissions, collectionsController.getcollectionMeta);

module.exports = router;
