const express = require("express");
const auth = require("../../middlewares/auth");
const productsController = require("../../controllers/products.controller");
const checkPermissions = require("../../middlewares/checkPermissions");

const router = express.Router();
router.use(auth());

router.get("/", checkPermissions, productsController.getProductsList);
router.get("/image/:id", checkPermissions, productsController.getProudctImage);
router.get("/meta/:id", checkPermissions, productsController.getProductMeta);
router.get("/details/:id", checkPermissions, productsController.getProdudctDetails);
router.get("/:shopId/analytics/:id", checkPermissions, productsController.getProductAnalytics);

module.exports = router;
