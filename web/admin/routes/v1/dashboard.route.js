// @ts-check
const express = require("express");
const auth = require("../../middlewares/auth");
const dashboardController = require("../../controllers/dashboard.controller");
const storesController = require("../../controllers/stores.controller");
const checkPermissions = require("../../middlewares/checkPermissions");

const router = express.Router();
router.use(auth());

router.get("/", checkPermissions, storesController.getStoresList);
router.get("/counter", checkPermissions, dashboardController.counter);
router.get("/resources-stats", checkPermissions, dashboardController.resourcesStats);
router.get("/chart", checkPermissions, dashboardController.chartReport);
router.get("/customer", checkPermissions, dashboardController.customerGrowthReport);

module.exports = router;
