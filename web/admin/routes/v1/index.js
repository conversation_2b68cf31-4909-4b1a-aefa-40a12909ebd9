const express = require("express");
const authRoute = require("./auth.route");
// const docsRoute = require('./docs.route');
const dashboardRoute = require("./dashboard.route");
const storesRoute = require("./stores.route");
const productsRoute = require("./products.route");
const collectionsRoute = require("./collections.route");
const articlesRoute = require("./articles.route");
const pagesRoute = require("./pages.route");
const settingsRoute = require("./settings.route");
const activityLogRoute = require("./activityLog.route");
const subscriptionTransactionsRoute = require("./subscriptionTransactions.route");
const reportsRoute = require("./reports.route");

const router = express.Router();

const defaultRoutes = [
  {
    path: "/auth",
    route: authRoute,
  },
  {
    path: "/dashboard",
    route: dashboardRoute,
  },
  {
    path: "/stores",
    route: storesRoute,
  },
  {
    path: "/products",
    route: productsRoute,
  },
  {
    path: "/collections",
    route: collectionsRoute,
  },
  {
    path: "/articles",
    route: articlesRoute,
  },
  {
    path: "/pages",
    route: pagesRoute,
  },
  {
    path: "/settings",
    route: settingsRoute,
  },
  {
    path: "/activityLog",
    route: activityLogRoute,
  },
  {
    path: "/subscription-transactions",
    route: subscriptionTransactionsRoute,
  },
  {
    path: "/reports",
    route: reportsRoute,
  },
];

// TODO: API documentation with swagger
const devRoutes = [
  // routes available only in development mode
  //   {
  //     path: '/docs',
  //     route: docsRoute
  //   }
];

defaultRoutes.forEach((route) => {
  router.use(route.path, route.route);
});

/* istanbul ignore next */
if (process.env === "development") {
  devRoutes.forEach((route) => {
    router.use(route.path, route.route);
  });
}

module.exports = router;
