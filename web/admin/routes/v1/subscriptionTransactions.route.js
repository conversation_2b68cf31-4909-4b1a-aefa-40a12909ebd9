const express = require("express");
const auth = require("../../middlewares/auth");
const subscriptionTransactionsController = require("../../controllers/subscriptionTransactions.controller");

const router = express.Router();

router.get("/payments", auth(), subscriptionTransactionsController.getSubscriptionTransactionList);
router.get("/payments/:id", auth(), subscriptionTransactionsController.getSingleTransactionData);

module.exports = router;
