const express = require("express");
const activityLogController = require("../../controllers/activityLog.controller");
const auth = require("../../middlewares/auth");
const checkPermissions = require("../../middlewares/checkPermissions");
const validate = require("../../middlewares/validate");
const { logValidation } = require("../../validations");

const router = express.Router();
router.use(auth());

router.get("/", checkPermissions, activityLogController.getActivityLogList);
router.delete(
  "/delete-logs",
  checkPermissions,
  validate(logValidation.deleteLog),
  activityLogController.deleteActivityLog
);

module.exports = router;
