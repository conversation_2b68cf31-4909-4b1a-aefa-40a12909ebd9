//@ts-check
const express = require("express");
const auth = require("../../middlewares/auth");
const reportController = require("../../controllers/reports.controller");
const checkPermissions = require("../../middlewares/checkPermissions");

const router = express.Router();

router.use(auth());

router.get("/daily-status", checkPermissions, reportController.dailyStatusReport);

module.exports = router;
