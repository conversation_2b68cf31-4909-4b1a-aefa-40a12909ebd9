// // @ts-check
const shopStatus = require("storeseo-enums/shopStatus");
const planType = require("storeseo-enums/planType");
const { Shop, SubscriptionPlan, Product, Op, sequelize } = require("../../sequelize");
const moment = require("moment");
const { generateTimePeriod, monthOrder } = require("../utils");
const {
  shopCustomerGrowthByDateQuery,
  shopCustomerGrowthUntilByDateQuery,
  parseCustomerGrowthSeriesData,
} = require("../libs/dashboard.lib");
const storeService = require("./store.service");

class DashboardService {
  constructor() {
    this.planCategories = [planType.FREE, planType.PRO];
  }

  getDashboardOverviewCounter = async (start, end) => {
    const whereCondition = start && end ? `WHERE s.created_at between '${start} 00:00:00' AND '${end} 23:59:59'` : "";

    const [result] = await sequelize.query(
      `SELECT 
        COALESCE(SUM(CASE WHEN s.status='${shopStatus.ACTIVE}' THEN 1 ELSE 0 END), 0) AS total_stores,
        COALESCE(SUM(CASE WHEN s.status='${shopStatus.ACTIVE}' and sp.type = '${planType.PRO}' THEN 1 ELSE 0 END), 0) AS total_pro_stores,
        COALESCE(SUM(CASE WHEN s.status='${shopStatus.ACTIVE}' and sp.type = '${planType.FREE}' THEN 1 ELSE 0 END), 0) AS total_free_stores,
        COALESCE(SUM(CASE WHEN s.status='${shopStatus.ACTIVE}' and s.plan_id is NULL THEN 1 ELSE 0 END), 0) AS without_plan,
        COALESCE(SUM(CASE WHEN s.status='${shopStatus.ACTIVE}' and (s.email ILIKE '%wpdeveloper.%' OR s.email ILIKE '%easy.jobs' OR s.email ILIKE '%test.com' OR s.email ILIKE '%ar.com.bd') THEN 1 ELSE 0 END), 0) AS test_stores,
        COALESCE(SUM(CASE WHEN s.status!='${shopStatus.ACTIVE}' THEN 1 ELSE 0 END), 0) AS inactive_stores
      FROM shops as s 
      LEFT JOIN subscription_plans as sp on sp.id = s.plan_id 
      ${whereCondition};
    `,
      { type: sequelize.QueryTypes.SELECT }
    );

    return [
      { title: "Active Stores", value: result.total_stores, key: "active_stores" },
      { title: "Pro Stores", value: result.total_pro_stores, key: "pro_stores" },
      { title: "Free Stores", value: result.total_free_stores, key: "free_stores" },
      { title: "Without Plan", value: result.without_plan, key: "without_plan" },
      { title: "Test Stores", value: result.test_stores, key: "test_stores" },
      { title: "Inactive Stores", value: result.inactive_stores, key: "inactive_stores" },
    ];
  };

  getDashboardResourcesStats = async (start, end) => {
    const whereCondition = start && end ? `WHERE s.created_at between '${start} 00:00:00' AND '${end} 23:59:59'` : "";

    const [{ total_products }] = await sequelize.query(
      `SELECT COUNT(p.id) as total_products FROM products as p JOIN shops as s ON s.id=p.shop_id AND s.status='${shopStatus.ACTIVE}' ${whereCondition};`,
      { type: sequelize.QueryTypes.SELECT }
    );

    const [{ total_pages }] = await sequelize.query(
      `SELECT COUNT(p.id) as total_pages FROM pages as p JOIN shops as s ON s.id=p.shop_id AND s.status='${shopStatus.ACTIVE}' ${whereCondition};`,
      { type: sequelize.QueryTypes.SELECT }
    );
    const [{ total_articles }] = await sequelize.query(
      `SELECT COUNT(a.id) as total_articles FROM articles as a JOIN shops as s ON s.id=a.shop_id AND s.status='${shopStatus.ACTIVE}' ${whereCondition};`,
      { type: sequelize.QueryTypes.SELECT }
    );
    const [{ total_collections }] = await sequelize.query(
      `SELECT COUNT(c.id) as total_collections FROM collections as c JOIN shops as s ON s.id=c.shop_id AND s.status='${shopStatus.ACTIVE}' ${whereCondition};`,
      { type: sequelize.QueryTypes.SELECT }
    );

    return [
      { title: "Total Products", value: total_products, key: "total_products" },
      { title: "Total Pages", value: total_pages, key: "total_pages" },
      { title: "Total Blog Posts", value: total_articles, key: "total_articles" },
      { title: "Total Collections", value: total_collections, key: "total_collections" },
    ];
  };

  getRecentStores = async () => {
    const recentStores = storeService.getRecentStores();
    return recentStores;
  };

  getAllChartReport = async (start, end) => {
    if (!start || !end) {
      // If start and end date is not provided, then default to the first and current date of the month
      start = moment().startOf("month");
      end = moment();
    }
    const timePeriod = generateTimePeriod(start, end);

    if (start && end && start === end) {
      // daily active installation report grouped by 24 hours
      return await this.getDailyChartReport(start);
    } else if (timePeriod.length > 31) {
      // yearly active installation report grouped by 12 months
      return await this.getYearlyChartReport(start, end);
    } else {
      // monthly active installation report grouped by days
      return await this.getMonthlyChartReport(timePeriod);
    }
  };

  getMonthlyChartReport = async (timePeriod) => {
    const dates = timePeriod.map((date) => date.format("YYYY-MM-DD"));
    let series = [];
    const monthlyCountsRows = await sequelize.query(
      `
      SELECT DATE(shops.created_at) AS date, subscription_plans.type AS category, COUNT(*) AS count
      FROM shops
      LEFT JOIN subscription_plans ON shops.plan_id = subscription_plans.id
      WHERE subscription_plans.type IN (:categories)
      AND DATE(shops.created_at) IN (:dates)
      GROUP BY DATE(shops.created_at), subscription_plans.type;
      `,
      {
        replacements: { categories: this.planCategories, dates },
        type: sequelize.QueryTypes.SELECT,
      }
    );

    series = this.planCategories.map((category) => {
      const categoryData = monthlyCountsRows.filter((item) => item.category === category);
      // Push missing dates with 0 count
      dates.forEach((date) => {
        const found = categoryData.find((item) => item.date === date);
        if (!found) {
          categoryData.push({ date, category, count: "0" });
        }
      });
      // Sort by date
      categoryData.sort((a, b) => (a.date > b.date ? 1 : -1));

      const data = categoryData.map((item) => ({
        x: moment(item.date).format("DD MMM"),
        y: item.count,
      }));

      return {
        name: category,
        data,
      };
    });

    return series;
  };
  getDailyChartReport = async (start) => {
    const timePeriod = generateTimePeriod(start, start, "hours");
    const startTime = moment(moment(start)).startOf("day").format("YYYY-MM-DD HH:mm:ss");
    const endTime = moment(moment(start)).endOf("day").format("YYYY-MM-DD HH:mm:ss");

    let series = [];
    const counts = await sequelize.query(
      `
      SELECT
        TO_CHAR(DATE_TRUNC('hour', shops.created_at) + INTERVAL '12 hour', 'HH:MI am') AS hour,
        subscription_plans.type AS category,
        COUNT(*) AS count
      FROM
          shops
      LEFT JOIN
          subscription_plans ON shops.plan_id = subscription_plans.id
      WHERE
          subscription_plans.type IN (:categories)
          AND shops.created_at >= :startTime
          AND shops.created_at <= :endTime
      GROUP BY
          DATE_TRUNC('hour', shops.created_at) + INTERVAL '12 hour',
          subscription_plans.type;
        `,
      {
        replacements: { categories: this.planCategories, startTime, endTime },
        type: sequelize.QueryTypes.SELECT,
      }
    );

    series = this.planCategories.map((category) => {
      const categoryData = counts.filter((item) => item.category === category);
      // Push missing time with 0 count
      timePeriod.forEach((date) => {
        const found = categoryData.find((item) => item.hour === date.format("hh:mm a"));
        if (!found) {
          categoryData.push({ hour: date.format("hh:mm a"), category, count: "0" });
        }
      });
      // Sort by date
      function compareTime(a, b) {
        // Parse the time strings into Date objects for comparison
        var timeA = new Date("2000/01/01 " + a);
        var timeB = new Date("2000/01/01 " + b);

        // Compare the time objects
        return timeA - timeB;
      }
      categoryData.sort((a, b) => compareTime(a.hour, b.hour));

      const data = categoryData.map((item) => ({
        x: item.hour,
        y: item.count,
      }));

      return {
        name: category,
        data,
      };
    });

    return series;
  };
  getYearlyChartReport = async (start, end) => {
    let series = [];
    const timePeriod = generateTimePeriod(start, end, "months");
    const startDate = moment(start).format("YYYY-MM-DD");
    const endDate = moment(end).format("YYYY-MM-DD");

    const yearlyCountsRow = await sequelize.query(
      `
        SELECT 
          DATE_TRUNC('month', shops.created_at) AS month,
          subscription_plans.type AS category,
          COUNT(*) AS count
        FROM shops
        LEFT JOIN subscription_plans ON shops.plan_id = subscription_plans.id
        WHERE 
          subscription_plans.type IN (:categories) 
          AND shops.created_at BETWEEN DATE(:startDate) 
          AND DATE(:endDate)
        GROUP BY DATE_TRUNC('month', shops.created_at), subscription_plans.type;
        `,
      {
        replacements: { categories: this.planCategories, startDate, endDate },
        type: sequelize.QueryTypes.SELECT,
      }
    );

    series = this.planCategories.map((category) => {
      const categoryData = yearlyCountsRow.filter((item) => item.category === category);
      // Push missing dates with 0 count
      timePeriod.forEach((date) => {
        const found = categoryData.find((item) => moment(item.month).format("MMM") === date.format("MMM"));
        if (!found) {
          categoryData.push({ month: date.toDate(), category, count: "0" });
        }
      });
      // Sort by date
      categoryData.sort(
        (a, b) => monthOrder[moment(a.month).format("MMM")] - monthOrder[moment(b.month).format("MMM")]
      );

      const data = categoryData.map((item) => ({
        x: moment(item.month).format("MMM"),
        y: item.count,
      }));

      return {
        name: category,
        data,
      };
    });
    return series;
  };
  getCustomerGrowthChart = async (start, end) => {
    if (!start || !end) {
      // If start and end date is not provided, then default to the first and current date of the month
      start = moment().startOf("month");
      end = moment();
    }
    const timePeriod = generateTimePeriod(start, end);

    if (start && end && start === end) {
      // daily active installation growth report grouped by 24 hours
      return await this.getDailyCustomerGrowthChartReport(start);
    } else if (timePeriod.length > 31) {
      // yearly active installation growth report grouped by 12 months
      return await this.getYearlyCustomerGrowthReport(start, end);
    } else {
      // monthly active installation growth report grouped by days
      return await this.getMonthlyCustomerGrowthChartReport(start, end, timePeriod);
    }
  };
  getMonthlyCustomerGrowthChartReport = async (start, end, timePeriod) => {
    // Get the total free shop count until now
    const freeShopUntilNow = await shopCustomerGrowthUntilByDateQuery(moment(start).format("YYYY-MM-DD"), "free");
    // Get the total free shop count for this month
    const freeShopForThisMonth = await shopCustomerGrowthByDateQuery(
      moment(start).format("YYYY-MM-DD"),
      moment(end).format("YYYY-MM-DD"),
      "free",
      "day"
    );
    // Get the total pro shop count until now
    const proShopUntilNow = await shopCustomerGrowthUntilByDateQuery(moment(start).format("YYYY-MM-DD"), "pro");
    // Get the total pro shop count for this month
    const proShopForThisMonth = await shopCustomerGrowthByDateQuery(
      moment(start).format("YYYY-MM-DD"),
      moment(end).format("YYYY-MM-DD"),
      "pro",
      "day"
    );
    // Final series data
    const series = parseCustomerGrowthSeriesData({
      freeShopUntilNow,
      proShopUntilNow,
      freeShopCount: freeShopForThisMonth,
      proShopCount: proShopForThisMonth,
      timePeriod,
      period: "day",
      xAxesFormat: "DD MMM",
    });

    return series;
  };
  getDailyCustomerGrowthChartReport = async (start) => {
    const timePeriod = generateTimePeriod(start, start, "hours");
    // Get the total free shop count until now
    const freeShopUntilNow = await shopCustomerGrowthUntilByDateQuery(moment(start).format("YYYY-MM-DD"), "free");
    // Get the total free shop count for today
    const freeShopForToday = await shopCustomerGrowthByDateQuery(
      moment(start).format("YYYY-MM-DD"),
      moment(start).format("YYYY-MM-DD"),
      "free"
    );
    // Get the total pro shop count until now
    const proShopUntilNow = await shopCustomerGrowthUntilByDateQuery(moment(start).format("YYYY-MM-DD"), "pro");
    // Get the total pro shop count for today
    const proShopForToday = await shopCustomerGrowthByDateQuery(
      moment(start).format("YYYY-MM-DD"),
      moment(start).format("YYYY-MM-DD"),
      "pro"
    );

    // Final series data
    const series = parseCustomerGrowthSeriesData({
      freeShopUntilNow,
      proShopUntilNow,
      freeShopCount: freeShopForToday,
      proShopCount: proShopForToday,
      timePeriod,
      period: "hour",
      xAxesFormat: "h:mm a",
    });

    return series;
  };
  getYearlyCustomerGrowthReport = async (start, end) => {
    const timePeriod = generateTimePeriod(start, end, "months");
    // Get the total free shop count until now
    const freeShopUntilNow = await shopCustomerGrowthUntilByDateQuery(moment(start).format("YYYY-MM-DD"), "free");
    // Get the total free shop count for this year
    const freeShopForThisYear = await shopCustomerGrowthByDateQuery(
      moment(start).format("YYYY-MM-DD"),
      moment(end).format("YYYY-MM-DD"),
      "free",
      "month"
    );
    // Get the total pro shop count until now
    const proShopUntilNow = await shopCustomerGrowthUntilByDateQuery(moment(start).format("YYYY-MM-DD"), "pro");
    // Get the total pro shop count for this year
    const proShopForThisMonth = await shopCustomerGrowthByDateQuery(
      moment(start).format("YYYY-MM-DD"),
      moment(end).format("YYYY-MM-DD"),
      "pro",
      "month"
    );
    // Final series data
    const series = parseCustomerGrowthSeriesData({
      freeShopUntilNow,
      proShopUntilNow,
      freeShopCount: freeShopForThisYear,
      proShopCount: proShopForThisMonth,
      timePeriod,
      period: "month",
      xAxesFormat: "MMM",
    });

    return series;
  };
}

module.exports = new DashboardService();
