const { ArticleImage, sequelize } = require("../../sequelize");

class ArticlesService {
  async getArticlesList(search = "", page = 1, limit = 20, score, shop, sortBy = "id", sortOrder = "ASC") {
    let queryParams = [];
    let conditions = [];

    const trimmedSearch = search.trim();

    let baseQuery = `
            SELECT
                a.id,
                a.shop_id,
                a.title,
                a.handle,
                a.author,
                a.body_html,
                a.tags,
                a.issues,
                a.score,
                a.passed,
                a.focus_keyword,
                a.is_analysed,
                a.is_optimized,
                ai.src,
                ai.alt_text,
                a.created_at,
                a.updated_at
            FROM 
                articles as a
            LEFT JOIN article_images as ai ON ai.article_id = a.id
        `;

    let countQuery = `
            SELECT 
                COUNT(*)
            FROM 
                articles as a
        `;

    if (search) {
      conditions.push(`a.title ILIKE ?`);
      queryParams.push(`%${trimmedSearch}%`);
    }

    if (shop) {
      conditions.push("a.shop_id = ?");
      queryParams.push(shop);
    }

    if (score) {
      if (score === "optimized") {
        conditions.push("a.is_optimized = ?");
        queryParams.push(true);
      } else if (score === "seo_issues") {
        conditions.push("a.is_optimized = ?");
        queryParams.push(false);
      }
    }

    if (conditions.length > 0) {
      baseQuery += ` WHERE ${conditions.join(" AND ")}`;
      countQuery += ` WHERE ${conditions.join(" AND ")}`;
    }

    baseQuery += ` ORDER BY a.${sortBy} ${sortOrder}`;

    baseQuery += `
            LIMIT ${limit}
            OFFSET ${page * limit - limit}
    
         `;

    const [articles] = await sequelize.query(baseQuery, { replacements: queryParams });
    const [articlesCount] = await sequelize.query(countQuery, { replacements: queryParams });
    return { articles, articlesCount: articlesCount[0]?.count || 0 };
  }

  async getArticleDetails(id) {
    const article = await sequelize.query(
      `
          SELECT
            a.id,
            a.shop_id,
            a.blog_id,
            a.article_id,
            a.title,
            shops.name,
            shops.domain,
            a.handle,
            a.author,
            a.body_html,
            a.tags,
            a.issues,
            a.score,
            a.passed,
            a.focus_keyword,
            a.tags,
            a.is_analysed,
            a.is_optimized,
            a.created_at,
            a.updated_at
          FROM
            articles a
          LEFT JOIN
            shops ON a.shop_id = shops.id
          WHERE
            a.id = ${id}       
        `
    );
    return article[0];
  }

  async getArticleImage(id) {
    const articleImage = await ArticleImage.findOne({
      attributes: ["src", "alt_text", "optimization_status", "optimization_meta"],
      where: { article_id: id },
    });
    return articleImage?.toJSON();
  }

  async getArticleMeta(id) {
    const articleMeta = await sequelize.query(`
        SELECT 
          MAX(CASE WHEN am.key = 'title_tag' THEN am.value END) AS title_tag_value,
          MAX(CASE WHEN am.key = 'description_tag' THEN am.value END) AS description_tag_value,
          am.type,
          am.namespace,
          s.name AS shop_name,
          a.title AS article_title,
          a.body_html AS article_description
        FROM 
          article_metas am
        LEFT JOIN 
          articles a ON am.article_id = a.id
        LEFT JOIN 
          shops s ON a.shop_id = s.id
        WHERE 
          am.article_id = ${id}
        GROUP BY
          am.type,
          am.namespace,
          s.name,
          a.title,
          a.body_html
      `);
    return articleMeta[0];
  }
}

module.exports = new ArticlesService();
