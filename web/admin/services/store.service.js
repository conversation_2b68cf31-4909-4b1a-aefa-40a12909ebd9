// @ts-nocheck
const {
  Shop,
  Product,
  Page,
  SubscriptionPlan,
  SubscriptionAddon,
  AddonUsage,
  Article,
  Sitemap,
  SubscriptionTransaction,
  sequelize,
  Collection,
  Location,
  ShopSetting,
  Webhook,
  Op,
} = require("../../sequelize");
const {
  shopCustomerGrowthByDateQuery,
  shopCustomerGrowthUntilByDateQuery,
  parseCustomerGrowthSeriesData,
} = require("../libs/dashboard.lib");
const shopStatus = require("storeseo-enums/shopStatus");
const planType = require("storeseo-enums/planType");
const { QueryTypes } = require("sequelize");
const WebhookService = require("../../api/services/WebhookService");
const AddonUsageService = require("../../api/services/AddonUsageService");
const subscriptionPlansService = require("./subscriptionPlans.service");
const subscriptionAddonsService = require("./subscriptionAddons.service");
const moment = require("moment");
const settingKeys = require("storeseo-enums/settingKeys");
const { preparePagination } = require("../../api/utils/helper");
const { AI_OPTIMIZER, IMAGE_OPTIMIZER } = require("storeseo-enums/subscriptionAddonGroup");
const { PRO, COMBINED } = require("storeseo-enums/subscriptionAddonType");
const { isEmpty } = require("lodash");

class StoresService {
  constructor() {
    this.planCategories = [planType.FREE, planType.PRO];
  }

  async getStoresList({
    page = 1,
    limit = 20,
    search = "",
    plan = "",
    status = "",
    startDate = "",
    endDate = "",
    sortBy = "id",
    sortOrder = "DESC",
  } = {}) {
    let queryParams = [];
    let conditions = [];

    const trimmedSearch = search.trim();
    let baseQuery = `
        SELECT
          s.id,
          s.name AS store,
          s.created_at,
          s.updated_at,
          s.url,
          s.domain,
          s.email,
          s.plan_status,
          s.plan_id,
          s.status,
          s.is_verified,
          sp.type AS TYPE,
          sp.name AS plan_name,
          sp.interval,
          COUNT(p.shop_id) AS total,
          ROUND(AVG(p.score), 0) AS overall_score,
          SUM(CASE WHEN p.is_analysed THEN 1 ELSE 0 END) AS analysed,
          SUM(p.issues) AS seo_issues,
          SUM(CASE WHEN p.is_optimized THEN 1 ELSE 0 END) AS optimized,
          CASE WHEN (ss. "value"::json ->> 'setupStep')::int = 3 THEN 'ACTIVE' ELSE 'INACTIVE' END as html_sitemap,
	        (ss. "value"::json ->> 'branding')::BOOLEAN AS branding
      FROM
        shops s
      LEFT JOIN 
        shop_settings ss ON s.id = ss.shop_id AND "key" = '${settingKeys.HTML_SITEMAP}'
      LEFT JOIN 
        products p ON s.id = p.shop_id
      LEFT JOIN 
        subscription_plans sp ON s.plan_id = sp.id
    `;

    let countQuery = `
            SELECT COUNT(s.name) 
            FROM shops s
            LEFT JOIN subscription_plans sp ON s.plan_id = sp.id
        `;
    if (search) {
      conditions.push("(s.name ILIKE ? OR s.email ILIKE ? OR s.domain ILIKE ? OR s.url ILIKE ?)");
      queryParams.push(`%${trimmedSearch}%`, `%${trimmedSearch}%`, `%${trimmedSearch}%`, `%${trimmedSearch}%`);
    }

    if (plan && plan !== "ALL") {
      conditions.push("sp.type = ?");
      queryParams.push(plan);
    }

    if (status) {
      conditions.push("s.status = ?");
      queryParams.push(status);
    }

    if (startDate && endDate) {
      conditions.push("s.created_at BETWEEN ? AND ?");
      queryParams.push(startDate, endDate);
    }

    if (conditions.length > 0) {
      baseQuery += `WHERE ${conditions.join(" AND ")}`;
      countQuery += `WHERE ${conditions.join(" AND ")}`;
    }

    baseQuery += `
          GROUP BY
            s.id,
            sp.name,
            sp.type,
            sp.interval,
            ss."value"
          ORDER BY ${sortBy} ${sortOrder}
          LIMIT ${limit}
          OFFSET ${page * limit - limit}
      `;

    const [fetchedResults] = await sequelize.query(baseQuery, { replacements: queryParams });
    const [totalStores] = await sequelize.query(countQuery, { replacements: queryParams, type: QueryTypes.SELECT });

    return {
      fetchedResults,
      totalStores: totalStores?.count || 0,
    };
  }

  async getStoresStatistics() {
    const [[result]] = await sequelize.query(`SELECT 
        COALESCE(SUM(CASE WHEN s.status='${shopStatus.ACTIVE}' THEN 1 ELSE 0 END), 0) AS total_stores,
        COALESCE(SUM(CASE WHEN s.status='${shopStatus.ACTIVE}' and sp.type = '${planType.PRO}' THEN 1 ELSE 0 END), 0) AS total_pro_stores,
        COALESCE(SUM(CASE WHEN s.status='${shopStatus.ACTIVE}' and sp.type = '${planType.FREE}' THEN 1 ELSE 0 END), 0) AS total_free_stores,
        COALESCE(SUM(CASE WHEN s.status='${shopStatus.ACTIVE}' and s.plan_id is NULL THEN 1 ELSE 0 END), 0) AS without_plan,
        COALESCE(SUM(CASE WHEN s.status='${shopStatus.ACTIVE}' and (s.email ILIKE '%wpdeveloper.%' OR s.email ILIKE '%easy.jobs' OR s.email ILIKE '%test.com' OR s.email ILIKE '%ar.com.bd') THEN 1 ELSE 0 END), 0) AS test_stores,
        COALESCE(SUM(CASE WHEN s.status!='${shopStatus.ACTIVE}' THEN 1 ELSE 0 END), 0) AS inactive_stores
      FROM shops as s 
      LEFT JOIN subscription_plans as sp on sp.id = s.plan_id 
    `);

    return [
      { title: "Active Stores", value: result.total_stores, key: "active_stores" },
      { title: "Pro Stores", value: result.total_pro_stores, key: "pro_stores" },
      { title: "Free Stores", value: result.total_free_stores, key: "free_stores" },
      { title: "Without Plan", value: result.without_plan, key: "without_plan" },
      { title: "Test Stores", value: result.test_stores, key: "test_stores" },
      { title: "Inactive Stores", value: result.inactive_stores, key: "inactive_stores" },
    ];
  }

  // async getStoresStatistics() {
  //   // total stores count
  //   let { count: total_stores } = await Shop.findAndCountAll({
  //     where: {
  //       status: shopStatus.ACTIVE,
  //     },
  //   });

  //   // total pro stores count
  //   let { count: total_pro_stores } = await Shop.findAndCountAll({
  //     where: {
  //       status: shopStatus.ACTIVE,
  //     },
  //     include: [
  //       {
  //         model: SubscriptionPlan,
  //         as: "plan",
  //         where: {
  //           type: planType.PRO,
  //         },
  //       },
  //     ],
  //   });

  //   // total free stores count
  //   let { count: total_free_stores } = await Shop.findAndCountAll({
  //     where: {
  //       status: shopStatus.ACTIVE,
  //     },
  //     include: [
  //       {
  //         model: SubscriptionPlan,
  //         as: "plan",
  //         where: {
  //           type: planType.FREE,
  //         },
  //       },
  //     ],
  //   });

  //   // total test stores count
  //   const testStores = await sequelize.query(
  //     `SELECT
  //         COUNT(shops.email)
  //     FROM
  //         shops
  //     WHERE
  //         shops.status = 'ACTIVE'
  //     AND
  //         email ILIKE '%easy.jobs'
  //     OR
  //         email ILIKE '%wpdeveloper.com'
  //     OR
  //         email ILIKE '%wpdeveloper.net'
  //     OR
  //         email ILIKE '%test.com'
  //     OR
  //         email ILIKE '%ar.com.bd'
  //     `
  //   );

  //   // total withoutPlan count
  //   const withoutPlan = await sequelize.query(
  //     `SELECT
  //         COUNT(shops.id)
  //     FROM
  //         shops
  //     WHERE
  //         plan_id IS NULL
  //     AND
  //         status = 'ACTIVE';`
  //   );

  //   const data = [
  //     {
  //       title: "Stores",
  //       value: total_stores,
  //     },
  //     {
  //       title: "Pro Stores",
  //       value: total_pro_stores,
  //     },
  //     {
  //       title: "Free Stores",
  //       value: total_free_stores,
  //     },
  //     {
  //       title: "Without Plan",
  //       value: parseFloat(withoutPlan[0][0].count),
  //     },
  //     {
  //       title: "Test Stores",
  //       value: parseFloat(testStores[0][0].count),
  //     },
  //   ];
  //   return data;
  // }

  async storeVerified(id) {
    const storeData = (
      await Shop.findOne({
        where: {
          id,
          // is_verified: false,
          plan_id: 1,
        },
      })
    )?.toJSON();

    const storeSettingData = await this.getShopSettingsData(id);

    if (!storeData) {
      throw new Error("Store is not elegible to verify.");
    }

    // store verification
    let rules = storeData?.plan_rules;
    rules.products = 100;

    const [, storeVerification] = await Shop.update(
      {
        is_verified: true,
        plan_rules: rules,
      },
      {
        where: {
          id: id,
        },
        returning: true,
      }
    );

    const response = { verification: storeVerification[0].toJSON(), removeBranding: null };

    // remove branding
    if (storeSettingData) {
      let settingValue = JSON.parse(storeSettingData?.value);
      settingValue.branding = false;

      const [, removeBranding] = await ShopSetting.update(
        {
          value: JSON.stringify(settingValue),
        },
        {
          where: {
            key: "html_sitemap",
            shop_id: id,
          },
          returning: true,
        }
      );

      response.removeBranding = removeBranding[0]?.toJSON();
    }

    return response;
  }

  async removeBranding(id) {
    const storeSettingData = await this.getShopSettingsData(id);

    if (!storeSettingData) {
      throw new Error("Html Sitemap Not Found");
    }

    let settingValue = JSON.parse(storeSettingData?.value);
    settingValue.branding = false;

    const removeBranding = await ShopSetting.update(
      {
        value: JSON.stringify(settingValue),
      },
      {
        where: {
          key: "html_sitemap",
          shop_id: id,
        },
        returning: true,
      }
    );

    return removeBranding[1][0].toJSON();
  }

  async getStoresName(query) {
    // const names = await sequelize.query(
    //   `
    //   SELECT
    //     id as value,
    //     name as label
    //   FROM
    //     shops
    //   ORDER BY
    //     id
    //   `
    // );
    // return names;

    const { page = 1, limit = 2, search = "" } = query;
    const trimmedSearch = search.trim();
    const { count, rows } = await Shop.findAndCountAll({
      where: {
        name: {
          [Op.iLike]: `%${trimmedSearch}%`,
        },
      },
      attributes: [
        ["id", "value"],
        ["name", "label"],
      ],
      order: [["created_at", "DESC"]],
      limit,
      offset: page * limit - limit,
    });

    return {
      names: rows.map((row) => row.toJSON()),
      pagination: preparePagination(count, page, limit),
    };
  }

  async getPlanDetails(id) {
    const details = await Shop.findOne({
      attributes: [
        "name",
        "plan_info",
        "plan_id",
        "plan_rules",
        "plan_status",
        "plan_validity",
        "subscribed_at",
        "appSubscriptionId",
        "appSubscriptionData",
      ],
      where: { id },
    });

    const planDetails = details.toJSON();

    const features =
      planDetails?.plan_rules &&
      Object.entries(planDetails?.plan_rules)
        .filter(([key, value]) => value === true)
        .map(([key, _]) => ({ value: key }));

    return { planDetails, features };
  }

  async getStoresDetails(id) {
    const [details] = await sequelize.query(
      `
      SELECT
	      s.id,
	      s.shop_id,
	      s.name,
	      i.title as industry_name,
	      s.email,
	      s.domain,
	      s.url,
        s.plan_rules,
        s.is_verified,
	      s.currency_code,
	      s.onboard_step,
	      s.is_sitemap_submitted,
	      s.sitemap_submitted_at,
	      s.theme_id,
	      s.shopify_plan_name,
	      s.shopify_plus_subscription,
	      s.google_indexing_enabled,
	      s.jsonld_enabled,
	      s.jsonld_data,
	      s.google_integration_info,
	      s.out_of_stock_redirect_enabled,
	      s.out_of_stock_redirect_url,
	      s.created_at,
	      s.updated_at,
	      s.status,
	      s.mailchimp
      FROM
	      shops s
      LEFT JOIN industries i ON s.industry_id = i.id
	      WHERE s.id = ${id}
      `,
      { type: QueryTypes.SELECT }
    );
    return details;
  }

  async getShopsdata(id) {
    const [
      shopName,
      productsCount,
      pagesCount,
      articlesCount,
      collectionsCount,
      sitemapsCount,
      subscriptionTransactionCount,
      locationCount,
    ] = await Promise.all([
      Shop.findOne({ attributes: ["name", "domain"], where: { id } }),
      Product.count({ where: { shop_id: id } }),
      Page.count({ where: { shop_id: id } }),
      Article.count({ where: { shop_id: id } }),
      Collection.count({ where: { shop_id: id } }),
      Sitemap.count({ where: { shop_id: id } }),
      SubscriptionTransaction.count({ where: { shop_id: id } }),
      Location.count({ where: { shop_id: id } }),
    ]);

    return {
      shopName,
      productsCount,
      pagesCount,
      articlesCount,
      collectionsCount,
      sitemapsCount,
      subscriptionTransactionCount,
      locationCount,
    };
  }

  async getShopSettingsData(id) {
    try {
      const settingsValue = await ShopSetting.findOne({
        attributes: ["value"],
        where: {
          shop_id: id,
          key: "html_sitemap",
        },
      });
      return settingsValue?.toJSON();
    } catch (err) {
      // throw new Error("Html Sitemap Not Found");
      console.error("err =", err);
      return null;
    }
  }

  async deleteShopData(id, tableNames) {
    let deleteData;
    let allTables = [...tableNames];

    if (tableNames?.includes("shops")) {
      deleteData = sequelize.query(`DELETE FROM shops WHERE id = ${id}`);
      allTables = allTables?.filter((table) => table !== "shops");
    }
    for (const table of allTables) {
      deleteData = await sequelize.query(`DELETE FROM ${table} WHERE shop_id = ${id}`);
    }
    return deleteData;
  }

  async getShopPlanData(id) {
    const shopPlanData = await Shop.findOne({
      attributes: ["plan_rules", "plan_info"],
      where: { id },
    });

    return shopPlanData.toJSON();
  }

  async getShopById(id, fields = null) {
    try {
      const shop = await Shop.findOne({
        attributes: fields || undefined,
        where: { id },
      });
      return shop?.toJSON();
    } catch (e) {
      return null;
    }
  }

  #updatePlanData = async (id, data) => {
    try {
      const plan_id = data.plan_id;

      let plan_rules = {
        ...data.features,
        products: data.products,
      };

      if (data?.imageAddonLimit) {
        plan_rules = { ...plan_rules, image_optimizer: data.imageAddonLimit };
      }

      if (data?.recurringAiAddonLimit && data.onetimeAiAddonLimit) {
        plan_rules = { ...plan_rules, ai_optimizer: data.recurringAiAddonLimit + data.onetimeAiAddonLimit };
      } else {
        plan_rules = { ...plan_rules, ai_optimizer: data.recurringAiAddonLimit };
      }

      const { 1: updatedPlanInfo } = await Shop.update(
        {
          plan_id,
          plan_rules,
        },
        {
          where: { id },
          returning: true,
        }
      );

      return updatedPlanInfo[0]?.toJSON();
    } catch (error) {
      throw new Error("Error updating plan data", error);
    }
  };

  #updateActiveAddon = async (activeAddon, addonId, addonLimit) => {
    const currentDate = moment().utc().format("YYYY-MM-DD HH:mm:ss+00:00");

    if (Number(activeAddon?.addon_id) === Number(addonId)) {
      if (activeAddon.current_limit !== addonLimit) {
        await AddonUsageService.updateRecordById(activeAddon.id, {
          current_limit: addonLimit,
          last_reset_date: currentDate,
        });
      }
    } else {
      const addon = await subscriptionAddonsService.getSingleSubscriptionAddon(addonId);
      await AddonUsageService.updateRecordById(activeAddon.id, {
        addon_id: addonId,
        limit: addon.limit,
        current_limit: addonLimit,
        last_reset_date: currentDate,
      });
    }
  };

  #updateAddonsRecord = async (shopId, data) => {
    try {
      const activeImageAddon = await AddonUsageService.getActiveRecord(shopId, IMAGE_OPTIMIZER, COMBINED);
      const activeRecurringAddon = await AddonUsageService.getActiveRecord(shopId, AI_OPTIMIZER, COMBINED);
      const activeOneTimeAddon = await AddonUsageService.getActiveRecord(shopId, AI_OPTIMIZER, PRO);

      // updating image optimizer addon
      if (activeImageAddon) {
        await this.#updateActiveAddon(activeImageAddon, data.imageAddonId, data.imageAddonLimit);
      }

      // updating recurring AI optimizer addon
      if (activeRecurringAddon) {
        await this.#updateActiveAddon(activeRecurringAddon, data.recurringAiAddonId, data.recurringAiAddonLimit);
      }

      // updating one time AI optimizer addon
      if (activeOneTimeAddon) {
        await this.#updateActiveAddon(activeOneTimeAddon, data?.onetimeAiAddonId, data?.onetimeAiAddonLimit);
      } else if (!activeOneTimeAddon && data?.onetimeAiAddonId) {
        // register addon
        await this.#registerAddonsRecord(shopId, data?.onetimeAiAddonId);
      }
    } catch (error) {
      throw new Error("Error updating addons record", error);
    }
  };

  #registerAddonsRecord = async (shopId, addonId) => {
    try {
      const currentDate = moment().utc().format("YYYY-MM-DD HH:mm:ss+00:00");

      if (addonId) {
        const addon = await subscriptionAddonsService.getSingleSubscriptionAddon(addonId);

        if (!addon) return null;

        await AddonUsageService.registerUsageRecord({
          shopId,
          purchaseDate: currentDate,
          lastResetDate: currentDate,
          addon: {
            id: addon.id,
            group: AI_OPTIMIZER,
            type: PRO,
            limit: addon.limit,
          },
        });
      }
    } catch (error) {
      throw new Error("Error registering addon", error);
    }
  };

  async updateStorePlanData(id, data) {
    try {
      const updatedPlan = await this.#updatePlanData(id, data);

      await this.#updateAddonsRecord(id, data);

      // await this.#registerAddonsRecord(id, data);

      return updatedPlan;
    } catch (error) {
      throw new Error("Error updating store plan data", error);
    }
  }

  async getWebhooksData(id) {
    const webhooks = await Webhook.findAll({
      // attributes: ["id", "wh_subs_id", "topic", "delivery_method"],
      where: { shop_id: id },
      order: [["topic", "ASC"]],
    });

    const formattedWebhooks = webhooks?.map((webhook) => webhook.toJSON());
    return formattedWebhooks;
  }

  async registerWebhooks(id) {
    const shopData = await Shop.findOne({
      attributes: ["plan_id", "domain", "access_token"],
      where: { id },
    });

    const formattedData = shopData.toJSON();

    const { domain, access_token, plan_id } = formattedData;

    const session = {
      shop: domain,
      accessToken: access_token,
    };

    const updatedWebhooks = await WebhookService.registerAllWebhooks(session, id, plan_id);

    return updatedWebhooks;
  }

  async changeShopStatus(id, status) {
    const { 1: updatedShop } = await Shop.update({ status }, { where: { id }, returning: true });
    const updatedShopData = updatedShop[0]?.toJSON();
    return { status: updatedShopData?.status };
  }
}

module.exports = new StoresService();
