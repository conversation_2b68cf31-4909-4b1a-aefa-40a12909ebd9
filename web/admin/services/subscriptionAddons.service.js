const { SubscriptionAddon, Op } = require("../../sequelize");

class SubscriptionAddonsService {
  async getSubscriptionAddonsList(search = "", sortBy = "order", sortOrder = "ASC") {
    const addons = await SubscriptionAddon.findAll({
      where: {
        [Op.or]: [{ name: { [Op.iLike]: `%${search}%` } }],
      },
      order: [[sortBy, sortOrder]],
    });
    const allAddons = addons?.map((addon) => addon?.toJSON());
    return allAddons;
  }

  async addSubscriptionAddon(data) {
    try {
      const newAddon = await SubscriptionAddon.create(data);
      return newAddon.toJSON();
    } catch (error) {
      throw new Error(error?.message);
    }
  }

  async getSingleSubscriptionAddon(id) {
    const addon = await SubscriptionAddon.findOne({
      attributes: [
        "id",
        "name",
        "group",
        "type",
        "discount",
        "interval",
        "subtotal",
        "limit",
        "price",
        "order",
        "featured",
        "status",
      ],
      where: { id },
    });
    return addon.toJSON();
  }

  async updateSubscriptionAddon(id, data) {
    try {
      const { 1: updateAddon } = await SubscriptionAddon.update(
        {
          name: data?.name,
          group: data?.group,
          type: data?.type,
          interval: data?.interval,
          price: data?.price,
          discount: data?.discount,
          subtotal: data?.subtotal,
          order: data?.order,
          limit: data?.limit,
          featured: data?.featured,
          status: data?.status,
        },
        { where: { id }, returning: true }
      );

      return updateAddon[0]?.toJSON();
    } catch (error) {
      throw new Error(error?.message);
    }
  }

  async deleteSubscriptionAddon(id) {
    const deletedAddon = await SubscriptionAddon.destroy({ where: { id } });
    return deletedAddon;
  }

  async getPrevAddonData(id) {
    const logData = await SubscriptionAddon.findOne({
      attributes: { exclude: ["created_at", "updated_at"] },
      where: { id },
    });

    return logData.toJSON();
  }
}

module.exports = new SubscriptionAddonsService();
