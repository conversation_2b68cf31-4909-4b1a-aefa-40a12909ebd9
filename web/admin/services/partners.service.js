const sharp = require("sharp");
const { Partner, Op, sequelize } = require("../../sequelize");
const path = require("path");
const fs = require("fs");
const GoogleBucketService = require("../../api/services/GoogleBucketService");
const { getFileNameFromGoogleBucketPublicURL } = require("../../api/utils/helper");

class PartnersService {
  async getPartnersList(search = "", page = 1, limit = 20, sortBy = "order", sortOrder = "ASC") {
    const trimmedSearch = search.trim();
    const offset = page * limit - limit;
    const { count, rows } = await Partner.findAndCountAll({
      where: {
        [Op.or]: [{ name: { [Op.iLike]: `%${trimmedSearch}%` } }],
      },
      offset: offset,
      limit: limit,
      order: [[sortBy, sortOrder]],
    });

    const partnersData = rows?.map((item) => item.toJSON());
    return { count, partnersData };
  }

  async getSinglePartner(id) {
    const partner = await Partner.findByPk(id);
    return partner.toJSON();
  }

  #resizeImageAndUpload = async (logoFile) => {
    try {
      const { data: logoBuffer, name } = logoFile;

      const folderPath = path.join(process.cwd(), "/uploads/partnership-logos");
      const tempFilePath = path.join(folderPath, name);

      if (!fs.existsSync(folderPath)) {
        fs.mkdirSync(folderPath, { recursive: true });
      }

      await sharp(logoBuffer).resize(128, 128).toFile(tempFilePath);
      return tempFilePath;
    } catch (error) {
      throw new Error(error.message);
    }
  };

  async addPartner(data, logo) {
    try {
      const tempFilePath = await this.#resizeImageAndUpload(logo);
      const uploadedImage = await GoogleBucketService.uploadMedia({
        folder: "partners",
        newFileName: `${data?.slug}-logo.${logo.name.split(".").reverse()[0]}`,
        originalFilePath: tempFilePath,
        makePublic: true,
      });

      const partnerData = {
        ...data,
        logo: uploadedImage.publicUrl,
      };

      if (data.order !== undefined) {
        await sequelize.query(`UPDATE partners SET "order" = "order" + 1 WHERE "order" >= :newOrder`, {
          replacements: { newOrder: data.order },
        });
      } else {
        const maxOrder = await Partner.max("order");
        partnerData.order = maxOrder + 1;
      }

      const newPartner = await Partner.create(partnerData);
      return newPartner.toJSON();
    } catch (error) {
      if (error.message === "Validation error") {
        throw new Error("Duplicate Key Found");
      } else {
        throw new Error(error.message);
      }
    }
  }

  async updatePartner(id, data, logo) {
    try {
      if (logo) {
        const prevPartner = await this.getSinglePartner(id);
        const pathName = getFileNameFromGoogleBucketPublicURL(prevPartner.logo);
        await GoogleBucketService.removeMedia(pathName);

        const tempFilePath = await this.#resizeImageAndUpload(logo);
        const uploadedImage = await GoogleBucketService.uploadMedia({
          folder: "partners",
          newFileName: `${data?.slug}-logo.${logo.name.split(".").reverse()[0]}`,
          originalFilePath: tempFilePath,
          makePublic: true,
        });

        data.logo = uploadedImage.publicUrl;
      }

      if (data.order !== undefined) {
        const currentPartner = await this.getSinglePartner(id);
        const currentOrder = currentPartner.order;
        const newOrder = data.order;

        if (currentPartner && currentOrder !== newOrder) {
          if (currentOrder < newOrder) {
            await sequelize.query(
              `UPDATE partners SET "order" = "order" - 1 WHERE "order" BETWEEN :currentOrder + 1 AND :newOrder`,
              {
                replacements: { currentOrder, newOrder },
              }
            );
          } else {
            await sequelize.query(
              `UPDATE partners SET "order" = "order" + 1 WHERE "order" BETWEEN :newOrder AND :currentOrder - 1`,
              {
                replacements: { currentOrder, newOrder },
              }
            );
          }
        }
      }

      const [_, updatedPartners] = await Partner.update(data, {
        where: { id },
        returning: true,
      });

      return updatedPartners[0].toJSON();
    } catch (error) {
      if (error.message === "Validation error") {
        throw new Error("Duplicate key found");
      } else {
        throw new Error(error.message);
      }
    }
  }

  async deletePartner(id) {
    const prevPartner = await this.getSinglePartner(id);
    const pathName = getFileNameFromGoogleBucketPublicURL(prevPartner.logo);
    await GoogleBucketService.removeMedia(pathName);

    const deletePartner = await Partner.destroy({ where: { id } });
    return deletePartner;
  }
}

module.exports = new PartnersService();
