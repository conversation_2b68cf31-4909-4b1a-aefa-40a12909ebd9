const { QueryTypes } = require("sequelize");
const { sequelize, Product } = require("../../sequelize");

class ProductsService {
  async getProductsData(page = 1, limit = 50, search = "", score, shop, sortBy, sortOrder) {
    let queryParams = [];
    let conditions = [];

    const trimmedSearch = search.trim();
    let baseQuery = `
          SELECT
            p.id,
            p.shop_id,
            p.product_id AS gql_id,
            regexp_replace(p.product_id, '.*\/', '') as product_id,
            p.title,
            p.score,
            p.passed,
            p.issues,
            p.focus_keyword,
            p.product_type,
            pi.src as featured_image,
            pi.alt_text as featured_image_alt_text
          FROM
            products as p
          LEFT JOIN product_images AS pi ON p.featured_media_id = pi.media_id
        `;

    let countQuery = `
          SELECT 
              COUNT(p.id)
          FROM 
              products as p
        `;

    if (search) {
      conditions.push(`p.title ILIKE ?`);
      queryParams.push(`%${trimmedSearch}%`);
    }

    if (score) {
      if (score === "optimized") {
        conditions.push("p.is_optimized = ?");
        queryParams.push(true);
      } else if (score === "seo_issues") {
        conditions.push("p.is_optimized = ?");
        queryParams.push(false);
      }
    }

    if (shop) {
      conditions.push("p.shop_id = ?");
      queryParams.push(shop);
    }

    if (conditions.length > 0) {
      baseQuery += ` WHERE ${conditions.join(" AND ")}`;
      countQuery += ` WHERE ${conditions.join(" AND ")}`;
    }

    if (sortBy === "title") {
      baseQuery += ` ORDER BY p.title ${sortOrder}`;
    } else if (sortBy === "score") {
      baseQuery += ` ORDER BY p.score ${sortOrder}`;
    }

    baseQuery += `
          LIMIT ${limit}
          OFFSET ${page * limit - limit}
        `;

    const productsData = await sequelize.query(baseQuery, { replacements: queryParams, type: QueryTypes.SELECT });
    const [productsCount] = await sequelize.query(countQuery, { replacements: queryParams, type: QueryTypes.SELECT });

    return {
      productsData,
      productsCount: productsCount?.count || 0,
    };
  }

  async getProductImage(id) {
    const productImage = await Product.findByPk(id, {
      include: [
        {
          association: "images",
          attributes: ["src", "position", "optimization_status", "alt_text", "media_id"],
        },
      ],
    });
    return productImage?.toJSON()?.images;
  }

  async getProductMeta(id) {
    const meta = await sequelize.query(`
      SELECT 
        MAX(CASE WHEN pm.key = 'title_tag' THEN pm.value END) AS title_tag_value,
        MAX(CASE WHEN pm.key = 'description_tag' THEN pm.value END) AS description_tag_value,
        pm.type,
        pm.namespace,
        s.name AS shop_name,
        p.title AS product_title,
        p.description AS product_description
      FROM 
        product_metas pm
      JOIN 
        products p ON pm.product_id = p.id
      LEFT JOIN 
        shops s ON p.shop_id = s.id
      WHERE 
        pm.product_id = ${id}
      GROUP BY
        pm.type,
        pm.namespace,
        s.name,
        p.title,
        p.description
    `);
    return meta[0];
  }

  async getProductDetails(id) {
    const [details] = await sequelize.query(
      `
        SELECT 
          pd.id,
          pd.shop_id, 
          pd.product_id, 
          pd.title,
          shops.name,
          shops.domain,
          pd.handle,
          pd.cursor, 
          pd.issues, 
          pd.score, 
          pd.passed,
          pd.focus_keyword, 
          pd.tags, 
          pd.is_analysed,
          pd.is_optimized, 
          pd.product_type, 
          pd.vendor, 
          pd.online_store_url,
          pd.online_store_preview_url,
          pd.status,
          pd.created_at,
          pd.updated_at,
          pi.src as featured_image,
          pi.alt_text as featured_image_alt_text
        FROM 
          products pd
        LEFT JOIN
          shops ON pd.shop_id = shops.id
        LEFT JOIN product_images AS pi ON pd.featured_media_id = pi.media_id
        WHERE pd.id=${id}
      `,
      { type: QueryTypes.SELECT }
    );

    return details;
  }
}

module.exports = new ProductsService();
