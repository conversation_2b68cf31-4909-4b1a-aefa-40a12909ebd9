const { Page, Op, sequelize } = require("../../sequelize");

class PagesService {
  async getPagesList(search = "", page = 1, limit = 20, score, shop, sortBy = "id", sortOrder = "ASC") {
    let queryParams = [];
    let conditions = [];

    const trimmedSearch = search.trim();

    let baseQuery = `
            SELECT *
            FROM 
                pages p
            `;

    let countQuery = `
            SELECT 
                COUNT(*)
            FROM 
                pages p
        `;

    if (search) {
      conditions.push(`p.title ILIKE ?`);
      queryParams.push(`%${trimmedSearch}%`);
    }

    if (shop) {
      conditions.push("p.shop_id = ?");
      queryParams.push(shop);
    }

    if (score) {
      if (score === "optimized") {
        conditions.push("p.is_optimized = ?");
        queryParams.push(true);
      } else if (score === "seo_issues") {
        conditions.push("p.is_optimized = ?");
        queryParams.push(false);
      }
    }

    if (conditions.length > 0) {
      baseQuery += ` WHERE ${conditions.join(" AND ")}`;
      countQuery += ` WHERE ${conditions.join(" AND ")}`;
    }

    baseQuery += ` ORDER BY p.${sortBy} ${sortOrder}`;

    baseQuery += `
            LIMIT ${limit}
            OFFSET ${page * limit - limit}
    
         `;

    const [pages] = await sequelize.query(baseQuery, { replacements: queryParams });
    const [pagesCount] = await sequelize.query(countQuery, { replacements: queryParams });
    return { pages, pagesCount: pagesCount[0]?.count || 0 };
  }

  async getPageDetails(id) {
    const page = await sequelize.query(
      `
              SELECT
                p.id,
                p.shop_id,
                p.page_id,
                p.title,
                shops.name,
                shops.domain,
                p.handle,
                p.author,
                p.body_html,
                p.metafields,
                p.tags,
                p.issues,
                p.score,
                p.passed,
                p.focus_keyword,
                p.tags,
                p.page_type,
                p.is_analysed,
                p.is_optimized,
                p.created_at,
                p.updated_at
              FROM
                pages p
              LEFT JOIN
                shops ON p.shop_id = shops.id
              WHERE
                p.id = ${id}       
            `
    );
    return page[0];
  }

  async getPageMeta(id) {
    const pageMeta = await sequelize.query(`
            SELECT 
              MAX(CASE WHEN pm.key = 'title_tag' THEN pm.value END) AS title_tag_value,
              MAX(CASE WHEN pm.key = 'description_tag' THEN pm.value END) AS description_tag_value,
              pm.type,
              pm.namespace,
              s.name AS shop_name,
              p.title AS page_title,
              p.body_html AS page_description
            FROM 
              page_metas pm
            LEFT JOIN 
              pages p ON pm.page_id = p.id
            LEFT JOIN 
              shops s ON p.shop_id = s.id
            WHERE 
              pm.page_id = ${id}
            GROUP BY
              pm.type,
              pm.namespace,
              s.name,
              p.title,
              p.body_html
          `);
    return pageMeta[0];
  }
}

module.exports = new PagesService();
