const { Coupon, sequelize } = require("../../sequelize");
const { Op } = require("sequelize");

class CouponsService {
  async getCouponList(search = "", sortBy = "created_at", sortOrder = "DESC", page, limit) {
    const coupons = await sequelize.query(
      `
      SELECT 
          c.id AS id,
          c.name AS name,
          c.code AS code,
          c.discount_type AS discount_type, 
          c.amount AS amount, 
          c.start_date AS start_date,
          c.end_date AS end_date,
          c.max_limit AS max_limit,
          c.redeem_count AS redeem_count, 
          c.discount_duration AS discount_duration,
          c.status AS status,
          c.created_at AS created_at,
          c.updated_at AS updated_at,
          COALESCE(jsonb_agg(jsonb_build_object ('id', sp.id, 'name', sp.name)) FILTER (WHERE sp.id IS NOT NULL), '[]'::jsonb) AS plans
      FROM 
          coupons c
      LEFT JOIN 
          LATERAL jsonb_array_elements_text(c.plans::jsonb) AS plan_id ON TRUE
      LEFT JOIN 
          subscription_plans sp ON sp.id = plan_id::int
      WHERE   
          c.name ILIKE '%${search}%' OR c.code ILIKE '%${search}%'
      GROUP BY 
          c.id, c.name, c.code
      ORDER BY 
        ${sortBy} ${sortOrder} NULLS LAST
      LIMIT ${limit}
      OFFSET ${page * limit - limit}
      `
    );
    const couponsCount = await Coupon.count({
      where: {
        [Op.or]: [{ name: { [Op.iLike]: `%${search}%` } }, { code: { [Op.iLike]: `%${search}%` } }],
      },
    });
    return { coupons: coupons[0], couponsCount };
  }

  async deleteCoupon(id) {
    const deletedCoupon = await Coupon.destroy({ where: { id } });
    return deletedCoupon;
  }

  async addCoupon(data) {
    try {
      if (data?.discount_type === 2 && data?.amount > 100) {
        throw new Error("Discount can't be more than 100");
      }
      const addCoupon = await Coupon.create(data);
      return addCoupon.toJSON();
    } catch (error) {
      if (error.message === "Validation error") {
        throw new Error("Coupon code must be unique");
      }
      throw new Error(error?.message);
    }
  }

  async updateCoupon(id, data) {
    try {
      const { 1: updateCoupon } = await Coupon.update(data, { where: { id }, returning: true });
      return updateCoupon[0]?.toJSON();
    } catch (error) {
      if (error.message === "Validation error") {
        throw new Error("Coupon code must be unique");
      }
      throw new Error(error?.message);
    }
  }

  async getCouponStatus() {
    const status = await Coupon.findAll({
      attributes: ["id", "name", "code", "discount_type", "amount"],
      where: { status: 1 },
    });
    return status;
  }

  async getSingleCoupon(id) {
    const status = await Coupon.findOne({ where: { id } });
    return status.toJSON();
  }
}

module.exports = new CouponsService();
