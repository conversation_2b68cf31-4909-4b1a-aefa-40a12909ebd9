const { ActivityLog, Op } = require("../../sequelize");
const agentIpFinder = require("../utils/agentIpFinder");
const diffChecker = require("../utils/diffChecker");
class ActivityLogService {
  async getActivityLogList(search = "", page = 1, limit = 20, originType, sortBy = "created_at", sortOrder = "DESC") {
    const trimmedSearch = search.trim();
    const offset = page * limit - limit;

    const condition = {
      [Op.or]: [{ subject: { [Op.iLike]: `%${trimmedSearch}%` } }],
    };

    if (originType) {
      if (originType === "ADMIN") {
        condition.log_origin = originType;
      }
      if (originType === "APP") {
        condition.log_origin = originType;
      }
    }
    const { count, rows } = await ActivityLog.findAndCountAll({
      where: condition,
      offset: offset,
      limit: limit,
      order: [[sortBy, sortOrder]],
    });
    const logData = rows?.map((item) => item?.toJSON());
    return { count, logData };
  }

  async addActivityLog({ req, prevData, updatedData, subject, logOrigin, domain }) {
    const { clientIp, mappedAgent } = agentIpFinder(req?.headers, req?.socket);
    const { prevDataValues, updatedDataValues } = diffChecker(prevData, updatedData);

    const user = req.user;
    const logData = {
      subject: `${subject} By ${user?.name || user?.shop}`,
      ip: clientIp,
      agent: mappedAgent,
      user_id: req?.user?.id,
      old_value: prevDataValues,
      updated_value: updatedDataValues,
      log_origin: logOrigin,
      domain: domain,
    };

    const newLog = await ActivityLog.create(logData);
    return newLog.toJSON();
  }

  async deleteActivityLog(startDate, endDate) {
    if (!startDate) {
      const firstLog = await ActivityLog.findOne({
        order: [["created_at", "ASC"]],
        attributes: ["created_at"],
      });

      const data = firstLog?.toJSON();

      if (data) {
        startDate = data.created_at;
      }
    }

    await ActivityLog.destroy({
      where: {
        created_at: {
          [Op.between]: [startDate, endDate],
        },
      },
    });
  }
}
module.exports = new ActivityLogService();
