const { Collection, CollectionImage, Op, sequelize } = require("../../sequelize");

class CollectionsService {
  async getCollectionsList(search = "", page = 1, limit = 20, score, shop, sortBy = "id", sortOrder = "ASC") {
    let queryParams = [];
    let conditions = [];

    const trimmedSearch = search.trim();

    let baseQuery = `
        SELECT
            c.id,
            c.shop_id,
            c.title,
            c.description,
            c.products_count,
            c.issues,
            c.score,
            c.passed,
            c.focus_keyword,
            c.is_analysed,
            c.is_optimized,
            ci.src,
            ci.alt_text,
            c.created_at,
            c.updated_at
        FROM 
            collections as c
        LEFT JOIN collection_images as ci ON ci.collection_id = c.id
    `;

    let countQuery = `
        SELECT 
            COUNT(*)
        FROM 
            collections as c
    `;

    if (search) {
      conditions.push(`c.title ILIKE ?`);
      queryParams.push(`%${trimmedSearch}%`);
    }

    if (shop) {
      conditions.push("c.shop_id = ?");
      queryParams.push(shop);
    }

    if (score) {
      if (score === "optimized") {
        conditions.push("c.is_optimized = ?");
        queryParams.push(true);
      } else if (score === "seo_issues") {
        conditions.push("c.is_optimized = ?");
        queryParams.push(false);
      }
    }

    if (conditions.length > 0) {
      baseQuery += ` WHERE ${conditions.join(" AND ")}`;
      countQuery += ` WHERE ${conditions.join(" AND ")}`;
    }

    baseQuery += ` ORDER BY c.${sortBy} ${sortOrder}`;

    baseQuery += `
        LIMIT ${limit}
        OFFSET ${page * limit - limit}

     `;

    const [collections] = await sequelize.query(baseQuery, { replacements: queryParams });
    const [collectionsCount] = await sequelize.query(countQuery, { replacements: queryParams });
    return { collections, collectionsCount: collectionsCount[0]?.count || 0 };
  }

  async getCollectionDetails(id) {
    const collection = await sequelize.query(
      `
        SELECT
          c.id,
          c.shop_id,
          c.title,
          shops.name,
          shops.domain,
          c.description,
          c.products_count,
          c.handle,
          c.image_id,
          c.issues,
          c.score,
          c.passed,
          c.focus_keyword,
          c.tags,
          c.is_analysed,
          c.is_optimized,
          c.created_at,
          c.updated_at
        FROM
          collections c
        LEFT JOIN
          shops ON c.shop_id = shops.id
        WHERE
          c.id = ${id}       
      `
    );
    return collection[0];
  }

  async getCollectionImage(id) {
    const collectionImage = await CollectionImage.findOne({
      attributes: ["gql_id", "src", "alt_text", "optimization_status"],
      where: { collection_id: id },
    });
    return collectionImage?.toJSON();
  }

  async getCollectionMeta(id) {
    const collectionMeta = await sequelize.query(`
        SELECT 
          MAX(CASE WHEN cm.key = 'title_tag' THEN cm.value END) AS title_tag_value,
          MAX(CASE WHEN cm.key = 'description_tag' THEN cm.value END) AS description_tag_value,
          cm.type,
          cm.namespace,
          s.name AS shop_name,
          c.title AS collection_title,
          c.description AS collection_description
        FROM 
          collection_metas cm
        JOIN 
          collections c ON cm.collection_id = c.id
        LEFT JOIN 
          shops s ON c.shop_id = s.id
        WHERE 
          cm.collection_id = ${id}
        GROUP BY
          cm.type,
          cm.namespace,
          s.name,
          c.title,
          c.description
      `);
    return collectionMeta[0];
  }
}

module.exports = new CollectionsService();
