const { Admins, Op } = require("../../sequelize");
const AdminTypes = require("storeseo-enums/admin/AdminTypes");
const bcrypt = require("bcrypt");

class SettingsService {
  async getAdminList(search = "") {
    const admins = await Admins.findAll({
      attributes: ["id", "name", "type", "scopes", "email", "created_at", "updated_at"],
      where: {
        [Op.or]: [{ name: { [Op.iLike]: `%${search}%` } }, { email: { [Op.iLike]: `%${search}%` } }],
      },
      order: [["id", "DESC"]],
    });

    const adminData = admins?.map((item) => item?.toJSON());

    const allAdmins = adminData?.map((admin) => {
      const typeKey = Object.keys(AdminTypes).find((key) => AdminTypes[key] === admin?.type);
      return {
        ...admin,
        type: typeKey || null,
      };
    });
    return { admin: allAdmins };
  }

  async deleteAdmin(id) {
    const deleteAdmin = await Admins.destroy({ where: { id } });
    return deleteAdmin;
  }

  async addAdmin({ name, email, type, password, scopes }) {
    try {
      const hashedPassword = await bcrypt.hash(password, 10);
      const adminCreated = await Admins.create({ name, email, password: hashedPassword, type, scopes });
      return adminCreated.toJSON();
    } catch (error) {
      if (error?.message === "Validation error") {
        throw new Error("Admin already exists");
      } else {
        throw new Error(error?.message);
      }
    }
  }

  async updateAdmin(id, name, email, type, scopes) {
    try {
      const { 1: admins } = await Admins.update({ name, email, type, scopes }, { where: { id }, returning: true });
      return admins[0].toJSON();
    } catch (error) {
      if (error?.message === "Validation error") {
        throw new Error("Email must be unique");
      } else {
        throw new Error(error?.message);
      }
    }
  }

  async updatePassword(id, password) {
    try {
      const hashedPassword = await bcrypt.hash(password, 10);
      const passwordUpdated = await Admins.update({ password: hashedPassword }, { where: { id } });
      return passwordUpdated;
    } catch (error) {
      throw new Error(error?.message);
    }
  }

  async getSingleAdmin(id) {
    const singleAdmin = await Admins.findOne({ attributes: { exclude: ["password"] }, where: { id } });
    return singleAdmin.toJSON();
  }
}

module.exports = new SettingsService();
