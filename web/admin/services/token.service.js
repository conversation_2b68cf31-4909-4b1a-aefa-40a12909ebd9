// @ts-check
const jwt = require("jsonwebtoken");
const moment = require("moment");
const config = require("../config/config");
const userTokenType = require("../config/userTokenType");

class TokenService {
  /**
   * Generate token
   * @returns {string}
   */
  generateToken = (userId, expires, type, secret = config.jwt.secret) => {
    const payload = {
      sub: userId,
      iat: moment().unix(),
      exp: expires.unix(),
      type,
    };
    return jwt.sign(payload, secret);
  };

  /**
   * Verify token and return token doc (or throw an error if it is not valid)
   */
  verifyToken = async (token, type) => {
    const payload = jwt.verify(token, config.jwt.secret);
    const userId = Number(payload.sub);
    return userId;
  };

  /**
   * Generate auth tokens
   */
  generateAuthTokens = async (user) => {
    const accessTokenExpires = moment().add(config.jwt.accessTokenExpirationDuration, "seconds");
    const accessToken = this.generateToken(user.id, accessTokenExpires, userTokenType.ACCESS);
    const refreshTokenExpires = moment().add(config.jwt.refreshTokenExpirationDuration, "seconds");
    const refreshToken = this.generateToken(user.id, refreshTokenExpires, userTokenType.REFRESH);

    return {
      access: {
        token: accessToken,
        expires: accessTokenExpires.toDate(),
      },
      refresh: {
        token: refreshToken,
        expires: refreshTokenExpires.toDate(),
      },
    };
  };
}

module.exports = new TokenService();
