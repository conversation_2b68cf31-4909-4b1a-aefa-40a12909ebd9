// // @ts-check
const { Admins } = require("../../sequelize");
class UserService {
  constructor() {
    this.keys = ["id", "email", "name", "password", "type", "scopes", "created_at", "updated_at"];
  }
  /**
   * Get user by email
   */
  getUserByEmail = async (email, keys = this.keys) => {
    const user = await Admins.findOne({
      where: { email },
      attributes: keys,
    });
    // console.log("user", user);
    return user.toJSON();
  };

  /**
   * Get user by id
   */
  getUserById = async (id, keys = this.keys) => {
    const user = await Admins.findOne({
      where: { id },
      attributes: keys,
    });
    return user.toJSON();
  };
}

module.exports = new UserService();
