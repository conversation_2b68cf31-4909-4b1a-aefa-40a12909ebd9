const { QueryTypes } = require("sequelize");
const { SubscriptionPlan, sequelize } = require("../../sequelize");
const planRulesFormatter = require("../utils/planRulesFormatter");

class subscriptionPlansService {
  async addSubscriptionPlan(data) {
    try {
      const rules = planRulesFormatter(data);
      const planData = { ...data, rules };
      const newPlan = await SubscriptionPlan.create(planData);
      return newPlan.toJSON();
    } catch (error) {
      if (error?.message === "Validation error") {
        throw new Error("Duplicate slug value");
      }
      throw new Error(error?.message);
    }
  }

  async getSubscriptionPlan(search = "", sortBy = "plan_order", sortOrder = "ASC") {
    let plans = await sequelize.query(
      `SELECT
        sp.id, 
        sp.name,
        sp.slug,
        sp.type,
        sp.interval,
        sp.price,
        sp.discount, 
        sp.subtotal,
        sp.meta, 
        sp.status,
        sp.featured,
        sp.coupon_code, 
        sp.order AS plan_order, 
        sp.created_at,
        sp.updated_at,
        sp.rules,
        c.discount_type as coupon_type,
        c.amount as coupon_amount
       FROM 
        subscription_plans sp
       LEFT JOIN
        coupons c ON sp.coupon_code = c.code
       WHERE
        sp.name ILIKE '%${search}%' OR sp.slug ILIKE '%${search}%'
       ORDER BY
        ${sortBy} ${sortOrder}
      `,
      { type: QueryTypes.SELECT }
    );

    return plans.map((p) => {
      const calculatedPrice = this.calculateCouponDiscount(p);
      return { ...p, ...calculatedPrice };
    });
  }

  calculateCouponDiscount = (plan) => {
    // Fixed coupon
    if (plan?.coupon_type == 1) {
      return {
        price: Number(plan.price).toFixed(2),
        discount: Number(plan.coupon_amount).toFixed(2),
        subtotal: (Number(plan.subtotal) - Number(plan.coupon_amount)).toFixed(2),
      };
    }

    // Percent coupon
    if (plan?.coupon_type == 2) {
      const discount = ((Number(plan.price) * Number(plan.coupon_amount)) / 100).toFixed(2);
      return {
        price: Number(plan.price).toFixed(2),
        discount,
        subtotal: (Number(plan.subtotal) - discount).toFixed(2),
      };
    }

    return {};
  };

  async deleteSubscriptionPlan(id) {
    const deletedPlan = await SubscriptionPlan.destroy({ where: { id } });
    return deletedPlan;
  }

  async updateSubscriptonPlan(id, data) {
    try {
      const rules = planRulesFormatter(data);
      const updatedPlanData = { ...data, rules };
      const { 1: updatedPlan } = await SubscriptionPlan.update(updatedPlanData, { where: { id }, returning: true });
      return updatedPlan[0].toJSON();
    } catch (error) {
      if (error?.message === "Validation error") {
        throw new Error("Duplicate slug value");
      }
      throw new Error(error?.message);
    }
  }

  async getSingleSubscriptionPlan(id) {
    const singlePlan = await SubscriptionPlan.findByPk(id);
    const planData = singlePlan?.toJSON();

    const features = Object.keys(planData?.rules)
      .filter((key) => planData?.rules[key] === true)
      .map((key) => ({ value: key }));

    return { ...planData, features };
  }

  async getPrevPlanData(id) {
    const logData = await SubscriptionPlan.findOne({
      attributes: { exclude: ["created_at", "updated_at"] },
      where: { id },
    });
    return logData.toJSON();
  }
}

module.exports = new subscriptionPlansService();
