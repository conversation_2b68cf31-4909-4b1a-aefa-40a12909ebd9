const { SubscriptionTransaction, Op } = require("../../sequelize");

class subscriptionTransactions {
  async getSubscriptionTransactionsList(
    search = "",
    page,
    limit,
    sortBy = "id",
    sortOrder = "DESC",
    shop,
    plan,
    status,
    transaction,
    startDate,
    endDate
  ) {
    const offset = page * limit - limit;

    let andConditions = [];

    if (search) {
      andConditions.push({
        [Op.or]: [{ name: { [Op.iLike]: `%${search}%` } }, { "$shop.name$": { [Op.iLike]: `%${search}%` } }],
      });
    }

    if (shop) {
      andConditions.push({ shop_id: shop });
    }

    if (plan) {
      andConditions.push({ plan_id: plan });
    }

    if (status) {
      if (status === "free") {
        andConditions.push({ is_paid: false });
      } else if (status === "paid") {
        andConditions.push({ is_paid: true });
      }
    }

    if (transaction && transaction !== "ALL") {
      andConditions.push({ transaction_type: transaction });
    }

    if (startDate && endDate) {
      andConditions.push({
        created_at: {
          [Op.between]: [startDate, endDate],
        },
      });
    }

    const whereCondition = {
      [Op.and]: andConditions,
    };

    const { count, rows } = await SubscriptionTransaction.findAndCountAll({
      attributes: [
        "id",
        "shop_id",
        "plan_id",
        "name",
        "type",
        "interval",
        "price",
        "discount",
        "subtotal",
        "coupon_code",
        "transaction_narration",
        "transaction_type",
        "is_paid",
        "created_at",
      ],
      include: [
        {
          association: "shop",
          attributes: ["name"],
        },
      ],
      where: whereCondition,
      offset: offset,
      limit: limit,
      order: [[sortBy, sortOrder]],
    });
    const transactionData = rows?.map((item) => item?.toJSON());
    return { count, transactionData };
  }

  async getSingleTransactionData(id) {
    const transactionData = await SubscriptionTransaction.findOne({
      include: [
        {
          association: "shop",
          attributes: ["name"],
        },
      ],
      where: { id },
    });

    return transactionData.toJSON();
  }
}

module.exports = new subscriptionTransactions();
