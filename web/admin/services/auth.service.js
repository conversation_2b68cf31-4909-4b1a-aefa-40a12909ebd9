// @ts-check
const httpStatus = require("http-status");
const userService = require("./user.service");
const ApiError = require("../utils/apiError");
const { isPasswordMatch } = require("../utils/encryption");
const exclude = require("../utils/exclude");
const tokenService = require("./token.service");
const userTokenType = require("../config/userTokenType");

class AuthService {
  /**
   * Login with username and password
   */
  loginUserWithEmailAndPassword = async (email, password) => {
    const user = await userService.getUserByEmail(email, [
      "id",
      "email",
      "name",
      "password",
      "type",
      "scopes",
      "created_at",
      "updated_at",
    ]);

    // console.log("user: ", user);

    if (!user || !(await isPasswordMatch(password, user.password))) {
      throw new ApiError(httpStatus.UNAUTHORIZED, "Incorrect email or password");
    }
    return exclude(user, ["password"]);
  };
  /**
   * Refresh auth tokens
   */
  refreshAuth = async (refreshToken) => {
    try {
      const userId = await tokenService.verifyToken(refreshToken, userTokenType.REFRESH);
      return tokenService.generateAuthTokens({ id: userId });
    } catch (error) {
      throw new ApiError(httpStatus.UNAUTHORIZED, "Please authenticate");
    }
  };
}

module.exports = new AuthService();
