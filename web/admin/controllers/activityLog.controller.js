const httpStatus = require("http-status");
const activityLogService = require("../services/activityLog.service");
const catchAsync = require("../utils/catchAsync");

class ActivityLogController {
  getActivityLogList = catchAsync(async (req, res) => {
    const { search, page, limit, originType, sortBy, sortOrder } = req.query;
    const { count, logData } = await activityLogService.getActivityLogList(
      search,
      page,
      limit,
      originType,
      sortBy,
      sortOrder
    );
    res.status(httpStatus.OK).send({ count, logData });
  });

  deleteActivityLog = catchAsync(async (req, res) => {
    try {
      const { startDate, endDate } = req.body;
      await activityLogService.deleteActivityLog(startDate, endDate);
      res.status(httpStatus.OK).send({ message: "Logs are deleted" });
    } catch (err) {
      res.status(httpStatus.BAD_REQUEST).send({ message: err?.message || "Logs Deletion Failed" });
    }
  });
}

module.exports = new ActivityLogController();
