// @ts-ignore
const cache = require("../../api/cache");
const { resetSync } = require("../../api/cache/product");
const ShopifyService = require("../../api/services/ShopifyService");
const ShopService = require("../../api/services/ShopService");
const activityLogService = require("../services/activityLog.service");
const storeService = require("../services/store.service");
const catchAsync = require("../utils/catchAsync");
const httpStatus = require("http-status");
const { IMAGE_OPTIMIZER, AI_OPTIMIZER } = require("storeseo-enums/cacheKeys");
const BulkOperationService = require("../../api/services/BulkOperationService");
const boStatus = require("storeseo-enums/bulkOperationStatus");
const boTypes = require("storeseo-enums/bulkOperationTypes");
const { Op } = require("../../sequelize");
const redisClient = require("../../api/cache/client");
const ProductService = require("../../api/services/ProductService");
const { calculateUsagePercentage } = require("../../api/utils/addonUtils");
const LogOriginTypes = require("storeseo-enums/logOriginTypes");
const { dispatchQueue } = require("../../api/queue/queueDispatcher");
const { QUEUE_NAMES } = require("../../api/queue/config");
const AddonUsageService = require("../../api/services/AddonUsageService");
const { COMBINED, PRO } = require("storeseo-enums/subscriptionAddonType");

class StoresController {
  getStoresList = catchAsync(async (req, res) => {
    const { page, limit, search, plan, status, startDate, endDate, sortBy, sortOrder } = req.query;
    const { fetchedResults, totalStores } = await storeService.getStoresList({
      page,
      limit,
      search,
      plan,
      status,
      startDate,
      endDate,
      sortBy,
      sortOrder,
    });
    res.status(httpStatus.OK).send({ fetchedResults, totalStores });
  });

  getStoresStatistics = catchAsync(async (req, res) => {
    const result = await storeService.getStoresStatistics();
    res.status(httpStatus.OK).send({ data: result });
  });

  storeVerified = catchAsync(async (req, res) => {
    const { id } = req.params;
    const prevShopData = await storeService.getStoresDetails(id);
    const prevShopSettingsData = await storeService.getShopSettingsData(id);
    const shopSettingJSONData = prevShopSettingsData ? JSON.parse(prevShopSettingsData?.value) : {};
    const prevData = { ...prevShopData?.plan_rules, ...shopSettingJSONData };

    const { verification, removeBranding } = await storeService.storeVerified(id);

    const newData = { ...verification?.plan_rules, ...removeBranding?.value };

    activityLogService.addActivityLog({
      req,
      prevData: prevData,
      updatedData: newData,
      subject: `Store Verified for ${prevShopData.name} (${prevShopData.domain})`,
      logOrigin: LogOriginTypes.ADMIN,
      domain: prevShopData.domain,
    });

    res.status(httpStatus.OK).send({ message: "Store is verified", data: newData });
  });

  removeBranding = catchAsync(async (req, res) => {
    const { id } = req.params;
    const shop = await ShopService.getShopById(id, {
      attributes: ["domain", "name"],
    });
    const prevShopSettingsData = await storeService.getShopSettingsData(id);
    const shopSettingJSONData = JSON.parse(prevShopSettingsData?.value);
    const removeBranding = await storeService.removeBranding(id);
    const newData = { ...removeBranding?.value };

    activityLogService.addActivityLog({
      req,
      prevData: shopSettingJSONData,
      updatedData: newData,
      subject: `Branding removed for ${shop.name} (${shop.domain})`,
      logOrigin: LogOriginTypes.ADMIN,
      domain: shop.domain,
    });
    res.status(httpStatus.OK).send({ message: "Store branding removed", data: newData });
  });

  cancelSubscription = catchAsync(async (req, res) => {
    const { id } = req.params;
    const session = await ShopService.getShopById(id, {
      attributes: [["domain", "shop"], ["access_token", "accessToken"], "appSubscriptionId", "name"],
    });

    await ShopifyService.appSubscriptionCancel(session.shop, session.appSubscriptionId);
    await activityLogService.addActivityLog({
      req,
      prevData: {},
      updatedData: {},
      subject: `Subscription cancelled for ${session.name} (${session.shop})`,
      logOrigin: LogOriginTypes.ADMIN,
      domain: session.shop,
    });
    res.status(httpStatus.OK).send({ message: "Subscription cancelled" });
  });

  resetCache = catchAsync(async (req, res) => {
    const { id } = req.params;
    const shop = await ShopService.getShopById(id, {
      attributes: ["domain", "plan_id", "name"],
    });

    await cache.clear(shop.domain);

    await cache.planId(shop.domain, shop.plan_id);

    activityLogService.addActivityLog({
      req,
      prevData: {},
      updatedData: {},
      subject: `Cache reset for ${shop.name} (${shop.domain})`,
      logOrigin: LogOriginTypes.ADMIN,
      domain: shop.domain,
    });

    res.status(httpStatus.OK).send({ message: "Cache reset" });
  });

  resetProductSync = catchAsync(async (req, res) => {
    try {
      const { id } = req.params;
      const shop = await ShopService.getShopById(id, {
        attributes: ["domain", "name"],
      });

      await resetSync(shop.domain);

      await BulkOperationService.updateBulkOperationByCondition(
        {
          shop_id: id,
          op_status: { [Op.or]: [boStatus.PROCESSING, boStatus.PENDING] },
          op_type: boTypes.PRODUCT_SYNC,
        },
        {
          status: boStatus.COMPLETED,
          op_status: boStatus.MANUALLY_ABORTED,
        }
      );

      activityLogService.addActivityLog({
        req,
        prevData: {},
        updatedData: {},
        subject: `Product Sync reset for ${shop.name} (${shop.domain})`,
        logOrigin: LogOriginTypes.ADMIN,
        domain: shop.domain,
      });
      res.status(httpStatus.OK).send({ message: "Product Sync reset" });
    } catch (error) {
      res.status(httpStatus.BAD_REQUEST).send({ message: "Product Sync reset failed" });
    }
  });

  getStoresName = catchAsync(async (req, res) => {
    const { query } = req;
    const { names, pagination } = await storeService.getStoresName(query);
    res.status(200).send({ names, pagination });
  });

  getPlanDetails = catchAsync(async (req, res) => {
    const { id } = req.params;
    const details = await storeService.getPlanDetails(id);
    res.status(httpStatus.OK).send(details);
  });

  getStoreDetails = catchAsync(async (req, res) => {
    const { id } = req.params;
    const details = await storeService.getStoresDetails(id);
    res.status(httpStatus.OK).send(details);
  });

  getShopData = catchAsync(async (req, res) => {
    const { id } = req.params;
    const shopData = await storeService.getShopsdata(id);
    res.status(httpStatus.OK).send(shopData);
  });

  removeShopData = catchAsync(async (req, res) => {
    const { id } = req.params;

    const shop = await ShopService.getShopById(id, {
      attributes: ["domain", "name"],
    });

    const tableNames = req.body?.tableNames;
    const tables = { tableNames };

    await storeService.deleteShopData(id, tableNames);

    activityLogService.addActivityLog({
      req,
      prevData: tables,
      updatedData: null,
      subject: `Shop Data Deleted for ${shop.name} (${shop.domain})`,
      logOrigin: LogOriginTypes.ADMIN,
      domain: shop.domain,
    });

    res.status(httpStatus.OK).send({ message: "Data Deleted" });
  });

  updateStorePlanData = catchAsync(async (req, res) => {
    const { id } = req.params;
    const data = req.body;

    const shop = await ShopService.getShopById(id, {
      attributes: ["domain"],
    });

    // update addons limit in cache

    if (data.imageAddonLimit) {
      await cache.addons.usageLimit(shop.domain, {
        limit: data.imageAddonLimit,
      });
    }

    if (data.recurringAiAddonLimit && data.onetimeAiAddonLimit) {
      await cache.addons.usageLimit(shop.domain, {
        limit: data.recurringAiAddonLimit + data.onetimeAiAddonLimit,
        addon: AI_OPTIMIZER,
      });
    } else {
      await cache.addons.usageLimit(shop.domain, {
        limit: data.recurringAiAddonLimit,
        addon: AI_OPTIMIZER,
      });
    }

    // update addons limit & plan_rules in db

    const prevPlanData = await storeService.getShopPlanData(id);
    const updatedPlanData = await storeService.updateStorePlanData(id, data);

    activityLogService.addActivityLog({
      req,
      prevData: { ...prevPlanData?.plan_rules },
      updatedData: { ...updatedPlanData?.plan_rules },
      subject: "Shop Plans Updated",
      logOrigin: LogOriginTypes.ADMIN,
      domain: shop.domain,
    });

    res.status(httpStatus.OK).send({ message: "Plan Data Updated" });
  });

  getWebhooksData = catchAsync(async (req, res) => {
    const { id } = req.params;
    const webhooksData = await storeService.getWebhooksData(id);
    res.status(httpStatus.OK).send(webhooksData);
  });

  registerWebhooks = catchAsync(async (req, res) => {
    const { id } = req.params;
    const updatedWebhooks = await storeService.registerWebhooks(id);
    res.status(httpStatus.OK).send(updatedWebhooks);
  });

  getAllCacheData = catchAsync(async (req, res) => {
    try {
      const { id } = req.params;
      const shop = await ShopService.getShopById(id, {
        attributes: ["domain"],
      });

      const keys = await redisClient.keys(`*${shop.domain}:*`);

      if (keys.length === 0) {
        return res.status(httpStatus.OK).send({ message: "No cache data found" });
      }

      const values = await redisClient.mGet(keys);
      const cacheData = keys.map((key, index) => ({
        key,
        value: values[index],
      }));
      res.status(httpStatus.OK).send({ message: "success", data: cacheData });
    } catch (error) {
      console.error("Error: ", error);
      res.status(httpStatus.BAD_REQUEST).send({ message: "something went wrong" });
    }
  });

  #getPrevCacheData = async (caches) => {
    const keys = caches.map((cache) => cache.key);
    const previousValues = await redisClient.mGet(keys);
    const previousCacheData = keys.reduce((acc, key, index) => {
      acc[key] = previousValues[index];
      return acc;
    }, {});
    return previousCacheData;
  };

  /**
   * Checks if the addon usage has crossed the email notification threshold
   * @param {string} domain - The shop domain
   * @param {number} usageCount - The addon usage count
   * @param {string} addonGroup - The addon group
   * @returns {Promise<boolean>} Returns true if usage has crossed threshold, false otherwise
   */
  #hasAddonUsageCrossedEmailNotificationThreshold = async (domain, usageCount, addonGroup) => {
    const usageLimit = await cache.addons.usageLimit(domain, {
      addon: addonGroup,
    });

    const ussagePercentage = await calculateUsagePercentage(usageCount, usageLimit);

    const currentThresholdReached = Number(
      await cache.addons.lastEmailSentForUsagePercentage(domain, {
        addon: addonGroup,
      })
    );

    if (ussagePercentage < currentThresholdReached) return true;
    return false;
  };

  /**
   * Updates the AI optimizer usage count in the database
   * @param {string} shopId - The ID of the shop
   * @param {number} prevUsageCount - The AI Optimizer previous usage count
   * @param {number} newUsageCount - The AI Optimizer new usage count
   * @returns {Promise<void>}
   */
  #updateAddonOptimizerUsageCount = async (id, prevUsageCount, newUsageCount, addonType) => {
    try {
      const activeAddons = await AddonUsageService.getActiveRecordsByAddonGroup(id, addonType);

      if (activeAddons.length === 0) return;

      const proAddonType = activeAddons.find((item) => item.addon_type === PRO);
      const combinedAddonType = activeAddons.find((item) => item.addon_type === COMBINED);

      if (proAddonType && combinedAddonType) {
        const reducingUsageCount = prevUsageCount - newUsageCount;

        // if reducing usage count is greater than combined current usage, then reduce from the combined usage first and then from the pro usage
        if (reducingUsageCount > combinedAddonType.current_usage) {
          const remainingUsageCountAfterSubstractedFromCombinedUsage =
            reducingUsageCount - combinedAddonType.current_usage;
          const updatedProAddonUsageCount =
            proAddonType.current_usage - remainingUsageCountAfterSubstractedFromCombinedUsage;

          await AddonUsageService.updateRecordByIdAndAddonType(combinedAddonType.id, COMBINED, {
            current_usage: 0,
          }),
            await AddonUsageService.updateRecordByIdAndAddonType(proAddonType.id, PRO, {
              current_usage: updatedProAddonUsageCount,
            });
        } else {
          // if reducing usage count is less than the combined current usage, then reduce only from the combined usage
          const updatedUsageCount = combinedAddonType.current_usage - reducingUsageCount;
          await AddonUsageService.updateRecordByIdAndAddonType(combinedAddonType.id, COMBINED, {
            current_usage: updatedUsageCount,
          });
        }
      } else {
        await AddonUsageService.updateRecordByIdAndAddonType(combinedAddonType.id, COMBINED, {
          current_usage: newUsageCount,
        });
      }
    } catch (err) {
      console.log("Addon Usage Count Update Failed: ", err);
    }
  };

  updateCacheData = catchAsync(async (req, res) => {
    try {
      const { id } = req.params;
      const { caches } = req.body;

      if (caches.length === 0) {
        return res.status(httpStatus.BAD_REQUEST).send({ message: "No caches to update" });
      }

      const shop = await ShopService.getShopById(id, {
        attributes: ["domain", "name"],
      });

      const prevCacheData = await this.#getPrevCacheData(caches);

      const newCacheData = caches.reduce((acc, cache) => {
        acc[cache.key] = cache.value;
        return acc;
      }, {});

      const aiOptimizerPrevUsageCount = Number(prevCacheData[`${shop.domain}:AI_OPTIMIZER:USAGE_COUNT`]);
      const aiOptimizerNewUsageCount = Number(newCacheData[`${shop.domain}:AI_OPTIMIZER:USAGE_COUNT`]);

      const imageOptimizerPrevUsageCount = Number(prevCacheData[`${shop.domain}:IMAGE_OPTIMIZER:USAGE_COUNT`]);
      const imageOptimizerNewUsageCount = Number(newCacheData[`${shop.domain}:IMAGE_OPTIMIZER:USAGE_COUNT`]);

      const isAiOptimizerUsageCountDecreased = aiOptimizerNewUsageCount < aiOptimizerPrevUsageCount;
      const isImageOptimizerUsageCountDecreased = imageOptimizerNewUsageCount < imageOptimizerPrevUsageCount;

      // if AI Optimizer usage count is changed, then check if the usage has crossed the threshold
      if (aiOptimizerPrevUsageCount !== aiOptimizerNewUsageCount) {
        const hasAiOptimzerUsageCrossedThreshold = await this.#hasAddonUsageCrossedEmailNotificationThreshold(
          shop.domain,
          aiOptimizerNewUsageCount,
          AI_OPTIMIZER
        );

        // resetting the last sent email usage to null if the usage has crossed the threshold
        if (hasAiOptimzerUsageCrossedThreshold) {
          await cache.addons.lastEmailSentForUsagePercentage(shop.domain, {
            addon: AI_OPTIMIZER,
            usagePerecentage: "",
          });
        }
      }

      // if Image Optimizer usage count is changed, then check if the usage has crossed the threshold
      if (imageOptimizerPrevUsageCount !== imageOptimizerNewUsageCount) {
        const hasImageOptimzerUsageCrossedThreshold = await this.#hasAddonUsageCrossedEmailNotificationThreshold(
          shop.domain,
          imageOptimizerNewUsageCount,
          IMAGE_OPTIMIZER
        );

        // resetting the last sent email usage to null if the usage has crossed the threshold
        if (hasImageOptimzerUsageCrossedThreshold) {
          await cache.addons.lastEmailSentForUsagePercentage(shop.domain, {
            addon: IMAGE_OPTIMIZER,
            usagePerecentage: "",
          });
        }
      }

      redisClient.mSet(newCacheData);

      // updating the addon usage db table according to the new cache data
      if (isAiOptimizerUsageCountDecreased) {
        await this.#updateAddonOptimizerUsageCount(
          id,
          aiOptimizerPrevUsageCount,
          aiOptimizerNewUsageCount,
          AI_OPTIMIZER
        );
      }

      if (isImageOptimizerUsageCountDecreased) {
        await this.#updateAddonOptimizerUsageCount(
          id,
          imageOptimizerPrevUsageCount,
          imageOptimizerNewUsageCount,
          IMAGE_OPTIMIZER
        );
      }

      activityLogService.addActivityLog({
        req,
        prevData: prevCacheData,
        updatedData: newCacheData,
        subject: "Cache Updated",
        logOrigin: LogOriginTypes.ADMIN,
        domain: shop.domain,
      });
      res.status(httpStatus.OK).send({ message: "Cache updated" });
    } catch (err) {
      console.error("Error: ", err);
      res.status(httpStatus.BAD_REQUEST).send({ message: err?.message || "Cache update failed" });
    }
  });

  getAddonsData = catchAsync(async (req, res) => {
    const { id } = req.params;
    const addons = await AddonUsageService.getActiveRecords(id);
    res.status(httpStatus.OK).send(addons);
  });

  removeDeletedProducts = catchAsync(async (req, res) => {
    try {
      const { id: shopId } = req.params;
      const shop = await ShopService.getShopById(shopId, {
        attributes: ["domain", "name"],
      });
      const products = await ProductService.getProductsByShopId(shopId, ["product_id"]);

      dispatchQueue({
        queueName: QUEUE_NAMES.REMOVE_DELETED_PRODUCTS_QUEUE,
        message: {
          domain: shop.domain,
          products,
        },
      });

      activityLogService.addActivityLog({
        req,
        prevData: null,
        updatedData: null,
        subject: `Removed deleted products for ${shop.name} (${shop.domain})`,
        logOrigin: LogOriginTypes.ADMIN,
        domain: shop.domain,
      });

      res.status(httpStatus.OK).send({ message: "Inactive products deletion started" });
    } catch (error) {
      res.status(httpStatus.BAD_REQUEST).send({ message: error?.message || "Products deletion failed" });
    }
  });

  changeShopStatus = catchAsync(async (req, res) => {
    try {
      const { id } = req.params;
      const { status } = req.body;
      const prevShopStatus = await storeService.getShopById(id, ["domain", "status"]);
      const updatedShopStatus = await storeService.changeShopStatus(id, status);

      activityLogService.addActivityLog({
        req,
        prevData: { status: prevShopStatus.status },
        updatedData: updatedShopStatus,
        subject: "Shop status updated",
        logOrigin: LogOriginTypes.ADMIN,
        domain: prevShopStatus.domain,
      });

      res.status(httpStatus.OK).send({ message: "Shop status updated", data: updatedShopStatus });
    } catch (err) {
      res.status(httpStatus.BAD_REQUEST).send({ message: err?.message || "Shop status update failed" });
    }
  });
}

module.exports = new StoresController();
