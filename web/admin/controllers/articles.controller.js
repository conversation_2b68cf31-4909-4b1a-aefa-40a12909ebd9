const httpStatus = require("http-status");
const articlesService = require("../services/articles.service");
const catchAsync = require("../utils/catchAsync");

class ArticlesController {
  getArticlesList = catchAsync(async (req, res) => {
    const { search, sortBy, sortOrder, page, limit, score, shop } = req.query;
    const articles = await articlesService.getArticlesList(search, page, limit, score, shop, sortBy, sortOrder);
    res.status(httpStatus.OK).send(articles);
  });

  getArticleDetails = catchAsync(async (req, res) => {
    const { id } = req.params;
    const [article] = await articlesService.getArticleDetails(id);
    res.status(httpStatus.OK).send(article);
  });

  getArticleImage = catchAsync(async (req, res) => {
    const { id } = req.params;
    const articleImage = await articlesService.getArticleImage(id);
    res.status(httpStatus.OK).send(articleImage);
  });

  getArticleMeta = catchAsync(async (req, res) => {
    const { id } = req.params;
    const articleMeta = await articlesService.getArticleMeta(id);
    res.status(httpStatus.OK).send(articleMeta);
  });
}

module.exports = new ArticlesController();
