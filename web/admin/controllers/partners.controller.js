const httpStatus = require("http-status");
const catchAsync = require("../utils/catchAsync");
const partnerService = require("../services/partners.service");
const activityLogService = require("../services/activityLog.service");
const LogOriginTypes = require("storeseo-enums/logOriginTypes");

class PartnersController {
  getPartnersList = catchAsync(async (req, res) => {
    const { search, page, limit, sortBy, sortOrder } = req.query;
    const { count, partnersData } = await partnerService.getPartnersList(search, page, limit, sortBy, sortOrder);
    res.status(httpStatus.OK).send({ count, partnersData });
  });

  getSinglePartner = catchAsync(async (req, res) => {
    try {
      const { id } = req.params;
      const partner = await partnerService.getSinglePartner(id);
      res.status(httpStatus.OK).send(partner);
    } catch (error) {
      res.status(httpStatus.NOT_FOUND).send({ message: "Partner not found" });
    }
  });

  addPartner = catchAsync(async (req, res) => {
    try {
      const newPartner = await partnerService.addPartner(req.body, req.files.logo);

      activityLogService.addActivityLog({
        req,
        prevData: null,
        updatedData: newPartner,
        subject: "Partner Created",
        logOrigin: LogOriginTypes.ADMIN,
        domain: null,
      });
      res.status(httpStatus.OK).send({ message: "Partner Added", data: newPartner });
    } catch (error) {
      res.status(httpStatus.BAD_REQUEST).send({ message: error.message });
    }
  });

  updatePartner = catchAsync(async (req, res) => {
    try {
      const { id } = req.params;
      const prevPartnerData = await partnerService.getSinglePartner(id);
      const updatedPartner = await partnerService.updatePartner(id, req.body, req.files?.logo);

      activityLogService.addActivityLog({
        req,
        prevData: prevPartnerData,
        updatedData: updatedPartner,
        subject: "Partner Updated",
        logOrigin: LogOriginTypes.ADMIN,
        domain: null,
      });

      res.status(httpStatus.OK).send({ message: "Partner is updated", data: updatedPartner });
    } catch (error) {
      res.status(httpStatus.BAD_REQUEST).send({ message: error.message });
    }
  });

  deletePartner = catchAsync(async (req, res) => {
    try {
      const { id } = req.params;
      const prevPartnerData = await partnerService.getSinglePartner(id);
      await partnerService.deletePartner(id);

      activityLogService.addActivityLog({
        req,
        prevData: prevPartnerData,
        updatedData: null,
        subject: "Partner Deleted",
        logOrigin: LogOriginTypes.ADMIN,
        domain: null,
      });

      res.status(httpStatus.OK).send({ message: "Partner is deleted" });
    } catch (error) {
      res.status(httpStatus.BAD_REQUEST).send({ message: "Delete Failed" });
    }
  });
}

module.exports = new PartnersController();
