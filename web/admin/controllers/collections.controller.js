const httpStatus = require("http-status");
const collectionsService = require("../services/collections.service");
const catchAsync = require("../utils/catchAsync");

class CollectionsController {
  getCollectionsList = catchAsync(async (req, res) => {
    const { search, sortBy, sortOrder, page, limit, score, shop } = req.query;
    const collections = await collectionsService.getCollectionsList(
      search,
      page,
      limit,
      score,
      shop,
      sortBy,
      sortOrder
    );
    res.status(httpStatus.OK).send(collections);
  });

  getCollectionDetails = catchAsync(async (req, res) => {
    const { id } = req.params;
    const [collection] = await collectionsService.getCollectionDetails(id);
    res.status(httpStatus.OK).send(collection);
  });

  getCollectionImage = catchAsync(async (req, res) => {
    const { id } = req.params;
    const collectionImage = await collectionsService.getCollectionImage(id);
    res.status(httpStatus.OK).send(collectionImage);
  });

  getcollectionMeta = catchAsync(async (req, res) => {
    const { id } = req.params;
    const collectionMeta = await collectionsService.getCollectionMeta(id);
    res.status(httpStatus.OK).send(collectionMeta);
  });
}

module.exports = new CollectionsController();
