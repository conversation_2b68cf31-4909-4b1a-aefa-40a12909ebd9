const httpStatus = require("http-status");
const subscriptionAddonsService = require("../services/subscriptionAddons.service");
const catchAsync = require("../utils/catchAsync");
const activityLogService = require("../services/activityLog.service");
const LogOriginTypes = require("storeseo-enums/logOriginTypes");

class SubscriptionAddonsController {
  getSubscriptionAddonsList = catchAsync(async (req, res) => {
    const { search, sortBy, sortOrder } = req.query;
    const addons = await subscriptionAddonsService.getSubscriptionAddonsList(search, sortBy, sortOrder);
    res.status(httpStatus.OK).send(addons);
  });

  addSubscriptionAddon = catchAsync(async (req, res) => {
    if (!req?.body?.addonId) {
      const newAddon = await subscriptionAddonsService.addSubscriptionAddon(req.body);
      await activityLogService.addActivityLog({
        req,
        prevData: null,
        updatedData: newAddon,
        subject: "Subscription Addon Created",
        logOrigin: LogOriginTypes.ADMIN,
        domain: null,
      });
      res.status(httpStatus.OK).send({ message: "Addon is created", data: newAddon });
    } else {
      const { addonId, ...addonData } = req.body;
      const prevAddonData = await subscriptionAddonsService.getPrevAddonData(addonId);
      const newAddon = await subscriptionAddonsService.addSubscriptionAddon(addonData);

      activityLogService.addActivityLog({
        req,
        prevData: prevAddonData,
        updatedData: newAddon,
        subject: "Subscription Addon Copied",
        logOrigin: LogOriginTypes.ADMIN,
        domain: null,
      });
      res.status(httpStatus.OK).send({ message: "Addon is Copied", data: newAddon });
    }
  });

  getSingleSubscriptionAddon = catchAsync(async (req, res) => {
    const { id } = req.params;
    const addon = await subscriptionAddonsService.getSingleSubscriptionAddon(id);
    res.status(httpStatus.OK).send(addon);
  });

  updateSubscriptionAddon = catchAsync(async (req, res) => {
    try {
      const { id } = req?.params;

      const prevAddonData = await subscriptionAddonsService.getPrevAddonData(id);
      const updateAddon = await subscriptionAddonsService.updateSubscriptionAddon(id, req.body);

      activityLogService.addActivityLog({
        req,
        prevData: prevAddonData,
        updatedData: updateAddon,
        subject: "Subscription Addon Updated",
        logOrigin: LogOriginTypes.ADMIN,
        domain: null,
      });
      res.status(httpStatus.OK).send({ message: "Addon is updated", data: updateAddon });
    } catch (error) {
      res.status(httpStatus.BAD_REQUEST).send({ message: "Addon update failed" });
    }
  });

  deleteSubscriptionAddon = catchAsync(async (req, res) => {
    const { id } = req.params;
    const prevAddonData = await subscriptionAddonsService.getPrevAddonData(id);
    await subscriptionAddonsService.deleteSubscriptionAddon(id);

    activityLogService.addActivityLog({
      req,
      prevData: prevAddonData,
      updatedData: null,
      subject: "Subscription Addon Deleted",
      logOrigin: LogOriginTypes.ADMIN,
      domain: null,
    });

    res.status(httpStatus.OK).send({ message: "Addon is deleted" });
  });
}

module.exports = new SubscriptionAddonsController();
