// @ts-check
const catchAsync = require("../utils/catchAsync");
const { authService, tokenService } = require("../services");

class AuthController {
  login = catchAsync(async (req, res) => {
    const { email, password } = req.body;
    const user = await authService.loginUserWithEmailAndPassword(email, password);
    const tokens = await tokenService.generateAuthTokens(user);
    res.send({ user, tokens });
  });
  refreshTokens = catchAsync(async (req, res) => {
    const tokens = await authService.refreshAuth(req.body.refreshToken);
    res.send({ ...tokens });
  });
}

module.exports = new AuthController();
