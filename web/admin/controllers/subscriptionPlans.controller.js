const httpStatus = require("http-status");
const subscriptionPlansService = require("../services/subscriptionPlans.service");
const catchAsync = require("../utils/catchAsync");
const activityLogService = require("../services/activityLog.service");
const LogOriginTypes = require("storeseo-enums/logOriginTypes");

class subscriptionPlansController {
  addSubscriptionPlan = catchAsync(async (req, res) => {
    if (!req?.body?.planId) {
      const newPlan = await subscriptionPlansService.addSubscriptionPlan(req.body);
      await activityLogService.addActivityLog({
        req,
        prevData: null,
        updatedData: newPlan,
        subject: "Subscription Plan Created",
        logOrigin: LogOriginTypes.ADMIN,
        domain: null,
      });
      res.status(httpStatus.OK).send({ message: "Plan is created", data: newPlan });
    } else {
      const { planId, ...planData } = req.body;
      const prevPlanData = await subscriptionPlansService.getPrevPlanData(planId);
      const newPlan = await subscriptionPlansService.addSubscriptionPlan(planData);

      activityLogService.addActivityLog({
        req,
        prevData: prevPlanData,
        updatedData: newPlan,
        subject: "Subscription Plan Copied",
        logOrigin: LogOriginTypes.ADMIN,
        domain: null,
      });
      res.status(httpStatus.OK).send({ message: "Plan is Copied", data: newPlan });
    }
  });

  getSubscriptionPlan = catchAsync(async (req, res) => {
    const { search, sortBy, sortOrder } = req.query;
    const plans = await subscriptionPlansService.getSubscriptionPlan(search, sortBy, sortOrder);
    res.status(httpStatus.OK).send(plans);
  });

  deleteSubscriptionPlan = catchAsync(async (req, res) => {
    const { id } = req.params;
    const prevPlanData = await subscriptionPlansService.getPrevPlanData(id);
    await subscriptionPlansService.deleteSubscriptionPlan(id);

    activityLogService.addActivityLog({
      req,
      prevData: prevPlanData,
      updatedData: null,
      subject: "Subscription Plan Deleted",
      logOrigin: LogOriginTypes.ADMIN,
      domain: null,
    });

    res.status(httpStatus.OK).send({ message: "Plan is deleted" });
  });

  updateSubscriptionPlan = catchAsync(async (req, res) => {
    const { id } = req?.params;

    const prevPlanData = await subscriptionPlansService.getPrevPlanData(id);
    const updatePlan = await subscriptionPlansService.updateSubscriptonPlan(id, req.body);

    activityLogService.addActivityLog({
      req,
      prevData: prevPlanData,
      updatedData: updatePlan,
      subject: "Subscription Plan Updated",
      logOrigin: LogOriginTypes.ADMIN,
      domain: null,
    });
    res.status(httpStatus.OK).send({ message: "Plan is updated", data: updatePlan });
  });

  getSingleSubscriptionPlan = catchAsync(async (req, res) => {
    const { id } = req.params;
    const plan = await subscriptionPlansService.getSingleSubscriptionPlan(id);
    res.status(httpStatus.OK).send(plan);
  });
}

module.exports = new subscriptionPlansController();
