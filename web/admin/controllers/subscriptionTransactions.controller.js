const httpStatus = require("http-status");
const subscriptionTransactionsService = require("../services/subscriptionTransactions.service");
const catchAsync = require("../utils/catchAsync");

class subscriptionTransactionsController {
  getSubscriptionTransactionList = catchAsync(async (req, res) => {
    const { search, page, limit, sortBy, sortOrder, shop, plan, status, transaction, startDate, endDate } = req.query;
    const { count, transactionData } = await subscriptionTransactionsService.getSubscriptionTransactionsList(
      search,
      page,
      limit,
      sortBy,
      sortOrder,
      shop,
      plan,
      status,
      transaction,
      startDate,
      endDate
    );
    res.status(httpStatus.OK).send({ count, transactionData });
  });

  getSingleTransactionData = catchAsync(async (req, res) => {
    const { id } = req.params;
    const transactionData = await subscriptionTransactionsService.getSingleTransactionData(id);
    res.status(httpStatus.OK).send(transactionData);
  });
}
module.exports = new subscriptionTransactionsController();
