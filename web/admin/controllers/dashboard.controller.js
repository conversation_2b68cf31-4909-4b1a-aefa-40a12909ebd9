// const balanceService = require('../services/balance.service');
const catchAsync = require("../utils/catchAsync");
const httpStatus = require("http-status");
const dashboardService = require("../services/dashboard.service");

class DashboardController {
  // recent stores API
  recentStores = catchAsync(async (_req, res) => {
    const recentShop = await dashboardService.getRecentStores();
    // console.log('recent stores', recentShop);
    res.status(httpStatus.OK).send({ stores: recentShop });
  });

  // stats counter API
  counter = catchAsync(async (req, res) => {
    const start = req.query.startDate;
    const end = req.query.endDate;
    const stores = await dashboardService.getDashboardOverviewCounter(start, end);
    res.status(httpStatus.OK).send({ stores });
  });

  resourcesStats = catchAsync(async (req, res) => {
    const start = req.query.startDate;
    const end = req.query.endDate;
    const stats = await dashboardService.getDashboardResourcesStats(start, end);
    res.status(httpStatus.OK).send({ stats });
  });

  // chartReport API
  chartReport = catchAsync(async (req, res) => {
    let start = req.query.startDate;
    let end = req.query.endDate;

    const series = await dashboardService.getAllChartReport(start, end);
    res.status(httpStatus.OK).send({ series });
  });

  // customer Growth API
  customerGrowthReport = catchAsync(async (req, res) => {
    let start = req.query.startDate;
    let end = req.query.endDate;

    const series = await dashboardService.getCustomerGrowthChart(start, end);
    res.status(httpStatus.OK).send({ series });
  });
}

module.exports = new DashboardController();
