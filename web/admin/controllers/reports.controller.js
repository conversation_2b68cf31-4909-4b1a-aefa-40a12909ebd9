const AdminReportTypes = require("storeseo-enums/admin/AdminReportTypes");
const { serializeDailyStatsData } = require("../../api/serializers/admin/DailyStatusDataSerializer");
const AdminReportService = require("../../api/services/admin/AdminReportService");
const catchAsync = require("../utils/catchAsync");
const httpStatus = require("http-status");
const { dailyStatusReportDegfaultValue } = require("../config/reportDafaultValues");

class ReportController {
  dailyStatusReport = catchAsync(async (req, res) => {
    const { from, to } = req.query;
    const dates = AdminReportService.prepareAdminReportDates(from, to);

    const report = await AdminReportService.getDailyStatusReportData(
      AdminReportTypes.DAILY_STATUS_REPORT,
      dates.startDate,
      dates.endDate
    );

    const compareReport = await AdminReportService.getDailyStatusReportData(
      AdminReportTypes.DAILY_STATUS_REPORT,
      dates.compareStartDate,
      dates.compareEndDate
    );

    if (!report.date || !report.type) {
      const data = serializeDailyStatsData(report.stats, report.stats);

      return res.status(httpStatus.OK).send({
        status: "REPORT_NOT_GENERATED",
        ...dates,
        ...data,
      });
    }

    const data = serializeDailyStatsData(report.stats, compareReport.stats);
    return res.status(httpStatus.OK).send({
      ...dates,
      ...data,
    });
  });
}

module.exports = new ReportController();
