const httpStatus = require("http-status");
const couponsService = require("../services/coupons.service");
const catchAsync = require("../utils/catchAsync");
const activityLogService = require("../services/activityLog.service");
const LogOriginTypes = require("storeseo-enums/logOriginTypes");

class CouponsController {
  getCouponList = catchAsync(async (req, res) => {
    const { search, sortBy, sortOrder, page, limit } = req.query;
    const coupons = await couponsService.getCouponList(search, sortBy, sortOrder, page, limit);
    res.status(httpStatus.OK).send(coupons);
  });

  deleteCoupon = catchAsync(async (req, res) => {
    const { id } = req.params;
    const prevCouponData = await couponsService.getSingleCoupon(id);
    await couponsService.deleteCoupon(id);

    activityLogService.addActivityLog({
      req,
      prevData: prevCouponData,
      updatedData: null,
      subject: "Coupon Deleted",
      logOrigin: LogOriginTypes.ADMIN,
      domain: null,
    });

    res.status(httpStatus.OK).send({ message: "Coupon is deleted" });
  });

  addCoupon = catchAsync(async (req, res) => {
    if (!req.body.couponId) {
      const createdCoupon = await couponsService.addCoupon(req.body);

      activityLogService.addActivityLog({
        req,
        prevData: null,
        updatedData: createdCoupon,
        subject: "Coupon Created",
        logOrigin: LogOriginTypes.ADMIN,
        domain: null,
      });
      res.status(httpStatus.OK).send({ message: "Coupon is created", data: createdCoupon });
    } else {
      const { couponId, ...couponData } = req.body;
      const prevCouponData = await couponsService.getSingleCoupon(couponId);
      const updatedCoupon = await couponsService.addCoupon(couponData);

      activityLogService.addActivityLog({
        req,
        prevData: prevCouponData,
        updatedData: updatedCoupon,
        subject: "Coupon Copied",
        logOrigin: LogOriginTypes.ADMIN,
        domain: null,
      });
      res.status(httpStatus.OK).send({ message: "Coupon is copied", data: updatedCoupon });
    }
  });

  updateCoupon = catchAsync(async (req, res) => {
    const { id } = req.params;
    const prevCouponData = await couponsService.getSingleCoupon(id);
    const updatedCoupon = await couponsService.updateCoupon(id, req.body);

    activityLogService.addActivityLog({
      req,
      prevData: prevCouponData,
      updatedData: updatedCoupon,
      subject: "Coupon Updated",
      logOrigin: LogOriginTypes.ADMIN,
      domain: null,
    });

    res.status(httpStatus.OK).send({ message: "Coupon is updated", data: updatedCoupon });
  });

  getCouponStatus = catchAsync(async (req, res) => {
    const status = await couponsService.getCouponStatus();
    res.send(status);
  });

  getSingleCoupon = catchAsync(async (req, res) => {
    const { id } = req.params;
    const coupon = await couponsService.getSingleCoupon(id);
    res.status(httpStatus.OK).send({ data: coupon });
  });
}

module.exports = new CouponsController();
