const httpStatus = require("http-status");
const settingsService = require("../services/settings.service");
const catchAsync = require("../utils/catchAsync");
const exclude = require("../utils/exclude");
const AdminScopes = require("storeseo-enums/admin/AdminScopes");
const activityLogService = require("../services/activityLog.service");
const AdminTypes = require("storeseo-enums/admin/AdminTypes");
const LogOriginTypes = require("storeseo-enums/logOriginTypes");

class SettingsController {
  getAdminList = catchAsync(async (req, res) => {
    const { search } = req.query;
    // console.log("req", req?.hasOwnProfile, req.user.id);

    const { admin } = await settingsService.getAdminList(search);

    let filteredAdminData = admin;

    if (req?.hasOwnProfile) {
      filteredAdminData = admin?.filter((adminData) => adminData.id === req.user.id);
    }
    res.status(httpStatus.OK).send(filteredAdminData);
  });

  deleteAdmin = catchAsync(async (req, res) => {
    const { id } = req.params;
    const prevAdminData = await settingsService.getSingleAdmin(id);
    await settingsService.deleteAdmin(id);

    activityLogService.addActivityLog({
      req,
      prevData: prevAdminData,
      updatedData: null,
      subject: "Admin Deleted",
      logOrigin: LogOriginTypes.ADMIN,
      domain: null,
    });

    res.status(httpStatus.OK).send({ message: "Admin is deleted" });
  });

  addAdmin = catchAsync(async (req, res) => {
    const createdAdmin = await settingsService.addAdmin(req.body);
    const { password, ...restAdminData } = createdAdmin;

    activityLogService.addActivityLog({
      req,
      prevData: null,
      updatedData: restAdminData,
      subject: "Admin Created",
      logOrigin: LogOriginTypes.ADMIN,
      domain: null,
    });

    res.status(httpStatus.OK).send({ message: "Admin Created", data: exclude(createdAdmin, ["password"]) });
  });

  updateAdmin = catchAsync(async (req, res) => {
    try {
      const { id } = req?.params;
      const { name, email, type, scopes } = req.body;
      const prevAdminData = await settingsService.getSingleAdmin(id);

      const hasRoleUpdatePermission = [AdminTypes.ADMIN, AdminTypes.SUPER_ADMIN].includes(req.user.type);

      if (!hasRoleUpdatePermission && type) {
        throw new Error("Permission Denied.");
      }

      const admin = await settingsService.updateAdmin(id, name, email, type, scopes);

      activityLogService.addActivityLog({
        req,
        prevData: prevAdminData,
        updatedData: admin,
        subject: "Admin Updated",
        logOrigin: LogOriginTypes.ADMIN,
        domain: null,
      });
      res.status(httpStatus.OK).send({ data: exclude(admin, ["password"]), message: "Admin Updated" });
    } catch (error) {
      res.status(httpStatus.BAD_REQUEST).send({ message: error.message });
    }
  });

  updatePassword = catchAsync(async (req, res) => {
    const { id } = req.params;
    const { password, confirmPassword } = req.body;

    await settingsService.updatePassword(id, password, confirmPassword);

    activityLogService.addActivityLog({
      req,
      prevData: null,
      updatedData: null,
      subject: "Password Changed",
      logOrigin: LogOriginTypes.ADMIN,
      domain: null,
    });

    res.status(httpStatus.OK).send({ message: "Password Changed" });
  });

  getSingleAdmin = catchAsync(async (req, res) => {
    const { id } = req.params;
    const admin = await settingsService.getSingleAdmin(id);
    res.status(httpStatus.OK).send({ data: exclude(admin, ["password"]) });
  });

  getAdminScopes = catchAsync(async (req, res) => {
    return res.status(httpStatus.OK).send(AdminScopes);
  });
}

module.exports = new SettingsController();
