const httpStatus = require("http-status");
const pagesService = require("../services/pages.service");
const catchAsync = require("../utils/catchAsync");

class PagesConteoller {
  getPagesList = catchAsync(async (req, res) => {
    const { search, sortBy, sortOrder, page, limit, score, shop } = req.query;
    const pages = await pagesService.getPagesList(search, page, limit, score, shop, sortBy, sortOrder);
    res.status(httpStatus.OK).send(pages);
  });

  getPageDetails = catchAsync(async (req, res) => {
    const { id } = req.params;
    const [page] = await pagesService.getPageDetails(id);
    res.status(httpStatus.OK).send(page);
  });

  getPageMeta = catchAsync(async (req, res) => {
    const { id } = req.params;
    const pageMeta = await pagesService.getPageMeta(id);
    res.status(httpStatus.OK).send(pageMeta);
  });
}

module.exports = new PagesConteoller();
