const toastMessages = require("storeseo-enums/toastMessages");
const ProductService = require("../../api/services/ProductService");
const productService = require("../services/products.service");
const catchAsync = require("../utils/catchAsync");
const httpStatus = require("http-status");
const { getFocusKeywordSuggestions } = require("../../api/utils/helper");
const { serializeProductOptimizationDetails } = require("../../api/serializers/ProductSerializer");

class ProductsController {
  getProductsList = catchAsync(async (req, res) => {
    const { page, limit, search, score, shop, sortBy, sortOrder } = req.query;
    const { productsData, productsCount } = await productService.getProductsData(
      page,
      limit,
      search,
      score,
      shop,
      sortBy,
      sortOrder
    );
    res.status(httpStatus.OK).send({ productsData, productsCount });
  });

  getProudctImage = catchAsync(async (req, res) => {
    const { id } = req.params;
    const productImage = await productService.getProductImage(id);
    res.status(httpStatus.OK).send({ productImage });
  });

  getProductMeta = catchAsync(async (req, res) => {
    const { id } = req.params;
    const productMeta = await productService.getProductMeta(id);
    res.status(httpStatus.OK).send(productMeta);
  });

  getProdudctDetails = catchAsync(async (req, res) => {
    const { id } = req.params;
    const details = await productService.getProductDetails(id);
    res.status(httpStatus.OK).send(details);
  });

  getProductAnalytics = catchAsync(async (req, res) => {
    try {
      const { shopId } = req.params;

      const [productPromise, paginationPromise] = await Promise.allSettled([
        ProductService.getProductDetailsByShopifyId(shopId, req.params.id),
        ProductService.getPaginationOfProduct(shopId, req.params.id),
      ]);

      const product = productPromise.value;
      const pagination = paginationPromise.value;

      const focusKeywordSuggestions = getFocusKeywordSuggestions({
        title: product?.title,
        description: product?.description,
        meta: product?.meta,
      });

      const optimizationData = serializeProductOptimizationDetails(product?.analysis);

      return res.send({ product, optimizationData, focusKeywordSuggestions, pagination });
    } catch (err) {
      // console.error("err =", err);
      return res.status(httpStatus.INTERNAL_SERVER_ERROR).send({ message: toastMessages.SOMETHING_WENT_WRONG });
    }
  });
}

module.exports = new ProductsController();
