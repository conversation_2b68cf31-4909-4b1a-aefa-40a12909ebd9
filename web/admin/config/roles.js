const allRoles = {
  ["SUPER_ADMIN"]: [],
  ["ADMIN"]: ["getUsers", "manageUsers"],
  ["TESTING"]: [],
  ["MARKETING"]: [],
  ["SUPPORT"]: [],
  ["OTHER"]: [],
};

const allRolesInNumber = {
  ["SUPER_ADMIN"]: 0,
  ["ADMIN"]: 1,
  ["TESTING"]: 2,
  ["MARKETING"]: 3,
  ["SUPPORT"]: 4,
  ["OTHER"]: 9,
};
const roles = Object.keys(allRoles);

const roleRights = new Map(Object.entries(allRoles));

module.exports = {
  roles,
  allRolesInNumber,
  roleRights,
};
