// @ts-check
const dotenv = require("dotenv");
const path = require("path");
const yup = require("yup");

dotenv.config({ path: path.join(process.cwd(), ".env") });

const envVarsSchema = yup.object().shape({
  NODE_ENV: yup.string().oneOf(["production", "development", "test"]).required(),
  ADMIN_PORT: yup.number().default(8000).required(),
  ADMIN_HOST: yup.string().required(),
  ADMIN_JWT_SECRET: yup.string().required(),
  ADMIN_JWT_ACCESS_EXPIRATION: yup.number().default(300000),
  ADMIN_JWT_REFRESH_EXPIRATION: yup.number().default(300000),
});

try {
  const envVars = envVarsSchema.validateSync(process.env, { abortEarly: false });
  module.exports = {
    env: envVars.NODE_ENV,
    port: envVars.ADMIN_PORT,
    HOST: envVars.ADMIN_HOST,
    jwt: {
      secret: envVars.ADMIN_JWT_SECRET,
      accessTokenExpirationDuration: envVars.ADMIN_JWT_ACCESS_EXPIRATION,
      refreshTokenExpirationDuration: envVars.ADMIN_JWT_REFRESH_EXPIRATION,
    },
  };
} catch (error) {
  throw new Error(`Config validation error: ${error.errors.join(", ")}`);
}
