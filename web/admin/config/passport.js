// @ts-check
const { ExtractJwt } = require("passport-jwt");
const { Strategy: JwtStrategy } = require("passport-jwt");
const config = require("./config");
const userTokenType = require("./userTokenType");
const { userService } = require("../services");

const jwtOptions = {
  secretOrKey: config.jwt.secret,
  jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
};

const jwtVerify = async (payload, done) => {
  try {
    if (payload.type !== userTokenType.ACCESS) {
      throw new Error("Invalid token type");
    }
    const user = await userService.getUserById(payload.sub, ["id", "email", "name", "scopes", "type"]);
    if (!user) {
      return done(null, false);
    }
    done(null, user);
  } catch (error) {
    done(error, false);
  }
};

module.exports = new JwtStrategy(jwtOptions, jwtVerify);
