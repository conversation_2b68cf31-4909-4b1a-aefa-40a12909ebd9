//@ts-check
const yup = require("yup");

const urlRegex = /^(https?:\/\/)?([\w\d-]+\.)+[\w\d-]+(\/[\w\d.,@?^=%&:/~+#-]*)?$/;

const addPartner = {
  body: yup.object().shape({
    name: yup.string().required(),
    slug: yup.string().required(),
    content: yup.string().required(),
    url: yup.string().matches(urlRegex, "Invalid URL format").required("URL is required"),
    logo: yup.object().shape({
      mimetype: yup.string().matches(/^image\/.*$/, "Invalid image format"),
    }),
    order: yup.number().required(),
    status: yup.number().required(),
  }),
};

const updatePartner = {
  body: yup.object().shape({
    name: yup.string().strict(),
    slug: yup.string().strict(),
    content: yup.string().strict(),
    url: yup.string().matches(urlRegex, "Invalid URL format"),
    logo: yup.object().shape({
      mimetype: yup.string().matches(/^image\/.*$/, "Invalid image format"),
    }),
    order: yup.number(),
    status: yup.number(),
  }),
};

module.exports = {
  addPartner,
  updatePartner,
};
