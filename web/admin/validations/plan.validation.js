//@ts-check
const yup = require("yup");

const addPlan = {
  body: yup.object().shape({
    planId: yup.number(),
    name: yup.string().required(),
    slug: yup.string().required(),
    type: yup.string().required(),
    interval: yup.string().required(),
    products: yup.number().nullable(),
    features: yup.array().min(1, "Features can't be empty").required(),
    price: yup.number().required(),
    subtotal: yup.number().default(function () {
      return this.parent.price;
    }),
    coupon_code: yup.string().nullable(),
    order: yup.number().required(),
    featured: yup.boolean().required(),
    subtitle: yup.string().required(),
    addons: yup.array().nullable(),
    status: yup.number().required(),
  }),
};

const updatePlan = {
  body: yup.object().shape({
    name: yup.string().optional().strict(),
    slug: yup.string().optional().strict(),
    type: yup.string().optional().strict(),
    interval: yup.string().optional().strict(),
    products: yup.number().nullable().strict(),
    features: yup.array().min(1, "Features can't be empty").optional().strict(),
    price: yup.number().optional().strict(),
    subtotal: yup.number().default(function () {
      return this.parent.price;
    }),
    coupon_code: yup.string().optional().strict().nullable(),
    order: yup.number().optional().strict(),
    featured: yup.boolean().optional(),
    subtitle: yup.string().optional().strict(),
    addons: yup.array().optional().strict().nullable(),
    status: yup.number().optional(),
  }),
};

module.exports = {
  addPlan,
  updatePlan,
};
