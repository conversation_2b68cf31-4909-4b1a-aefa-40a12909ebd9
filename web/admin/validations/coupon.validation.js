//@ts-check
const yup = require("yup");

const addCoupon = {
  body: yup.object().shape({
    couponId: yup.number().nullable(),
    name: yup.string().required(),
    code: yup.string().min(6, "Coupon code must be at least 6 characters").required("Coupon code is required"),
    amount: yup.number().required(),
    discount_type: yup.number().required(),
    discount_duration: yup.number().nullable(),
    max_limit: yup.number().nullable(),
    start_date: yup.date().nullable(),
    end_date: yup.date().nullable(),
    plans: yup.array().nullable(),
    status: yup.number().optional(),
  }),
};

const updateCoupon = {
  body: yup.object().shape({
    name: yup.string().strict().optional(),
    code: yup
      .mixed()
      .notRequired()
      .test("forbidden", "Coupon code cannot be updated", (value) => value === undefined),
    amount: yup
      .mixed()
      .notRequired()
      .test("forbidden", "Amount cannot be updated", (value) => value === undefined),
    discount_type: yup
      .mixed()
      .notRequired()
      .test("forbidden", "Discount type cannot be updated", (value) => value === undefined),
    discount_duration: yup.number().nullable().optional(),
    max_limit: yup.number().nullable().optional(),
    start_date: yup.date().nullable().optional(),
    end_date: yup.date().nullable().optional(),
    plans: yup.array().nullable().optional(),
    status: yup.number().optional(),
  }),
};

module.exports = {
  addCoupon,
  updateCoupon,
};
