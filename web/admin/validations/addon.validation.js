// @ts-check
const yup = require("yup");

const addAddon = {
  body: yup.object().shape({
    addonId: yup.number(),
    name: yup.string().required(),
    group: yup.string().required(),
    type: yup.string().required(),
    interval: yup.string().required(),
    price: yup.number().required(),
    subtotal: yup.number().default(function () {
      return this.parent.price;
    }),
    order: yup.number().required(),
    limit: yup.number().required(),
    featured: yup.boolean().required(),
    status: yup.number().required(),
  }),
};

const updateAddon = {
  body: yup.object().shape({
    name: yup.string().optional().strict(),
    group: yup.string().optional().strict(),
    type: yup.string().optional().strict(),
    interval: yup.string().optional().strict(),
    price: yup.number().optional().strict(),
    subtotal: yup.number().default(function () {
      return this.parent.price;
    }),
    order: yup.number().optional().strict(),
    limit: yup.number().optional().strict(),
    featured: yup.boolean().optional(),
    status: yup.number().optional(),
  }),
};

module.exports = {
  addAddon,
  updateAddon,
};
