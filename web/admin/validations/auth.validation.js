// @ts-check
const yup = require("yup");

const login = {
  body: yup.object().shape({
    email: yup.string().required(),
    password: yup.string().required(),
  }),
};

const logout = {
  body: yup.object().shape({
    refreshToken: yup.string().required(),
  }),
};

const refreshTokens = {
  body: yup.object().shape({
    refreshToken: yup.string().required(),
  }),
};

module.exports = {
  login,
  logout,
  refreshTokens,
};
