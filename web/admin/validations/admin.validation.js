// @ts-check
const yup = require("yup");

const addAdmin = {
  body: yup.object().shape({
    name: yup.string().min(3, "Name must be at least 3 characters").required("Name is required"),
    email: yup.string().email().required(),
    type: yup.number().required(),
    scopes: yup.array().required(),
    password: yup.string().min(6, "Password must be at least 6 characters").required("Password is required"),
    confirmPassword: yup
      .string()
      .oneOf([yup.ref("password")], "Passwords didn't match")
      .required(),
  }),
};

const updateAdmin = {
  body: yup.object().shape({
    name: yup.string().min(3, "Name must be at least 3 characters").optional().strict(),
    email: yup.string().optional().strict(),
    type: yup.number().optional().strict(),
    scopes: yup.array().optional().strict(),
  }),
};

const updatePassword = {
  body: yup.object().shape({
    password: yup.string().min(6).required(),
    confirmPassword: yup
      .string()
      .oneOf([yup.ref("password")], "Passwords didn't match")
      .required(),
  }),
};

module.exports = {
  addAdmin,
  updateAdmin,
  updatePassword,
};
