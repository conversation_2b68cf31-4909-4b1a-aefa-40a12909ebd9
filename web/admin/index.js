try {
  // // @ts-check
  const helmet = require("helmet");
  const cors = require("cors");
  const passport = require("passport");
  const httpStatus = require("http-status");
  const { middleware: xss } = require("./middlewares/xss");
  const jwtStrategy = require("./config/passport");
  // const { authLimiter } = require("./admin/middlewares/rateLimiter");
  const ApiError = require("./utils/apiError");
  const { errorConverter, errorHandler } = require("./middlewares/error");
  const routes = require("./routes/v1");
  const express = require("express");
  const config = require("./config/config");
  const morgan = require("morgan");
  const fileUpload = require("express-fileupload");

  const app = express();
  // set security HTTP headers
  app.use(helmet());
  // parse json request body
  app.use(express.json());
  // parse urlencoded request body
  app.use(express.urlencoded({ extended: true }));
  // sanitize request data
  app.use(xss());
  // enable cors
  app.use(cors());
  app.options("*", cors());
  // jwt authentication
  app.use(passport.initialize());
  app.use(fileUpload({}));

  passport.use("jwt", jwtStrategy); // TODO: implement jwt strategy

  // limit repeated failed requests to auth endpoints
  // if (process.env === "production") {
  //   app.use("/v1/auth", authLimiter);
  // }

  // log requests to console
  app.use(morgan(":method :status :url :res[content-length] - :response-time ms"));

  app.get("/", (req, res) => {
    console.info("Admin server received a request");
    res.json({ message: "Hello World!" });
  });

  // v1 api routes
  app.use("/v1", routes);

  // send back a 404 error for any unknown api request
  app.use((req, res, next) => {
    next(new ApiError(httpStatus.NOT_FOUND, "Not found"));
  });

  // convert error to ApiError, if needed
  app.use(errorConverter);

  // handle error
  app.use(errorHandler);

  app
    .listen(config.port, () => {
      console.info(`Admin server running here ${config.HOST}`);
      console.info(`Press Ctrl+C to quit.`);
    })
    .on("error", (err) => {
      console.error(err);
    });
} catch (error) {
  console.error("error =", error);
}
