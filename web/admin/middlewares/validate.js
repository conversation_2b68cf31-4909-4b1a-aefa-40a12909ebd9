// @ts-check
const httpStatus = require("http-status");
const ApiError = require("../utils/apiError");
const pick = require("../utils/pick");
const yup = require("yup");

const validate = (schema) => async (req, _res, next) => {
  const validSchema = pick(schema, ["params", "query", "body"]);
  const obj = pick(req, Object.keys(validSchema));

  try {
    const value = await yup.object(validSchema).validate(obj, { abortEarly: false });
    const filteredValue = Object.keys(value).reduce((acc, key) => {
      acc[key] = Object.fromEntries(Object.entries(value[key]).filter(([subKey]) => obj[key].hasOwnProperty(subKey)));
      return acc;
    }, {});

    Object.assign(req, filteredValue);
    return next();
  } catch (error) {
    const errorMessage = error.inner.map((err) => err.message).join(", ");
    return next(new ApiError(httpStatus.BAD_REQUEST, errorMessage));
  }
};

module.exports = validate;
