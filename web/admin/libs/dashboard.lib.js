// // @ts-check
const shopStatus = require("storeseo-enums/shopStatus");
const { sequelize } = require("../../sequelize");
const moment = require("moment");
const planType = require("storeseo-enums/planType");

class DashboardLib {
  constructor() {
    this.defaultCustomerGrowthParserConfig = {
      freeShopUntilNow: [[{ total_shops: 0 }]],
      proShopUntilNow: [[{ total_shops: 0 }]],
      freeShopCount: [],
      proShopCount: [],
      timePeriod: [],
      period: "hour",
      xAxesFormat: "h:mm a",
    };
  }
  /**
   * This function is used to query the total number of active shops created until a given date. By default it returns the total number of active shops created until now.
   * @param {*} untilDate
   * @param {*} planCategory - all, free, pro
   * @returns
   */
  async shopCustomerGrowthUntilByDateQuery(untilDate, planCategory = "all") {
    const planCategoryCondition =
      planCategory === "all" ? "" : planCategory == "free" ? `AND plan_id = 1` : `AND plan_id != 1`;
    return await sequelize.query(
      `
        SELECT 
            COUNT(*) AS total_shops
        FROM 
            shops
        WHERE created_at::date < :untilDate AND status = :status ${planCategoryCondition};
      `,
      {
        replacements: {
          untilDate: moment(untilDate).format("YYYY-MM-DD"),
          status: shopStatus.ACTIVE,
        },
        type: sequelize.QueryTypes.SELECT,
      }
    );
  }
  async shopCustomerGrowthByDateQuery(startDate, endDate, planCategory = "all", period = "hour") {
    const planCategoryCondition =
      planCategory === "all" ? "" : planCategory == "free" ? `AND plan_id = 1` : `AND plan_id != 1`;

    return await sequelize.query(
      `
    SELECT 
        date_trunc('${period}',created_at) AS ${period},
        SUM(COUNT(*)) OVER (ORDER BY date_trunc('${period}', created_at)) AS total_shops
    FROM 
        shops
    WHERE created_at::date >= :startDate and created_at::date <= :endDate AND status = :status ${planCategoryCondition}
    GROUP BY 
        date_trunc('${period}', created_at)
    ORDER BY 
        date_trunc('${period}', created_at);
    `,
      {
        replacements: {
          startDate: moment(startDate).format("YYYY-MM-DD"),
          endDate: moment(endDate).format("YYYY-MM-DD"),
          status: shopStatus.ACTIVE,
        },
        type: sequelize.QueryTypes.SELECT,
      }
    );
  }
  parseCustomerGrowthSeriesData(config = this.defaultCustomerGrowthParserConfig) {
    const { freeShopUntilNow, proShopUntilNow, freeShopCount, proShopCount, timePeriod, period, xAxesFormat } = config;
    // Initialize series data for free and pro plan
    const seriesData = {
      [planType.FREE]: [],
      [planType.PRO]: [],
    };
    // Set series data for free plan
    seriesData[planType.FREE] = freeShopCount.map((item) => ({
      x: moment(item[period]).endOf(period).format(xAxesFormat),
      y: (parseInt(item.total_shops) + parseInt(freeShopUntilNow[0].total_shops)).toString(),
    }));
    // push missing time with value of the last time.
    // Fact:
    // 1. The value should be until now value if the time is missing and less than the last time.
    // 2. Otherwise the value should be the same as the last time.
    let flagFreeItem;
    timePeriod.forEach((date) => {
      const found = freeShopCount.find((item) => {
        const isMatch =
          moment(item[period]).endOf(period).format(xAxesFormat) === date.endOf(period).format(xAxesFormat);
        if (isMatch) {
          flagFreeItem = item;
          return true;
        }
        return false;
      });
      if (!found) {
        seriesData[planType.FREE].push({
          x: date.endOf(period).format(xAxesFormat),
          y:
            flagFreeItem && moment(date, xAxesFormat).isAfter(moment(moment(flagFreeItem[period]), xAxesFormat))
              ? (parseInt(flagFreeItem.total_shops) + parseInt(freeShopUntilNow[0].total_shops)).toString()
              : freeShopUntilNow[0].total_shops,
        });
      }
    });
    // Sort series data by time
    seriesData[planType.FREE].sort((a, b) => {
      return moment(a.x, xAxesFormat).isAfter(moment(b.x, xAxesFormat)) ? 1 : -1;
    });
    seriesData[planType.PRO] = proShopCount.map((item) => ({
      x: moment(item[period]).endOf(period).format(xAxesFormat),
      y: (parseInt(item.total_shops) + parseInt(proShopUntilNow[0].total_shops)).toString(),
    }));
    // push missing time with value of the last time.
    // Fact:
    // 1. The value should be until now value if the time is missing and less than the last time.
    // 2. Otherwise the value should be the same as the last time.
    let flagProItem;
    timePeriod.forEach((date) => {
      const found = proShopCount.find((item) => {
        const isMatch =
          moment(item[period]).endOf(period).format(xAxesFormat) === date.endOf(period).format(xAxesFormat);
        if (isMatch) {
          flagProItem = item;
          return true;
        }
        return false;
      });
      if (!found) {
        seriesData[planType.PRO].push({
          x: date.endOf(period).format(xAxesFormat),
          y:
            flagProItem && moment(date, xAxesFormat).isAfter(moment(moment(flagProItem[period]), xAxesFormat))
              ? (parseInt(flagProItem.total_shops) + parseInt(proShopUntilNow[0].total_shops)).toString()
              : proShopUntilNow[0].total_shops,
        });
      }
    });
    // Sort series data by time
    seriesData[planType.PRO].sort((a, b) => {
      return moment(a.x, xAxesFormat).isAfter(moment(b.x, xAxesFormat)) ? 1 : -1;
    });
    const series = [
      {
        name: planType.FREE,
        data: seriesData[planType.FREE],
      },
      {
        name: planType.PRO,
        data: seriesData[planType.PRO],
      },
    ];
    return series;
    // return {};
  }
}

module.exports = new DashboardLib();
