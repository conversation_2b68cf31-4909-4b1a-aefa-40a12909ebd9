"use strict";

const logOriginTypes = require("storeseo-enums/logOriginTypes");
const { ACTIVITY_LOG } = require("../config/table-names");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn(ACTIVITY_LOG, "log_origin", {
      type: Sequelize.STRING(20),
      allowNull: false,
      defaultValue: logOriginTypes.ADMIN,
    });
    await queryInterface.addColumn(ACTIVITY_LOG, "domain", {
      type: Sequelize.STRING,
      defaultValue: null,
    });
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.removeColumn(ACTIVITY_LOG, "log_origin");
    await queryInterface.removeColumn(ACTIVITY_LOG, "domain");
  },
};
