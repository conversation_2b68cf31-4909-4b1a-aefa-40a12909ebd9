{% if template contains 'product' %}
  {%- if shop.metafields.store_seo.local_seo_product_schema.value != null -%}
    {% assign product_schema_settings = shop.metafields.store_seo.local_seo_product_schema.value %}
    {% assign product_merchant_schema_settings = shop.metafields.store_seo.local_seo_product_merchant_schema.value %}

    {% if product_schema_settings.status == true %}
      <script type="application/ld+json" injected-by-storeseo="true">
        [{
          "@context": "http://schema.org/",
          "@type": "Product",
          "name": {{ product.title | json }},
          "url": {{ shop.url | append: product.url | json }},
          "image": {{ product | image_url: width: 115 | prepend: "https:" | json }},
          "description": {{ product.description | strip_html | json }},
          {% if product.selected_or_first_available_variant.sku != blank -%}
          "sku": {{ product.selected_or_first_available_variant.sku | json }},
          {%- endif %}
          "brand": {
            "@type": "Brand",
            "name": {{ product.vendor | json }}
          },
          {% if product_schema_settings.settings.reviewApp == "lai-product-reviews" %}
          {% assign countAverage = product.metafields.scm_review_importer.reviewsData.reviewCountInfo.average | plus: 0 %}
          {%- if countAverage > 0 %}
          "review": {{ product.metafields.scm_review_importer.reviewsData['seoReviews'] }},
                    "aggregateRating": {
                    "@type": "AggregateRating",
                    "ratingValue": "{{ product.metafields.scm_review_importer.reviewsData.reviewCountInfo.average }}",
                    "ratingCount": "{{ product.metafields.scm_review_importer.reviewsData.reviewCountInfo.total }}"
                },
          {%- endif -%}
          {% endif %}
          "offers": [
            {%- for variant in product.variants -%}
              {% assign is_physical_product = variant.requires_shipping %}
              {% assign variant_weight = variant.weight | weight_with_unit: variant.weight_unit| split: ' ' | first | json %}
              {% assign variant_weight_unit = variant.weight_unit |  json %}
            {
              "@type": "Offer",
              {%- if variant.sku != blank -%}
              "sku": {{ variant.sku | json }},
              {%- endif -%}
              "availability": "http://schema.org/{% if variant.available %}InStock{% else %}OutOfStock{% endif %}",
              "price": {{ variant.price | divided_by: 100.00 | json }},
              "priceCurrency": {{ cart.currency.iso_code | json }},
              "url": {{ shop.url | append: variant.url | json }},
              "weight": {
                "@type": "QuantitativeValue",
                "value": {% if is_physical_product %} {{variant_weight}}{% else %}"0"{% endif %},
                "unitCode": {% if is_physical_product %}{{variant_weight_unit}}{% else %} "g"{% endif %}
              }
              {% if product_merchant_schema_settings.status == true %}
              ,"shippingDetails" : {
                "@id": "#store_seo_shipping_policy"
              },
              "hasMerchantReturnPolicy": {
                "@id": "#store_seo_return_policy"
              }
              {% endif %}
            }{% unless forloop.last %},{% endunless %}
            {%- endfor -%}
          ]
        }
        {% if product_merchant_schema_settings.status == true %}
        ,{
          "@context": "https://schema.org/",
          "@type": "OfferShippingDetails",
          "@id": "#store_seo_shipping_policy",
          "shippingRate": {
            "@type": "MonetaryAmount",
            "value": {{ product_merchant_schema_settings.settings.shippingAmount | json }},
            "currency": {{ product_merchant_schema_settings.settings.shippingCurrency | json }}
          },
          "shippingDestination": {
            "@type": "DefinedRegion",
            "addressCountry": {{ product_merchant_schema_settings.settings.shippingDestination | json }}
          },
          "deliveryTime": {
            "@type": "ShippingDeliveryTime",
            "handlingTime": {
              "@type": "QuantitativeValue",
              "minValue": {{ product_merchant_schema_settings.settings.shippingHandingMinDays | json }},
              "maxValue": {{ product_merchant_schema_settings.settings.shippingHandingMaxDays | json }},
              "unitCode": "DAY"
            },
            "transitTime": {
              "@type": "QuantitativeValue",
              "minValue": {{ product_merchant_schema_settings.settings.shippingTransitMinDays | json }},
              "maxValue": {{ product_merchant_schema_settings.settings.shippingTransitMaxDays | json }},
              "unitCode": "DAY"
            }
          }
        },
        {
          "@context": "http://schema.org/",
          "@type": "MerchantReturnPolicy",
          "@id": "#store_seo_return_policy",
          "applicableCountry": {{ product_merchant_schema_settings.settings.returnApplicableCountries | json }},
          "returnPolicyCategory": {{ product_merchant_schema_settings.settings.returnPolicyCategory | json }}
          {% if product_merchant_schema_settings.settings.returnPolicyCategory contains 'MerchantReturnFiniteReturnWindow' %}
          ,"merchantReturnDays": {{ product_merchant_schema_settings.settings.returnDays | json }}
          {% endif %}
          {% if product_merchant_schema_settings.settings.returnPolicyCategory contains 'MerchantReturnFiniteReturnWindow' or product_merchant_schema_settings.settings.returnPolicyCategory contains 'MerchantReturnUnlimitedWindow'  %}
          ,"returnMethod": {{ product_merchant_schema_settings.settings.returnMethod | json }},
          "returnFees": {{ product_merchant_schema_settings.settings.returnFees | json }}
          {% endif %}
        }
        {% endif %}
        ]
      </script>
    {%- endif -%}
  {%- endif -%}
{%- endif -%}