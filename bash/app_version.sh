#!/bin/bash

# Check if the correct number of arguments are provided
if [ "$#" -ne 1 ]; then
  echo "Usage: $0 <value>"
  exit 1
fi

# The value to set for FRONTEND_APP_VERSION
APP_VERSION_VALUE=$1

# Path to the .env file
ENV_FILE="web/.env"

# Check if the .env file exists
if [ ! -f "$ENV_FILE" ]; then
  echo "$ENV_FILE file not found!"
  exit 1
fi

# Use sed to find and replace the FRONTEND_APP_VERSION value
if grep -q "^FRONTEND_APP_VERSION=" "$ENV_FILE"; then
  # If the variable exists, replace it with the new value
  sed -i '' "s/^FRONTEND_APP_VERSION=.*/FRONTEND_APP_VERSION=$APP_VERSION_VALUE/" "$ENV_FILE"
else
  # If the variable doesn't exist, add it to the end of the file
  echo "FRONTEND_APP_VERSION=$APP_VERSION_VALUE" >> "$ENV_FILE"
fi

echo "FRONTEND_APP_VERSION updated to $APP_VERSION_VALUE in $ENV_FILE file."