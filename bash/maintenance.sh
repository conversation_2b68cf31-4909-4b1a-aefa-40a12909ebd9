#!/bin/bash

# Check if the correct number of arguments are provided
if [ "$#" -ne 1 ]; then
  echo "Usage: $0 <value>"
  exit 1
fi

# The value to set for FRONTEND_MAINTENANCE_MODE
MAINTENANCE_MODE_VALUE=$1

# Path to the .env file
ENV_FILE="web/.env"

# Check if the .env file exists
if [ ! -f "$ENV_FILE" ]; then
  echo "$ENV_FILE file not found!"
  exit 1
fi

# Use sed to find and replace the FRONTEND_MAINTENANCE_MODE value
if grep -q "^FRONTEND_MAINTENANCE_MODE=" "$ENV_FILE"; then
  # If the variable exists, replace it with the new value
  sed -i '' "s/^FRONTEND_MAINTENANCE_MODE=.*/FRONTEND_MAINTENANCE_MODE=$MAINTENANCE_MODE_VALUE/" "$ENV_FILE"
else
  # If the variable doesn't exist, add it to the end of the file
  echo "FRONTEND_MAINTENANCE_MODE=$MAINTENANCE_MODE_VALUE" >> "$ENV_FILE"
fi

echo "FRONTEND_MAINTENANCE_MODE updated to $MAINTENANCE_MODE_VALUE in $ENV_FILE file."

echo "Building frontend"
yarn build

echo "Restarting application processes"
yarn app:prod