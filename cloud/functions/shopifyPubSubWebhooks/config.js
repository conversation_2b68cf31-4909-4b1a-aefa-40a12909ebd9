require("dotenv").config();

const QUEUE_NAMES = {
  WEBHOOK_PRODUCT_CREATE: "webhook.product.create",
  WEBHOOK_PRODUCT_UPDATE: "webhook.product.update",
  WEBHOOK_PRODUCT_DELETE: "webhook.product.delete",
  WEBHOOK_APP_SUBSCRIPTION_UPDATE_QUEUE: "webhook.app.subscription.update",
  WEBHOOK_THEME_PUBLISH_QUEUE: "webhook.theme.publish",
  WEBHOOK_SHOP_UPDATE_QUEUE: "webhook.shop.update",
  WEBHOOK_APP_UNINSTALL_QUEUE: "webhook.app.uninstall",
  WEBHOOK_LOCATION_CREATE_QUEUE: "webhook.location.create",
  WEBHOOK_LOCATION_UPDATE_QUEUE: "webhook.location.update",
  WEBHOOK_LOCATION_DELETE_QUEUE: "webhook.location.delete",
  WEBHOOK_BULK_OPERATION_FINISH_QUEUE: "webhook.bulk.operation.finish",
};

const PREFETCH_LIMITS = {
  [QUEUE_NAMES.WEBHOOK_PRODUCT_CREATE]: 2,
  [QUEUE_NAMES.WEBHOOK_PRODUCT_UPDATE]: 5,
  [QUEUE_NAMES.WEBHOOK_PRODUCT_DELETE]: 2,
  [QUEUE_NAMES.WEBHOOK_APP_SUBSCRIPTION_UPDATE_QUEUE]: 3,
  [QUEUE_NAMES.WEBHOOK_THEME_PUBLISH_QUEUE]: 1,
  [QUEUE_NAMES.WEBHOOK_SHOP_UPDATE_QUEUE]: 2,
  [QUEUE_NAMES.WEBHOOK_APP_UNINSTALL_QUEUE]: 1,
  [QUEUE_NAMES.WEBHOOK_LOCATION_CREATE_QUEUE]: 1,
  [QUEUE_NAMES.WEBHOOK_LOCATION_UPDATE_QUEUE]: 1,
  [QUEUE_NAMES.WEBHOOK_LOCATION_DELETE_QUEUE]: 1,
  [QUEUE_NAMES.WEBHOOK_BULK_OPERATION_FINISH_QUEUE]: 1,
};

const EXCHANGE_TYPES = {
  DIRECT: "direct",
  DELAYED: "x-delayed-message",
};

const EXCHANGE_NAMES = {
  DEFAULT: "default",
  EX_DELAYED: "delayed",
  MESSAGE_DUMP: "message-dump",
};

const KEYS = {
  STORESEO: "storeseo",
};

const CONNECTION_CONFIG = {
  host: process.env.AMPQ_HOST || "localhost",
  port: process.env.AMPQ_PORT || 5672,
  username: process.env.AMPQ_USERNAME || "test",
  password: process.env.AMPQ_PASSWORD || "123456",
};

const SHOPIFY_TOPIC_QUEUE_NAMES = {
  "products/create": QUEUE_NAMES.WEBHOOK_PRODUCT_CREATE,
  "products/update": QUEUE_NAMES.WEBHOOK_PRODUCT_UPDATE,
  "products/delete": QUEUE_NAMES.WEBHOOK_PRODUCT_DELETE,

  "app_subscriptions/update": QUEUE_NAMES.WEBHOOK_APP_SUBSCRIPTION_UPDATE_QUEUE,
  "app/uninstalled": QUEUE_NAMES.WEBHOOK_APP_UNINSTALL_QUEUE,

  "shop/update": QUEUE_NAMES.WEBHOOK_SHOP_UPDATE_QUEUE,
  "themes/publish": QUEUE_NAMES.WEBHOOK_THEME_PUBLISH_QUEUE,

  "locations/create": QUEUE_NAMES.WEBHOOK_LOCATION_CREATE_QUEUE,
  "locations/update": QUEUE_NAMES.WEBHOOK_LOCATION_UPDATE_QUEUE,
  "locations/delete": QUEUE_NAMES.WEBHOOK_LOCATION_DELETE_QUEUE,

  "bulk_operations/finish": QUEUE_NAMES.WEBHOOK_BULK_OPERATION_FINISH_QUEUE,
};

const MAX_RETRY_FOR_FAILED_MESSAGES = 3;

module.exports = {
  QUEUE_NAMES,
  PREFETCH_LIMITS,
  EXCHANGE_TYPES,
  EXCHANGE_NAMES,
  KEYS,
  CONNECTION_CONFIG,
  MAX_RETRY_FOR_FAILED_MESSAGES,
  SHOPIFY_TOPIC_QUEUE_NAMES,
};
