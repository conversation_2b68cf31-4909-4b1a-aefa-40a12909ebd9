const functions = require("@google-cloud/functions-framework");
const { SHOPIFY_TOPIC_QUEUE_NAMES, EXCHANGE_NAMES } = require("./config");
const { prepareForDispatch } = require("./queue");

const DISPATCH_CHANNEL = prepareForDispatch();

/**
 *
 * @param {import("@google-cloud/functions-framework").CloudEvent} eventPayload
 */
const destinationQueueName = (eventPayload) => {
  const topic = eventPayload.data.message.attributes["X-Shopify-Topic"];
  return SHOPIFY_TOPIC_QUEUE_NAMES[topic];
};

/**
 *
 * @param {import("@google-cloud/functions-framework").CloudEvent} eventPayload
 * @returns {{ headers: { [x]: string }, body: { [x]: string }}}
 */
const formatMessage = (eventPayload) => {
  const { attributes, data } = eventPayload.data.message;
  return {
    headers: attributes,
    body: JSON.parse(Buffer.from(data, "base64").toString()),
  };
};

/**
 * Callback to verfiy that RabbitMQ has received the message & routed it properly
 * @param {{ message: any, queue: string, ttl?: number, options: import("amqplib").Options.Publish }} message
 * @param {CallableFunction} resolve
 * @returns rabbitmq confirm channel message deliver callback
 */
const messageDeliveryCallback = (messageDetails, resolve) => (err, ok) => {
  if (err !== null) {
    console.error("Failed to dispatch: ", message.message, err);
    console.log("Trying to dispatch again...");
    dispatchQueue(messageDetails, resolve);
  } else {
    console.log("dispatched ", messageDetails.queue);
    resolve(true);
  }
};

/**
 * @param {{ message: any, queue: string, ttl: number | null, options: import("amqplib").Options.Publish }} messageDetails
 * @param {CallableFunction} resolve
 */
const dispatchQueue = async (messageDetails, resolve) => {
  const { queue, message } = messageDetails;
  const channel = await DISPATCH_CHANNEL;

  channel.publish(
    EXCHANGE_NAMES.DEFAULT,
    queue,
    Buffer.from(JSON.stringify(message)),
    { persistent: true },
    messageDeliveryCallback(messageDetails, resolve)
  );
};

functions.cloudEvent("shopify-webhook", async (eventPayload) => {
  const queue = destinationQueueName(eventPayload);
  const message = formatMessage(eventPayload);

  await new Promise((resolve) => {
    dispatchQueue(
      {
        queue,
        message,
      },
      resolve
    );
  });
});
