const amqp = require("amqplib");

const { QUEUE_NAMES, EXCHANGE_NAMES, EXCHANGE_TYPES, CONNECTION_CONFIG } = require("./config");

const createConnection = async (name = "PUB_SUB") => {
  try {
    const conn = await amqp.connect(`amqp://${CONNECTION_CONFIG.host}:${CONNECTION_CONFIG.port}`, {
      credentials: amqp.credentials.plain(`${CONNECTION_CONFIG.username}`, `${CONNECTION_CONFIG.password}`),
      clientProperties: { connection_name: name },
    });

    console.log("> RabbitMQ connection success =>", `${CONNECTION_CONFIG.host}:${CONNECTION_CONFIG.port}`);

    return conn;
  } catch (err) {
    console.error("RabbitMQ connection failed to", `${CONNECTION_CONFIG.host}:${CONNECTION_CONFIG.port}`, err);
    return false;
  }
};

/**
 * @type {Promise<import("amqplib").Connection>}
 */
const RABBIT_MQ_CONNECTION = createConnection(process.env?.QUEUE_CONN_NAME);

/**
 * Assert exchanges into existance for use in queue consume & dispatch
 * @param {import("amqplib").Channel} channel
 * @returns {Promise<import("amqplib").Channel} same channel provided in the input
 */
const createExchanges = async (channel) => {
  await channel.assertExchange(EXCHANGE_NAMES.DEFAULT, EXCHANGE_TYPES.DIRECT);
  await channel.assertExchange(EXCHANGE_NAMES.EX_DELAYED, EXCHANGE_TYPES.DELAYED, {
    arguments: { "x-delayed-type": "direct" },
  });
  await channel.assertExchange(EXCHANGE_NAMES.MESSAGE_DUMP, EXCHANGE_TYPES.DIRECT);

  return channel;
};

/**
 * Prepare queue with exchange & routing key after asserting the queue into existance
 * @param {QueueDeclarationInput} input
 * @returns {Promise<void>}
 */
const declareQueue = async ({ queueName, exchanges, routingKeys = null, options = {} }) => {
  const connection = await RABBIT_MQ_CONNECTION;
  const channel = await connection.createChannel();

  await channel.assertQueue(queueName, options);

  await createExchanges(channel);

  routingKeys = routingKeys || [queueName];

  for (let exchange of exchanges) {
    for (let routingKey of routingKeys) {
      await channel.bindQueue(queueName, exchange, routingKey);
    }
  }

  await channel.close();
};

/**
 * Prepare & create all the queues with proper routing
 */
const declareQueues = async () => {
  const { MESSAGE_DUMP_QUEUE, ...queues } = QUEUE_NAMES;
  const { DEFAULT, EX_DELAYED } = EXCHANGE_NAMES;

  for (let queueName of Object.values(queues)) {
    await declareQueue({ queueName, exchanges: [DEFAULT, EX_DELAYED] });
  }

  console.log("queues declared and ready!");
  return true;
};

/**
 * Create a new channel & perapre it for message dispatch
 * @returns {Promise<import("amqplib").ConfirmChannel>}
 */
const prepareDispatchChannel = async () => {
  console.log("creating dispatch channel...");
  const connection = await RABBIT_MQ_CONNECTION;
  const channel = await connection.createConfirmChannel();
  await createExchanges(channel);
  console.log("done.");

  return channel;
};

const prepareForDispatch = async () => {
  await declareQueues();
  return prepareDispatchChannel();
};

module.exports = {
  declareQueues,
  prepareForDispatch,
};
