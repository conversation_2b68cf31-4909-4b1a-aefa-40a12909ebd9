{"name": "shopifypubsubwebhooks", "version": "1.0.0", "private": true, "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "npm-watch start", "start": "functions-framework --target=shopify-webhook --signature-type=cloudevent", "deploy:dev": "gcloud functions deploy shopify_webhook_handler_dev --gen2 --region=us-east1 --runtime=nodejs18 --source=. --env-vars-file .env.dev.yaml --entry-point=shopify-webhook --trigger-topic=shopify-webhook-dev", "deploy:prod": "gcloud functions deploy shopify_webhook_handler_prod --gen2 --region=us-east1 --runtime=nodejs18 --memory=2GiB --source=. --env-vars-file .env.prod.yaml --entry-point=shopify-webhook --trigger-topic=shopify-webhook-prod"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@google-cloud/functions-framework": "^3.2.0", "amqplib": "^0.10.3", "dotenv": "^16.3.1"}, "devDependencies": {"npm-watch": "^0.11.0"}, "watch": {"start": "*.js"}}