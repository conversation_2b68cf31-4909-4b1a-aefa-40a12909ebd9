server {
    listen 80;
    server_name admin.v2.storeseo.dev;

    # Redirect all traffic to the new domain
    location / {
        return 301 https://admin.storeseo.dev$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name admin.v2.storeseo.dev;

    # SSL configuration (use your certificate and key files)
    ssl_certificate /etc/letsencrypt/live/admin.v2.storeseo.dev/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/admin.v2.storeseo.dev/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot

    # Redirect all traffic to the new domain
    location / {
        return 301 https://admin.storeseo.dev$request_uri;
    }
}