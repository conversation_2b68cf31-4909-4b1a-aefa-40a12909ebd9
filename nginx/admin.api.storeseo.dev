server {
   server_name admin.api.storeseo.dev;

    gzip on;
    gzip_proxied any;
    gzip_comp_level 5;
    gzip_types
        text/css
        text/javascript
        text/xml
        text/plain
        text/x-component
        application/javascript
        application/json
        application/xml
        application/rss+xml
        font/truetype
        font/opentype
        application/vnd.ms-fontobject
        image/svg+xml;

   client_max_body_size 10M;

   location /{
        proxy_pass http://localhost:4500;
        proxy_set_header Host $host;
        proxy_set_header X-Real-Ip $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        error_page 502 /500.html;
   }

   location /500.html{
        return 500 "<center>Nothing to show...</center>";
   }

}