server {
        root /var/www/html/admin.storeseo.dev/public;

        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-Content-Type-Options "nosniff";

        index index.php index.html index.htm index.nginx-debian.html;

        charset utf-8;

        server_name admin.storeseo.dev;

        location / {
               try_files $uri $uri/ /index.php?$query_string;
        }

        location = /favicon.ico { access_log off; log_not_found off; }
        location = /robots.txt  { access_log off; log_not_found off; }

        error_page 404 /index.php;

        location ~ \.php$ {
               include snippets/fastcgi-php.conf;
               fastcgi_pass unix:/run/php/php8.1-fpm.sock;
               fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        }

        location ~ /\.ht {
                deny all;
        }

location ~ /\.(?!well-known).* {
        deny all;
    }


    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/admin.storeseo.dev/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/admin.storeseo.dev/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot

}
server {
    if ($host = admin.storeseo.dev) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


        listen 80;

        server_name admin.storeseo.dev;
    return 404; # managed by Certbot
}