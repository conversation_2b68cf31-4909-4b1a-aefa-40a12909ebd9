server {
   server_name admin.storeseo.dev;

    gzip on;
    gzip_proxied any;
    gzip_comp_level 5;
    gzip_types
        text/css
        text/javascript
        text/xml
        text/plain
        text/x-component
        application/javascript
        application/json
        application/xml
        application/rss+xml
        font/truetype
        font/opentype
        application/vnd.ms-fontobject
        image/svg+xml;

   client_max_body_size 10M;

   location /{
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-Ip $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        error_page 502 /500.html;
   }

   location /500.html{
        return 500 "<center>Nothing to show...</center>";
   }


    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/admin.storeseo.dev/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/admin.storeseo.dev/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot

}
server {
    if ($host = admin.storeseo.dev) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


   server_name admin.storeseo.dev;
    listen 80;
    return 404; # managed by Certbot


}