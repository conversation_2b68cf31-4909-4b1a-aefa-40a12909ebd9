FROM node:14.5.0

WORKDIR /storeseo

COPY package.json ./

RUN npm install

ENV NODE_ENV=production
ENV APP_URL=https://app.storeseo.dev
ENV DB_CONNECTION=pgsql
ENV DB_HOST=postgres
ENV DB_PORT=5432
ENV DB_NAME=storeseo
ENV DB_USER=storeseo
ENV DB_PASSWORD=storeseo
ENV SHOPIFY_API_KEY=27e9ed11ba279f2c9c7071ad5ba23043
ENV SHOPIFY_API_SECRET=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
ENV SCOPES=read_products,write_products,read_script_tags,write_script_tags,read_analytics
ENV SHOPIFY_API_VERSION="2021-07"
ENV HOST=https://app.storeseo.dev
ENV SHOPIFY_SUBSCRIPTION_TEST_MODE=true
ENV NEXT_PUBLIC_API_BASE_URL=https://app.storeseo.dev/api/v1
ENV NEXT_PUBLIC_API_API_KEY=27e9ed11ba279f2c9c7071ad5ba23043
ENV NEXT_PUBLIC_API_APP_VERSION=1.0.0

ENV AMPQ_HOST=rabbitmq
ENV AMPQ_PORT=5672
ENV AMPQ_USERNAME=storeseo
ENV AMPQ_PASSWORD=123456

COPY . .

CMD ["sh", "-c", "NODE_ENV=production node server/job/QueueV2/process.js"]
